#!/usr/bin/env python3
"""
Test WNBA Data Integration
"""

import asyncio
import sys
import os
sys.path.insert(0, os.getcwd())

async def test_wnba_integration():
    from src.data.wnba_data_integration import wnba_service
    from src.data.basketball_data_loader import BasketballDataLoader
    
    print('🏀 Testing WNBA Data Integration...')
    
    # Test WNBA service directly
    print('\n1. Testing WNBA Service:')
    team_data = await wnba_service.get_team_by_name('New York Liberty')
    if team_data:
        print(f'✅ Found: {team_data.team_name} - {team_data.wins}W-{team_data.losses}L ({team_data.win_percentage:.3f})')
    else:
        print('❌ WNBA team not found')
    
    # Test basketball data loader
    print('\n2. Testing Basketball Data Loader:')
    loader = BasketballDataLoader()
    
    # Test WNBA team stats
    stats = await loader.get_team_stats('New York Liberty', 'WNBA')
    if stats:
        print(f'✅ WNBA Stats: {stats["team_name"]} - {stats["wins"]}W-{stats["losses"]}L')
        print(f'   Win Rate: {stats["win_rate"]:.3f}')
    else:
        print('❌ WNBA stats not found')
    
    # Test WNBA win rate
    win_rate = await loader.get_team_win_rate('New York Liberty', 'WNBA')
    print(f'✅ WNBA Win Rate: {win_rate:.3f}')
    
    print('\n3. Testing Multiple Teams:')
    teams = ['Las Vegas Aces', 'Minnesota Lynx', 'Connecticut Sun']
    for team in teams:
        team_data = await wnba_service.get_team_by_name(team)
        if team_data:
            print(f'✅ {team}: {team_data.wins}W-{team_data.losses}L')
        else:
            print(f'❌ {team}: Not found')

if __name__ == "__main__":
    asyncio.run(test_wnba_integration())
