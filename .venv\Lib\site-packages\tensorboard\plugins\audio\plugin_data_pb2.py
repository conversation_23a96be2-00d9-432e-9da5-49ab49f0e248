# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/audio/plugin_data.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n+tensorboard/plugins/audio/plugin_data.proto\x12\x0btensorboard\"\x9a\x01\n\x0f\x41udioPluginData\x12\x0f\n\x07version\x18\x01 \x01(\x05\x12\x37\n\x08\x65ncoding\x18\x02 \x01(\x0e\x32%.tensorboard.AudioPluginData.Encoding\x12\x1b\n\x13\x63onverted_to_tensor\x18\x03 \x01(\x08\" \n\x08\x45ncoding\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x07\n\x03WAV\x10\x0b\x62\x06proto3')



_AUDIOPLUGINDATA = DESCRIPTOR.message_types_by_name['AudioPluginData']
_AUDIOPLUGINDATA_ENCODING = _AUDIOPLUGINDATA.enum_types_by_name['Encoding']
AudioPluginData = _reflection.GeneratedProtocolMessageType('AudioPluginData', (_message.Message,), {
  'DESCRIPTOR' : _AUDIOPLUGINDATA,
  '__module__' : 'tensorboard.plugins.audio.plugin_data_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.AudioPluginData)
  })
_sym_db.RegisterMessage(AudioPluginData)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _AUDIOPLUGINDATA._serialized_start=61
  _AUDIOPLUGINDATA._serialized_end=215
  _AUDIOPLUGINDATA_ENCODING._serialized_start=183
  _AUDIOPLUGINDATA_ENCODING._serialized_end=215
# @@protoc_insertion_point(module_scope)
