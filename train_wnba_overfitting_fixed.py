#!/usr/bin/env python3
"""
WNBA Training with Overfitting Prevention
========================================
Enhanced training script with comprehensive overfitting prevention measures.
"""

import logging
from src.neural_cortex.neural_training_pipeline import NeuralTrainingPipeline, create_training_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """Train WNBA models with overfitting prevention"""
    logger.info("🏀 MEDUSA VAULT: WNBA TRAINING WITH OVERFITTING PREVENTION")
    logger.info("🔧 Enhanced regularization and early stopping enabled")
    logger.info("=" * 70)
    
    # WNBA Training Configuration - OVERFITTING PREVENTION
    wnba_config = create_training_config(
        league="WNBA",
        # REDUCED MODEL CAPACITY
        hidden_dim=128,        # Reduced from 512 to prevent overfitting
        num_layers=3,          # Reduced from 5 to prevent overfitting
        dropout_rate=0.6,      # Increased from 0.4 for better regularization
        
        # IMPROVED TRAINING PARAMETERS
        batch_size=128,        # Increased from 64 for better gradient estimates
        learning_rate=0.0005,  # Reduced from 0.001 for more stable training
        num_epochs=20,         # Reduced from 50 to prevent overfitting
        early_stopping_patience=3,  # Reduced from 7 for faster early stopping
        weight_decay=1e-3,     # Increased from 1e-4 for stronger regularization
        
        # ENHANCED FEATURES
        use_data_augmentation=True,
        mixed_precision=True,
        model_save_path="./models/neural_core/wnba_overfitting_fixed"
    )
    
    logger.info("🏀 Starting WNBA Neural Training Pipeline...")
    logger.info("📊 Expected data: 104,463 WNBA records")
    logger.info("🎯 Target: Generalizable WNBA predictions (overfitting prevented)")
    logger.info("🔧 Overfitting Prevention Measures:")
    logger.info("   • Reduced model capacity (128 hidden, 3 layers)")
    logger.info("   • Increased dropout (0.6)")
    logger.info("   • Stronger weight decay (1e-3)")
    logger.info("   • Stricter early stopping (3 patience)")
    logger.info("   • Class balancing for imbalanced data")
    logger.info("   • Temporal data splitting")
    logger.info("-" * 50)
    
    # Initialize WNBA training pipeline
    wnba_pipeline = NeuralTrainingPipeline(wnba_config)
    
    # Start WNBA training
    logger.info("🚀 LAUNCHING WNBA TRAINING WITH OVERFITTING PREVENTION...")
    wnba_results = wnba_pipeline.train()
    
    # Report WNBA results
    logger.info("✅ WNBA TRAINING COMPLETED!")
    logger.info(f"🎯 Best validation loss: {wnba_results['best_val_loss']:.4f}")
    logger.info(f"🎯 Test accuracy: {wnba_results['test_metrics']['accuracy']:.3f}")
    logger.info("🏀 WNBA MODEL READY FOR GENERALIZED PREDICTIONS!")
    
    # Analyze overfitting prevention effectiveness
    training_history = wnba_results.get('training_history', [])
    if training_history:
        final_epoch = training_history[-1]
        train_acc = final_epoch.get('train_accuracy', 0)
        val_acc = final_epoch.get('val_accuracy', 0)
        overfitting_gap = train_acc - val_acc
        
        logger.info("=" * 50)
        logger.info("📊 OVERFITTING ANALYSIS:")
        logger.info(f"   Final Training Accuracy: {train_acc:.3f}")
        logger.info(f"   Final Validation Accuracy: {val_acc:.3f}")
        logger.info(f"   Overfitting Gap: {overfitting_gap:.3f}")
        
        if overfitting_gap < 0.05:
            logger.info("   ✅ EXCELLENT: Minimal overfitting detected!")
        elif overfitting_gap < 0.10:
            logger.info("   ✅ GOOD: Low overfitting detected")
        elif overfitting_gap < 0.15:
            logger.info("   ⚠️ MODERATE: Some overfitting detected")
        else:
            logger.info("   🚨 HIGH: Significant overfitting detected")
        
        logger.info(f"   Total Epochs Completed: {len(training_history)}")
        logger.info("=" * 50)
    
    return wnba_results

if __name__ == "__main__":
    main()
