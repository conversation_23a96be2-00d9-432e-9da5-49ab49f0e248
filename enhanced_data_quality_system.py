import asyncio
import sqlite3
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

#!/usr/bin/env python3
"""
Enhanced Data Quality Assurance System
=====================================

Comprehensive data quality system that builds upon existing validation infrastructure
and integrates with the consolidated basketball analytics database.

Features:
- Real-time data quality monitoring
- Neural threat detection for data integrity
- Automated data cleaning and preprocessing
- Quality scoring and reporting
- Integration with existing ExpertDataValidator
"""


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QualityLevel(Enum):
    """Data quality assessment levels"""
    PRISTINE = "PRISTINE"      # 95-100%
    EXCELLENT = "EXCELLENT"    # 90-95%
    GOOD = "GOOD"             # 80-90%
    FAIR = "FAIR"             # 70-80%
    POOR = "POOR"             # 50-70%
    CRITICAL = "CRITICAL"     # <50%

@dataclass
class QualityMetrics:
    """Comprehensive quality metrics for basketball data"""
    table_name: str
    total_records: int
    completeness_score: float
    consistency_score: float
    accuracy_score: float
    uniqueness_score: float
    timeliness_score: float
    overall_score: float
    quality_level: QualityLevel
    anomalies_detected: int
    recommendations: List[str]
    timestamp: datetime

@dataclass
class DataAnomalyReport:
    """Report for detected data anomalies"""
    table_name: str
    anomaly_type: str
    severity: str
    affected_records: int
    description: str
    suggested_action: str
    timestamp: datetime

class EnhancedDataQualitySystem:
    """
    Enhanced Data Quality Assurance System for HYPER MEDUSA NEURAL VAULT
    
    Integrates with existing validation systems and provides comprehensive
    quality monitoring for the consolidated basketball analytics database.
    """
    
    def __init__(self, db_path: str = "hyper_medusa_consolidated.db"):
        self.db_path = db_path
        self.quality_history = []
        self.anomaly_reports = []
        
        # Quality thresholds
        self.quality_thresholds = {
            'completeness': 0.85,
            'consistency': 0.80,
            'accuracy': 0.75,
            'uniqueness': 0.95,
            'timeliness': 0.70,
            'overall': 0.80
        }
        
        # Basketball-specific validation rules
        self.basketball_rules = {
            'teams': {
                'required_fields': ['team_id', 'team_name', 'abbreviation'],
                'numeric_ranges': {},
                'categorical_values': {}
            },
            'players': {
                'required_fields': ['player_id', 'player_name', 'team_id'],
                'numeric_ranges': {
                    'height': (60, 90),  # inches
                    'weight': (150, 350),  # pounds
                    'age': (18, 50)
                },
                'categorical_values': {}
            },
            'games': {
                'required_fields': ['game_id', 'home_team_id', 'away_team_id', 'game_date'],
                'numeric_ranges': {
                    'home_score': (50, 200),
                    'away_score': (50, 200)
                },
                'categorical_values': {}
            },
            'player_game_stats': {
                'required_fields': ['player_id', 'game_id'],
                'numeric_ranges': {
                    'points': (0, 100),
                    'rebounds': (0, 30),
                    'assists': (0, 25),
                    'minutes': (0, 48)
                },
                'categorical_values': {}
            },
            'shot_analytics': {
                'required_fields': ['player_id', 'season', 'league'],
                'numeric_ranges': {
                    'fgm': (0, 1000),
                    'fga': (0, 2000),
                    'fg_pct': (0, 1)
                },
                'categorical_values': {
                    'league': ['NBA', 'WNBA'],
                    'shot_distance': ['5ft', '8ft', 'by_zone']
                }
            },
            'clutch_analytics': {
                'required_fields': ['player_id', 'season', 'league'],
                'numeric_ranges': {
                    'clutch_points': (0, 50),
                    'clutch_fg_pct': (0, 1)
                },
                'categorical_values': {
                    'league': ['NBA', 'WNBA'],
                    'clutch_situation': ['1min_5pt', '2min_5pt', '5min_3pt', '5min_5pt', '5min_10pt']
                }
            },
            'advanced_analytics': {
                'required_fields': ['player_id', 'season', 'league'],
                'numeric_ranges': {
                    'off_rating': (50, 150),
                    'def_rating': (50, 150),
                    'usage_pct': (0, 50),
                    'true_shooting_pct': (0, 1)
                },
                'categorical_values': {
                    'league': ['NBA', 'WNBA']
                }
            },
            'defense_analytics': {
                'required_fields': ['player_id', 'season', 'league'],
                'numeric_ranges': {
                    'def_rating': (50, 150),
                    'steals': (0, 5),
                    'blocks': (0, 10)
                },
                'categorical_values': {
                    'league': ['NBA', 'WNBA']
                }
            },
            'shooting_analytics': {
                'required_fields': ['player_id', 'season', 'league'],
                'numeric_ranges': {
                    'fg_pct': (0, 1),
                    'fg3_pct': (0, 1),
                    'efg_pct': (0, 1)
                },
                'categorical_values': {
                    'league': ['NBA', 'WNBA'],
                    'shot_category': ['catch_shoot', 'pullup_shot']
                }
            }
        }
        
        logger.info("🔍 Enhanced Data Quality System initialized")
    
    async def run_comprehensive_quality_assessment(self) -> Dict[str, QualityMetrics]:
        """Run comprehensive quality assessment on all tables"""
        logger.info("🚀 Starting comprehensive data quality assessment")
        
        quality_results = {}
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get all table names
            tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table'").fetchall()
            
            for table_tuple in tables:
                table_name = table_tuple[0]
                logger.info(f"📊 Assessing quality for table: {table_name}")
                
                # Load table data
                df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
                
                # Assess quality metrics
                quality_metrics = await self._assess_table_quality(table_name, df)
                quality_results[table_name] = quality_metrics
                
                # Store in history
                self.quality_history.append(quality_metrics)
            
            conn.close()
            
            # Generate overall assessment
            await self._generate_quality_summary(quality_results)
            
            logger.info("✅ Comprehensive quality assessment completed")
            return quality_results
            
        except Exception as e:
            logger.error(f"❌ Quality assessment failed: {e}")
            raise
    
    async def _assess_table_quality(self, table_name: str, df: pd.DataFrame) -> QualityMetrics:
        """Assess quality metrics for a specific table"""
        
        # Calculate individual quality scores
        completeness = self._calculate_completeness(df)
        consistency = await self._calculate_consistency(table_name, df)
        accuracy = await self._calculate_accuracy(table_name, df)
        uniqueness = self._calculate_uniqueness(df)
        timeliness = await self._calculate_timeliness(table_name, df)
        
        # Calculate overall score
        overall_score = (completeness + consistency + accuracy + uniqueness + timeliness) / 5
        
        # Determine quality level
        quality_level = self._determine_quality_level(overall_score)
        
        # Detect anomalies
        anomalies = await self._detect_table_anomalies(table_name, df)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(
            table_name, completeness, consistency, accuracy, uniqueness, timeliness, anomalies
        )
        
        return QualityMetrics(
            table_name=table_name,
            total_records=len(df),
            completeness_score=completeness,
            consistency_score=consistency,
            accuracy_score=accuracy,
            uniqueness_score=uniqueness,
            timeliness_score=timeliness,
            overall_score=overall_score,
            quality_level=quality_level,
            anomalies_detected=len(anomalies),
            recommendations=recommendations,
            timestamp=datetime.now()
        )
    
    def _calculate_completeness(self, df: pd.DataFrame) -> float:
        """Calculate data completeness score"""
        if df.empty:
            return 0.0
        
        total_cells = df.shape[0] * df.shape[1]
        missing_cells = df.isnull().sum().sum()
        completeness = 1.0 - (missing_cells / total_cells)
        
        return max(0.0, min(1.0, completeness))
    
    async def _calculate_consistency(self, table_name: str, df: pd.DataFrame) -> float:
        """Calculate data consistency score"""
        if df.empty:
            return 0.0
        
        consistency_score = 1.0
        
        # Check for consistent data types
        for column in df.columns:
            if column in df.select_dtypes(include=[np.number]).columns:
                # Check for numeric consistency
                numeric_series = pd.to_numeric(df[column], errors='coerce')
                consistency_score *= (1.0 - numeric_series.isnull().sum() / len(df))
        
        return max(0.0, min(1.0, consistency_score))

    async def _calculate_accuracy(self, table_name: str, df: pd.DataFrame) -> float:
        """Calculate data accuracy score based on basketball-specific rules"""
        if df.empty or table_name not in self.basketball_rules:
            return 0.8  # Default score for unknown tables

        rules = self.basketball_rules[table_name]
        accuracy_score = 1.0
        violations = 0
        total_checks = 0

        # Check numeric ranges
        for field, (min_val, max_val) in rules.get('numeric_ranges', {}).items():
            if field in df.columns:
                numeric_series = pd.to_numeric(df[field], errors='coerce').dropna()
                if len(numeric_series) > 0:
                    out_of_range = ((numeric_series < min_val) | (numeric_series > max_val)).sum()
                    violations += out_of_range
                    total_checks += len(numeric_series)

        # Check categorical values
        for field, valid_values in rules.get('categorical_values', {}).items():
            if field in df.columns:
                invalid_values = (~df[field].isin(valid_values + [None, np.nan])).sum()
                violations += invalid_values
                total_checks += len(df)

        if total_checks > 0:
            accuracy_score = 1.0 - (violations / total_checks)

        return max(0.0, min(1.0, accuracy_score))

    def _calculate_uniqueness(self, df: pd.DataFrame) -> float:
        """Calculate data uniqueness score"""
        if df.empty:
            return 0.0

        # Check for duplicate rows
        total_rows = len(df)
        unique_rows = len(df.drop_duplicates())
        uniqueness = unique_rows / total_rows if total_rows > 0 else 0.0

        return max(0.0, min(1.0, uniqueness))

    async def _calculate_timeliness(self, table_name: str, df: pd.DataFrame) -> float:
        """Calculate data timeliness score"""
        if df.empty:
            return 0.0

        # Look for date columns
        date_columns = []
        for col in df.columns:
            if 'date' in col.lower() or 'time' in col.lower() or col.lower() in ['season']:
                date_columns.append(col)

        if not date_columns:
            return 0.8  # Default score if no date columns

        timeliness_score = 1.0
        current_year = datetime.now().year

        for col in date_columns:
            if col.lower() == 'season':
                # Check season data recency
                if df[col].dtype == 'object':
                    # Extract year from season strings like "2023-24"
                    seasons = df[col].dropna().astype(str)
                    if len(seasons) > 0:
                        try:
                            latest_season = max([int(s.split('-')[0]) for s in seasons if '-' in s])
                            years_old = current_year - latest_season
                            timeliness_score *= max(0.0, 1.0 - (years_old / 10))  # Decay over 10 years
                        except:
                            pass

        return max(0.0, min(1.0, timeliness_score))

    async def _detect_table_anomalies(self, table_name: str, df: pd.DataFrame) -> List[DataAnomalyReport]:
        """Detect anomalies in table data"""
        anomalies = []

        if df.empty:
            return anomalies

        # Detect extreme outliers in numeric columns
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            series = df[col].dropna()
            if len(series) > 10:
                # Use IQR method for outlier detection
                Q1 = series.quantile(0.25)
                Q3 = series.quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 3 * IQR
                upper_bound = Q3 + 3 * IQR

                outliers = ((series < lower_bound) | (series > upper_bound)).sum()
                if outliers > len(series) * 0.05:  # More than 5% outliers
                    anomalies.append(DataAnomalyReport(
                        table_name=table_name,
                        anomaly_type="EXTREME_OUTLIERS",
                        severity="WARNING",
                        affected_records=outliers,
                        description=f"Column '{col}' has {outliers} extreme outliers ({outliers/len(series)*100:.1f}%)",
                        suggested_action="Review data collection process and consider outlier treatment",
                        timestamp=datetime.now()
                    ))

        # Detect suspicious patterns
        if len(df) > 100:
            # Check for too many identical values
            for col in df.columns:
                if df[col].dtype == 'object':
                    value_counts = df[col].value_counts()
                    if len(value_counts) > 0:
                        most_common_pct = value_counts.iloc[0] / len(df)
                        if most_common_pct > 0.8:  # More than 80% same value
                            anomalies.append(DataAnomalyReport(
                                table_name=table_name,
                                anomaly_type="SUSPICIOUS_UNIFORMITY",
                                severity="WARNING",
                                affected_records=value_counts.iloc[0],
                                description=f"Column '{col}' has {most_common_pct*100:.1f}% identical values",
                                suggested_action="Investigate data source for potential data quality issues",
                                timestamp=datetime.now()
                            ))

        return anomalies

    def _determine_quality_level(self, overall_score: float) -> QualityLevel:
        """Determine quality level based on overall score"""
        if overall_score >= 0.95:
            return QualityLevel.PRISTINE
        elif overall_score >= 0.90:
            return QualityLevel.EXCELLENT
        elif overall_score >= 0.80:
            return QualityLevel.GOOD
        elif overall_score >= 0.70:
            return QualityLevel.FAIR
        elif overall_score >= 0.50:
            return QualityLevel.POOR
        else:
            return QualityLevel.CRITICAL

    def _generate_recommendations(self, table_name: str, completeness: float,
                                consistency: float, accuracy: float, uniqueness: float,
                                timeliness: float, anomalies: List[DataAnomalyReport]) -> List[str]:
        """Generate actionable recommendations based on quality scores"""
        recommendations = []

        if completeness < self.quality_thresholds['completeness']:
            recommendations.append(f"Improve data completeness (current: {completeness:.1%}, target: {self.quality_thresholds['completeness']:.1%})")

        if consistency < self.quality_thresholds['consistency']:
            recommendations.append(f"Address data consistency issues (current: {consistency:.1%}, target: {self.quality_thresholds['consistency']:.1%})")

        if accuracy < self.quality_thresholds['accuracy']:
            recommendations.append(f"Improve data accuracy through validation (current: {accuracy:.1%}, target: {self.quality_thresholds['accuracy']:.1%})")

        if uniqueness < self.quality_thresholds['uniqueness']:
            recommendations.append(f"Remove duplicate records (current uniqueness: {uniqueness:.1%}, target: {self.quality_thresholds['uniqueness']:.1%})")

        if timeliness < self.quality_thresholds['timeliness']:
            recommendations.append(f"Update data sources for more recent data (current: {timeliness:.1%}, target: {self.quality_thresholds['timeliness']:.1%})")

        if len(anomalies) > 0:
            recommendations.append(f"Investigate and resolve {len(anomalies)} detected anomalies")

        if not recommendations:
            recommendations.append("Data quality is excellent - maintain current standards")

        return recommendations

    async def _generate_quality_summary(self, quality_results: Dict[str, QualityMetrics]) -> None:
        """Generate and log comprehensive quality summary"""
        logger.info("📋 ENHANCED DATA QUALITY ASSESSMENT SUMMARY")
        logger.info("=" * 60)

        total_records = sum(metrics.total_records for metrics in quality_results.values())
        avg_quality = sum(metrics.overall_score for metrics in quality_results.values()) / len(quality_results)
        total_anomalies = sum(metrics.anomalies_detected for metrics in quality_results.values())

        logger.info(f"📊 Total Records Assessed: {total_records:,}")
        logger.info(f"🎯 Average Quality Score: {avg_quality:.1%}")
        logger.info(f"⚠️  Total Anomalies Detected: {total_anomalies}")
        logger.info("")

        # Table-by-table summary
        for table_name, metrics in quality_results.items():
            logger.info(f"📈 {table_name.upper()}:")
            logger.info(f"   Records: {metrics.total_records:,}")
            logger.info(f"   Quality: {metrics.overall_score:.1%} ({metrics.quality_level.value})")
            logger.info(f"   Completeness: {metrics.completeness_score:.1%}")
            logger.info(f"   Consistency: {metrics.consistency_score:.1%}")
            logger.info(f"   Accuracy: {metrics.accuracy_score:.1%}")
            logger.info(f"   Uniqueness: {metrics.uniqueness_score:.1%}")
            logger.info(f"   Timeliness: {metrics.timeliness_score:.1%}")
            logger.info(f"   Anomalies: {metrics.anomalies_detected}")
            logger.info("")

        # Overall assessment
        if avg_quality >= 0.90:
            logger.info("✅ OVERALL ASSESSMENT: EXCELLENT - Database quality exceeds industry standards")
        elif avg_quality >= 0.80:
            logger.info("✅ OVERALL ASSESSMENT: GOOD - Database quality meets production standards")
        elif avg_quality >= 0.70:
            logger.info("⚠️  OVERALL ASSESSMENT: FAIR - Some quality improvements needed")
        else:
            logger.info("❌ OVERALL ASSESSMENT: POOR - Significant quality improvements required")

async def main():
    """Main execution function"""
    quality_system = EnhancedDataQualitySystem()

    try:
        # Run comprehensive quality assessment
        quality_results = await quality_system.run_comprehensive_quality_assessment()

        # Save results to JSON for further analysis
        results_summary = {
            'timestamp': datetime.now().isoformat(),
            'total_tables': len(quality_results),
            'average_quality': sum(m.overall_score for m in quality_results.values()) / len(quality_results),
            'tables': {
                name: {
                    'records': metrics.total_records,
                    'overall_score': metrics.overall_score,
                    'quality_level': metrics.quality_level.value,
                    'anomalies': metrics.anomalies_detected,
                    'recommendations': metrics.recommendations
                }
                for name, metrics in quality_results.items()
            }
        }

        with open('data_quality_assessment.json', 'w') as f:
            json.dump(results_summary, f, indent=2)

        logger.info("💾 Quality assessment results saved to data_quality_assessment.json")

    except Exception as e:
        logger.error(f"❌ Quality assessment failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
