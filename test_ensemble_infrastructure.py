#!/usr/bin/env python3
"""
Test script for Ensemble Model Training Infrastructure
"""

import sys
import asyncio
import numpy as np
from ensemble_model_training_infrastructure import EnsembleTrainingConfig, EnsembleModelTrainingInfrastructure

async def test_ensemble_infrastructure():
    """Test the ensemble training infrastructure"""
    print("🚀 Testing Enhanced Ensemble Infrastructure")
    
    # Create configuration
    config = EnsembleTrainingConfig(
        base_models=['xgboost', 'random_forest', 'gradient_boosting'],
        target_accuracy=0.75,
        basketball_features=True,
        parallel_training=True
    )
    
    # Initialize infrastructure
    infrastructure = EnsembleModelTrainingInfrastructure(config)
    
    # Create realistic basketball test data
    np.random.seed(42)
    n_samples = 500
    n_features = 50
    
    X = np.random.randn(n_samples, n_features)
    
    # Create correlated target for realistic performance
    # Simulate basketball game outcomes based on team stats
    team_points = X[:, 0] * 10 + 100  # Team points
    opponent_points = X[:, 1] * 10 + 95  # Opponent points
    team_rebounds = X[:, 2] * 5 + 45  # Team rebounds
    
    # Win probability based on point differential and rebounds
    point_diff = team_points - opponent_points
    win_prob = 1 / (1 + np.exp(-(point_diff + team_rebounds * 0.1) / 10))
    y = np.random.binomial(1, win_prob, n_samples)
    
    print(f"📊 Dataset: {X.shape[0]} samples, {X.shape[1]} features")
    print(f"🎯 Win rate: {np.mean(y):.3f}")
    
    # Train ensemble
    try:
        result = await infrastructure.train_ensemble(X, y)
        
        if result.success:
            print("✅ ENSEMBLE TRAINING SUCCESSFUL!")
            print(f"🏆 Ensemble Accuracy: {result.ensemble_performance.accuracy:.3f}")
            print(f"🏆 Ensemble AUC: {result.ensemble_performance.auc_roc:.3f}")
            print(f"🏆 Ensemble F1: {result.ensemble_performance.f1_score:.3f}")
            
            training_time = result.training_summary.get('total_training_time', 0)
            successful_models = result.training_summary.get('successful_base_models', 0)
            
            print(f"📈 Training Time: {training_time:.1f}s")
            print(f"🤖 Successful Models: {successful_models}")
            
            # Test predictions
            test_predictions = await infrastructure.predict(X[:5])
            print(f"🔮 Sample Predictions: {test_predictions[:3]}")
            
            # Check if target accuracy achieved
            if result.ensemble_performance.accuracy >= 0.75:
                print("🌟 TARGET ACCURACY ACHIEVED!")
            else:
                print("⚠️ Target accuracy not reached, but system is functional")
                
            # Overall quality assessment
            overall_score = (result.ensemble_performance.accuracy + 
                           result.ensemble_performance.auc_roc + 
                           result.ensemble_performance.f1_score) / 3
            
            if overall_score >= 0.85:
                print("🌟 EXCELLENT - Elite ensemble performance!")
            elif overall_score >= 0.75:
                print("✅ GOOD - Strong ensemble performance")
            else:
                print("⚠️ NEEDS IMPROVEMENT - Ensemble requires optimization")
                
            return True
            
        else:
            print(f"❌ Training failed: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_ensemble_infrastructure())
    if success:
        print("\n🎉 ENSEMBLE INFRASTRUCTURE TEST COMPLETED SUCCESSFULLY!")
    else:
        print("\n💥 ENSEMBLE INFRASTRUCTURE TEST FAILED!")
