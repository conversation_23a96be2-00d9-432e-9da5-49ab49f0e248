#!/usr/bin/env python3
"""
WNBA Neural Model Training Script
Trains comprehensive WNBA models using the 11-year dataset
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.neural_cortex.neural_training_pipeline import NeuralTrainingPipeline, TrainingConfig

def train_wnba_models():
    """Train WNBA neural models with comprehensive dataset"""
    print('🚀 Starting WNBA Neural Model Training')
    print('=' * 50)
    
    # Create proper TrainingConfig for WNBA
    config = TrainingConfig(
        league='WNBA',
        model_type='neural_basketball_core',
        num_epochs=50,
        batch_size=32,
        learning_rate=0.001,
        model_save_path='models/wnba_neural_models',
        device='auto',
        early_stopping_patience=10,
        weight_decay=0.01,
        data_path='data/wnba_training.csv',  # Will be overridden by auto data loading
        use_database=True,  # Enable database integration for comprehensive data
        max_samples=None,   # Use all available data
        hidden_dim=64,      # Increase for better capacity with large dataset
        num_layers=3,       # More layers for complex patterns
        dropout_rate=0.3    # Lower dropout since we have lots of data
    )
    
    # Initialize pipeline
    print('🔧 Initializing neural training pipeline...')
    pipeline = NeuralTrainingPipeline(config)
    
    print(f'🏀 Training WNBA models with comprehensive 11-year dataset...')
    print(f'📊 League: {config.league}')
    print(f'📊 Model: {config.model_type}')
    print(f'📊 Epochs: {config.num_epochs}')
    print(f'📊 Batch size: {config.batch_size}')
    print(f'📊 Learning rate: {config.learning_rate}')
    
    # Start training
    try:
        print('\n🎯 Starting model training...')
        results = pipeline.train()
        
        print('\n🎉 WNBA Training Results:')
        if results:
            if isinstance(results, dict):
                for key, value in results.items():
                    if key == 'test_metrics':
                        if hasattr(value, 'test_accuracy'):
                            print(f'   📊 Test Accuracy: {value.test_accuracy:.3f}')
                        if hasattr(value, 'test_loss'):
                            print(f'   📊 Test Loss: {value.test_loss:.4f}')
                    elif key == 'total_epochs':
                        print(f'   📊 Total Epochs: {value}')
                    elif key == 'best_val_loss':
                        print(f'   📊 Best Validation Loss: {value:.4f}')
            else:
                print(f'   {results}')
        else:
            print('   Training completed successfully!')
            
        print('\n✅ WNBA neural models training complete!')
        print(f'📁 Models saved to: {config.model_save_path}')
        
    except Exception as e:
        print(f'❌ Training failed: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the training
    train_wnba_models()
