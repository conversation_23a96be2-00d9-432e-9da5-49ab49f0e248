#!/usr/bin/env python3
"""
🔍 HYPER MEDUSA NEURAL VAULT - Comprehensive Feature Audit & Data Leakage Analysis
═══════════════════════════════════════════════════════════════════════════════════

CRITICAL ANALYSIS:
1. ✅ Feature Type Classification (ID, Categorical, Numeric, Binary)
2. ✅ Data Leakage Detection (Future information, Target leakage)
3. ✅ Feature Importance Analysis (Correlation, Variance)
4. ✅ Redundancy Detection (High correlation features)
5. ✅ Data Source Consistency (CSV vs DB features)
6. ✅ Target Quality Assessment (Balance, Noise, Consistency)
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from sklearn.feature_selection import mutual_info_classif, VarianceThreshold
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
# import matplotlib.pyplot as plt
# import seaborn as sns
from typing import Dict, List, Tuple, Set
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FeatureAuditor:
    """Comprehensive feature auditing and data leakage detection"""
    
    def __init__(self):
        self.feature_types = {
            'id_features': [],
            'categorical_features': [],
            'numeric_features': [],
            'binary_features': [],
            'engineered_features': [],
            'potential_leakage': []
        }
        self.correlation_matrix = None
        self.feature_importance = None
        
    def load_wnba_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Load WNBA data from both sources for comparison"""
        logger.info("🔍 LOADING WNBA DATA FOR COMPREHENSIVE AUDIT")
        
        # Load CSV data
        csv_data = None
        csv_paths = [
            "data/clean_wnba_training_data.csv",
            "data/ml_training/wnba_training_data.csv"
        ]
        
        for path in csv_paths:
            if os.path.exists(path):
                csv_data = pd.read_csv(path)
                logger.info(f"📄 CSV Data: {len(csv_data)} records, {len(csv_data.columns)} columns from {path}")
                break
        
        # Load database data using the data loader
        try:
            sys.path.insert(0, os.getcwd())
            from src.data.basketball_data_loader import BasketballDataLoader
            
            loader = BasketballDataLoader()
            db_data = loader.load_training_data("WNBA", data_source="database")
            
            if db_data is not None:
                logger.info(f"🗄️ DB Data: {len(db_data)} records, {len(db_data.columns)} columns")
            else:
                logger.warning("⚠️ No database data loaded")
                
        except Exception as e:
            logger.error(f"❌ Error loading database data: {e}")
            db_data = None
            
        return csv_data, db_data
    
    def classify_features(self, df: pd.DataFrame) -> Dict[str, List[str]]:
        """Classify features by type and potential issues"""
        logger.info("🔍 CLASSIFYING FEATURES BY TYPE")
        
        for col in df.columns:
            col_lower = col.lower()
            
            # ID features (potential data leakage)
            if any(id_term in col_lower for id_term in ['id', 'index', 'key', 'uuid']):
                self.feature_types['id_features'].append(col)
                if col_lower not in ['player_id', 'team_id']:  # These might be legitimate
                    self.feature_types['potential_leakage'].append(f"{col} (ID feature)")
            
            # Engineered features
            elif any(eng_term in col_lower for eng_term in ['engineered', 'synthetic', 'generated']):
                self.feature_types['engineered_features'].append(col)
                
            # Binary features
            elif df[col].nunique() == 2 and set(df[col].unique()).issubset({0, 1, True, False}):
                self.feature_types['binary_features'].append(col)
                
            # Categorical features
            elif df[col].dtype == 'object' or df[col].nunique() < 20:
                self.feature_types['categorical_features'].append(col)
                
            # Numeric features
            elif pd.api.types.is_numeric_dtype(df[col]):
                self.feature_types['numeric_features'].append(col)
                
        # Check for potential target leakage
        target_related = ['win', 'result', 'outcome', 'performance', 'rank', 'elite', 'top']
        for col in df.columns:
            if any(target_term in col.lower() for target_term in target_related):
                if col not in ['win_prediction', 'elite_performer', 'top_tier']:  # Known targets
                    self.feature_types['potential_leakage'].append(f"{col} (target-related)")
        
        # Log classification results
        for feature_type, features in self.feature_types.items():
            logger.info(f"   {feature_type}: {len(features)} features")
            if len(features) <= 10:
                logger.info(f"      {features}")
            else:
                logger.info(f"      {features[:5]} ... (+{len(features)-5} more)")
                
        return self.feature_types
    
    def detect_data_leakage(self, df: pd.DataFrame) -> List[str]:
        """Detect potential data leakage issues"""
        logger.info("🚨 DETECTING POTENTIAL DATA LEAKAGE")
        
        leakage_issues = []
        
        # Check for perfect correlations (suspicious)
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 1:
            corr_matrix = df[numeric_cols].corr()
            
            # Find perfect correlations (excluding self-correlation)
            perfect_corr = np.where((np.abs(corr_matrix) > 0.99) & (corr_matrix != 1.0))
            for i, j in zip(perfect_corr[0], perfect_corr[1]):
                if i < j:  # Avoid duplicates
                    col1, col2 = numeric_cols[i], numeric_cols[j]
                    leakage_issues.append(f"Perfect correlation: {col1} <-> {col2}")
        
        # Check for constant features
        for col in df.columns:
            if df[col].nunique() == 1:
                leakage_issues.append(f"Constant feature: {col}")
        
        # Check for features with too many unique values (potential IDs)
        for col in df.columns:
            if df[col].nunique() > len(df) * 0.9:
                leakage_issues.append(f"Too many unique values: {col} ({df[col].nunique()}/{len(df)})")
        
        # Log leakage issues
        if leakage_issues:
            logger.warning(f"⚠️ Found {len(leakage_issues)} potential data leakage issues:")
            for issue in leakage_issues[:10]:  # Show first 10
                logger.warning(f"   {issue}")
        else:
            logger.info("✅ No obvious data leakage detected")
            
        return leakage_issues
    
    def analyze_feature_importance(self, df: pd.DataFrame, target_col: str = 'win_prediction') -> pd.DataFrame:
        """Analyze feature importance using multiple methods"""
        logger.info("📊 ANALYZING FEATURE IMPORTANCE")
        
        if target_col not in df.columns:
            logger.warning(f"⚠️ Target column '{target_col}' not found")
            return pd.DataFrame()
        
        # Prepare features and target
        feature_cols = [col for col in df.columns if col not in [target_col, 'elite_performer', 'top_tier']]
        X = df[feature_cols].fillna(0)
        y = df[target_col].fillna(0)
        
        # Remove constant features
        variance_selector = VarianceThreshold(threshold=0.01)
        X_filtered = variance_selector.fit_transform(X)
        selected_features = [feature_cols[i] for i in range(len(feature_cols)) if variance_selector.variances_[i] > 0.01]
        
        logger.info(f"   Filtered {len(feature_cols)} -> {len(selected_features)} features (removed constant)")
        
        if len(selected_features) == 0:
            logger.error("❌ No features remaining after variance filtering")
            return pd.DataFrame()
        
        # Calculate mutual information
        try:
            mi_scores = mutual_info_classif(X_filtered, y, random_state=42)
            
            # Create importance dataframe
            importance_df = pd.DataFrame({
                'feature': selected_features,
                'mutual_info': mi_scores,
                'variance': [X[col].var() for col in selected_features]
            })
            
            importance_df = importance_df.sort_values('mutual_info', ascending=False)
            
            logger.info("📈 Top 10 most important features:")
            for idx, row in importance_df.head(10).iterrows():
                logger.info(f"   {row['feature']}: MI={row['mutual_info']:.4f}, Var={row['variance']:.4f}")
                
            return importance_df
            
        except Exception as e:
            logger.error(f"❌ Error calculating feature importance: {e}")
            return pd.DataFrame()
    
    def recommend_feature_selection(self, df: pd.DataFrame, target_col: str = 'win_prediction') -> Dict[str, List[str]]:
        """Recommend features to keep, remove, or investigate"""
        logger.info("💡 GENERATING FEATURE SELECTION RECOMMENDATIONS")
        
        recommendations = {
            'keep': [],
            'remove': [],
            'investigate': []
        }
        
        # Get feature importance
        importance_df = self.analyze_feature_importance(df, target_col)
        
        if not importance_df.empty:
            # Keep top features with good importance and variance
            high_importance = importance_df[
                (importance_df['mutual_info'] > 0.01) & 
                (importance_df['variance'] > 0.01)
            ]['feature'].tolist()
            
            recommendations['keep'] = high_importance[:50]  # Top 50 features
            
            # Remove low importance features
            low_importance = importance_df[
                importance_df['mutual_info'] < 0.001
            ]['feature'].tolist()
            
            recommendations['remove'].extend(low_importance)
        
        # Remove obvious problematic features
        recommendations['remove'].extend(self.feature_types['id_features'])
        recommendations['remove'].extend([f.split(' ')[0] for f in self.feature_types['potential_leakage']])
        
        # Investigate engineered features
        recommendations['investigate'].extend(self.feature_types['engineered_features'])
        
        # Remove duplicates and ensure we don't remove targets
        targets = ['win_prediction', 'elite_performer', 'top_tier']
        recommendations['remove'] = [f for f in recommendations['remove'] if f not in targets]
        recommendations['keep'] = list(set(recommendations['keep']) - set(recommendations['remove']))
        
        logger.info(f"📋 FEATURE SELECTION RECOMMENDATIONS:")
        logger.info(f"   Keep: {len(recommendations['keep'])} features")
        logger.info(f"   Remove: {len(recommendations['remove'])} features")
        logger.info(f"   Investigate: {len(recommendations['investigate'])} features")
        
        return recommendations

def main():
    """Main feature audit function"""
    logger.info("🔍 STARTING COMPREHENSIVE FEATURE AUDIT")
    logger.info("=" * 60)
    
    auditor = FeatureAuditor()
    
    # Load data
    csv_data, db_data = auditor.load_wnba_data()
    
    if csv_data is None and db_data is None:
        logger.error("❌ No data available for audit")
        return
    
    # Use the data with more features for analysis
    if db_data is not None and len(db_data.columns) > (len(csv_data.columns) if csv_data is not None else 0):
        analysis_data = db_data
        logger.info("📊 Using database data for analysis (more features)")
    elif csv_data is not None:
        analysis_data = csv_data
        logger.info("📊 Using CSV data for analysis")
    else:
        logger.error("❌ No suitable data for analysis")
        return
    
    logger.info(f"📈 Analyzing {len(analysis_data)} records with {len(analysis_data.columns)} features")
    
    # Perform comprehensive audit
    feature_types = auditor.classify_features(analysis_data)
    leakage_issues = auditor.detect_data_leakage(analysis_data)
    recommendations = auditor.recommend_feature_selection(analysis_data)
    
    # Generate summary report
    logger.info("=" * 60)
    logger.info("📋 COMPREHENSIVE AUDIT SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total Features: {len(analysis_data.columns)}")
    logger.info(f"Data Leakage Issues: {len(leakage_issues)}")
    logger.info(f"Recommended to Keep: {len(recommendations['keep'])}")
    logger.info(f"Recommended to Remove: {len(recommendations['remove'])}")
    logger.info(f"Need Investigation: {len(recommendations['investigate'])}")
    
    if len(recommendations['keep']) < 50:
        logger.info("✅ GOOD: Manageable feature count after filtering")
    else:
        logger.warning("⚠️ MODERATE: Still many features, consider further reduction")
    
    if len(leakage_issues) == 0:
        logger.info("✅ EXCELLENT: No obvious data leakage detected")
    elif len(leakage_issues) < 5:
        logger.warning("⚠️ MODERATE: Some potential leakage issues found")
    else:
        logger.error("❌ HIGH: Many potential data leakage issues")
    
    return {
        'feature_types': feature_types,
        'leakage_issues': leakage_issues,
        'recommendations': recommendations,
        'analysis_data': analysis_data
    }

if __name__ == "__main__":
    results = main()
