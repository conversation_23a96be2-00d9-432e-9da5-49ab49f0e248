import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
import requests
import time
import json
from datetime import datetime
import sys
import os
from collections import defaultdict
from smart_duplicate_prevention_system import SmartDuplicatePreventionSystem


#!/usr/bin/env python3
"""
Phase 3 - Advanced Tracking Collection
Collect advanced analytics and tracking data with medium-high success rates
"""


# Add the scripts directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase3AdvancedTrackingCollector:
    """Collect advanced tracking and analytics data with medium-high success rates"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }
        
        # Phase 3 Advanced Tracking Endpoints
        self.phase3_endpoints = {
            'leaguehustlestatsplayer': {
                'description': 'League hustle stats for players',
                'success_rate': 'MEDIUM',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'PlayerOrTeam': 'Player'
                }
            },
            'leaguehustlestatsteam': {
                'description': 'League hustle stats for teams',
                'success_rate': 'MEDIUM',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'PlayerOrTeam': 'Team'
                }
            },
            'leaguedashptstats': {
                'description': 'League player tracking stats',
                'success_rate': 'MEDIUM',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'PlayerOrTeam': 'Player',
                    'PtMeasureType': 'SpeedDistance'
                }
            },
            'leaguedashptdefend': {
                'description': 'League defense tracking stats',
                'success_rate': 'MEDIUM',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'DefenseCategory': 'Overall'
                }
            },
            'leaguedashplayerbiostats': {
                'description': 'League player biographical stats',
                'success_rate': 'HIGH',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame'
                }
            },
            'leaguedashteamshotlocations': {
                'description': 'League team shot locations',
                'success_rate': 'HIGH',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'DistanceRange': '5ft Range'
                }
            },
            'leaguedashoppptshot': {
                'description': 'League opponent shooting stats',
                'success_rate': 'MEDIUM',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame'
                }
            },
            'leaguedashplayerptshot': {
                'description': 'League player shot tracking',
                'success_rate': 'MEDIUM',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame'
                }
            }
        }
        
        # Priority seasons (recent first)
        self.seasons = {
            'NBA': ['2024-25', '2023-24', '2022-23', '2021-22', '2020-21'],
            'WNBA': ['2025', '2024', '2023', '2022', '2021']
        }
        
        # Initialize duplicate prevention
        logger.info("🔍 Initializing Smart Duplicate Prevention System...")
        self.duplicate_prevention = SmartDuplicatePreventionSystem(db_path)
        logger.info("✅ Duplicate prevention system ready")
        
    def collect_advanced_endpoint_data(self, endpoint: str, league: str, season: str) -> List[Dict[str, Any]]:
        """Collect data from an advanced tracking endpoint"""
        endpoint_config = self.phase3_endpoints[endpoint]
        
        # Check if we already have this data
        if self.duplicate_prevention.check_endpoint_data_exists(league, 'LEAGUE', season, endpoint):
            logger.info(f"   ⏭️ Skipping {endpoint} for {league} {season} - already exists")
            return []
        
        collected_data = []
        
        try:
            logger.info(f"   📊 Collecting {endpoint} for {league} {season}...")
            
            url = f"{self.base_url}/{endpoint}"
            params = endpoint_config['params'].copy()
            
            # Set league and season parameters
            params['Season'] = season
            params['LeagueID'] = '00' if league == 'NBA' else '10'
            
            # Handle special parameter variations for different endpoints
            if endpoint == 'leaguedashptstats':
                # Try different tracking types
                tracking_types = ['SpeedDistance', 'Rebounding', 'Touches', 'SpeedDistance', 'Defense']
                for tracking_type in tracking_types:
                    params_copy = params.copy()
                    params_copy['PtMeasureType'] = tracking_type
                    
                    try:
                        response = requests.get(url, headers=self.headers, params=params_copy, timeout=30)
                        response.raise_for_status()
                        
                        data = response.json()
                        tracking_data = self._process_response_data(data, endpoint, league, season, tracking_type)
                        collected_data.extend(tracking_data)
                        
                        time.sleep(0.6)  # Rate limiting
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Error collecting {endpoint} {tracking_type} for {league} {season}: {e}")
                        continue
            
            elif endpoint == 'leaguedashptdefend':
                # Try different defense categories
                defense_categories = ['Overall', '3 Pointers', '2 Pointers', 'Less Than 6Ft', 'Less Than 10Ft']
                for defense_cat in defense_categories:
                    params_copy = params.copy()
                    params_copy['DefenseCategory'] = defense_cat
                    
                    try:
                        response = requests.get(url, headers=self.headers, params=params_copy, timeout=30)
                        response.raise_for_status()
                        
                        data = response.json()
                        defense_data = self._process_response_data(data, endpoint, league, season, defense_cat)
                        collected_data.extend(defense_data)
                        
                        time.sleep(0.6)  # Rate limiting
                        
                    except Exception as e:
                        logger.warning(f"⚠️ Error collecting {endpoint} {defense_cat} for {league} {season}: {e}")
                        continue
            
            else:
                # Standard endpoint collection
                response = requests.get(url, headers=self.headers, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                standard_data = self._process_response_data(data, endpoint, league, season)
                collected_data.extend(standard_data)
                
                time.sleep(0.6)  # Rate limiting
            
        except Exception as e:
            logger.warning(f"⚠️ Error collecting {endpoint} for {league} {season}: {e}")
        
        logger.info(f"✅ Collected {len(collected_data)} {endpoint} records for {league} {season}")
        return collected_data
    
    def _process_response_data(self, data: Dict, endpoint: str, league: str, season: str, 
                             sub_category: str = None) -> List[Dict[str, Any]]:
        """Process response data into standardized records"""
        collected_data = []
        
        if 'resultSets' in data and len(data['resultSets']) > 0:
            for result_set_idx, result_set in enumerate(data['resultSets']):
                headers = result_set.get('headers', [])
                rows = result_set.get('rowSet', [])
                result_set_name = result_set.get('name', f'ResultSet_{result_set_idx}')
                
                for row_idx, row in enumerate(rows):
                    if len(row) == len(headers):
                        row_dict = dict(zip(headers, row))
                        
                        # Create comprehensive record
                        record = {
                            'league_name': league,
                            'league_id': '00' if league == 'NBA' else '10',
                            'season': season,
                            'endpoint': endpoint,
                            'result_set_name': result_set_name,
                            'sub_category': sub_category or 'standard',
                            'data_category': f'phase3_advanced_{endpoint}',
                            'data_type': f'{endpoint}_advanced_data',
                            'source_file': f"{endpoint}_{league}_{season}_{sub_category or 'standard'}.json",
                            'source_table': endpoint,
                            'raw_data': json.dumps(row_dict),
                            'created_at': datetime.now().isoformat()
                        }
                        
                        # Extract key information
                        self._extract_advanced_data_fields(record, row_dict, endpoint)
                        
                        collected_data.append(record)
        
        return collected_data
    
    def _extract_advanced_data_fields(self, record: Dict[str, Any], row_dict: Dict[str, Any], endpoint: str) -> None:
        """Extract key fields from advanced tracking data"""
        
        # Common extractions
        record['player_id'] = row_dict.get('PLAYER_ID') or row_dict.get('TEAM_ID') or 'LEAGUE'
        record['player_name'] = row_dict.get('PLAYER_NAME') or row_dict.get('TEAM_NAME') or 'LEAGUE_DATA'
        record['team_id'] = row_dict.get('TEAM_ID')
        record['team_abbreviation'] = row_dict.get('TEAM_ABBREVIATION')
        
        # Endpoint-specific extractions
        if endpoint == 'leaguehustlestatsplayer':
            record['stat_category'] = 'hustle_stats'
            record['stat_value'] = row_dict.get('CONTESTED_SHOTS', 0)
            record['deflections'] = row_dict.get('DEFLECTIONS', 0)
            record['loose_balls_recovered'] = row_dict.get('LOOSE_BALLS_RECOVERED', 0)
            record['charges_drawn'] = row_dict.get('CHARGES_DRAWN', 0)
            
        elif endpoint == 'leaguehustlestatsteam':
            record['stat_category'] = 'team_hustle'
            record['stat_value'] = row_dict.get('CONTESTED_SHOTS', 0)
            record['deflections'] = row_dict.get('DEFLECTIONS', 0)
            record['loose_balls_recovered'] = row_dict.get('LOOSE_BALLS_RECOVERED', 0)
            
        elif endpoint == 'leaguedashptstats':
            record['stat_category'] = 'player_tracking'
            record['stat_value'] = row_dict.get('DIST_MILES', 0)
            record['speed_mph'] = row_dict.get('AVG_SPEED', 0)
            record['distance_miles'] = row_dict.get('DIST_MILES', 0)
            record['touches'] = row_dict.get('TOUCHES', 0)
            
        elif endpoint == 'leaguedashptdefend':
            record['stat_category'] = 'defense_tracking'
            record['stat_value'] = row_dict.get('D_FG_PCT', 0)
            record['defense_fgm'] = row_dict.get('D_FGM', 0)
            record['defense_fga'] = row_dict.get('D_FGA', 0)
            record['defense_fg_pct'] = row_dict.get('D_FG_PCT', 0)
            
        elif endpoint == 'leaguedashplayerbiostats':
            record['stat_category'] = 'bio_stats'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['height'] = row_dict.get('PLAYER_HEIGHT', '')
            record['weight'] = row_dict.get('PLAYER_WEIGHT', 0)
            record['age'] = row_dict.get('AGE', 0)
            
        elif endpoint == 'leaguedashteamshotlocations':
            record['stat_category'] = 'team_shot_locations'
            record['stat_value'] = row_dict.get('FG_PCT', 0)
            record['shot_zone'] = row_dict.get('SHOT_ZONE_RANGE', '')
            record['shot_attempts'] = row_dict.get('FGA', 0)
            record['shot_makes'] = row_dict.get('FGM', 0)
            
        elif endpoint == 'leaguedashoppptshot':
            record['stat_category'] = 'opponent_shooting'
            record['stat_value'] = row_dict.get('OPP_FG_PCT', 0)
            record['opponent_fgm'] = row_dict.get('OPP_FGM', 0)
            record['opponent_fga'] = row_dict.get('OPP_FGA', 0)
            
        elif endpoint == 'leaguedashplayerptshot':
            record['stat_category'] = 'player_shot_tracking'
            record['stat_value'] = row_dict.get('FG_PCT', 0)
            record['closest_defender_distance'] = row_dict.get('CLOSE_DEF_DIST', 0)
            record['shot_clock'] = row_dict.get('SHOT_CLOCK', 0)
    
    def insert_advanced_data(self, data_batch: List[Dict[str, Any]]) -> int:
        """Insert advanced tracking data with duplicate prevention"""
        if not data_batch:
            return 0
        
        # Filter duplicates
        new_records, duplicate_stats = self.duplicate_prevention.filter_new_data_only(data_batch)
        
        if duplicate_stats['duplicates'] > 0:
            logger.info(f"   🔍 Filtered out {duplicate_stats['duplicates']} duplicates, inserting {duplicate_stats['new_records']} new records")
        
        if not new_records:
            return 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        
        for record in new_records:
            try:
                cursor.execute("""
                    INSERT INTO unified_nba_wnba_data (
                        source_file, source_table, data_category, season, league_id, league_name,
                        season_type, player_id, player_name, team_id, team_abbreviation,
                        stat_category, stat_value, data_type, raw_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['source_file'], record['source_table'], record['data_category'],
                    record['season'], record['league_id'], record['league_name'],
                    'Regular Season', record['player_id'], record['player_name'],
                    record.get('team_id'), record.get('team_abbreviation'),
                    record.get('stat_category'), record.get('stat_value'),
                    record['data_type'], record['raw_data'], record['created_at']
                ))
                inserted_count += 1
                
            except Exception as e:
                logger.warning(f"⚠️ Error inserting record: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        return inserted_count
    
    def collect_phase3_advanced_data(self, league: str) -> Dict[str, Any]:
        """Collect all Phase 3 advanced tracking data for a league"""
        logger.info(f"🚀 PHASE 3 ADVANCED TRACKING COLLECTION - {league}")
        logger.info("=" * 70)
        
        start_time = datetime.now()
        total_records = 0
        
        for season in self.seasons[league]:
            logger.info(f"\n📅 Processing {league} {season}...")
            
            # Collect data from each endpoint
            for endpoint, config in self.phase3_endpoints.items():
                logger.info(f"\n🎯 Collecting {endpoint} ({config['description']})...")
                
                endpoint_data = self.collect_advanced_endpoint_data(endpoint, league, season)
                
                if endpoint_data:
                    inserted = self.insert_advanced_data(endpoint_data)
                    total_records += inserted
                    if inserted > 0:
                        logger.info(f"   ✅ Inserted {inserted:,} records")
                else:
                    logger.info(f"   ⏭️ No new data for {endpoint}")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎉 PHASE 3 {league} ADVANCED COLLECTION COMPLETE!")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   📊 Total records: {total_records:,}")
        
        return {
            'success': True,
            'league': league,
            'duration': str(duration),
            'total_records': total_records
        }

def main():
    """Execute Phase 3 advanced tracking collection"""
    
    collector = Phase3AdvancedTrackingCollector()
    
    # Start with NBA
    nba_results = collector.collect_phase3_advanced_data('NBA')
    
    if nba_results['success']:
        
        # Then WNBA
        wnba_results = collector.collect_phase3_advanced_data('WNBA')
        
        if wnba_results['success']:
            
            total_records = nba_results['total_records'] + wnba_results['total_records']
            
            
    
    return nba_results

if __name__ == "__main__":
    main()
