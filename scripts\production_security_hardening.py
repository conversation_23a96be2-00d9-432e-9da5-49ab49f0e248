import os
import re
import sys
import json
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Set
from dataclasses import dataclass
import secrets
import string


#!/usr/bin/env python3
"""
🔒 HYPER MEDUSA NEURAL VAULT - Production Security Hardening Script
================================================================

Comprehensive security hardening script that:
1. Identifies and removes hardcoded credentials
2. Validates environment variable usage
3. Implements secure configuration management
4. Generates production-ready configuration templates

Usage:
    python scripts/production_security_hardening.py --scan
    python scripts/production_security_hardening.py --fix
    python scripts/production_security_hardening.py --validate
"""


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - 🔒 SECURITY: %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class SecurityIssue:
    """Represents a security issue found in the codebase"""
    file_path: str
    line_number: int
    issue_type: str
    description: str
    severity: str
    suggested_fix: str

class ProductionSecurityHardening:
    """Production security hardening system"""
    
    def __init__(self, project_root: str = None):
        self.project_root = Path(project_root or os.getcwd())
        self.issues: List[SecurityIssue] = []
        
        # Patterns for detecting security issues
        self.hardcoded_patterns = {
            'password': [
                r'password\s*=\s*["\'][^"\']+["\']',
                r'PASSWORD\s*=\s*["\'][^"\']+["\']',
                r'pwd\s*=\s*["\'][^"\']+["\']',
            ],
            'api_key': [
                r'api_key\s*=\s*["\'][^"\']+["\']',
                r'API_KEY\s*=\s*["\'][^"\']+["\']',
                r'key\s*=\s*["\'][a-zA-Z0-9]{20,}["\']',
            ],
            'secret': [
                r'secret\s*=\s*["\'][^"\']+["\']',
                r'SECRET\s*=\s*["\'][^"\']+["\']',
                r'token\s*=\s*["\'][^"\']+["\']',
                r'TOKEN\s*=\s*["\'][^"\']+["\']',
            ],
            'database_url': [
                r'DATABASE_URL\s*=\s*["\']postgresql://[^"\']+["\']',
                r'database_url\s*=\s*["\']postgresql://[^"\']+["\']',
                r'REDIS_URL\s*=\s*["\']redis://[^"\']+["\']',
            ],
            'jwt_secret': [
                r'jwt_secret\s*=\s*["\'][^"\']+["\']',
                r'JWT_SECRET\s*=\s*["\'][^"\']+["\']',
                r'secret_key\s*=\s*["\'][^"\']+["\']',
            ]
        }
        
        # Files to exclude from scanning
        self.exclude_patterns = [
            r'\.git/',
            r'\.venv/',
            r'__pycache__/',
            r'\.pyc$',
            r'node_modules/',
            r'\.env\.example',
            r'\.env\.template',
            r'test_',
            r'_test\.py$',
            r'scripts/production_security_hardening\.py$'
        ]
    
    def scan_for_security_issues(self) -> List[SecurityIssue]:
        """Scan the codebase for security issues"""
        logger.info("🔍 Scanning codebase for security issues...")
        
        self.issues = []
        
        # Scan all Python files
        for file_path in self._get_python_files():
            self._scan_file(file_path)
        
        # Scan configuration files
        for file_path in self._get_config_files():
            self._scan_file(file_path)
        
        logger.info(f"📊 Found {len(self.issues)} security issues")
        return self.issues
    
    def _get_python_files(self) -> List[Path]:
        """Get all Python files in the project"""
        python_files = []
        for file_path in self.project_root.rglob("*.py"):
            if not self._should_exclude_file(file_path):
                python_files.append(file_path)
        return python_files
    
    def _get_config_files(self) -> List[Path]:
        """Get all configuration files"""
        config_files = []
        patterns = ["*.yaml", "*.yml", "*.toml", "*.json", ".env*"]
        
        for pattern in patterns:
            for file_path in self.project_root.rglob(pattern):
                if not self._should_exclude_file(file_path):
                    config_files.append(file_path)
        
        return config_files
    
    def _should_exclude_file(self, file_path: Path) -> bool:
        """Check if file should be excluded from scanning"""
        file_str = str(file_path.relative_to(self.project_root))
        
        for pattern in self.exclude_patterns:
            if re.search(pattern, file_str):
                return True
        
        return False
    
    def _scan_file(self, file_path: Path):
        """Scan a single file for security issues"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                self._check_line_for_issues(file_path, line_num, line)
                
        except Exception as e:
            logger.warning(f"⚠️ Could not scan {file_path}: {e}")
    
    def _check_line_for_issues(self, file_path: Path, line_num: int, line: str):
        """Check a single line for security issues"""
        line_lower = line.lower().strip()
        
        # Skip comments and empty lines
        if line_lower.startswith('#') or not line_lower:
            return
        
        # Check for hardcoded patterns
        for issue_type, patterns in self.hardcoded_patterns.items():
            for pattern in patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # Skip if it's already using environment variables
                    if '${' in line or 'os.getenv' in line or 'getenv' in line:
                        continue
                    
                    # Skip if it's a placeholder or example
                    if any(placeholder in line.lower() for placeholder in [
                        'your-', 'replace-', 'change-', 'example-', 'placeholder',
                        'dev_secret', 'test_', 'dummy', 'fake'
                    ]):
                        continue
                    
                    self.issues.append(SecurityIssue(
                        file_path=str(file_path.relative_to(self.project_root)),
                        line_number=line_num,
                        issue_type=issue_type,
                        description=f"Hardcoded {issue_type} detected",
                        severity="HIGH",
                        suggested_fix=f"Replace with environment variable: ${{{issue_type.upper()}}}"
                    ))
    
    def generate_security_report(self) -> str:
        """Generate a comprehensive security report"""
        report = []
        report.append("🔒 HYPER MEDUSA NEURAL VAULT - Security Audit Report")
        report.append("=" * 60)
        report.append(f"Total Issues Found: {len(self.issues)}")
        report.append("")
        
        # Group issues by severity
        severity_groups = {}
        for issue in self.issues:
            if issue.severity not in severity_groups:
                severity_groups[issue.severity] = []
            severity_groups[issue.severity].append(issue)
        
        for severity in ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW']:
            if severity in severity_groups:
                issues = severity_groups[severity]
                report.append(f"{severity} SEVERITY ISSUES ({len(issues)})")
                report.append("-" * 40)
                
                for issue in issues:
                    report.append(f"File: {issue.file_path}:{issue.line_number}")
                    report.append(f"Type: {issue.issue_type}")
                    report.append(f"Description: {issue.description}")
                    report.append(f"Suggested Fix: {issue.suggested_fix}")
                    report.append("")
        
        return "\n".join(report)
    
    def generate_secure_env_template(self) -> str:
        """Generate a secure .env.production template"""
        template = []
        template.append("# 🔒 HYPER MEDUSA NEURAL VAULT - Production Environment Variables")
        template.append("# " + "=" * 70)
        template.append("# SECURITY NOTICE: Replace all placeholder values with secure credentials")
        template.append("")
        
        # Application Configuration
        template.append("# Application Configuration")
        template.append("APP_ENV=production")
        template.append("DEBUG=false")
        template.append("LOG_LEVEL=INFO")
        template.append("")
        
        # Security Configuration
        template.append("# Security Configuration (REQUIRED)")
        template.append(f"SECRET_KEY={self._generate_secure_key()}")
        template.append(f"JWT_SECRET={self._generate_secure_key()}")
        template.append(f"ENCRYPTION_KEY={self._generate_secure_key()}")
        template.append("JWT_ALGORITHM=HS256")
        template.append("JWT_EXPIRATION=3600")
        template.append("")
        
        # Database Configuration
        template.append("# Database Configuration")
        template.append("DATABASE_URL=postgresql://username:password@localhost:5432/hyper_medusa_prod")
        template.append("DATABASE_POOL_SIZE=50")
        template.append("DATABASE_MAX_OVERFLOW=100")
        template.append("")
        
        # Redis Configuration
        template.append("# Redis Configuration")
        template.append("REDIS_URL=redis://localhost:6379/0")
        template.append("REDIS_PASSWORD=your_secure_redis_password")
        template.append("REDIS_MAX_CONNECTIONS=100")
        template.append("")
        
        # External API Keys
        template.append("# External API Keys")
        template.append("NBA_API_KEY=your_nba_api_key")
        template.append("ODDS_API_KEY=your_odds_api_key")
        template.append("SENTRY_DSN=your_sentry_dsn")
        template.append("GRAFANA_TOKEN=your_grafana_token")
        template.append("")
        
        # Monitoring Configuration
        template.append("# Monitoring Configuration")
        template.append("ENABLE_METRICS=true")
        template.append("PROMETHEUS_PORT=9090")
        template.append("GRAFANA_PORT=3000")
        template.append("ALERT_WEBHOOK_URL=your_alert_webhook_url")
        template.append("")
        
        return "\n".join(template)
    
    def _generate_secure_key(self, length: int = 32) -> str:
        """Generate a cryptographically secure random key"""
        return secrets.token_urlsafe(length)
    
    def fix_security_issues(self) -> int:
        """Automatically fix security issues where possible"""
        logger.info("🔧 Attempting to fix security issues...")
        
        fixed_count = 0
        
        # Group issues by file
        files_to_fix = {}
        for issue in self.issues:
            if issue.file_path not in files_to_fix:
                files_to_fix[issue.file_path] = []
            files_to_fix[issue.file_path].append(issue)
        
        for file_path, issues in files_to_fix.items():
            if self._fix_file_issues(file_path, issues):
                fixed_count += len(issues)
        
        logger.info(f"✅ Fixed {fixed_count} security issues")
        return fixed_count
    
    def _fix_file_issues(self, file_path: str, issues: List[SecurityIssue]) -> bool:
        """Fix security issues in a specific file"""
        try:
            full_path = self.project_root / file_path
            
            with open(full_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # Sort issues by line number in reverse order to avoid line number shifts
            issues.sort(key=lambda x: x.line_number, reverse=True)
            
            for issue in issues:
                if issue.line_number <= len(lines):
                    line = lines[issue.line_number - 1]
                    fixed_line = self._fix_line(line, issue)
                    if fixed_line != line:
                        lines[issue.line_number - 1] = fixed_line
            
            # Write back the fixed file
            with open(full_path, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            logger.info(f"✅ Fixed issues in {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Could not fix {file_path}: {e}")
            return False
    
    def _fix_line(self, line: str, issue: SecurityIssue) -> str:
        """Fix a specific line based on the issue type"""
        # Implement real security fixes for production
        if issue.issue_type == 'hardcoded_password':
            # Replace hardcoded passwords with environment variable references
            if 'password' in line.lower() and '=' in line:
                var_name = line.split('=')[0].strip().upper()
                return f'{var_name} = os.getenv("{var_name}", "")  # Security: Use environment variable\n'
        elif issue.issue_type == 'hardcoded_api_key':
            # Replace hardcoded API keys with environment variable references
            if 'api_key' in line.lower() and '=' in line:
                var_name = line.split('=')[0].strip().upper()
                return f'{var_name} = os.getenv("{var_name}", "")  # Security: Use environment variable\n'
        elif issue.issue_type == 'hardcoded_secret':
            # Replace hardcoded secrets with environment variable references
            if any(secret in line.lower() for secret in ['secret', 'token']) and '=' in line:
                var_name = line.split('=')[0].strip().upper()
                return f'{var_name} = os.getenv("{var_name}", "")  # Security: Use environment variable\n'

        # If no specific fix available, add security comment
        if not line.strip().endswith('# Security: Replace with environment variable'):
            return line.rstrip() + '  # Security: Replace with environment variable\n'
        return line

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='HYPER MEDUSA NEURAL VAULT Security Hardening')
    parser.add_argument('--scan', action='store_true', help='Scan for security issues')
    parser.add_argument('--fix', action='store_true', help='Attempt to fix security issues')
    parser.add_argument('--validate', action='store_true', help='Validate current security configuration')
    parser.add_argument('--generate-template', action='store_true', help='Generate secure .env template')
    parser.add_argument('--project-root', help='Project root directory')
    
    args = parser.parse_args()
    
    if not any([args.scan, args.fix, args.validate, args.generate_template]):
        parser.print_help()
        return
    
    hardening = ProductionSecurityHardening(args.project_root)
    
    if args.scan or args.fix or args.validate:
        issues = hardening.scan_for_security_issues()
        
        if args.scan:
            report = hardening.generate_security_report()
            
            # Save report to file
            report_file = hardening.project_root / 'security_audit_report.txt'
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            logger.info(f"📄 Security report saved to {report_file}")
        
        if args.fix:
            fixed_count = hardening.fix_security_issues()
            logger.info(f"🔧 Fixed {fixed_count} security issues")
        
        if args.validate:
            if len(issues) == 0:
                logger.info("✅ No security issues found - configuration is secure")
            else:
                logger.warning(f"⚠️ Found {len(issues)} security issues that need attention")
    
    if args.generate_template:
        template = hardening.generate_secure_env_template()
        template_file = hardening.project_root / '.env.production.secure'
        
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(template)
        
        logger.info(f"🔒 Secure environment template generated: {template_file}")

if __name__ == "__main__":
    main()
