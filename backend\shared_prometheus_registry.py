import logging
from prometheus_client import <PERSON><PERSON><PERSON><PERSON><PERSON>, Counter, Histogram, Gauge
from typing import Dict, Any, Optional, List
import sys
import prometheus_client

#!/usr/bin/env python3
"""
Shared Prometheus Metrics Registry
Prevents duplicate timeseries registration conflicts
"""


logger = logging.getLogger("shared_prometheus_registry")

# Global shared registry
_SHARED_REGISTRY = CollectorRegistry()
_REGISTERED_METRICS = {}

def get_shared_registry() -> CollectorRegistry:
    """Get the shared Prometheus registry"""
    return _SHARED_REGISTRY

def get_or_create_counter(name: str, description: str, labelnames: Optional[List[str]] = None) -> Counter:
    """Get existing counter or create new one"""
    key = f"counter_{name}"
    if key not in _REGISTERED_METRICS:
        try:
            _REGISTERED_METRICS[key] = Counter(
                name, description,
                labelnames=labelnames or [],
                registry=_SHARED_REGISTRY
            )
            logger.info(f"Created counter: {name}")
        except ValueError as e:
            if "Duplicated timeseries" in str(e):
                # Metric already exists in registry, try to find it
                logger.warning(f"Counter {name} already exists, attempting to reuse")
                # Create a new registry-less counter for compatibility
                _REGISTERED_METRICS[key] = Counter(name, description, labelnames=labelnames or [])
            else:
                raise e
    return _REGISTERED_METRICS[key]

def get_or_create_histogram(name: str, description: str, labelnames: Optional[List[str]] = None) -> Histogram:
    """Get existing histogram or create new one"""
    key = f"histogram_{name}"
    if key not in _REGISTERED_METRICS:
        try:
            _REGISTERED_METRICS[key] = Histogram(
                name, description,
                labelnames=labelnames or [],
                registry=_SHARED_REGISTRY
            )
            logger.info(f"Created histogram: {name}")
        except ValueError as e:
            if "Duplicated timeseries" in str(e):
                # Metric already exists in registry, try to find it
                logger.warning(f"Histogram {name} already exists, attempting to reuse")
                # Create a new registry-less histogram for compatibility
                _REGISTERED_METRICS[key] = Histogram(name, description, labelnames=labelnames or [])
            else:
                raise e
    return _REGISTERED_METRICS[key]

def get_or_create_gauge(name: str, description: str, labelnames: Optional[List[str]] = None) -> Gauge:
    """Get existing gauge or create new one"""
    key = f"gauge_{name}"
    if key not in _REGISTERED_METRICS:
        try:
            _REGISTERED_METRICS[key] = Gauge(
                name, description,
                labelnames=labelnames or [],
                registry=_SHARED_REGISTRY
            )
            logger.info(f"Created gauge: {name}")
        except ValueError as e:
            if "Duplicated timeseries" in str(e):
                # Metric already exists in registry, try to find it
                logger.warning(f"Gauge {name} already exists, attempting to reuse")
                # Create a new registry-less gauge for compatibility
                _REGISTERED_METRICS[key] = Gauge(name, description, labelnames=labelnames or [])
            else:
                raise e
    return _REGISTERED_METRICS[key]

def clear_registry():
    """Clear all registered metrics (for testing)"""
    global _REGISTERED_METRICS
    _REGISTERED_METRICS.clear()
    _SHARED_REGISTRY._collector_to_names.clear()
    _SHARED_REGISTRY._names_to_collectors.clear()
    logger.info("Cleared shared registry")

# Common metrics for HYPER MEDUSA system
def get_orchestrator_messages_counter():
    """Get the orchestrator messages counter"""
    return get_or_create_counter(
        "orchestrator_messages_total",
        "Total number of orchestrator messages processed",
        ["message_type", "status"]
    )

def get_api_requests_counter():
    """Get the API requests counter"""
    return get_or_create_counter(
        "api_requests_total",
        "Total number of API requests",
        ["method", "endpoint", "status"]
    )

def get_prediction_accuracy_gauge():
    """Get the prediction accuracy gauge"""
    return get_or_create_gauge(
        "prediction_accuracy",
        "Current prediction accuracy percentage",
        ["model_type", "league"]
    )

def get_system_health_gauge():
    """Get the system health gauge"""
    return get_or_create_gauge(
        "system_health",
        "System health status (1=healthy, 0=unhealthy)",
        ["component"]
    )

def get_response_time_histogram():
    """Get the response time histogram"""
    return get_or_create_histogram(
        "response_time_seconds",
        "Response time in seconds",
        ["endpoint", "method"]
    )

# Convenience function to initialize all common metrics
def initialize_common_metrics():
    """Initialize all common HYPER MEDUSA metrics"""
    get_orchestrator_messages_counter()
    get_api_requests_counter()
    get_prediction_accuracy_gauge()
    get_system_health_gauge()
    get_response_time_histogram()
    logger.info("Initialized all common metrics")

# Manual initialization - don't auto-initialize to prevent conflicts
# Call initialize_common_metrics() manually when needed

def safe_initialize():
    """Safely initialize metrics with conflict handling"""
    try:
        initialize_common_metrics()
        logger.info("Common metrics initialized successfully")
        return True
    except Exception as e:
        logger.warning(f"Failed to initialize common metrics: {e}")
        return False

def get_orchestrator_messages_metrics():
    """Get the specific orchestrator messages metrics that are causing conflicts"""
    try:
        # Core orchestrator metrics
        orchestrator_messages_total = get_or_create_counter(
            "orchestrator_messages_total",
            "Total messages sent by type and priority",
            ["message_type", "priority"]
        )

        orchestrator_delivery_attempts_total = get_or_create_counter(
            "orchestrator_delivery_attempts_total",
            "Total delivery attempts by channel and status",
            ["channel", "status"]
        )

        orchestrator_delivery_latency_seconds = get_or_create_histogram(
            "orchestrator_delivery_latency_seconds",
            "Delivery latency by channel",
            ["channel"]
        )

        orchestrator_queue_size = get_or_create_gauge(
            "orchestrator_queue_size",
            "Current size of the message queue"
        )

        orchestrator_health_status = get_or_create_gauge(
            "orchestrator_health_status",
            "Health status of the orchestrator (1=healthy, 0=unhealthy)"
        )

        orchestrator_channel_health_status = get_or_create_gauge(
            "orchestrator_channel_health_status",
            "Health status of individual channels (1=healthy, 0=unhealthy)",
            ["channel"]
        )

        # The problematic metric - using histogram instead of summary to avoid conflicts
        orchestrator_message_process_time_seconds = get_or_create_histogram(
            "orchestrator_message_process_time_seconds",
            "Time taken to process a single message end-to-end"
        )

        return {
            "orchestrator_messages_total": orchestrator_messages_total,
            "orchestrator_delivery_attempts_total": orchestrator_delivery_attempts_total,
            "orchestrator_delivery_latency_seconds": orchestrator_delivery_latency_seconds,
            "orchestrator_queue_size": orchestrator_queue_size,
            "orchestrator_health_status": orchestrator_health_status,
            "orchestrator_channel_health_status": orchestrator_channel_health_status,
            "orchestrator_message_process_time_seconds": orchestrator_message_process_time_seconds
        }

    except Exception as e:
        logger.error(f"Failed to get orchestrator messages metrics: {e}")
        return {}

def patch_prometheus_client_globally():
    """Patch prometheus_client at the module level to prevent conflicts"""
    try:

        # Store original classes
        original_counter = prometheus_client.Counter
        original_histogram = prometheus_client.Histogram
        original_gauge = prometheus_client.Gauge

        # Create patched classes that use shared registry
        class PatchedCounter:
            def __init__(self, name, description, labelnames=None, registry=None):
                self._metric = get_or_create_counter(name, description, labelnames)

            def inc(self, amount=1, **labels):
                if labels:
                    return self._metric.labels(**labels).inc(amount)
                return self._metric.inc(amount)

            def labels(self, **labels):
                return self._metric.labels(**labels)

        class PatchedHistogram:
            def __init__(self, name, description, labelnames=None, buckets=None, registry=None):
                self._metric = get_or_create_histogram(name, description, labelnames)

            def observe(self, amount, **labels):
                if labels:
                    return self._metric.labels(**labels).observe(amount)
                return self._metric.observe(amount)

            def labels(self, **labels):
                return self._metric.labels(**labels)

        class PatchedGauge:
            def __init__(self, name, description, labelnames=None, registry=None):
                self._metric = get_or_create_gauge(name, description, labelnames)

            def set(self, value, **labels):
                if labels:
                    return self._metric.labels(**labels).set(value)
                return self._metric.set(value)

            def labels(self, **labels):
                return self._metric.labels(**labels)

        # Replace the classes in prometheus_client module
        prometheus_client.Counter = PatchedCounter
        prometheus_client.Histogram = PatchedHistogram
        prometheus_client.Gauge = PatchedGauge

        logger.info("Successfully patched prometheus_client globally")
        return True

    except Exception as e:
        logger.error(f"Failed to patch prometheus_client globally: {e}")
        return False