# HYPER MEDUSA NEURAL VAULT - Environment Configuration Template
# ==============================================================
# Copy this file to .env and configure values for your environment

# Environment Mode
MEDUSA_ENV=production

# Model Configuration
MODEL_CONFIDENCE_THRESHOLD=0.75
MODEL_ACCURACY_THRESHOLD=0.65
DATA_QUALITY_THRESHOLD=0.80
BASKETBALL_IQ_THRESHOLD=0.85

# Model Paths
NBA_MODEL_PATH=./models/nba/ensemble_v1.pkl
WNBA_MODEL_PATH=./models/wnba/ensemble_v1.pkl
MODEL_BASE_PATH=./models

# Basketball Statistics (NBA)
NBA_AVG_TOTAL=220.0
NBA_HOME_ADVANTAGE=0.54
NBA_PACE_FACTOR=100.0
NBA_OT_PROB=0.12

# Basketball Statistics (WNBA)
WNBA_AVG_TOTAL=165.0
WNBA_HOME_ADVANTAGE=0.52
WNBA_PACE_FACTOR=92.0
WNBA_OT_PROB=0.08

# Performance Configuration
UPDATE_INTERVAL=60
CACHE_TTL=3600
MAX_PREDICTIONS_PER_REQUEST=100
MAX_BATCH_SIZE=32
MAX_CONCURRENT_LOADS=10

# Security Configuration
SESSION_TIMEOUT=30
MAX_LOGIN_ATTEMPTS=3
REQUIRE_MFA=true
JWT_SECRET_KEY=CHANGE_THIS_IN_PRODUCTION_IMMEDIATELY
ENCRYPTION_ENABLED=true

# Notification Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# External Services
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_FROM_NUMBER=+**********
FCM_SERVER_KEY=your_fcm_server_key
REDIS_URL=redis://localhost:6379

# System Configuration
DEBUG=false
PERFORMANCE_MONITORING=true
REAL_DATA_ENABLED=true
BASKETBALL_INTELLIGENCE=true
SYSTEM_LOAD_FACTOR=1.0
RESOURCE_SCALE_FACTOR=1.0
