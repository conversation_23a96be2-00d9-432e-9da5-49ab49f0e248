#!/usr/bin/env python3
"""
🧪 HYPER MEDUSA NEURAL VAULT - Automated Reporting System Test Suite
===================================================================
Comprehensive testing for the automated analytics reporting and delivery system.
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timezone

# Add project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("AUTOMATED_REPORTING_TEST")

async def test_automated_reporting_system():
    """Comprehensive test suite for automated reporting system"""
    
    print("🧪 HYPER MEDUSA NEURAL VAULT - Automated Reporting System Test Suite")
    print("=" * 80)
    
    try:
        # Test 1: Import Automated Reporting Service
        print("\n📦 Test 1: Import Automated Reporting Service")
        from backend.services.automated_reporting_service import (
            reporting_service, ReportType, ReportFrequency, ReportFormat, 
            ReportSchedule, GeneratedReport
        )
        print("✅ Import Test PASSED: All reporting components imported successfully")
        
        # Test 2: Service Initialization
        print("\n🔧 Test 2: Service Initialization")
        print(f"   📊 Service running: {reporting_service.is_running}")
        print(f"   📅 Default schedules: {len(reporting_service.get_schedules())}")
        print(f"   📁 Reports directory: {reporting_service.reports_directory}")
        
        if len(reporting_service.get_schedules()) > 0:
            print("✅ Initialization Test PASSED: Service initialized with default schedules")
        else:
            print("❌ Initialization Test FAILED: No default schedules found")
            return False
        
        # Test 3: Report Generation - Performance Summary
        print("\n📊 Test 3: Performance Summary Report Generation")
        try:
            performance_report = await reporting_service.generate_report(
                ReportType.PERFORMANCE_SUMMARY,
                {"timeframe": "test", "include_trends": True}
            )
            
            if performance_report and performance_report.data:
                print("✅ Performance Report Test PASSED: Report generated successfully")
                print(f"   📋 Report ID: {performance_report.report_id}")
                print(f"   📊 Report Type: {performance_report.report_type.value}")
                print(f"   📅 Generated At: {performance_report.generated_at}")
                print(f"   📈 Data Keys: {list(performance_report.data.keys())}")
            else:
                print("❌ Performance Report Test FAILED: No report data generated")
                return False
        except Exception as e:
            print(f"❌ Performance Report Test FAILED: {e}")
            return False
        
        # Test 4: Report Generation - System Health
        print("\n🏥 Test 4: System Health Report Generation")
        try:
            health_report = await reporting_service.generate_report(
                ReportType.SYSTEM_HEALTH,
                {"timeframe": "1h", "alerts_only": False}
            )
            
            if health_report and health_report.data:
                print("✅ System Health Report Test PASSED: Report generated successfully")
                print(f"   📋 Report ID: {health_report.report_id}")
                print(f"   🏥 Health Overview: {health_report.data.get('health_overview', {})}")
                print(f"   📊 Metrics Count: {health_report.data.get('system_metrics', {}).get('total_metrics_collected', 0)}")
            else:
                print("❌ System Health Report Test FAILED: No report data generated")
                return False
        except Exception as e:
            print(f"❌ System Health Report Test FAILED: {e}")
            return False
        
        # Test 5: Report Generation - Prediction Accuracy
        print("\n🎯 Test 5: Prediction Accuracy Report Generation")
        try:
            accuracy_report = await reporting_service.generate_report(
                ReportType.PREDICTION_ACCURACY,
                {"timeframe": "24h", "include_breakdown": True}
            )
            
            if accuracy_report and accuracy_report.data:
                print("✅ Prediction Accuracy Report Test PASSED: Report generated successfully")
                print(f"   📋 Report ID: {accuracy_report.report_id}")
                print(f"   🎯 Overall Accuracy: {accuracy_report.data.get('accuracy_overview', {}).get('overall_accuracy', 0)}")
                print(f"   🏀 NBA Accuracy: {accuracy_report.data.get('accuracy_overview', {}).get('nba_accuracy', 0)}")
                print(f"   🏀 WNBA Accuracy: {accuracy_report.data.get('accuracy_overview', {}).get('wnba_accuracy', 0)}")
            else:
                print("❌ Prediction Accuracy Report Test FAILED: No report data generated")
                return False
        except Exception as e:
            print(f"❌ Prediction Accuracy Report Test FAILED: {e}")
            return False
        
        # Test 6: Report Generation - Comprehensive
        print("\n📈 Test 6: Comprehensive Report Generation")
        try:
            comprehensive_report = await reporting_service.generate_report(
                ReportType.COMPREHENSIVE,
                {"timeframe": "7d", "detailed": True}
            )
            
            if comprehensive_report and comprehensive_report.data:
                print("✅ Comprehensive Report Test PASSED: Report generated successfully")
                print(f"   📋 Report ID: {comprehensive_report.report_id}")
                print(f"   📊 Executive Summary: {comprehensive_report.data.get('executive_summary', {})}")
                print(f"   📈 Data Sections: {list(comprehensive_report.data.keys())}")
            else:
                print("❌ Comprehensive Report Test FAILED: No report data generated")
                return False
        except Exception as e:
            print(f"❌ Comprehensive Report Test FAILED: {e}")
            return False
        
        # Test 7: Schedule Management
        print("\n📅 Test 7: Schedule Management")
        try:
            # Create test schedule
            test_schedule = ReportSchedule(
                schedule_id="test_schedule",
                report_type=ReportType.SYSTEM_HEALTH,
                frequency=ReportFrequency.HOURLY,
                format=ReportFormat.JSON,
                recipients=["<EMAIL>"],
                parameters={"timeframe": "1h"}
            )
            
            # Add schedule
            reporting_service.add_schedule(test_schedule)
            
            # Verify schedule was added
            schedules = reporting_service.get_schedules()
            test_schedule_found = any(s.schedule_id == "test_schedule" for s in schedules)
            
            if test_schedule_found:
                print("✅ Schedule Management Test PASSED: Schedule added successfully")
                print(f"   📅 Total Schedules: {len(schedules)}")
                print(f"   🆔 Test Schedule ID: test_schedule")
                
                # Remove test schedule
                reporting_service.remove_schedule("test_schedule")
                schedules_after = reporting_service.get_schedules()
                print(f"   📅 Schedules After Removal: {len(schedules_after)}")
            else:
                print("❌ Schedule Management Test FAILED: Schedule not found after adding")
                return False
        except Exception as e:
            print(f"❌ Schedule Management Test FAILED: {e}")
            return False
        
        # Test 8: Report History
        print("\n📚 Test 8: Report History Management")
        try:
            # Get report history
            history = reporting_service.get_report_history(limit=10)
            
            if len(history) > 0:
                print("✅ Report History Test PASSED: Report history available")
                print(f"   📚 Total Reports in History: {len(history)}")
                print(f"   📋 Latest Report: {history[-1].report_id}")
                print(f"   📊 Report Types: {set(r.report_type.value for r in history)}")
            else:
                print("⚠️ Report History Test WARNING: No reports in history (expected for fresh test)")
        except Exception as e:
            print(f"❌ Report History Test FAILED: {e}")
            return False
        
        # Test 9: File Operations
        print("\n💾 Test 9: File Operations")
        try:
            # Test JSON report saving
            test_report = GeneratedReport(
                report_id="test_file_report",
                report_type=ReportType.SYSTEM_HEALTH,
                generated_at=datetime.now(timezone.utc),
                timeframe="test",
                format=ReportFormat.JSON,
                data={"test": "data", "timestamp": datetime.now(timezone.utc).isoformat()}
            )
            
            # Save as JSON
            await reporting_service._save_json_report(test_report)
            
            if test_report.file_path and os.path.exists(test_report.file_path):
                print("✅ File Operations Test PASSED: JSON report saved successfully")
                print(f"   💾 File Path: {test_report.file_path}")
                print(f"   📏 File Size: {test_report.size_bytes} bytes")
                
                # Clean up test file
                os.remove(test_report.file_path)
            else:
                print("❌ File Operations Test FAILED: JSON file not created")
                return False
        except Exception as e:
            print(f"❌ File Operations Test FAILED: {e}")
            return False
        
        # Test 10: Router Import
        print("\n🛣️ Test 10: Router Import")
        try:
            from backend.routers.automated_reporting import router as reporting_router
            from backend.routers.automated_reporting import (
                ReportGenerationRequest, ScheduleCreateRequest, ReportResponse
            )
            print("✅ Router Import Test PASSED: Reporting router imported successfully")
            print(f"   🛣️ Router Prefix: {reporting_router.prefix}")
            print(f"   🏷️ Router Tags: {reporting_router.tags}")
        except Exception as e:
            print(f"❌ Router Import Test FAILED: {e}")
            return False
        
        # Test 11: Integration with Analytics System
        print("\n🧠 Test 11: Analytics System Integration")
        try:
            # Test analytics system integration
            analytics_system = reporting_service.analytics_system
            
            if analytics_system:
                print("✅ Analytics Integration Test PASSED: Analytics system available")
                print(f"   🧠 Analytics System: {type(analytics_system).__name__}")
                
                # Test metrics collection
                metrics = await analytics_system.collect_system_metrics()
                print(f"   📊 Metrics Collected: {len(metrics)}")
                
                # Test dashboard data
                dashboard_data = await analytics_system.get_real_time_dashboard_data()
                print(f"   📈 Dashboard Data Keys: {list(dashboard_data.keys())}")
            else:
                print("❌ Analytics Integration Test FAILED: Analytics system not available")
                return False
        except Exception as e:
            print(f"❌ Analytics Integration Test FAILED: {e}")
            return False
        
        # Test 12: Notification Service Integration
        print("\n📧 Test 12: Notification Service Integration")
        try:
            from backend.services.notification_service import notification_service
            print("✅ Notification Integration Test PASSED: Notification service available")
            print(f"   📧 Notification Service: {type(notification_service).__name__}")
        except Exception as e:
            print(f"❌ Notification Integration Test FAILED: {e}")
            return False
        
        print("\n" + "=" * 80)
        print("🎉 ALL TESTS PASSED! Automated Reporting System is working correctly!")
        print("=" * 80)
        
        # Summary
        print("\n📊 Test Summary:")
        print("✅ Service initialization and configuration")
        print("✅ Performance summary report generation")
        print("✅ System health report generation")
        print("✅ Prediction accuracy report generation")
        print("✅ Comprehensive report generation")
        print("✅ Schedule management (add/remove)")
        print("✅ Report history management")
        print("✅ File operations (JSON export)")
        print("✅ Router and API model imports")
        print("✅ Analytics system integration")
        print("✅ Notification service integration")
        
        print("\n🚀 Automated Reporting System is ready for production!")
        return True
        
    except Exception as e:
        print(f"\n❌ CRITICAL TEST FAILURE: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test execution"""
    print("🧪 Starting Automated Reporting System Test Suite...")
    
    success = await test_automated_reporting_system()
    
    if success:
        print("\n✅ All tests completed successfully!")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
