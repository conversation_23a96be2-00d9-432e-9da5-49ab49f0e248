import asyncio
import logging
import pandas as pd
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
from pathlib import Path
from dataclasses import dataclass
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import SQLAlchemyError
import hashlib
import uuid
import sys
from backend.database.models import Base, UserModel, GameModel, TeamModel, PlayerModel, PlayerGameStatModel
from kingdom.config.unified_config_system import UnifiedConfigSystem
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, String, Integer, Float, Boolean, DateTime, Text
import re


"""
Database Migration and Consolidation System
==========================================

Production-ready database migration system for HYPER MEDUSA NEURAL VAULT
- Consolidates scattered data files into unified production schema
- Implements proper indexing and performance optimization
- <PERSON>les data deduplication and quality validation
- Supports both PostgreSQL and SQLite for development
- Includes rollback capabilities and migration tracking
"""


# Import our models and config
sys.path.append('.')
Base = declarative_base()

# Basic model definitions for migration
class UserModel(Base):
    __tablename__ = 'users'
    id = Column(String, primary_key=True)

    class TeamModel(Base):
        __tablename__ = 'teams'
        id = Column(String, primary_key=True)
        name = Column(String)
        abbreviation = Column(String)
        city = Column(String)
        conference = Column(String)
        division = Column(String)
        league = Column(String)
        season = Column(String)
        wins = Column(Integer, default=0)
        losses = Column(Integer, default=0)
        win_percentage = Column(Float, default=0.0)
        games_played = Column(Integer, default=0)
        points_per_game = Column(Float, default=0.0)
        points_allowed_per_game = Column(Float, default=0.0)
        net_rating = Column(Float, default=0.0)
        offensive_rating = Column(Float, default=0.0)
        defensive_rating = Column(Float, default=0.0)
        pace = Column(Float, default=0.0)
        true_shooting_percentage = Column(Float, default=0.0)
        effective_field_goal_percentage = Column(Float, default=0.0)
        created_at = Column(DateTime)
        updated_at = Column(DateTime)

    class PlayerModel(Base):
        __tablename__ = 'players'
        id = Column(String, primary_key=True)
        full_name = Column(String)
        first_name = Column(String)
        last_name = Column(String)
        position = Column(String)
        height = Column(String)
        weight = Column(Integer)
        birth_date = Column(DateTime)
        years_experience = Column(Integer)
        college = Column(String)
        country = Column(String)
        mythic_roster_id = Column(String)
        league = Column(String)
        season = Column(String)
        is_active = Column(Boolean, default=True)
        jersey_number = Column(Integer)
        points_per_game = Column(Float, default=0.0)
        rebounds_per_game = Column(Float, default=0.0)
        assists_per_game = Column(Float, default=0.0)
        steals_per_game = Column(Float, default=0.0)
        blocks_per_game = Column(Float, default=0.0)
        field_goal_percentage = Column(Float, default=0.0)
        three_point_percentage = Column(Float, default=0.0)
        free_throw_percentage = Column(Float, default=0.0)
        player_efficiency_rating = Column(Float, default=0.0)
        usage_rate = Column(Float, default=0.0)
        true_shooting_percentage = Column(Float, default=0.0)
        created_at = Column(DateTime)
        updated_at = Column(DateTime)

    class GameModel(Base):
        __tablename__ = 'games'
        id = Column(String, primary_key=True)
        date = Column(DateTime)
        season = Column(String)
        status = Column(String)
        home_team_id = Column(String)
        away_team_id = Column(String)
        league = Column(String)
        home_score = Column(Integer, default=0)
        away_score = Column(Integer, default=0)
        attendance = Column(Integer)
        arena = Column(String)
        broadcast_network = Column(String)
        spread_home = Column(Float)
        spread_away = Column(Float)
        total_points = Column(Float)
        moneyline_home = Column(Float)
        moneyline_away = Column(Float)
        over_under_result = Column(String)
        spread_result_home = Column(String)
        created_at = Column(DateTime)
        updated_at = Column(DateTime)

    class PlayerGameStatModel(Base):
        __tablename__ = 'player_game_stats'
        id = Column(String, primary_key=True)
        hero_id = Column(String)
        titan_clash_id = Column(String)
        date = Column(DateTime)
        season = Column(String)
        league = Column(String)
        minutes_played = Column(Float)
        points = Column(Integer, default=0)
        field_goals_made = Column(Integer, default=0)
        field_goals_attempted = Column(Integer, default=0)
        field_goal_percentage = Column(Float, default=0.0)
        three_pointers_made = Column(Integer, default=0)
        three_pointers_attempted = Column(Integer, default=0)
        three_point_percentage = Column(Float, default=0.0)
        free_throws_made = Column(Integer, default=0)
        free_throws_attempted = Column(Integer, default=0)
        free_throw_percentage = Column(Float, default=0.0)
        offensive_rebounds = Column(Integer, default=0)
        defensive_rebounds = Column(Integer, default=0)
        total_rebounds = Column(Integer, default=0)
        assists = Column(Integer, default=0)
        steals = Column(Integer, default=0)
        blocks = Column(Integer, default=0)
        turnovers = Column(Integer, default=0)
        personal_fouls = Column(Integer, default=0)
        plus_minus = Column(Float, default=0.0)
        created_at = Column(DateTime)
        updated_at = Column(DateTime)

    # Mock config for standalone mode
    class ProductionConfig:
        DATABASE_URL_PROD = None

logger = logging.getLogger(__name__)

@dataclass
class MigrationResult:
    """Result of a migration operation"""
    success: bool
    records_processed: int
    records_inserted: int
    records_updated: int
    records_skipped: int
    errors: List[str]
    duration_seconds: float
    migration_id: str

class DatabaseMigrator:
    """Expert-level database migration and consolidation system"""
    
    def __init__(self, config: Optional[ProductionConfig] = None):
        self.config = config or ProductionConfig()
        self.data_dir = Path("data")
        self.migration_history = []
        self.engine = None
        self.async_engine = None
        
    async def initialize_engines(self):
        """Initialize database engines"""
        try:
            # Create async engine for production operations
            if self.config.DATABASE_URL_PROD:
                self.async_engine = create_async_engine(
                    self.config.DATABASE_URL_PROD,
                    echo=False,
                    pool_size=20,
                    max_overflow=30,
                    pool_pre_ping=True
                )
                
                # Create sync engine for migration operations
                sync_url = self.config.DATABASE_URL_PROD.replace("postgresql+asyncpg://", "postgresql://")
                self.engine = create_engine(sync_url, echo=False)
            else:
                # Fallback to SQLite for development
                db_path = "hyper_medusa_production.db"
                self.engine = create_engine(f"sqlite:///{db_path}", echo=False)
                
            logger.info("Database engines initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database engines: {e}")
            raise
    
    async def create_production_schema(self) -> MigrationResult:
        """Create production database schema with all tables and indexes"""
        migration_id = f"schema_creation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()
        errors = []
        
        try:
            logger.info("Creating production database schema...")
            
            # Create all tables
            Base.metadata.create_all(self.engine)
            
            # Create additional indexes for performance
            await self._create_performance_indexes()
            
            # Create migration tracking table
            await self._create_migration_table()
            
            duration = (datetime.now() - start_time).total_seconds()
            
            result = MigrationResult(
                success=True,
                records_processed=0,
                records_inserted=0,
                records_updated=0,
                records_skipped=0,
                errors=errors,
                duration_seconds=duration,
                migration_id=migration_id
            )
            
            logger.info(f"Schema creation completed in {duration:.2f} seconds")
            return result
            
        except Exception as e:
            errors.append(f"Schema creation failed: {str(e)}")
            logger.error(f"Schema creation failed: {e}")
            
            return MigrationResult(
                success=False,
                records_processed=0,
                records_inserted=0,
                records_updated=0,
                records_skipped=0,
                errors=errors,
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                migration_id=migration_id
            )
    
    async def _create_performance_indexes(self):
        """Create additional performance indexes"""
        indexes = [
            # Game performance indexes
            "CREATE INDEX IF NOT EXISTS idx_games_date_teams ON games(date, home_team_id, away_team_id);",
            "CREATE INDEX IF NOT EXISTS idx_games_season_status ON games(season, status);",
            "CREATE INDEX IF NOT EXISTS idx_games_betting_lines ON games(spread_home, total_points, moneyline_home);",
            
            # Player performance indexes
            "CREATE INDEX IF NOT EXISTS idx_players_team_active ON players(mythic_roster_id, is_active);",
            "CREATE INDEX IF NOT EXISTS idx_players_stats_composite ON players(points_per_game, rebounds_per_game, assists_per_game);",
            "CREATE INDEX IF NOT EXISTS idx_players_efficiency ON players(player_efficiency_rating, usage_rate);",
            
            # Team performance indexes
            "CREATE INDEX IF NOT EXISTS idx_teams_conference_performance ON teams(conference, win_percentage, net_rating);",
            "CREATE INDEX IF NOT EXISTS idx_teams_season_active ON teams(season, wins, losses);",
            
            # Game stats performance indexes
            "CREATE INDEX IF NOT EXISTS idx_player_game_stats_composite ON player_game_stats(hero_id, titan_clash_id, points);",
            "CREATE INDEX IF NOT EXISTS idx_player_game_stats_performance ON player_game_stats(points, total_rebounds, assists, plus_minus);",
        ]
        
        try:
            with self.engine.connect() as conn:
                for index_sql in indexes:
                    try:
                        conn.execute(text(index_sql))
                        conn.commit()
                    except Exception as e:
                        logger.warning(f"Index creation warning: {e}")
                        
            logger.info("Performance indexes created successfully")
            
        except Exception as e:
            logger.error(f"Failed to create performance indexes: {e}")
            raise
    
    async def _create_migration_table(self):
        """Create migration tracking table"""
        migration_table_sql = """
        CREATE TABLE IF NOT EXISTS migration_history (
            id VARCHAR PRIMARY KEY,
            migration_name VARCHAR NOT NULL,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            success BOOLEAN NOT NULL,
            records_processed INTEGER DEFAULT 0,
            records_inserted INTEGER DEFAULT 0,
            records_updated INTEGER DEFAULT 0,
            records_skipped INTEGER DEFAULT 0,
            duration_seconds FLOAT DEFAULT 0,
            errors TEXT,
            metadata JSONB
        );
        """
        
        try:
            with self.engine.connect() as conn:
                conn.execute(text(migration_table_sql))
                conn.commit()
                
            logger.info("Migration tracking table created")
            
        except Exception as e:
            logger.error(f"Failed to create migration table: {e}")
            raise
    
    def _record_migration(self, result: MigrationResult, migration_name: str):
        """Record migration result in tracking table"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("""
                    INSERT INTO migration_history 
                    (id, migration_name, success, records_processed, records_inserted, 
                     records_updated, records_skipped, duration_seconds, errors)
                    VALUES (:id, :name, :success, :processed, :inserted, :updated, 
                            :skipped, :duration, :errors)
                """), {
                    "id": result.migration_id,
                    "name": migration_name,
                    "success": result.success,
                    "processed": result.records_processed,
                    "inserted": result.records_inserted,
                    "updated": result.records_updated,
                    "skipped": result.records_skipped,
                    "duration": result.duration_seconds,
                    "errors": json.dumps(result.errors) if result.errors else None
                })
                conn.commit()
                
        except Exception as e:
            logger.error(f"Failed to record migration: {e}")
    
    async def consolidate_csv_data(self) -> List[MigrationResult]:
        """Consolidate all CSV data files into production database"""
        results = []
        
        # Define data consolidation tasks
        consolidation_tasks = [
            ("teams", self._consolidate_team_data),
            ("players", self._consolidate_player_data),
            ("games", self._consolidate_game_data),
            ("player_stats", self._consolidate_player_stats_data),
        ]
        
        for task_name, task_func in consolidation_tasks:
            try:
                logger.info(f"Starting consolidation task: {task_name}")
                result = await task_func()
                results.append(result)
                self._record_migration(result, f"consolidate_{task_name}")
                
                if result.success:
                    logger.info(f"✅ {task_name} consolidation completed: {result.records_inserted} records inserted")
                else:
                    logger.error(f"❌ {task_name} consolidation failed: {result.errors}")
                    
            except Exception as e:
                logger.error(f"Consolidation task {task_name} failed: {e}")
                error_result = MigrationResult(
                    success=False,
                    records_processed=0,
                    records_inserted=0,
                    records_updated=0,
                    records_skipped=0,
                    errors=[str(e)],
                    duration_seconds=0,
                    migration_id=f"{task_name}_error_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                )
                results.append(error_result)
                self._record_migration(error_result, f"consolidate_{task_name}")
        
        return results

    async def _consolidate_team_data(self) -> MigrationResult:
        """Consolidate team data from CSV files"""
        migration_id = f"teams_consolidation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()
        errors = []
        processed = 0
        inserted = 0
        skipped = 0

        try:
            # Find all team CSV files
            team_files = list(self.data_dir.glob("**/teams_*.csv"))
            team_files.extend(list(self.data_dir.glob("**/wnba_teams_*.csv")))

            logger.info(f"Found {len(team_files)} team data files")

            # Track unique teams to prevent duplicates
            seen_teams = set()

            with self.engine.connect() as conn:
                for file_path in team_files:
                    try:
                        df = pd.read_csv(file_path)
                        processed += len(df)

                        # Determine league from filename
                        league = "WNBA" if "wnba" in file_path.name.lower() else "NBA"

                        # Extract season from filename
                        season = self._extract_season_from_filename(file_path.name)

                        for _, row in df.iterrows():
                            # Create unique team identifier
                            team_key = f"{league}_{row.get('id', row.get('TEAM_ID', ''))}"

                            if team_key in seen_teams:
                                skipped += 1
                                continue

                            seen_teams.add(team_key)

                            # Map CSV columns to our model
                            team_data = self._map_team_data(row, league, season)

                            # Insert team
                            conn.execute(text("""
                                INSERT OR IGNORE INTO teams
                                (id, name, abbreviation, city, conference, division, league, season,
                                 wins, losses, win_percentage, games_played, points_per_game,
                                 points_allowed_per_game, net_rating, offensive_rating, defensive_rating,
                                 pace, true_shooting_percentage, effective_field_goal_percentage,
                                 created_at, updated_at)
                                VALUES (:id, :name, :abbreviation, :city, :conference, :division, :league, :season,
                                        :wins, :losses, :win_percentage, :games_played, :points_per_game,
                                        :points_allowed_per_game, :net_rating, :offensive_rating, :defensive_rating,
                                        :pace, :true_shooting_percentage, :effective_field_goal_percentage,
                                        :created_at, :updated_at)
                            """), team_data)

                            inserted += 1

                    except Exception as e:
                        error_msg = f"Error processing {file_path}: {str(e)}"
                        errors.append(error_msg)
                        logger.warning(error_msg)
                        continue

                conn.commit()

            duration = (datetime.now() - start_time).total_seconds()

            return MigrationResult(
                success=len(errors) == 0,
                records_processed=processed,
                records_inserted=inserted,
                records_updated=0,
                records_skipped=skipped,
                errors=errors,
                duration_seconds=duration,
                migration_id=migration_id
            )

        except Exception as e:
            errors.append(f"Team consolidation failed: {str(e)}")
            logger.error(f"Team consolidation failed: {e}")

            return MigrationResult(
                success=False,
                records_processed=processed,
                records_inserted=inserted,
                records_updated=0,
                records_skipped=skipped,
                errors=errors,
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                migration_id=migration_id
            )

    async def _consolidate_player_data(self) -> MigrationResult:
        """Consolidate player data from CSV files"""
        migration_id = f"players_consolidation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()
        errors = []
        processed = 0
        inserted = 0
        skipped = 0

        try:
            # Find all player CSV files
            player_files = list(self.data_dir.glob("**/players_*.csv"))
            player_files.extend(list(self.data_dir.glob("**/wnba_players_*.csv")))

            logger.info(f"Found {len(player_files)} player data files")

            # Track unique players to prevent duplicates
            seen_players = set()

            with self.engine.connect() as conn:
                for file_path in player_files:
                    try:
                        df = pd.read_csv(file_path)
                        processed += len(df)

                        # Determine league from filename
                        league = "WNBA" if "wnba" in file_path.name.lower() else "NBA"

                        # Extract season from filename
                        season = self._extract_season_from_filename(file_path.name)

                        for _, row in df.iterrows():
                            # Create unique player identifier
                            player_key = f"{league}_{row.get('id', row.get('PLAYER_ID', ''))}"

                            if player_key in seen_players:
                                skipped += 1
                                continue

                            seen_players.add(player_key)

                            # Map CSV columns to our model
                            player_data = self._map_player_data(row, league, season)

                            # Insert player
                            conn.execute(text("""
                                INSERT OR IGNORE INTO players
                                (id, full_name, first_name, last_name, position, height, weight,
                                 birth_date, years_experience, college, country, mythic_roster_id,
                                 league, season, is_active, jersey_number, points_per_game,
                                 rebounds_per_game, assists_per_game, steals_per_game, blocks_per_game,
                                 field_goal_percentage, three_point_percentage, free_throw_percentage,
                                 player_efficiency_rating, usage_rate, true_shooting_percentage,
                                 created_at, updated_at)
                                VALUES (:id, :full_name, :first_name, :last_name, :position, :height, :weight,
                                        :birth_date, :years_experience, :college, :country, :mythic_roster_id,
                                        :league, :season, :is_active, :jersey_number, :points_per_game,
                                        :rebounds_per_game, :assists_per_game, :steals_per_game, :blocks_per_game,
                                        :field_goal_percentage, :three_point_percentage, :free_throw_percentage,
                                        :player_efficiency_rating, :usage_rate, :true_shooting_percentage,
                                        :created_at, :updated_at)
                            """), player_data)

                            inserted += 1

                    except Exception as e:
                        error_msg = f"Error processing {file_path}: {str(e)}"
                        errors.append(error_msg)
                        logger.warning(error_msg)
                        continue

                conn.commit()

            duration = (datetime.now() - start_time).total_seconds()

            return MigrationResult(
                success=len(errors) == 0,
                records_processed=processed,
                records_inserted=inserted,
                records_updated=0,
                records_skipped=skipped,
                errors=errors,
                duration_seconds=duration,
                migration_id=migration_id
            )

        except Exception as e:
            errors.append(f"Player consolidation failed: {str(e)}")
            logger.error(f"Player consolidation failed: {e}")

            return MigrationResult(
                success=False,
                records_processed=processed,
                records_inserted=inserted,
                records_updated=0,
                records_skipped=skipped,
                errors=errors,
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                migration_id=migration_id
            )

    def _extract_season_from_filename(self, filename: str) -> str:
        """Extract season from filename"""

        # Look for patterns like 2024, 2024-25, etc.
        patterns = [
            r'(\d{4})-(\d{2})',  # 2024-25 format
            r'(\d{4})',          # 2024 format
        ]

        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                if len(match.groups()) == 2:
                    return f"{match.group(1)}-{match.group(2)}"
                else:
                    year = int(match.group(1))
                    return f"{year}-{str(year + 1)[-2:]}"

        # Default to current season
        current_year = datetime.now().year
        return f"{current_year}-{str(current_year + 1)[-2:]}"

    def _map_team_data(self, row: pd.Series, league: str, season: str) -> Dict[str, Any]:
        """Map CSV row to team model data"""
        now = datetime.now(timezone.utc)

        # Generate unique ID if not present
        team_id = row.get('id') or row.get('TEAM_ID') or str(uuid.uuid4())

        return {
            'id': f"{league}_{team_id}",
            'name': row.get('full_name') or row.get('TEAM_NAME') or row.get('name', ''),
            'abbreviation': row.get('abbreviation') or row.get('TEAM_ABBREVIATION') or row.get('abbr', ''),
            'city': row.get('city') or row.get('TEAM_CITY') or '',
            'conference': row.get('conference') or row.get('CONFERENCE') or '',
            'division': row.get('division') or row.get('DIVISION') or '',
            'league': league,
            'season': season,
            'wins': int(row.get('wins', row.get('W', 0)) or 0),
            'losses': int(row.get('losses', row.get('L', 0)) or 0),
            'win_percentage': float(row.get('win_percentage', row.get('W_PCT', 0)) or 0),
            'games_played': int(row.get('games_played', row.get('GP', 0)) or 0),
            'points_per_game': float(row.get('points_per_game', row.get('PTS', 0)) or 0),
            'points_allowed_per_game': float(row.get('opp_pts', row.get('OPP_PTS', 0)) or 0),
            'net_rating': float(row.get('net_rating', row.get('NET_RATING', 0)) or 0),
            'offensive_rating': float(row.get('off_rating', row.get('OFF_RATING', 0)) or 0),
            'defensive_rating': float(row.get('def_rating', row.get('DEF_RATING', 0)) or 0),
            'pace': float(row.get('pace', row.get('PACE', 0)) or 0),
            'true_shooting_percentage': float(row.get('ts_pct', row.get('TS_PCT', 0)) or 0),
            'effective_field_goal_percentage': float(row.get('efg_pct', row.get('EFG_PCT', 0)) or 0),
            'created_at': now,
            'updated_at': now
        }

    def _map_player_data(self, row: pd.Series, league: str, season: str) -> Dict[str, Any]:
        """Map CSV row to player model data"""
        now = datetime.now(timezone.utc)

        # Generate unique ID if not present
        player_id = row.get('id') or row.get('PLAYER_ID') or str(uuid.uuid4())

        # Parse name fields
        full_name = row.get('full_name') or row.get('PLAYER_NAME') or row.get('name', '')
        first_name = row.get('first_name') or row.get('FIRST_NAME') or ''
        last_name = row.get('last_name') or row.get('LAST_NAME') or ''

        # If full name exists but first/last don't, try to split
        if full_name and not (first_name and last_name):
            name_parts = full_name.split(' ', 1)
            first_name = name_parts[0] if len(name_parts) > 0 else ''
            last_name = name_parts[1] if len(name_parts) > 1 else ''

        return {
            'id': f"{league}_{player_id}",
            'full_name': full_name,
            'first_name': first_name,
            'last_name': last_name,
            'position': row.get('position') or row.get('POSITION') or '',
            'height': row.get('height') or row.get('HEIGHT') or '',
            'weight': int(row.get('weight', row.get('WEIGHT', 0)) or 0),
            'birth_date': self._parse_date(row.get('birth_date') or row.get('BIRTHDATE')),
            'years_experience': int(row.get('years_experience', row.get('YEARS_EXPERIENCE', 0)) or 0),
            'college': row.get('college') or row.get('SCHOOL') or '',
            'country': row.get('country') or row.get('COUNTRY') or '',
            'mythic_roster_id': row.get('team_id') or row.get('TEAM_ID') or '',
            'league': league,
            'season': season,
            'is_active': bool(row.get('is_active', True)),
            'jersey_number': int(row.get('jersey_number', row.get('JERSEY', 0)) or 0),
            'points_per_game': float(row.get('pts', row.get('PTS', 0)) or 0),
            'rebounds_per_game': float(row.get('reb', row.get('REB', 0)) or 0),
            'assists_per_game': float(row.get('ast', row.get('AST', 0)) or 0),
            'steals_per_game': float(row.get('stl', row.get('STL', 0)) or 0),
            'blocks_per_game': float(row.get('blk', row.get('BLK', 0)) or 0),
            'field_goal_percentage': float(row.get('fg_pct', row.get('FG_PCT', 0)) or 0),
            'three_point_percentage': float(row.get('fg3_pct', row.get('FG3_PCT', 0)) or 0),
            'free_throw_percentage': float(row.get('ft_pct', row.get('FT_PCT', 0)) or 0),
            'player_efficiency_rating': float(row.get('per', row.get('PER', 0)) or 0),
            'usage_rate': float(row.get('usg_pct', row.get('USG_PCT', 0)) or 0),
            'true_shooting_percentage': float(row.get('ts_pct', row.get('TS_PCT', 0)) or 0),
            'created_at': now,
            'updated_at': now
        }

    def _parse_date(self, date_str: Any) -> Optional[datetime]:
        """Parse date string to datetime object"""
        if not date_str or pd.isna(date_str):
            return None

        try:
            if isinstance(date_str, str):
                # Try common date formats
                for fmt in ['%Y-%m-%d', '%m/%d/%Y', '%Y-%m-%d %H:%M:%S']:
                    try:
                        return datetime.strptime(date_str, fmt)
                    except ValueError:
                        continue
            return None
        except Exception:
            return None

    async def _consolidate_game_data(self) -> MigrationResult:
        """Consolidate game data from CSV files"""
        migration_id = f"games_consolidation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()
        errors = []
        processed = 0
        inserted = 0
        skipped = 0

        try:
            # Find all game/schedule CSV files
            game_files = list(self.data_dir.glob("**/schedule_*.csv"))
            game_files.extend(list(self.data_dir.glob("**/games_*.csv")))
            game_files.extend(list(self.data_dir.glob("**/wnba_games_*.csv")))

            logger.info(f"Found {len(game_files)} game data files")

            # Track unique games to prevent duplicates
            seen_games = set()

            with self.engine.connect() as conn:
                for file_path in game_files:
                    try:
                        df = pd.read_csv(file_path)
                        processed += len(df)

                        # Determine league from filename
                        league = "WNBA" if "wnba" in file_path.name.lower() else "NBA"

                        # Extract season from filename
                        season = self._extract_season_from_filename(file_path.name)

                        for _, row in df.iterrows():
                            # Create unique game identifier
                            game_id = row.get('GAME_ID') or row.get('id') or str(uuid.uuid4())
                            game_key = f"{league}_{game_id}"

                            if game_key in seen_games:
                                skipped += 1
                                continue

                            seen_games.add(game_key)

                            # Map CSV columns to our model
                            game_data = self._map_game_data(row, league, season)

                            # Insert game
                            conn.execute(text("""
                                INSERT OR IGNORE INTO games
                                (id, date, season, status, home_team_id, away_team_id, league,
                                 home_score, away_score, attendance, arena, broadcast_network,
                                 spread_home, spread_away, total_points, moneyline_home, moneyline_away,
                                 over_under_result, spread_result_home, created_at, updated_at)
                                VALUES (:id, :date, :season, :status, :home_team_id, :away_team_id, :league,
                                        :home_score, :away_score, :attendance, :arena, :broadcast_network,
                                        :spread_home, :spread_away, :total_points, :moneyline_home, :moneyline_away,
                                        :over_under_result, :spread_result_home, :created_at, :updated_at)
                            """), game_data)

                            inserted += 1

                    except Exception as e:
                        error_msg = f"Error processing {file_path}: {str(e)}"
                        errors.append(error_msg)
                        logger.warning(error_msg)
                        continue

                conn.commit()

            duration = (datetime.now() - start_time).total_seconds()

            return MigrationResult(
                success=len(errors) == 0,
                records_processed=processed,
                records_inserted=inserted,
                records_updated=0,
                records_skipped=skipped,
                errors=errors,
                duration_seconds=duration,
                migration_id=migration_id
            )

        except Exception as e:
            errors.append(f"Game consolidation failed: {str(e)}")
            logger.error(f"Game consolidation failed: {e}")

            return MigrationResult(
                success=False,
                records_processed=processed,
                records_inserted=inserted,
                records_updated=0,
                records_skipped=skipped,
                errors=errors,
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                migration_id=migration_id
            )

    def _map_game_data(self, row: pd.Series, league: str, season: str) -> Dict[str, Any]:
        """Map CSV row to game model data"""
        now = datetime.now(timezone.utc)

        # Generate unique ID if not present
        game_id = row.get('GAME_ID') or row.get('id') or str(uuid.uuid4())

        return {
            'id': f"{league}_{game_id}",
            'date': self._parse_date(row.get('GAME_DATE') or row.get('date')),
            'season': season,
            'status': row.get('status') or row.get('GAME_STATUS_TEXT') or 'Final',
            'home_team_id': f"{league}_{row.get('HOME_TEAM_ID', '')}" if row.get('HOME_TEAM_ID') else '',
            'away_team_id': f"{league}_{row.get('VISITOR_TEAM_ID', '')}" if row.get('VISITOR_TEAM_ID') else '',
            'league': league,
            'home_score': int(row.get('PTS_home', row.get('HOME_TEAM_SCORE', 0)) or 0),
            'away_score': int(row.get('PTS_away', row.get('VISITOR_TEAM_SCORE', 0)) or 0),
            'attendance': int(row.get('attendance', 0) or 0),
            'arena': row.get('arena') or row.get('ARENA') or '',
            'broadcast_network': row.get('broadcast') or row.get('NATL_TV_BROADCASTER_ABBREVIATION') or '',
            'spread_home': float(row.get('spread_home', 0) or 0),
            'spread_away': float(row.get('spread_away', 0) or 0),
            'total_points': float(row.get('total_points', 0) or 0),
            'moneyline_home': float(row.get('moneyline_home', 0) or 0),
            'moneyline_away': float(row.get('moneyline_away', 0) or 0),
            'over_under_result': row.get('over_under_result') or '',
            'spread_result_home': row.get('spread_result_home') or '',
            'created_at': now,
            'updated_at': now
        }

    async def _consolidate_player_stats_data(self) -> MigrationResult:
        """Consolidate player game stats from CSV files"""
        migration_id = f"player_stats_consolidation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        start_time = datetime.now()
        errors = []
        processed = 0
        inserted = 0
        skipped = 0

        try:
            # Find all player stats CSV files
            stats_files = list(self.data_dir.glob("**/player_stats_*.csv"))
            stats_files.extend(list(self.data_dir.glob("**/advanced_player_stats_*.csv")))

            logger.info(f"Found {len(stats_files)} player stats files")

            # Track unique stats to prevent duplicates
            seen_stats = set()

            with self.engine.connect() as conn:
                for file_path in stats_files:
                    try:
                        df = pd.read_csv(file_path)
                        processed += len(df)

                        # Determine league from filename
                        league = "WNBA" if "wnba" in file_path.name.lower() else "NBA"

                        # Extract season from filename
                        season = self._extract_season_from_filename(file_path.name)

                        for _, row in df.iterrows():
                            # Create unique stat identifier
                            player_id = row.get('PLAYER_ID') or row.get('id') or ''
                            game_id = row.get('GAME_ID') or row.get('game_id') or str(uuid.uuid4())
                            stat_key = f"{league}_{player_id}_{game_id}"

                            if stat_key in seen_stats:
                                skipped += 1
                                continue

                            seen_stats.add(stat_key)

                            # Map CSV columns to our model
                            stat_data = self._map_player_stat_data(row, league, season)

                            # Insert player game stat
                            conn.execute(text("""
                                INSERT OR IGNORE INTO player_game_stats
                                (id, hero_id, titan_clash_id, date, season, league, minutes_played,
                                 points, field_goals_made, field_goals_attempted, field_goal_percentage,
                                 three_pointers_made, three_pointers_attempted, three_point_percentage,
                                 free_throws_made, free_throws_attempted, free_throw_percentage,
                                 offensive_rebounds, defensive_rebounds, total_rebounds, assists,
                                 steals, blocks, turnovers, personal_fouls, plus_minus,
                                 created_at, updated_at)
                                VALUES (:id, :hero_id, :titan_clash_id, :date, :season, :league, :minutes_played,
                                        :points, :field_goals_made, :field_goals_attempted, :field_goal_percentage,
                                        :three_pointers_made, :three_pointers_attempted, :three_point_percentage,
                                        :free_throws_made, :free_throws_attempted, :free_throw_percentage,
                                        :offensive_rebounds, :defensive_rebounds, :total_rebounds, :assists,
                                        :steals, :blocks, :turnovers, :personal_fouls, :plus_minus,
                                        :created_at, :updated_at)
                            """), stat_data)

                            inserted += 1

                    except Exception as e:
                        error_msg = f"Error processing {file_path}: {str(e)}"
                        errors.append(error_msg)
                        logger.warning(error_msg)
                        continue

                conn.commit()

            duration = (datetime.now() - start_time).total_seconds()

            return MigrationResult(
                success=len(errors) == 0,
                records_processed=processed,
                records_inserted=inserted,
                records_updated=0,
                records_skipped=skipped,
                errors=errors,
                duration_seconds=duration,
                migration_id=migration_id
            )

        except Exception as e:
            errors.append(f"Player stats consolidation failed: {str(e)}")
            logger.error(f"Player stats consolidation failed: {e}")

            return MigrationResult(
                success=False,
                records_processed=processed,
                records_inserted=inserted,
                records_updated=0,
                records_skipped=skipped,
                errors=errors,
                duration_seconds=(datetime.now() - start_time).total_seconds(),
                migration_id=migration_id
            )

    def _map_player_stat_data(self, row: pd.Series, league: str, season: str) -> Dict[str, Any]:
        """Map CSV row to player game stat model data"""
        now = datetime.now(timezone.utc)

        # Generate unique IDs
        player_id = row.get('PLAYER_ID') or row.get('id') or str(uuid.uuid4())
        game_id = row.get('GAME_ID') or row.get('game_id') or str(uuid.uuid4())
        stat_id = f"{league}_{player_id}_{game_id}"

        return {
            'id': stat_id,
            'hero_id': f"{league}_{player_id}",
            'titan_clash_id': f"{league}_{game_id}",
            'date': self._parse_date(row.get('GAME_DATE') or row.get('date')),
            'season': season,
            'league': league,
            'minutes_played': float(row.get('MIN', 0) or 0),
            'points': int(row.get('PTS', 0) or 0),
            'field_goals_made': int(row.get('FGM', 0) or 0),
            'field_goals_attempted': int(row.get('FGA', 0) or 0),
            'field_goal_percentage': float(row.get('FG_PCT', 0) or 0),
            'three_pointers_made': int(row.get('FG3M', 0) or 0),
            'three_pointers_attempted': int(row.get('FG3A', 0) or 0),
            'three_point_percentage': float(row.get('FG3_PCT', 0) or 0),
            'free_throws_made': int(row.get('FTM', 0) or 0),
            'free_throws_attempted': int(row.get('FTA', 0) or 0),
            'free_throw_percentage': float(row.get('FT_PCT', 0) or 0),
            'offensive_rebounds': int(row.get('OREB', 0) or 0),
            'defensive_rebounds': int(row.get('DREB', 0) or 0),
            'total_rebounds': int(row.get('REB', 0) or 0),
            'assists': int(row.get('AST', 0) or 0),
            'steals': int(row.get('STL', 0) or 0),
            'blocks': int(row.get('BLK', 0) or 0),
            'turnovers': int(row.get('TOV', 0) or 0),
            'personal_fouls': int(row.get('PF', 0) or 0),
            'plus_minus': float(row.get('PLUS_MINUS', 0) or 0),
            'created_at': now,
            'updated_at': now
        }

async def run_full_migration():
    """Run complete database migration and consolidation"""

    migrator = DatabaseMigrator()

    try:
        # Initialize database engines
        await migrator.initialize_engines()

        # Create production schema
        schema_result = await migrator.create_production_schema()
        if schema_result.success:
            pass  # You can add logic here if needed
        else:
            return

        # Consolidate all CSV data
        consolidation_results = await migrator.consolidate_csv_data()

        # Print summary

        total_processed = 0
        total_inserted = 0
        total_errors = 0

        for result in consolidation_results:
            status = "✅" if result.success else "❌"

            if result.errors:
                for error in result.errors[:3]:  # Show first 3 errors
                    logger.error(f"{status} {error}")
                if len(result.errors) > 3:
                    logger.error(f"...and {len(result.errors) - 3} more errors.")

            total_processed += result.records_processed
            total_inserted += result.records_inserted
            total_errors += len(result.errors)

        if total_errors == 0:
            logger.info(f"Migration completed successfully. Processed: {total_processed}, Inserted: {total_inserted}")
        else:
            logger.error(f"Migration completed with errors. Processed: {total_processed}, Inserted: {total_inserted}, Errors: {total_errors}")

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        raise

if __name__ == "__main__":

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('migration.log'),
            logging.StreamHandler()
        ]
    )

    # Run migration
    asyncio.run(run_full_migration())
