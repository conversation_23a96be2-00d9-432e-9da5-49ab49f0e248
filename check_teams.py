import os
import sqlite3
import pandas as pd

# Check what teams exist in the unified database
db_path = 'data/unified_nba_wnba_data.db'
if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    
    # Check what tables exist
    tables_query = "SELECT name FROM sqlite_master WHERE type='table'"
    tables_df = pd.read_sql_query(tables_query, conn)
    print('Tables in database:')
    print(tables_df)
    
    # Check unified table structure
    if 'unified_nba_wnba_data' in tables_df['name'].values:
        # Get column info
        columns_query = "PRAGMA table_info(unified_nba_wnba_data)"
        columns_df = pd.read_sql_query(columns_query, conn)
        print('\nColumns in unified_nba_wnba_data:')
        print(columns_df)

        # Check sample data
        sample_query = 'SELECT * FROM unified_nba_wnba_data LIMIT 5'
        sample_df = pd.read_sql_query(sample_query, conn)
        print('\nSample data:')
        print(sample_df)

        # Check unique teams if team column exists
        if 'team_abbreviation' in sample_df.columns:
            unique_teams_query = 'SELECT COUNT(DISTINCT team_abbreviation) as unique_teams FROM unified_nba_wnba_data'
            unique_count = pd.read_sql_query(unique_teams_query, conn)
            print(f'\nUnique teams: {unique_count.iloc[0]["unique_teams"]}')

            # Show unique team abbreviations
            teams_query = 'SELECT DISTINCT team_abbreviation FROM unified_nba_wnba_data ORDER BY team_abbreviation'
            teams_df = pd.read_sql_query(teams_query, conn)
            print('\nAll WNBA teams in database:')
            print(teams_df)

            # Check which WNBA team is missing (should be 13 teams)
            expected_wnba_teams = [
                'Atlanta Dream', 'Chicago Sky', 'Connecticut Sun', 'Dallas Wings',
                'Indiana Fever', 'Las Vegas Aces', 'Los Angeles Sparks', 'Minnesota Lynx',
                'New York Liberty', 'Phoenix Mercury', 'Seattle Storm', 'Washington Mystics',
                'Golden State Valkyries'  # New team added in 2025
            ]

            current_teams = set(teams_df['team_abbreviation'].tolist())
            expected_teams = set(expected_wnba_teams)
            missing_teams = expected_teams - current_teams

            print(f'\nExpected 13 WNBA teams, found {len(current_teams)}')
            if missing_teams:
                print(f'Missing teams: {missing_teams}')

        # Check leagues and NBA data
        if 'league_id' in sample_df.columns:
            leagues_query = 'SELECT DISTINCT league_id, COUNT(*) as count FROM unified_nba_wnba_data GROUP BY league_id'
            leagues_df = pd.read_sql_query(leagues_query, conn)
            print('\nLeagues in database:')
            print(leagues_df)
            print('Note: league_id 10 = WNBA, league_id 00 = NBA')

            # Check if there's any NBA data
            nba_check = 'SELECT COUNT(*) as nba_count FROM unified_nba_wnba_data WHERE league_id = "00"'
            nba_count = pd.read_sql_query(nba_check, conn)
            print(f'NBA records: {nba_count.iloc[0]["nba_count"]}')

        # Check total record count
        total_query = 'SELECT COUNT(*) as total_records FROM unified_nba_wnba_data'
        total_count = pd.read_sql_query(total_query, conn)
        print(f'\nTotal records in database: {total_count.iloc[0]["total_records"]}')

        # Check leagues if league column exists
        if 'league_id' in sample_df.columns:
            leagues_query = 'SELECT DISTINCT league_id, COUNT(*) as count FROM unified_nba_wnba_data GROUP BY league_id'
            leagues_df = pd.read_sql_query(leagues_query, conn)
            print('\nLeagues in database:')
            print(leagues_df)

        # Check data categories
        if 'data_category' in sample_df.columns:
            categories_query = 'SELECT DISTINCT data_category, COUNT(*) as count FROM unified_nba_wnba_data GROUP BY data_category'
            categories_df = pd.read_sql_query(categories_query, conn)
            print('\nData categories:')
            print(categories_df)
    
    conn.close()
else:
    print('Unified database not found')
