{"training_session": {"timestamp": "2025-06-30T00:19:43.830222", "database": "medusa_master.db", "database_size_mb": 50.93}, "training_results": {"hybrid_training": {"status": "success", "training_results": {}, "report": {"training_timestamp": "2025-06-30T00:19:42.251944", "database_path": "medusa_master.db", "sklearn_available": true, "pandas_available": true, "training_results": {}, "data_summary": {"total_features": 10, "total_targets": 0, "feature_names": ["league_name_encoded", "season_type_encoded", "player_name_encoded", "team_abbreviation_encoded", "stat_value", "rank_position", "league_name_encoded", "season_type_encoded", "player_name_encoded", "team_abbreviation_encoded"], "target_names": []}, "models_trained": [], "models_directory": "models/hybrid_trained", "status": "failed"}, "data_summary": {"tables_loaded": ["advanced_stats"], "features_engineered": 10, "models_trained": []}}, "standard_training": {"status": "failed", "error": "BasketballDataConnector.__init__() got an unexpected keyword argument 'league'"}, "neural_training": {"status": "failed", "error": "No module named 'torch'"}}, "summary": {"hybrid_success": true, "standard_success": false, "neural_success": false, "models_trained": 0}}