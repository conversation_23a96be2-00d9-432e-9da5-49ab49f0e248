import sqlite3
import pandas as pd
import logging
import os
from datetime import datetime

#!/usr/bin/env python3
"""
Create Hybrid Database Tables for HYPER MEDUSA NEURAL VAULT
Transforms unified data into specialized hybrid analytics tables
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_hybrid_tables(db_path="medusa_master.db"):
    """Create hybrid database tables with proper schema"""
    logger.info("🔧 Creating hybrid database tables...")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Advanced Stats table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS hybrid_advanced_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        collection_id TEXT NOT NULL,
        data_type TEXT NOT NULL,
        season TEXT NOT NULL,
        league_id TEXT NOT NULL,
        league_name TEXT NOT NULL,
        season_type TEXT NOT NULL,
        game_id TEXT,
        player_id TEXT,
        player_name TEXT,
        team_id TEXT,
        team_abbreviation TEXT,
        off_rating REAL,
        def_rating REAL,
        net_rating REAL,
        ast_pct REAL,
        reb_pct REAL,
        usg_pct REAL,
        pie REAL,
        pace REAL,
        quantum_metadata TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(collection_id, game_id, player_id, data_type)
    )
    ''')
    
    # League Leaders table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS hybrid_league_leaders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        collection_id TEXT NOT NULL,
        data_type TEXT NOT NULL,
        season TEXT NOT NULL,
        league_id TEXT NOT NULL,
        league_name TEXT NOT NULL,
        season_type TEXT NOT NULL,
        stat_category TEXT NOT NULL,
        player_id TEXT,
        player_name TEXT,
        team_abbreviation TEXT,
        rank_position INTEGER,
        stat_value REAL,
        games_played INTEGER,
        quantum_metadata TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(collection_id, stat_category, player_id, season)
    )
    ''')
    
    # Game Data table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS hybrid_game_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        collection_id TEXT NOT NULL,
        data_type TEXT NOT NULL,
        season TEXT NOT NULL,
        league_id TEXT NOT NULL,
        league_name TEXT NOT NULL,
        season_type TEXT NOT NULL,
        game_id TEXT UNIQUE,
        game_date TEXT,
        home_team_id TEXT,
        home_team_name TEXT,
        away_team_id TEXT,
        away_team_name TEXT,
        home_score INTEGER,
        away_score INTEGER,
        game_status TEXT,
        quantum_metadata TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Assist Tracker table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS hybrid_assist_tracker (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        collection_id TEXT NOT NULL,
        data_type TEXT NOT NULL,
        season TEXT NOT NULL,
        league_id TEXT NOT NULL,
        league_name TEXT NOT NULL,
        season_type TEXT NOT NULL,
        player_id TEXT,
        player_name TEXT,
        team_id TEXT,
        team_abbreviation TEXT,
        games_played INTEGER,
        minutes_played REAL,
        assists REAL,
        assist_percentage REAL,
        assist_to_turnover_ratio REAL,
        assist_rank INTEGER,
        passes_made REAL,
        passes_received REAL,
        assist_opportunities REAL,
        assist_points_created REAL,
        secondary_assists REAL,
        potential_assists REAL,
        filter_type TEXT,
        quantum_metadata TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(collection_id, player_id, season, filter_type, data_type)
    )
    ''')
    
    # Create performance indexes
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_hybrid_season_league ON hybrid_advanced_stats(season, league_id)",
        "CREATE INDEX IF NOT EXISTS idx_hybrid_leaders ON hybrid_league_leaders(stat_category, rank_position)",
        "CREATE INDEX IF NOT EXISTS idx_hybrid_game ON hybrid_game_data(game_id, game_date)",
        "CREATE INDEX IF NOT EXISTS idx_hybrid_assist ON hybrid_assist_tracker(player_id, season, filter_type)"
    ]
    
    for index in indexes:
        cursor.execute(index)
    
    conn.commit()
    conn.close()
    logger.info("✅ Hybrid database tables created successfully")

def populate_hybrid_tables(db_path="medusa_master.db"):
    """Populate hybrid tables from unified data"""
    logger.info("📊 Populating hybrid tables from unified data...")

    conn = sqlite3.connect(db_path)

    # Check if tables are already populated
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM hybrid_league_leaders")
    existing_count = cursor.fetchone()[0]

    if existing_count > 0:
        logger.info(f"✅ Hybrid tables already populated with {existing_count} records")
        conn.close()
        return

    # Load unified data
    unified_df = pd.read_sql_query("SELECT * FROM unified_nba_wnba_data", conn)
    logger.info(f"📊 Loaded {len(unified_df)} records from unified table")

    collection_id = f"hybrid_migration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

    # Transform to league leaders format
    leaders_data = []
    advanced_stats_data = []

    for _, row in unified_df.iterrows():
        league_name = row.get('league_name') or 'NBA'
        league_id = '00' if str(league_name).upper() == 'NBA' else '10'

        # League leaders data
        leaders_data.append({
            'collection_id': collection_id,
            'data_type': 'league_leaders',
            'season': row.get('season') or '2024',
            'league_id': league_id,
            'league_name': league_name,
            'season_type': row.get('season_type') or 'Regular Season',
            'stat_category': 'POINTS',  # Default category
            'player_id': str(row.get('player_id') or ''),
            'player_name': row.get('player_name') or '',
            'team_abbreviation': row.get('team_abbreviation') or '',
            'rank_position': row.get('rank_position') or 1,
            'stat_value': float(row.get('stat_value') or 0.0),
            'games_played': 82,  # Default
            'quantum_metadata': '{"source": "unified_migration"}'
        })

        # Advanced stats data (synthetic for ML training)
        advanced_stats_data.append({
            'collection_id': collection_id,
            'data_type': 'advanced_stats',
            'season': row.get('season') or '2024',
            'league_id': league_id,
            'league_name': league_name,
            'season_type': row.get('season_type') or 'Regular Season',
            'game_id': f"game_{row.get('player_id', '')}_{row.get('rank_position', 1)}",
            'player_id': str(row.get('player_id') or ''),
            'player_name': row.get('player_name') or '',
            'team_id': str(row.get('team_abbreviation') or ''),
            'team_abbreviation': row.get('team_abbreviation') or '',
            'off_rating': float(row.get('stat_value', 0.0)) * 1.1,  # Synthetic
            'def_rating': 100.0,  # Default
            'net_rating': float(row.get('stat_value', 0.0)) * 0.1,  # Synthetic
            'ast_pct': 15.0,  # Default
            'reb_pct': 10.0,  # Default
            'usg_pct': 20.0,  # Default
            'pie': 0.5,  # Default
            'pace': 100.0,  # Default
            'quantum_metadata': '{"source": "unified_migration"}'
        })

    # Insert data in batches to avoid constraint issues
    try:
        # Insert league leaders data
        leaders_df = pd.DataFrame(leaders_data)
        leaders_df.to_sql('hybrid_league_leaders', conn, if_exists='replace', index=False)
        logger.info(f"✅ Inserted {len(leaders_df)} league leaders records")

        # Insert advanced stats data
        advanced_df = pd.DataFrame(advanced_stats_data)
        advanced_df.to_sql('hybrid_advanced_stats', conn, if_exists='replace', index=False)
        logger.info(f"✅ Inserted {len(advanced_df)} advanced stats records")

    except Exception as e:
        logger.error(f"❌ Error inserting data: {e}")

    conn.close()
    logger.info("✅ Hybrid tables populated successfully")

if __name__ == "__main__":
    create_hybrid_tables()
    populate_hybrid_tables()
    logger.info("🎯 Hybrid database setup complete!")
