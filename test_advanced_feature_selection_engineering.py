#!/usr/bin/env python3
"""
🎯 Test Suite for Advanced Feature Selection & Engineering System
Comprehensive testing for the HYPER MEDUSA NEURAL VAULT feature selection system

This test suite validates:
- Multi-method feature selection algorithms
- Basketball-specific feature engineering
- Feature quality assessment and ranking
- Integration with existing HMNV systems
- Performance optimization and validation
"""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Any
import logging
import warnings

# Suppress warnings for cleaner test output
warnings.filterwarnings('ignore')

# Import the system under test
from advanced_feature_selection_engineering_system import (
    AdvancedFeatureSelectionEngineeringSystem,
    FeatureSelectionConfig,
    FeatureSelectionMethod,
    FeatureEngineeringStrategy,
    create_advanced_feature_selector
)

# Configure logging for tests
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def test_feature_selection_system_initialization():
    """Test system initialization and configuration"""
    print("🎯 Testing Advanced Feature Selection System Initialization")
    
    # Test default configuration
    selector = AdvancedFeatureSelectionEngineeringSystem()
    assert selector.config is not None
    assert len(selector.config.methods) > 0
    assert len(selector.config.engineering_strategies) > 0
    
    # Test custom configuration
    custom_config = FeatureSelectionConfig(
        methods=[FeatureSelectionMethod.MUTUAL_INFORMATION, FeatureSelectionMethod.BASKETBALL_INTELLIGENCE],
        max_features=20,
        enable_basketball_intelligence=True
    )
    custom_selector = AdvancedFeatureSelectionEngineeringSystem(custom_config)
    assert custom_selector.config.max_features == 20
    assert len(custom_selector.config.methods) == 2
    
    print("✅ System initialization test passed")

async def test_basketball_feature_engineering():
    """Test basketball-specific feature engineering"""
    print("🏀 Testing Basketball Feature Engineering")
    
    # Create sample data
    np.random.seed(42)
    n_samples = 100
    data = {
        'points': np.random.normal(100, 15, n_samples),
        'field_goals_attempted': np.random.normal(80, 10, n_samples),
        'free_throws_attempted': np.random.normal(20, 5, n_samples),
        'field_goals_made': np.random.normal(40, 8, n_samples),
        'three_pointers_made': np.random.normal(12, 4, n_samples),
        'offensive_rating': np.random.normal(110, 10, n_samples),
        'defensive_rating': np.random.normal(105, 8, n_samples)
    }
    X = pd.DataFrame(data)
    
    selector = AdvancedFeatureSelectionEngineeringSystem()
    basketball_context = {'league': 'NBA'}
    
    # Test basketball feature engineering
    X_engineered = await selector._engineer_basketball_features(X, basketball_context)
    
    # Verify basketball features were created
    assert X_engineered.shape[1] > X.shape[1], "Basketball features should be added"
    
    # Check for specific basketball metrics
    expected_features = ['true_shooting_pct', 'effective_fg_pct', 'net_rating', 'efficiency_ratio']
    for feature in expected_features:
        if feature in X_engineered.columns:
            assert not X_engineered[feature].isnull().all(), f"{feature} should have valid values"
    
    print(f"✅ Basketball feature engineering: {X.shape[1]} → {X_engineered.shape[1]} features")

async def test_interaction_feature_engineering():
    """Test interaction feature engineering"""
    print("🔗 Testing Interaction Feature Engineering")
    
    # Create sample data
    np.random.seed(42)
    data = {
        'feature_1': np.random.normal(0, 1, 100),
        'feature_2': np.random.normal(0, 1, 100),
        'feature_3': np.random.normal(0, 1, 100)
    }
    X = pd.DataFrame(data)
    
    selector = AdvancedFeatureSelectionEngineeringSystem()
    X_interaction = await selector._engineer_interaction_features(X)
    
    # Verify interaction features were created
    assert X_interaction.shape[1] > X.shape[1], "Interaction features should be added"
    
    # Check for interaction feature patterns
    interaction_features = [col for col in X_interaction.columns if '_x_' in col or '_div_' in col or '_diff_' in col]
    assert len(interaction_features) > 0, "Should create interaction features"
    
    print(f"✅ Interaction feature engineering: {X.shape[1]} → {X_interaction.shape[1]} features")

async def test_mutual_information_selection():
    """Test mutual information feature selection"""
    print("🧠 Testing Mutual Information Selection")
    
    # Create sample data with known relationships
    np.random.seed(42)
    n_samples = 200
    X = pd.DataFrame({
        'relevant_1': np.random.normal(0, 1, n_samples),
        'relevant_2': np.random.normal(0, 1, n_samples),
        'noise_1': np.random.normal(0, 1, n_samples),
        'noise_2': np.random.normal(0, 1, n_samples)
    })
    
    # Create target with known relationships
    y = pd.Series(0.5 * X['relevant_1'] + 0.3 * X['relevant_2'] + 0.1 * np.random.normal(0, 1, n_samples))
    y = (y > y.median()).astype(int)
    
    selector = AdvancedFeatureSelectionEngineeringSystem()
    scores = await selector._mutual_information_selection(X, y)
    
    # Verify mutual information scores
    assert len(scores) > 0, "Should return feature scores"
    
    # Check that relevant features have higher scores
    relevant_scores = [s for s in scores if 'relevant' in s.feature_name]
    noise_scores = [s for s in scores if 'noise' in s.feature_name]
    
    if relevant_scores and noise_scores:
        avg_relevant = np.mean([s.importance_score for s in relevant_scores])
        avg_noise = np.mean([s.importance_score for s in noise_scores])
        print(f"   Relevant features avg score: {avg_relevant:.3f}")
        print(f"   Noise features avg score: {avg_noise:.3f}")
    
    print(f"✅ Mutual information selection: {len(scores)} features scored")

async def test_comprehensive_feature_selection():
    """Test comprehensive feature selection and engineering pipeline"""
    print("🎯 Testing Comprehensive Feature Selection Pipeline")
    
    # Create comprehensive test data
    np.random.seed(42)
    n_samples = 300
    
    # Basketball-style features with realistic relationships
    data = {
        'points': np.random.normal(100, 15, n_samples),
        'field_goals_made': np.random.normal(40, 8, n_samples),
        'field_goals_attempted': np.random.normal(80, 10, n_samples),
        'three_pointers_made': np.random.normal(12, 4, n_samples),
        'free_throws_attempted': np.random.normal(20, 5, n_samples),
        'total_rebounds': np.random.normal(45, 8, n_samples),
        'assists': np.random.normal(25, 6, n_samples),
        'steals': np.random.normal(8, 3, n_samples),
        'blocks': np.random.normal(5, 2, n_samples),
        'turnovers': np.random.normal(15, 4, n_samples),
        'offensive_rating': np.random.normal(110, 10, n_samples),
        'defensive_rating': np.random.normal(105, 8, n_samples),
        'pace': np.random.normal(100, 5, n_samples),
        'win_streak': np.random.randint(0, 10, n_samples),
        'loss_streak': np.random.randint(0, 8, n_samples),
        'noise_feature_1': np.random.normal(0, 1, n_samples),
        'noise_feature_2': np.random.normal(0, 1, n_samples)
    }
    
    X = pd.DataFrame(data)
    
    # Create realistic target (team performance)
    y = (
        0.25 * (X['points'] - X['points'].mean()) / X['points'].std() +
        0.20 * (X['offensive_rating'] - X['defensive_rating']) / 10 +
        0.15 * (X['assists'] - X['turnovers']) / 10 +
        0.10 * (X['total_rebounds'] + X['steals'] + X['blocks']) / 30 +
        0.10 * (X['win_streak'] - X['loss_streak']) / 5 +
        np.random.normal(0, 0.15, n_samples)
    )
    y = (y > y.median()).astype(int)
    
    # Create feature selector
    selector = create_advanced_feature_selector(
        max_features=12,
        enable_basketball_intelligence=True
    )
    
    # Run comprehensive feature selection
    basketball_context = {'league': 'NBA', 'season': '2023-24'}
    result = await selector.select_and_engineer_features(X, y, basketball_context)
    
    # Validate results
    assert result is not None, "Should return results"
    assert len(result.selected_features) > 0, "Should select features"
    assert len(result.selected_features) <= 12, "Should respect max_features limit"
    assert result.original_feature_count == X.shape[1], "Should track original feature count"
    assert result.execution_time > 0, "Should track execution time"
    
    # Check basketball insights
    assert 'basketball_features_count' in result.basketball_insights
    assert 'feature_categories' in result.basketball_insights
    assert 'recommendations' in result.basketball_insights
    
    # Check performance metrics
    assert 'estimated_accuracy' in result.performance_metrics
    assert 'feature_count' in result.performance_metrics
    
    # Verify feature quality
    basketball_features = [f for f in result.selected_features if any(
        keyword in f.lower() for keyword in ['points', 'rating', 'efficiency', 'basketball']
    )]
    
    print(f"   Original features: {result.original_feature_count}")
    print(f"   Selected features: {result.selected_feature_count}")
    print(f"   Basketball features: {len(basketball_features)}")
    print(f"   Estimated accuracy: {result.performance_metrics.get('estimated_accuracy', 0):.3f}")
    print(f"   Execution time: {result.execution_time:.2f}s")
    
    # Performance validation
    estimated_accuracy = result.performance_metrics.get('estimated_accuracy', 0)
    assert estimated_accuracy >= 0.5, f"Should achieve reasonable accuracy, got {estimated_accuracy:.3f}"
    
    print("✅ Comprehensive feature selection pipeline test passed")

async def test_basketball_intelligence_enhancement():
    """Test basketball intelligence enhancement"""
    print("🏀 Testing Basketball Intelligence Enhancement")
    
    # Create data with basketball features
    np.random.seed(42)
    n_samples = 150
    data = {
        'points': np.random.normal(100, 15, n_samples),
        'true_shooting_pct': np.random.uniform(0.4, 0.7, n_samples),
        'effective_fg_pct': np.random.uniform(0.4, 0.6, n_samples),
        'net_rating': np.random.normal(0, 5, n_samples),
        'random_feature': np.random.normal(0, 1, n_samples)
    }
    X = pd.DataFrame(data)
    y = pd.Series(np.random.randint(0, 2, n_samples))
    
    selector = AdvancedFeatureSelectionEngineeringSystem()
    
    # Test basketball intelligence enhancement
    initial_features = ['points', 'random_feature']
    initial_scores = []
    
    enhanced_features, enhanced_scores = await selector._basketball_intelligence_enhancement(
        X, y, initial_features, initial_scores, {'league': 'NBA'}
    )
    
    # Verify basketball features are prioritized
    basketball_priority_features = ['true_shooting_pct', 'effective_fg_pct', 'net_rating']
    for feature in basketball_priority_features:
        if feature in X.columns:
            assert feature in enhanced_features, f"Basketball feature {feature} should be included"
    
    print(f"✅ Basketball intelligence enhancement: {len(initial_features)} → {len(enhanced_features)} features")

async def run_all_tests():
    """Run all tests for the Advanced Feature Selection & Engineering System"""
    print("🎯 HYPER MEDUSA NEURAL VAULT - Advanced Feature Selection & Engineering Tests")
    print("=" * 80)
    
    try:
        # Run individual tests
        await test_feature_selection_system_initialization()
        await test_basketball_feature_engineering()
        await test_interaction_feature_engineering()
        await test_mutual_information_selection()
        await test_basketball_intelligence_enhancement()
        await test_comprehensive_feature_selection()
        
        print("\n" + "=" * 80)
        print("✅ ALL TESTS PASSED - Advanced Feature Selection & Engineering System is working correctly!")
        print("🎯 System ready for production use with basketball intelligence")
        
        return True
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Run the test suite
    success = asyncio.run(run_all_tests())
    exit(0 if success else 1)
