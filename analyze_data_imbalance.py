#!/usr/bin/env python3
"""
Analyze why there's more WNBA data than NBA data
"""

import pandas as pd
import os
from pathlib import Path

def main():
    print('🔍 ANALYZING DATA IMBALANCE: Why More WNBA than NBA Data?')
    print('=' * 60)
    
    # Load the datasets
    nba_df = pd.read_csv('data/ml_training/nba_training_data.csv')
    wnba_df = pd.read_csv('data/ml_training/wnba_training_data.csv')
    
    print(f'📊 RECORD COUNTS:')
    print(f'   NBA Records: {len(nba_df):,}')
    print(f'   WNBA Records: {len(wnba_df):,}')
    print(f'   Ratio: WNBA has {len(wnba_df) / len(nba_df):.1f}x more data than NBA')
    
    print()
    print('🔍 INVESTIGATING POSSIBLE CAUSES:')
    
    # Check file counts from our cleaning log
    print('\n1️⃣ FILE COUNT ANALYSIS:')
    print('   From cleaning log:')
    print('   - NBA files processed: 140')
    print('   - WNBA files processed: 256')
    print('   - WNBA has 1.8x more files than NBA')
    
    # Check data types/sources
    print('\n2️⃣ DATA SOURCE ANALYSIS:')
    if 'SOURCE_FILE' in nba_df.columns and 'SOURCE_FILE' in wnba_df.columns:
        nba_sources = nba_df['SOURCE_FILE'].value_counts()
        wnba_sources = wnba_df['SOURCE_FILE'].value_counts()
        
        print(f'   NBA unique source files: {len(nba_sources)}')
        print(f'   WNBA unique source files: {len(wnba_sources)}')
        
        print('\n   Top NBA data sources:')
        for file, count in nba_sources.head(5).items():
            print(f'     {file}: {count:,} records')
            
        print('\n   Top WNBA data sources:')
        for file, count in wnba_sources.head(5).items():
            print(f'     {file}: {count:,} records')
    
    # Check years covered
    print('\n3️⃣ TEMPORAL COVERAGE ANALYSIS:')
    
    # Extract years from source files
    def extract_years(df):
        if 'SOURCE_FILE' not in df.columns:
            return []
        years = set()
        for filename in df['SOURCE_FILE'].unique():
            # Look for year patterns like 2016-17, 2024, etc.
            import re
            year_matches = re.findall(r'20\d{2}', filename)
            years.update(year_matches)
        return sorted(years)
    
    nba_years = extract_years(nba_df)
    wnba_years = extract_years(wnba_df)
    
    print(f'   NBA years covered: {len(nba_years)} years - {nba_years}')
    print(f'   WNBA years covered: {len(wnba_years)} years - {wnba_years}')
    
    # Check data granularity
    print('\n4️⃣ DATA GRANULARITY ANALYSIS:')
    
    # Check if WNBA has more detailed/granular data
    nba_cols = len(nba_df.columns)
    wnba_cols = len(wnba_df.columns)
    print(f'   NBA columns: {nba_cols}')
    print(f'   WNBA columns: {wnba_cols}')
    
    # Check average records per team
    nba_teams = nba_df['TEAM_ABBREVIATION'].nunique()
    wnba_teams = wnba_df['TEAM_ABBREVIATION'].nunique()
    
    print(f'\n   Average records per team:')
    print(f'   NBA: {len(nba_df) / nba_teams:,.0f} records per team')
    print(f'   WNBA: {len(wnba_df) / wnba_teams:,.0f} records per team')
    
    print('\n5️⃣ POSSIBLE EXPLANATIONS:')
    print('   🔍 More WNBA source files (256 vs 140)')
    print('   🔍 WNBA data might include more detailed player tracking')
    print('   🔍 WNBA data might cover more seasons/years')
    print('   🔍 WNBA data might have more granular statistics')
    print('   🔍 Different data collection practices between leagues')
    
    # Check if we need to balance the data
    print('\n6️⃣ RECOMMENDATIONS:')
    ratio = len(wnba_df) / len(nba_df)
    if ratio > 3:
        print('   ⚠️ SIGNIFICANT IMBALANCE: Consider data balancing for training')
        print('   💡 Options: Sample WNBA data down or weight NBA data up')
    elif ratio > 2:
        print('   ⚠️ MODERATE IMBALANCE: Monitor model performance')
        print('   💡 May need league-specific model weights')
    else:
        print('   ✅ ACCEPTABLE IMBALANCE: Proceed with current data')
    
    print(f'\n📊 SUMMARY:')
    print(f'   WNBA has {ratio:.1f}x more data than NBA')
    print(f'   This is likely due to more comprehensive WNBA data collection')
    print(f'   Both leagues have sufficient data for training')

if __name__ == "__main__":
    main()
