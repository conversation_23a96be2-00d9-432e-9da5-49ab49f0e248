#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Player Props Prediction Service
Service for making individual player performance predictions using trained neural models
"""

import sys
import os
import torch
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass

# Add project root to path
sys.path.append('.')

from src.neural_cortex.player_props_neural_pipeline import (
    PlayerPropsNeuralNetwork, 
    PlayerPropsConfig
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class PlayerPropPrediction:
    """Player prop prediction result"""
    player_id: str
    player_name: str
    prop_type: str
    predicted_value: float
    confidence_interval: Tuple[float, float]
    confidence_score: float
    over_probability: float
    under_probability: float
    line_value: Optional[float] = None
    recommendation: str = ""
    factors: List[str] = None

class PlayerPropsPredictor:
    """Neural network-based player props predictor"""
    
    def __init__(self, league: str = "WNBA"):
        self.league = league.upper()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.models = {}  # Store loaded models by prop type
        self.configs = {}  # Store model configs
        
        # Supported prop types
        self.supported_props = [
            "points", "rebounds", "assists", "steals", "blocks", "threes"
        ]
        
        logger.info(f"🏀 Player Props Predictor initialized for {self.league}")
        logger.info(f"💻 Device: {self.device}")
    
    def load_model(self, prop_type: str) -> bool:
        """Load a trained model for specific prop type"""
        try:
            if prop_type not in self.supported_props:
                logger.error(f"❌ Unsupported prop type: {prop_type}")
                return False
            
            # Model path
            model_path = Path(f"models/player_props/{self.league.lower()}_{prop_type}/best_{prop_type}_model.pt")
            
            if not model_path.exists():
                logger.warning(f"⚠️ Model not found: {model_path}")
                return False
            
            logger.info(f"🔥 Loading {prop_type} model from: {model_path}")
            
            # Load checkpoint
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # Reconstruct config
            config_dict = checkpoint['config']
            config = PlayerPropsConfig(**config_dict)
            
            # Build model
            model = PlayerPropsNeuralNetwork(
                input_dim=config.input_dim,
                hidden_dim=config.hidden_dim,
                num_layers=config.num_layers,
                dropout_rate=config.dropout_rate,
                output_activation=config.output_activation,
                use_batch_norm=config.use_batch_norm
            ).to(self.device)
            
            # Load weights
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            
            # Store model and config
            self.models[prop_type] = model
            self.configs[prop_type] = config
            
            logger.info(f"✅ {prop_type} model loaded successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load {prop_type} model: {e}")
            return False
    
    def load_all_models(self) -> Dict[str, bool]:
        """Load all available models"""
        results = {}
        
        for prop_type in self.supported_props:
            results[prop_type] = self.load_model(prop_type)
        
        loaded_count = sum(results.values())
        logger.info(f"📊 Loaded {loaded_count}/{len(self.supported_props)} models")
        
        return results
    
    def prepare_player_features(self, player_data: Dict[str, Any], 
                              game_context: Dict[str, Any] = None) -> np.ndarray:
        """Prepare features for a player prediction"""
        try:
            # Base features (these should match training data structure)
            features = []
            
            # Player performance features
            features.extend([
                player_data.get('recent_avg', 15.0),  # Recent average for this prop
                player_data.get('season_avg', 14.0),  # Season average
                player_data.get('rank_position', 50),  # Player ranking
                player_data.get('consistency', 0.7),   # Performance consistency
                player_data.get('tier', 2),            # Player tier (0-4)
                player_data.get('position_encoded', 1) # Position encoding
            ])
            
            # Matchup features
            if game_context:
                features.extend([
                    game_context.get('opponent_strength', 0.5),  # Opponent strength
                    game_context.get('home_advantage', 1),       # Home/away
                    game_context.get('rest_days', 1),            # Rest days
                    game_context.get('back_to_back', 0),         # Back-to-back game
                    game_context.get('season_progress', 0.5)     # Season progression
                ])
            else:
                # Default values
                features.extend([0.5, 1, 1, 0, 0.5])
            
            # Recent form features
            features.extend([
                player_data.get('recent_form', 0.0),   # Recent form trend
                player_data.get('hot_streak', 0),      # Hot streak indicator
                player_data.get('cold_streak', 0)      # Cold streak indicator
            ])
            
            # Convert to numpy array and pad/truncate to match model input
            features_array = np.array(features, dtype=np.float32)
            
            # Ensure we have the right number of features for the model
            # This should match the training data feature count
            target_features = 13  # Based on our training pipeline
            
            if len(features_array) < target_features:
                # Pad with zeros
                padding = np.zeros(target_features - len(features_array))
                features_array = np.concatenate([features_array, padding])
            elif len(features_array) > target_features:
                # Truncate
                features_array = features_array[:target_features]
            
            return features_array
            
        except Exception as e:
            logger.error(f"❌ Failed to prepare features: {e}")
            # Return default features
            return np.zeros(13, dtype=np.float32)
    
    def predict_prop(self, player_data: Dict[str, Any], prop_type: str, 
                    game_context: Dict[str, Any] = None, 
                    line_value: Optional[float] = None) -> PlayerPropPrediction:
        """Predict a single player prop"""
        try:
            # Check if model is loaded
            if prop_type not in self.models:
                if not self.load_model(prop_type):
                    raise ValueError(f"Could not load model for {prop_type}")
            
            # Prepare features
            features = self.prepare_player_features(player_data, game_context)
            features_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)
            
            # Make prediction
            model = self.models[prop_type]
            model.eval()
            
            with torch.no_grad():
                prediction = model.forward_inference(features_tensor)
                predicted_value = prediction.item()
            
            # Ensure positive values for counting stats
            if prop_type in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']:
                predicted_value = max(0.0, predicted_value)
            
            # Calculate confidence interval (±15% as estimate)
            confidence_range = predicted_value * 0.15
            confidence_interval = (
                max(0.0, predicted_value - confidence_range),
                predicted_value + confidence_range
            )
            
            # Calculate confidence score based on consistency
            consistency = player_data.get('consistency', 0.7)
            confidence_score = min(0.95, max(0.5, consistency))
            
            # Calculate over/under probabilities if line provided
            over_prob = 0.5
            under_prob = 0.5
            recommendation = "HOLD"
            
            if line_value is not None:
                # Simple probability calculation based on prediction vs line
                diff = predicted_value - line_value
                std_dev = confidence_range / 1.96  # Approximate standard deviation
                
                if std_dev > 0:
                    # Use normal distribution to estimate probabilities
                    from scipy.stats import norm
                    over_prob = 1 - norm.cdf(line_value, predicted_value, std_dev)
                    under_prob = 1 - over_prob
                    
                    # Generate recommendation
                    if over_prob > 0.6:
                        recommendation = "OVER"
                    elif under_prob > 0.6:
                        recommendation = "UNDER"
                    else:
                        recommendation = "HOLD"
            
            # Identify key factors
            factors = []
            if player_data.get('hot_streak', 0):
                factors.append("Hot streak")
            if player_data.get('cold_streak', 0):
                factors.append("Cold streak")
            if game_context and game_context.get('home_advantage', 0):
                factors.append("Home advantage")
            if game_context and game_context.get('back_to_back', 0):
                factors.append("Back-to-back game")
            
            return PlayerPropPrediction(
                player_id=player_data.get('player_id', 'unknown'),
                player_name=player_data.get('player_name', 'Unknown Player'),
                prop_type=prop_type,
                predicted_value=predicted_value,
                confidence_interval=confidence_interval,
                confidence_score=confidence_score,
                over_probability=over_prob,
                under_probability=under_prob,
                line_value=line_value,
                recommendation=recommendation,
                factors=factors
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to predict {prop_type}: {e}")
            # Return fallback prediction
            return PlayerPropPrediction(
                player_id=player_data.get('player_id', 'unknown'),
                player_name=player_data.get('player_name', 'Unknown Player'),
                prop_type=prop_type,
                predicted_value=10.0,  # Fallback value
                confidence_interval=(8.0, 12.0),
                confidence_score=0.5,
                over_probability=0.5,
                under_probability=0.5,
                line_value=line_value,
                recommendation="HOLD",
                factors=["Fallback prediction"]
            )
    
    def predict_multiple_props(self, player_data: Dict[str, Any], 
                             prop_types: List[str],
                             game_context: Dict[str, Any] = None,
                             lines: Dict[str, float] = None) -> List[PlayerPropPrediction]:
        """Predict multiple props for a player"""
        predictions = []
        
        for prop_type in prop_types:
            line_value = lines.get(prop_type) if lines else None
            prediction = self.predict_prop(player_data, prop_type, game_context, line_value)
            predictions.append(prediction)
        
        return predictions
    
    def get_available_models(self) -> List[str]:
        """Get list of available/loaded models"""
        return list(self.models.keys())
    
    def get_model_info(self, prop_type: str) -> Dict[str, Any]:
        """Get information about a loaded model"""
        if prop_type not in self.models:
            return {"error": "Model not loaded"}
        
        config = self.configs[prop_type]
        model = self.models[prop_type]
        
        return {
            "prop_type": prop_type,
            "league": config.league,
            "input_dim": config.input_dim,
            "hidden_dim": config.hidden_dim,
            "num_layers": config.num_layers,
            "parameters": sum(p.numel() for p in model.parameters()),
            "device": str(self.device)
        }
