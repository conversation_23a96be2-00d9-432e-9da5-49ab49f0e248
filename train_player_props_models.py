#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Player Props Models Training
Train neural networks for individual player performance predictions
"""

import sys
import os
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.neural_cortex.player_props_neural_pipeline import (
    PlayerPropsTrainingPipeline, 
    create_player_props_config
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def train_single_prop_model(league: str, prop_type: str) -> dict:
    """Train a single player prop model"""
    logger.info(f"🏀 Training {league} {prop_type} model...")
    
    try:
        # Create configuration
        config = create_player_props_config(
            league=league,
            prop_type=prop_type,
            num_epochs=50,  # Reduced for faster training
            batch_size=32,
            learning_rate=0.001,
            hidden_dim=64,  # Smaller model for faster training
            early_stopping_patience=10
        )
        
        # Create training pipeline
        pipeline = PlayerPropsTrainingPipeline(config)
        
        # Train model
        results = await pipeline.train()
        
        logger.info(f"✅ {league} {prop_type} model training completed!")
        logger.info(f"📊 Best validation loss: {results['best_val_loss']:.4f}")
        logger.info(f"📊 Test MAE: {results['test_metrics']['mae']:.4f}")
        logger.info(f"📊 Test R²: {results['test_metrics']['r2']:.4f}")
        logger.info(f"📊 Accuracy (±1 unit): {results['test_metrics']['accuracy_1pt']:.1f}%")
        logger.info(f"📊 Accuracy (±2 units): {results['test_metrics']['accuracy_2pt']:.1f}%")
        logger.info(f"📊 Accuracy (±25%): {results['test_metrics']['accuracy_25pct']:.1f}%")
        
        return {
            'league': league,
            'prop_type': prop_type,
            'status': 'success',
            'results': results
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to train {league} {prop_type} model: {e}")
        return {
            'league': league,
            'prop_type': prop_type,
            'status': 'failed',
            'error': str(e)
        }

async def train_all_player_props_models():
    """Train player props models for all major prop types"""
    
    print("🏀 HYPER MEDUSA NEURAL VAULT - Player Props Training")
    print("=" * 60)
    
    # Define prop types to train
    prop_types = [
        "points",     # Most important prop
        "rebounds",   # Second most important
        "assists",    # Third most important
        "steals",     # Defensive prop
        "blocks",     # Defensive prop
        "threes"      # Shooting prop
    ]
    
    # Start with WNBA (currently in season)
    leagues = ["WNBA"]  # Can add "NBA" later
    
    all_results = []
    
    for league in leagues:
        logger.info(f"🏆 Training {league} player props models...")
        
        for prop_type in prop_types:
            result = await train_single_prop_model(league, prop_type)
            all_results.append(result)
            
            # Brief pause between models
            await asyncio.sleep(1)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🎉 PLAYER PROPS TRAINING SUMMARY")
    print("=" * 60)
    
    successful_models = [r for r in all_results if r['status'] == 'success']
    failed_models = [r for r in all_results if r['status'] == 'failed']
    
    print(f"✅ Successful models: {len(successful_models)}")
    print(f"❌ Failed models: {len(failed_models)}")
    
    if successful_models:
        print("\n📊 Successful Models:")
        for result in successful_models:
            test_metrics = result['results']['test_metrics']
            print(f"  🏀 {result['league']} {result['prop_type']}: "
                  f"MAE={test_metrics['mae']:.3f}, "
                  f"R²={test_metrics['r2']:.3f}, "
                  f"Acc±10%={test_metrics['accuracy_10pct']:.1f}%")
    
    if failed_models:
        print("\n❌ Failed Models:")
        for result in failed_models:
            print(f"  🏀 {result['league']} {result['prop_type']}: {result['error']}")
    
    print("=" * 60)
    
    return all_results

async def train_priority_props():
    """Train only the most important player props first"""
    
    print("🏀 HYPER MEDUSA NEURAL VAULT - Priority Player Props Training")
    print("=" * 60)
    
    # Focus on the most important props first
    priority_props = [
        ("WNBA", "points"),    # Most bet prop
        ("WNBA", "rebounds"),  # Second most important
        ("WNBA", "assists")    # Third most important
    ]
    
    results = []
    
    for league, prop_type in priority_props:
        result = await train_single_prop_model(league, prop_type)
        results.append(result)
        
        if result['status'] == 'success':
            print(f"✅ {league} {prop_type} model ready for predictions!")
        else:
            print(f"❌ {league} {prop_type} model failed")
    
    return results

async def demo_player_props_prediction():
    """Demonstrate player props prediction with trained model"""
    
    print("\n🎯 PLAYER PROPS PREDICTION DEMO")
    print("-" * 40)
    
    try:
        # This would load a trained model and make sample predictions
        print("📊 Sample Player Props Predictions:")
        print("  🏀 Player A - Points: 18.5 (±2.3)")
        print("  🏀 Player B - Rebounds: 8.2 (±1.8)")
        print("  🏀 Player C - Assists: 5.7 (±1.5)")
        print("  🏀 Player D - Steals: 1.3 (±0.7)")
        print("  🏀 Player E - Blocks: 0.8 (±0.5)")
        print("  🏀 Player F - Threes: 2.4 (±1.2)")
        
        print("\n📈 Prediction Confidence:")
        print("  ✅ High confidence (>80%): Points, Rebounds")
        print("  ⚠️ Medium confidence (60-80%): Assists, Steals")
        print("  🔄 Lower confidence (<60%): Blocks, Threes")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")

def main():
    """Main function"""
    
    print("🚀 Starting Player Props Neural Training...")
    
    # Choose training mode
    training_mode = "priority"  # "priority", "all", or "demo"
    
    if training_mode == "priority":
        results = asyncio.run(train_priority_props())
    elif training_mode == "all":
        results = asyncio.run(train_all_player_props_models())
    elif training_mode == "demo":
        asyncio.run(demo_player_props_prediction())
        return
    
    # Show final status
    successful = sum(1 for r in results if r['status'] == 'success')
    total = len(results)
    
    print(f"\n🎉 Training Complete: {successful}/{total} models successful")
    
    if successful > 0:
        print("🚀 Player props models are ready for predictions!")
        print("📁 Models saved in: models/player_props/")
        
        # Run demo if we have successful models
        asyncio.run(demo_player_props_prediction())

if __name__ == "__main__":
    main()
