# Testing Environment Configuration - HYPER MEDUSA NEURAL VAULT
# ==============================================================
# Testing-specific settings optimized for automated testing

database:
  host: "localhost"
  port: 5432
  name: "hyper_medusa_vault_test"
  user: "postgres"
  password: "test_password"
  pool_size: 5
  max_overflow: 10
  pool_timeout: 10
  pool_recycle: 3600
  echo: false  # Keep logs clean during testing

redis:
  host: "localhost"
  port: 6379
  db: 2  # Separate DB for testing
  password: null
  max_connections: 10
  socket_timeout: 5
  socket_connect_timeout: 5

api:
  host: "127.0.0.1"
  port: 8001  # Different port to avoid conflicts
  workers: 1
  timeout: 10  # Shorter timeout for faster test feedback
  max_requests: 100
  reload: false
  debug: false
  cors_origins:
    - "http://localhost:3000"
    - "http://127.0.0.1:3000"
  rate_limit: "1000/minute"  # Lenient for testing

ml:
  model_path: "models/test"
  batch_size: 8  # Small batch size for fast testing
  learning_rate: 0.1  # High learning rate for quick convergence
  epochs: 5  # Very few epochs for testing
  validation_split: 0.2
  early_stopping_patience: 2
  device: "cpu"  # Force CPU for consistent testing
  mixed_precision: false
  gradient_clipping: 1.0
  checkpoint_interval: 1

logging:
  level: "WARNING"  # Reduce log noise during testing
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/testing.log"
  max_bytes: 1048576  # 1MB
  backup_count: 2
  json_format: false
  structured_logging: false

security:
  secret_key: "test-secret-key-for-testing-only"
  jwt_algorithm: "HS256"
  jwt_expiration: 300  # 5 minutes for testing
  password_min_length: 4
  max_login_attempts: 100  # No lockouts during testing
  lockout_duration: 1
  cors_enabled: true
  csrf_protection: false

monitoring:
  prometheus_enabled: false  # Disable monitoring during testing
  grafana_enabled: false
  health_check_interval: 300  # Infrequent checks
  metrics_retention_days: 1
  alerting_enabled: false
  slack_webhook: null
  email_alerts: false
