import sqlite3
import pandas as pd
from datetime import datetime
import json
from pathlib import Path

#!/usr/bin/env python3
"""
Standalone Data Quality Validation Script
=========================================

Validates the consolidated basketball database for data quality and completeness.
"""


def validate_database_quality(db_path="hyper_medusa_consolidated.db"):
    """Validate data quality of consolidated database"""
    
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    
    # Get database info
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = [row[0] for row in cursor.fetchall()]
    
    
    quality_report = {
        'timestamp': datetime.now().isoformat(),
        'database_path': db_path,
        'tables': {},
        'overall_stats': {},
        'quality_score': 0.0
    }
    
    total_records = 0
    total_quality_score = 0
    
    # Analyze each table
    for table in tables:
        
        # Get table info
        df = pd.read_sql_query(f"SELECT * FROM {table} LIMIT 1000", conn)
        total_count = pd.read_sql_query(f"SELECT COUNT(*) as count FROM {table}", conn)['count'].iloc[0]
        
        # Calculate quality metrics
        null_percentage = (df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
        duplicate_count = df.duplicated().sum()
        duplicate_percentage = (duplicate_count / len(df)) * 100 if len(df) > 0 else 0
        
        # Quality score (higher is better)
        completeness_score = max(0, 100 - null_percentage)
        uniqueness_score = max(0, 100 - duplicate_percentage)
        table_quality_score = (completeness_score + uniqueness_score) / 2
        
        table_stats = {
            'total_records': int(total_count),
            'sample_records': int(len(df)),
            'columns': int(len(df.columns)),
            'null_percentage': float(round(null_percentage, 2)),
            'duplicate_count': int(duplicate_count),
            'duplicate_percentage': float(round(duplicate_percentage, 2)),
            'completeness_score': float(round(completeness_score, 2)),
            'uniqueness_score': float(round(uniqueness_score, 2)),
            'quality_score': float(round(table_quality_score, 2)),
            'column_names': [str(col) for col in df.columns]
        }
        
        quality_report['tables'][table] = table_stats
        total_records += total_count
        total_quality_score += table_quality_score
        
    
    # Overall statistics
    overall_quality_score = total_quality_score / len(tables) if tables else 0
    quality_report['overall_stats'] = {
        'total_tables': int(len(tables)),
        'total_records': int(total_records),
        'overall_quality_score': float(round(overall_quality_score, 2))
    }
    quality_report['quality_score'] = float(round(overall_quality_score, 2))
    
    # Generate recommendations
    recommendations = []
    if overall_quality_score < 80:
        recommendations.append("Consider data cleaning to improve overall quality score")
    if any(stats['null_percentage'] > 20 for stats in quality_report['tables'].values()):
        recommendations.append("Some tables have high null percentages - investigate data sources")
    if any(stats['duplicate_percentage'] > 5 for stats in quality_report['tables'].values()):
        recommendations.append("Some tables have high duplicate rates - implement deduplication")
    
    quality_report['recommendations'] = recommendations
    
    # Print summary
    
    if overall_quality_score >= 90:
    elif overall_quality_score >= 80:
    elif overall_quality_score >= 70:
    else:
    
    if recommendations:
        for i, rec in enumerate(recommendations, 1):
    
    # Save detailed report
    report_file = f"data_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(quality_report, f, indent=2)
    
    
    conn.close()
    return quality_report

if __name__ == "__main__":
    # Run validation
    report = validate_database_quality()
    
    # Print final status
