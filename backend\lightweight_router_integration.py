import os
import importlib
import logging
from typing import Dict, List, Any
from fastapi import Fast<PERSON>I

"""
🏀 HYPER MEDUSA NEURAL VAULT - Lightweight Router Integration
============================================================

Lightweight router integration that avoids heavy imports during startup.
Uses lazy loading and dependency injection to prevent initialization cascades.
"""


logger = logging.getLogger("🏀 HYPER_MEDUSA_LIGHTWEIGHT_ROUTER")

class LightweightRouterIntegrator:
    """Lightweight router integration without heavy initialization"""
    
    def __init__(self):
        self.routers_path = os.path.join(os.path.dirname(__file__), "routers")
        self.discovered_routers = {}
        self.expert_routers = {}
        self.regular_routers = {}
        self.integration_stats = {
            "total_discovered": 0,
            "expert_routers": 0,
            "regular_routers": 0,
            "successfully_loaded": 0,
            "failed_imports": 0,
            "total_endpoints": 0
        }
    
    def discover_router_files(self) -> List[str]:
        """Discover router files without importing them"""
        logger.info("🔍 Discovering router files...")
        
        if not os.path.exists(self.routers_path):
            logger.warning(f"Routers path not found: {self.routers_path}")
            return []
        
        router_files = []
        # Skip non-router files
        skip_files = {'props_models', 'models', 'schemas', 'dependencies'}

        for file in os.listdir(self.routers_path):
            if file.endswith('.py') and not file.startswith('__'):
                router_name = file[:-3]  # Remove .py extension
                if router_name not in skip_files:
                    router_files.append(router_name)
        
        logger.info(f"📁 Found {len(router_files)} router files")
        return router_files
    
    def categorize_routers(self, router_files: List[str]) -> Dict[str, List[str]]:
        """Categorize routers into expert and regular without importing"""
        expert_routers = []
        regular_routers = []
        
        for router_name in router_files:
            if "expert" in router_name.lower():
                expert_routers.append(router_name)
            else:
                regular_routers.append(router_name)
        
        logger.info(f"📊 Categorized: {len(expert_routers)} expert, {len(regular_routers)} regular")
        
        return {
            "expert": expert_routers,
            "regular": regular_routers
        }
    
    def identify_duplicates(self, categorized: Dict[str, List[str]]) -> Dict[str, Dict[str, Any]]:
        """Identify duplicate router functionality without importing"""
        duplicates = {}
        
        for expert_name in categorized["expert"]:
            base_name = expert_name.replace("_expert", "").replace("expert_", "")
            
            # Look for corresponding regular router
            regular_candidates = [
                name for name in categorized["regular"]
                if name.replace("_expert", "").replace("expert_", "") == base_name
            ]
            
            if regular_candidates:
                duplicates[base_name] = {
                    "expert": expert_name,
                    "regular": regular_candidates,
                    "consolidation_strategy": "prefer_expert"
                }
                logger.info(f"🔄 Found duplicate pair: {expert_name} <-> {regular_candidates}")
        
        return duplicates
    
    def get_priority_routers(self, categorized: Dict[str, List[str]], duplicates: Dict[str, Dict[str, Any]]) -> List[str]:
        """Get prioritized list of routers to load (expert over regular)"""
        priority_routers = []
        
        # Add all expert routers first
        priority_routers.extend(categorized["expert"])
        
        # Add regular routers that don't have expert equivalents
        for regular_name in categorized["regular"]:
            base_name = regular_name.replace("_expert", "").replace("expert_", "")
            
            # Only add if no expert version exists
            if base_name not in duplicates:
                priority_routers.append(regular_name)
        
        logger.info(f"📋 Priority loading order: {len(priority_routers)} routers")
        return priority_routers
    
    def load_router_safely(self, router_name: str) -> Dict[str, Any]:
        """Safely load a single router with error handling"""
        try:
            module_path = f"backend.routers.{router_name}"
            module = importlib.import_module(module_path)
            
            if hasattr(module, 'router'):
                router = module.router
                endpoint_count = len(router.routes) if hasattr(router, 'routes') else 0
                
                return {
                    "success": True,
                    "module": module,
                    "router": router,
                    "endpoints": endpoint_count,
                    "error": None
                }
            else:
                return {
                    "success": False,
                    "module": None,
                    "router": None,
                    "endpoints": 0,
                    "error": f"No 'router' attribute found in {router_name}"
                }
                
        except ImportError as e:
            return {
                "success": False,
                "module": None,
                "router": None,
                "endpoints": 0,
                "error": f"Import error: {e}"
            }
        except Exception as e:
            return {
                "success": False,
                "module": None,
                "router": None,
                "endpoints": 0,
                "error": f"Unexpected error: {e}"
            }
    
    def integrate_routers(self, app: FastAPI) -> Dict[str, Any]:
        """Integrate routers into FastAPI app with lightweight approach"""
        logger.info("🚀 Starting lightweight router integration...")
        
        # Step 1: Discover router files
        router_files = self.discover_router_files()
        
        # Step 2: Categorize routers
        categorized = self.categorize_routers(router_files)
        
        # Step 3: Identify duplicates
        duplicates = self.identify_duplicates(categorized)
        
        # Step 4: Get priority loading order
        priority_routers = self.get_priority_routers(categorized, duplicates)
        
        # Step 5: Load routers one by one with error handling
        successful_routers = []
        failed_routers = []
        total_endpoints = 0
        
        for router_name in priority_routers:
            logger.info(f"📦 Loading router: {router_name}")
            result = self.load_router_safely(router_name)
            
            if result["success"]:
                app.include_router(result["router"])
                successful_routers.append({
                    "name": router_name,
                    "endpoints": result["endpoints"]
                })
                total_endpoints += result["endpoints"]
                logger.info(f"✅ Successfully loaded {router_name} ({result['endpoints']} endpoints)")
            else:
                failed_routers.append({
                    "name": router_name,
                    "error": result["error"]
                })
                logger.warning(f"⚠️ Failed to load {router_name}: {result['error']}")
        
        # Update stats
        self.integration_stats.update({
            "total_discovered": len(router_files),
            "expert_routers": len(categorized["expert"]),
            "regular_routers": len(categorized["regular"]),
            "successfully_loaded": len(successful_routers),
            "failed_imports": len(failed_routers),
            "total_endpoints": total_endpoints
        })
        
        logger.info(f"🎉 Router integration complete: {len(successful_routers)}/{len(priority_routers)} routers loaded")
        logger.info(f"📊 Total endpoints registered: {total_endpoints}")
        
        return {
            "integration_stats": self.integration_stats,
            "successful_routers": successful_routers,
            "failed_routers": failed_routers,
            "duplicates_eliminated": duplicates,
            "total_endpoints": total_endpoints
        }

def integrate_routers_lightweight(app: FastAPI) -> Dict[str, Any]:
    """Main function to integrate routers using lightweight approach"""
    integrator = LightweightRouterIntegrator()
    return integrator.integrate_routers(app)
