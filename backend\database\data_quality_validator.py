import logging
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone
from dataclasses import dataclass, field
from sqlalchemy import create_engine, text, func
from sqlalchemy.orm import sessionmaker
import json
from pathlib import Path
from backend.database.models import Base, TeamModel, PlayerModel, GameModel, PlayerGameStatModel
from kingdom.config.unified_config_system import UnifiedConfigSystem


"""
Data Quality Validation System
=============================

Production-grade data quality validation for HYPER MEDUSA NEURAL VAULT
- Validates data completeness and accuracy
- Checks for duplicates and inconsistencies
- Generates quality reports and metrics
- Implements automated data cleaning
- Provides recommendations for data improvement
"""



logger = logging.getLogger(__name__)

@dataclass
class QualityMetric:
    """Data quality metric result"""
    metric_name: str
    table_name: str
    value: float
    threshold: float
    passed: bool
    details: Dict[str, Any] = field(default_factory=dict)
    recommendations: List[str] = field(default_factory=list)

@dataclass
class QualityReport:
    """Comprehensive data quality report"""
    timestamp: datetime
    overall_score: float
    metrics: List[QualityMetric]
    summary: Dict[str, Any]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'timestamp': self.timestamp.isoformat(),
            'overall_score': self.overall_score,
            'metrics': [
                {
                    'metric_name': m.metric_name,
                    'table_name': m.table_name,
                    'value': m.value,
                    'threshold': m.threshold,
                    'passed': m.passed,
                    'details': m.details,
                    'recommendations': m.recommendations
                }
                for m in self.metrics
            ],
            'summary': self.summary,
            'recommendations': self.recommendations
        }

class DataQualityValidator:
    """Expert-level data quality validation system"""
    
    def __init__(self, config: Optional[ProductionConfig] = None):
        self.config = config or ProductionConfig()
        self.engine = None
        self.session = None
        
        # Quality thresholds
        self.thresholds = {
            'completeness': 0.95,  # 95% completeness required
            'uniqueness': 0.99,    # 99% uniqueness required
            'validity': 0.98,      # 98% validity required
            'consistency': 0.97,   # 97% consistency required
            'accuracy': 0.95,      # 95% accuracy required
        }
    
    def initialize_connection(self):
        """Initialize database connection"""
        try:
            if self.config.DATABASE_URL_PROD:
                sync_url = self.config.DATABASE_URL_PROD.replace("postgresql+asyncpg://", "postgresql://")
                self.engine = create_engine(sync_url, echo=False)
            else:
                self.engine = create_engine("sqlite:///hyper_medusa_production.db", echo=False)
                
            Session = sessionmaker(bind=self.engine)
            self.session = Session()
            
            logger.info("Database connection initialized for quality validation")
            
        except Exception as e:
            logger.error(f"Failed to initialize database connection: {e}")
            raise
    
    def validate_all_tables(self) -> QualityReport:
        """Run comprehensive quality validation on all tables"""
        logger.info("Starting comprehensive data quality validation...")
        
        metrics = []
        
        # Validate each table
        table_validators = [
            ('teams', self._validate_teams),
            ('players', self._validate_players),
            ('games', self._validate_games),
            ('player_game_stats', self._validate_player_stats),
        ]
        
        for table_name, validator_func in table_validators:
            try:
                table_metrics = validator_func()
                metrics.extend(table_metrics)
                logger.info(f"✅ Validated {table_name} table")
            except Exception as e:
                logger.error(f"❌ Failed to validate {table_name}: {e}")
                # Add error metric
                error_metric = QualityMetric(
                    metric_name="validation_error",
                    table_name=table_name,
                    value=0.0,
                    threshold=1.0,
                    passed=False,
                    details={"error": str(e)},
                    recommendations=[f"Fix validation error in {table_name} table"]
                )
                metrics.append(error_metric)
        
        # Calculate overall score
        passed_metrics = [m for m in metrics if m.passed]
        overall_score = len(passed_metrics) / len(metrics) if metrics else 0.0
        
        # Generate summary
        summary = self._generate_summary(metrics)
        
        # Generate recommendations
        recommendations = self._generate_recommendations(metrics)
        
        report = QualityReport(
            timestamp=datetime.now(timezone.utc),
            overall_score=overall_score,
            metrics=metrics,
            summary=summary,
            recommendations=recommendations
        )
        
        logger.info(f"Quality validation completed. Overall score: {overall_score:.2%}")
        return report
    
    def _validate_teams(self) -> List[QualityMetric]:
        """Validate teams table data quality"""
        metrics = []
        
        # Completeness check
        total_teams = self.session.query(func.count(TeamModel.id)).scalar()
        complete_teams = self.session.query(func.count(TeamModel.id)).filter(
            TeamModel.name.isnot(None),
            TeamModel.abbreviation.isnot(None),
            TeamModel.league.isnot(None)
        ).scalar()
        
        completeness = complete_teams / total_teams if total_teams > 0 else 0
        metrics.append(QualityMetric(
            metric_name="completeness",
            table_name="teams",
            value=completeness,
            threshold=self.thresholds['completeness'],
            passed=completeness >= self.thresholds['completeness'],
            details={"total_teams": total_teams, "complete_teams": complete_teams},
            recommendations=["Fill missing team information"] if completeness < self.thresholds['completeness'] else []
        ))
        
        # Uniqueness check
        unique_teams = self.session.query(func.count(func.distinct(TeamModel.id))).scalar()
        uniqueness = unique_teams / total_teams if total_teams > 0 else 0
        metrics.append(QualityMetric(
            metric_name="uniqueness",
            table_name="teams",
            value=uniqueness,
            threshold=self.thresholds['uniqueness'],
            passed=uniqueness >= self.thresholds['uniqueness'],
            details={"total_teams": total_teams, "unique_teams": unique_teams},
            recommendations=["Remove duplicate team records"] if uniqueness < self.thresholds['uniqueness'] else []
        ))
        
        # League consistency check
        valid_leagues = ['NBA', 'WNBA']
        invalid_leagues = self.session.query(func.count(TeamModel.id)).filter(
            ~TeamModel.league.in_(valid_leagues)
        ).scalar()
        
        league_consistency = (total_teams - invalid_leagues) / total_teams if total_teams > 0 else 0
        metrics.append(QualityMetric(
            metric_name="league_consistency",
            table_name="teams",
            value=league_consistency,
            threshold=self.thresholds['consistency'],
            passed=league_consistency >= self.thresholds['consistency'],
            details={"total_teams": total_teams, "invalid_leagues": invalid_leagues},
            recommendations=["Fix invalid league values"] if league_consistency < self.thresholds['consistency'] else []
        ))
        
        return metrics
    
    def _validate_players(self) -> List[QualityMetric]:
        """Validate players table data quality"""
        metrics = []
        
        # Completeness check
        total_players = self.session.query(func.count(PlayerModel.id)).scalar()
        complete_players = self.session.query(func.count(PlayerModel.id)).filter(
            PlayerModel.full_name.isnot(None),
            PlayerModel.league.isnot(None),
            PlayerModel.position.isnot(None)
        ).scalar()
        
        completeness = complete_players / total_players if total_players > 0 else 0
        metrics.append(QualityMetric(
            metric_name="completeness",
            table_name="players",
            value=completeness,
            threshold=self.thresholds['completeness'],
            passed=completeness >= self.thresholds['completeness'],
            details={"total_players": total_players, "complete_players": complete_players},
            recommendations=["Fill missing player information"] if completeness < self.thresholds['completeness'] else []
        ))
        
        # Uniqueness check
        unique_players = self.session.query(func.count(func.distinct(PlayerModel.id))).scalar()
        uniqueness = unique_players / total_players if total_players > 0 else 0
        metrics.append(QualityMetric(
            metric_name="uniqueness",
            table_name="players",
            value=uniqueness,
            threshold=self.thresholds['uniqueness'],
            passed=uniqueness >= self.thresholds['uniqueness'],
            details={"total_players": total_players, "unique_players": unique_players},
            recommendations=["Remove duplicate player records"] if uniqueness < self.thresholds['uniqueness'] else []
        ))
        
        # Statistical validity check (reasonable stat ranges)
        invalid_stats = self.session.query(func.count(PlayerModel.id)).filter(
            (PlayerModel.points_per_game < 0) | (PlayerModel.points_per_game > 50) |
            (PlayerModel.rebounds_per_game < 0) | (PlayerModel.rebounds_per_game > 25) |
            (PlayerModel.assists_per_game < 0) | (PlayerModel.assists_per_game > 15)
        ).scalar()
        
        stat_validity = (total_players - invalid_stats) / total_players if total_players > 0 else 0
        metrics.append(QualityMetric(
            metric_name="statistical_validity",
            table_name="players",
            value=stat_validity,
            threshold=self.thresholds['validity'],
            passed=stat_validity >= self.thresholds['validity'],
            details={"total_players": total_players, "invalid_stats": invalid_stats},
            recommendations=["Review and correct unrealistic player statistics"] if stat_validity < self.thresholds['validity'] else []
        ))
        
        return metrics

    def _validate_games(self) -> List[QualityMetric]:
        """Validate games table data quality"""
        metrics = []

        # Completeness check
        total_games = self.session.query(func.count(GameModel.id)).scalar()
        complete_games = self.session.query(func.count(GameModel.id)).filter(
            GameModel.date.isnot(None),
            GameModel.home_team_id.isnot(None),
            GameModel.away_team_id.isnot(None),
            GameModel.league.isnot(None)
        ).scalar()

        completeness = complete_games / total_games if total_games > 0 else 0
        metrics.append(QualityMetric(
            metric_name="completeness",
            table_name="games",
            value=completeness,
            threshold=self.thresholds['completeness'],
            passed=completeness >= self.thresholds['completeness'],
            details={"total_games": total_games, "complete_games": complete_games},
            recommendations=["Fill missing game information"] if completeness < self.thresholds['completeness'] else []
        ))

        # Score validity check
        invalid_scores = self.session.query(func.count(GameModel.id)).filter(
            (GameModel.home_score < 0) | (GameModel.home_score > 200) |
            (GameModel.away_score < 0) | (GameModel.away_score > 200)
        ).scalar()

        score_validity = (total_games - invalid_scores) / total_games if total_games > 0 else 0
        metrics.append(QualityMetric(
            metric_name="score_validity",
            table_name="games",
            value=score_validity,
            threshold=self.thresholds['validity'],
            passed=score_validity >= self.thresholds['validity'],
            details={"total_games": total_games, "invalid_scores": invalid_scores},
            recommendations=["Review and correct unrealistic game scores"] if score_validity < self.thresholds['validity'] else []
        ))

        # Team consistency check (home != away)
        same_team_games = self.session.query(func.count(GameModel.id)).filter(
            GameModel.home_team_id == GameModel.away_team_id
        ).scalar()

        team_consistency = (total_games - same_team_games) / total_games if total_games > 0 else 0
        metrics.append(QualityMetric(
            metric_name="team_consistency",
            table_name="games",
            value=team_consistency,
            threshold=self.thresholds['consistency'],
            passed=team_consistency >= self.thresholds['consistency'],
            details={"total_games": total_games, "same_team_games": same_team_games},
            recommendations=["Fix games where home and away teams are the same"] if team_consistency < self.thresholds['consistency'] else []
        ))

        return metrics

    def _validate_player_stats(self) -> List[QualityMetric]:
        """Validate player game stats table data quality"""
        metrics = []

        # Completeness check
        total_stats = self.session.query(func.count(PlayerGameStatModel.id)).scalar()
        complete_stats = self.session.query(func.count(PlayerGameStatModel.id)).filter(
            PlayerGameStatModel.hero_id.isnot(None),
            PlayerGameStatModel.titan_clash_id.isnot(None),
            PlayerGameStatModel.points.isnot(None)
        ).scalar()

        completeness = complete_stats / total_stats if total_stats > 0 else 0
        metrics.append(QualityMetric(
            metric_name="completeness",
            table_name="player_game_stats",
            value=completeness,
            threshold=self.thresholds['completeness'],
            passed=completeness >= self.thresholds['completeness'],
            details={"total_stats": total_stats, "complete_stats": complete_stats},
            recommendations=["Fill missing player game stat information"] if completeness < self.thresholds['completeness'] else []
        ))

        # Statistical validity check
        invalid_stats = self.session.query(func.count(PlayerGameStatModel.id)).filter(
            (PlayerGameStatModel.points < 0) | (PlayerGameStatModel.points > 100) |
            (PlayerGameStatModel.total_rebounds < 0) | (PlayerGameStatModel.total_rebounds > 30) |
            (PlayerGameStatModel.assists < 0) | (PlayerGameStatModel.assists > 25) |
            (PlayerGameStatModel.field_goal_percentage < 0) | (PlayerGameStatModel.field_goal_percentage > 1)
        ).scalar()

        stat_validity = (total_stats - invalid_stats) / total_stats if total_stats > 0 else 0
        metrics.append(QualityMetric(
            metric_name="statistical_validity",
            table_name="player_game_stats",
            value=stat_validity,
            threshold=self.thresholds['validity'],
            passed=stat_validity >= self.thresholds['validity'],
            details={"total_stats": total_stats, "invalid_stats": invalid_stats},
            recommendations=["Review and correct unrealistic player game statistics"] if stat_validity < self.thresholds['validity'] else []
        ))

        return metrics

    def _generate_summary(self, metrics: List[QualityMetric]) -> Dict[str, Any]:
        """Generate quality summary statistics"""
        total_metrics = len(metrics)
        passed_metrics = len([m for m in metrics if m.passed])

        # Group by table
        table_summary = {}
        for metric in metrics:
            if metric.table_name not in table_summary:
                table_summary[metric.table_name] = {'total': 0, 'passed': 0}
            table_summary[metric.table_name]['total'] += 1
            if metric.passed:
                table_summary[metric.table_name]['passed'] += 1

        # Calculate table scores
        for table in table_summary:
            table_summary[table]['score'] = table_summary[table]['passed'] / table_summary[table]['total']

        return {
            'total_metrics': total_metrics,
            'passed_metrics': passed_metrics,
            'failed_metrics': total_metrics - passed_metrics,
            'overall_pass_rate': passed_metrics / total_metrics if total_metrics > 0 else 0,
            'table_summary': table_summary
        }

    def _generate_recommendations(self, metrics: List[QualityMetric]) -> List[str]:
        """Generate overall recommendations based on failed metrics"""
        recommendations = []

        # Collect all recommendations from failed metrics
        for metric in metrics:
            if not metric.passed and metric.recommendations:
                recommendations.extend(metric.recommendations)

        # Add general recommendations
        failed_metrics = [m for m in metrics if not m.passed]
        if failed_metrics:
            recommendations.append("Run data cleaning procedures to improve quality scores")
            recommendations.append("Implement automated data validation in ingestion pipeline")
            recommendations.append("Set up monitoring alerts for data quality degradation")

        # Remove duplicates while preserving order
        seen = set()
        unique_recommendations = []
        for rec in recommendations:
            if rec not in seen:
                seen.add(rec)
                unique_recommendations.append(rec)

        return unique_recommendations

    def save_report(self, report: QualityReport, filename: Optional[str] = None) -> str:
        """Save quality report to file"""
        if not filename:
            timestamp = report.timestamp.strftime('%Y%m%d_%H%M%S')
            filename = f"data_quality_report_{timestamp}.json"

        filepath = Path("logs") / filename
        filepath.parent.mkdir(exist_ok=True)

        with open(filepath, 'w') as f:
            json.dump(report.to_dict(), f, indent=2)

        logger.info(f"Quality report saved to {filepath}")
        return str(filepath)

    def print_report(self, report: QualityReport):
        """Print formatted quality report to console"""

        for table, summary in report.summary['table_summary'].items():
            score = summary['score']
            status = "✅" if score >= 0.95 else "⚠️" if score >= 0.80 else "❌"

        failed_metrics = [m for m in report.metrics if not m.passed]
        if failed_metrics:
            for metric in failed_metrics:
                if metric.details:
                if metric.recommendations:
                    for rec in metric.recommendations:
        else:

        if report.recommendations:
            for i, rec in enumerate(report.recommendations, 1):
        else:



def run_quality_validation():
    """Run data quality validation and generate report"""
    validator = DataQualityValidator()

    try:
        validator.initialize_connection()
        report = validator.validate_all_tables()

        # Print report to console
        validator.print_report(report)

        # Save report to file
        filepath = validator.save_report(report)

        return report

    except Exception as e:
        logger.error(f"Quality validation failed: {e}")
        raise
    finally:
        if validator.session:
            validator.session.close()

if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    run_quality_validation()
