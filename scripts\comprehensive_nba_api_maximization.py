import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any, Set, Tuple
import requests
import time
import json
from datetime import datetime
import os
from collections import defaultdict

#!/usr/bin/env python3
"""
Comprehensive NBA API Data Maximization Strategy
Systematic approach to collect ALL available data from NBA API endpoints
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveNBAAPIMaximizer:
    """Maximize data collection from ALL available NBA API endpoints"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }
        
        # Comprehensive NBA API endpoint catalog (100+ endpoints)
        self.nba_api_endpoints = self._build_comprehensive_endpoint_catalog()
        
        # Track what we have vs what we need
        self.existing_data_analysis = self._analyze_existing_data()
        
    def _build_comprehensive_endpoint_catalog(self) -> Dict[str, Dict]:
        """Build comprehensive catalog of ALL NBA API endpoints"""
        
        return {
            # TIER 1: CRITICAL PLAYER ENDPOINTS (High Priority)
            'playerdashboardbygeneralsplits': {
                'category': 'player_core',
                'priority': 'CRITICAL',
                'description': 'Player general performance splits',
                'requires_player_id': True,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'playerdashboardbyshootingsplits': {
                'category': 'player_shooting',
                'priority': 'CRITICAL', 
                'description': 'Player shooting splits by location/situation',
                'requires_player_id': True,
                'requires_season': True,
                'success_rate': 'MEDIUM'
            },
            'playerdashboardbyclutch': {
                'category': 'player_clutch',
                'priority': 'CRITICAL',
                'description': 'Player clutch performance stats',
                'requires_player_id': True,
                'requires_season': True,
                'success_rate': 'LOW'
            },
            'playerdashptshots': {
                'category': 'player_tracking',
                'priority': 'CRITICAL',
                'description': 'Player shot tracking data',
                'requires_player_id': True,
                'requires_season': True,
                'success_rate': 'MEDIUM'
            },
            'playerdashptpass': {
                'category': 'player_tracking',
                'priority': 'CRITICAL',
                'description': 'Player passing tracking data',
                'requires_player_id': True,
                'requires_season': True,
                'success_rate': 'MEDIUM'
            },
            'playerdashptreb': {
                'category': 'player_tracking',
                'priority': 'CRITICAL',
                'description': 'Player rebounding tracking data',
                'requires_player_id': True,
                'requires_season': True,
                'success_rate': 'MEDIUM'
            },
            
            # TIER 2: LEAGUE-WIDE ENDPOINTS (High Success Rate)
            'leaguedashplayerstats': {
                'category': 'league_stats',
                'priority': 'HIGH',
                'description': 'League-wide player statistics',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'leaguedashteamstats': {
                'category': 'league_stats',
                'priority': 'HIGH',
                'description': 'League-wide team statistics',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'leaguedashplayerclutch': {
                'category': 'league_clutch',
                'priority': 'HIGH',
                'description': 'League-wide player clutch stats',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'leaguedashplayershotlocations': {
                'category': 'league_shooting',
                'priority': 'HIGH',
                'description': 'League player shot locations',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'leaguedashteamclutch': {
                'category': 'league_clutch',
                'priority': 'HIGH',
                'description': 'League-wide team clutch stats',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'leaguedashlineups': {
                'category': 'league_lineups',
                'priority': 'HIGH',
                'description': 'League lineup statistics',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'leagueleaders': {
                'category': 'league_leaders',
                'priority': 'HIGH',
                'description': 'League statistical leaders',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            
            # TIER 3: TEAM ENDPOINTS (Medium Priority)
            'teamdashboardbygeneralsplits': {
                'category': 'team_core',
                'priority': 'MEDIUM',
                'description': 'Team general performance splits',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'teamdashboardbyshootingsplits': {
                'category': 'team_shooting',
                'priority': 'MEDIUM',
                'description': 'Team shooting splits',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'teamdashboardbyclutch': {
                'category': 'team_clutch',
                'priority': 'MEDIUM',
                'description': 'Team clutch performance',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'teamplayerdashboard': {
                'category': 'team_players',
                'priority': 'MEDIUM',
                'description': 'Team player dashboard',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            'teamgamelog': {
                'category': 'team_games',
                'priority': 'MEDIUM',
                'description': 'Team game logs',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'HIGH'
            },
            
            # TIER 4: ADVANCED ANALYTICS (Medium Priority)
            'leaguehustlestatsplayer': {
                'category': 'advanced_hustle',
                'priority': 'MEDIUM',
                'description': 'League hustle stats for players',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'MEDIUM'
            },
            'leaguehustlestatsteam': {
                'category': 'advanced_hustle',
                'priority': 'MEDIUM',
                'description': 'League hustle stats for teams',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'MEDIUM'
            },
            'leaguedashptstats': {
                'category': 'advanced_tracking',
                'priority': 'MEDIUM',
                'description': 'League player tracking stats',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'MEDIUM'
            },
            'leaguedashptdefend': {
                'category': 'advanced_defense',
                'priority': 'MEDIUM',
                'description': 'League defense tracking',
                'requires_player_id': False,
                'requires_season': True,
                'success_rate': 'MEDIUM'
            },
            
            # TIER 5: GAME-SPECIFIC ENDPOINTS (Lower Priority, High Volume)
            'boxscoretraditionalv2': {
                'category': 'game_boxscore',
                'priority': 'LOW',
                'description': 'Traditional box scores',
                'requires_player_id': False,
                'requires_season': False,
                'success_rate': 'HIGH'
            },
            'boxscoreadvancedv2': {
                'category': 'game_boxscore',
                'priority': 'LOW',
                'description': 'Advanced box scores',
                'requires_player_id': False,
                'requires_season': False,
                'success_rate': 'MEDIUM'
            },
            'boxscoreplayertrackv2': {
                'category': 'game_tracking',
                'priority': 'LOW',
                'description': 'Player tracking box scores',
                'requires_player_id': False,
                'requires_season': False,
                'success_rate': 'MEDIUM'
            },
            'shotchartdetail': {
                'category': 'shot_data',
                'priority': 'MEDIUM',
                'description': 'Shot chart details',
                'requires_player_id': True,
                'requires_season': True,
                'success_rate': 'LOW'
            }
        }
    
    def _analyze_existing_data(self) -> Dict[str, Any]:
        """Analyze what data we currently have"""
        logger.info("📊 ANALYZING EXISTING DATA COVERAGE")
        
        conn = sqlite3.connect(self.db_path)
        
        # Get endpoint coverage
        endpoint_query = """
        SELECT 
            source_table,
            league_name,
            COUNT(*) as records,
            COUNT(DISTINCT player_name) as players,
            COUNT(DISTINCT season) as seasons,
            MIN(season) as first_season,
            MAX(season) as last_season
        FROM unified_nba_wnba_data
        WHERE source_table IS NOT NULL
        GROUP BY source_table, league_name
        ORDER BY league_name, records DESC
        """
        
        existing_endpoints = pd.read_sql_query(endpoint_query, conn)
        
        # Get season coverage
        season_query = """
        SELECT 
            league_name,
            season,
            COUNT(DISTINCT source_table) as endpoints,
            COUNT(*) as total_records
        FROM unified_nba_wnba_data
        WHERE league_name IN ('NBA', 'WNBA')
        GROUP BY league_name, season
        ORDER BY league_name, season DESC
        """
        
        season_coverage = pd.read_sql_query(season_query, conn)
        
        conn.close()
        
        # Calculate coverage statistics
        existing_endpoint_names = set(existing_endpoints['source_table'].unique())
        total_available_endpoints = len(self.nba_api_endpoints)
        coverage_percentage = (len(existing_endpoint_names) / total_available_endpoints) * 100
        
        analysis = {
            'existing_endpoints': existing_endpoints.to_dict('records'),
            'season_coverage': season_coverage.to_dict('records'),
            'existing_endpoint_names': existing_endpoint_names,
            'total_available_endpoints': total_available_endpoints,
            'coverage_percentage': coverage_percentage,
            'missing_endpoints': set(self.nba_api_endpoints.keys()) - existing_endpoint_names
        }
        
        logger.info(f"✅ EXISTING DATA ANALYSIS COMPLETE:")
        logger.info(f"   Endpoint coverage: {coverage_percentage:.1f}% ({len(existing_endpoint_names)}/{total_available_endpoints})")
        logger.info(f"   Missing endpoints: {len(analysis['missing_endpoints'])}")
        
        return analysis
    
    def create_prioritized_collection_plan(self) -> Dict[str, Any]:
        """Create prioritized plan for maximum data collection"""
        logger.info("🎯 CREATING PRIORITIZED COLLECTION PLAN")
        logger.info("=" * 45)
        
        missing_endpoints = self.existing_data_analysis['missing_endpoints']
        
        # Organize by priority and success rate
        collection_phases = {
            'Phase 1 - High Success League Endpoints': {
                'priority': 'IMMEDIATE',
                'endpoints': [],
                'estimated_success_rate': 90,
                'estimated_records': 500000,
                'description': 'League-wide endpoints with high success rates'
            },
            'Phase 2 - Core Player Endpoints': {
                'priority': 'HIGH',
                'endpoints': [],
                'estimated_success_rate': 70,
                'estimated_records': 300000,
                'description': 'Essential player performance endpoints'
            },
            'Phase 3 - Team Analytics': {
                'priority': 'MEDIUM',
                'endpoints': [],
                'estimated_success_rate': 85,
                'estimated_records': 200000,
                'description': 'Team-level analytics and performance'
            },
            'Phase 4 - Advanced Tracking': {
                'priority': 'MEDIUM',
                'endpoints': [],
                'estimated_success_rate': 60,
                'estimated_records': 150000,
                'description': 'Advanced player and team tracking data'
            },
            'Phase 5 - Game-Level Data': {
                'priority': 'LOW',
                'endpoints': [],
                'estimated_success_rate': 75,
                'estimated_records': 1000000,
                'description': 'Individual game box scores and details'
            }
        }
        
        # Categorize missing endpoints
        for endpoint in missing_endpoints:
            if endpoint in self.nba_api_endpoints:
                endpoint_info = self.nba_api_endpoints[endpoint]
                category = endpoint_info['category']
                success_rate = endpoint_info['success_rate']
                
                if category.startswith('league_') and success_rate == 'HIGH':
                    collection_phases['Phase 1 - High Success League Endpoints']['endpoints'].append(endpoint)
                elif category.startswith('player_') and endpoint_info['priority'] == 'CRITICAL':
                    collection_phases['Phase 2 - Core Player Endpoints']['endpoints'].append(endpoint)
                elif category.startswith('team_'):
                    collection_phases['Phase 3 - Team Analytics']['endpoints'].append(endpoint)
                elif category.startswith('advanced_'):
                    collection_phases['Phase 4 - Advanced Tracking']['endpoints'].append(endpoint)
                elif category.startswith('game_'):
                    collection_phases['Phase 5 - Game-Level Data']['endpoints'].append(endpoint)
        
        # Calculate total potential
        total_estimated_records = sum(phase['estimated_records'] for phase in collection_phases.values())
        
        logger.info("📋 COLLECTION PHASES:")
        for phase_name, phase_info in collection_phases.items():
            logger.info(f"\n   {phase_name} ({phase_info['priority']} Priority):")
            logger.info(f"     Endpoints: {len(phase_info['endpoints'])}")
            logger.info(f"     Success rate: {phase_info['estimated_success_rate']}%")
            logger.info(f"     Estimated records: {phase_info['estimated_records']:,}")
            logger.info(f"     Description: {phase_info['description']}")
        
        logger.info(f"\n🎯 TOTAL COLLECTION POTENTIAL:")
        logger.info(f"   Total missing endpoints: {len(missing_endpoints)}")
        logger.info(f"   Total estimated new records: {total_estimated_records:,}")
        logger.info(f"   Current database: ~3.9M records")
        logger.info(f"   Projected total: ~{(3900000 + total_estimated_records) / 1000000:.1f}M records")
        
        return {
            'collection_phases': collection_phases,
            'missing_endpoints': missing_endpoints,
            'total_estimated_records': total_estimated_records,
            'existing_analysis': self.existing_data_analysis
        }
    
    def get_optimal_collection_strategy(self) -> Dict[str, Any]:
        """Get optimal strategy based on success rates and data value"""
        
        plan = self.create_prioritized_collection_plan()
        
        # Recommend immediate actions
        immediate_actions = []
        
        # Phase 1: High-success league endpoints
        phase1_endpoints = plan['collection_phases']['Phase 1 - High Success League Endpoints']['endpoints']
        if phase1_endpoints:
            immediate_actions.append({
                'action': 'Execute Phase 1 - League Endpoints',
                'endpoints': phase1_endpoints,
                'priority': 'IMMEDIATE',
                'success_probability': 90,
                'estimated_records': 500000,
                'rationale': 'High success rate, league-wide coverage, immediate impact'
            })
        
        # Alternative approach for failed player endpoints
        if 'playerdashboardbyclutch' in plan['missing_endpoints']:
            immediate_actions.append({
                'action': 'Alternative Player Data Collection',
                'endpoints': ['leaguedashplayerstats', 'leaguedashplayerclutch'],
                'priority': 'HIGH',
                'success_probability': 95,
                'estimated_records': 100000,
                'rationale': 'Collect player data through league endpoints instead of individual player calls'
            })
        
        # Season-by-season approach
        seasons_to_prioritize = ['2024-25', '2023-24', '2022-23', '2021-22', '2020-21']
        
        strategy = {
            'immediate_actions': immediate_actions,
            'collection_plan': plan,
            'recommended_approach': 'League-first, then player-specific',
            'seasons_to_prioritize': seasons_to_prioritize,
            'success_optimization': {
                'start_with_league_endpoints': True,
                'avoid_individual_player_calls': True,
                'focus_on_recent_seasons': True,
                'batch_process': True
            }
        }
        
        return strategy

def main():
    """Execute comprehensive NBA API maximization analysis"""
    
    maximizer = ComprehensiveNBAAPIMaximizer()
    
    # Get optimal strategy
    strategy = maximizer.get_optimal_collection_strategy()
    
    existing_analysis = strategy['collection_plan']['existing_analysis']
    
    for action in strategy['immediate_actions']:
    
    total_records = strategy['collection_plan']['total_estimated_records']
    
    
    return strategy

if __name__ == "__main__":
    main()
