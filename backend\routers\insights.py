from datetime import datetime

﻿from fastapi import APIRouter

"""
DEPRECATED: This insights router has been consolidated into unified_analytics.py

All insights functionality has been moved to the unified analytics service.
Please use /api/v1/analytics/unified instead.
"""

router = APIRouter(prefix="/insights", tags=["insights - DEPRECATED"])

@router.get("/")
async def deprecated_notice():
    return {
        "status": "deprecated",
        "message": "This insights router has been deprecated and consolidated into unified_analytics.py",
        "redirect_to": "/api/v1/analytics/unified",
        "migration_guide": {
            "trend_analysis": "/api/v1/analytics/unified (with analytics_type=GAME_INSIGHTS)",
            "performance_metrics": "/api/v1/analytics/performance/monitoring",
            "market_intelligence": "/api/v1/analytics/unified (with analytics_type=TEAM_ANALYTICS)",
            "player_analytics": "/api/v1/analytics/player/{player_id}"
        },
        "timestamp": datetime.now()
    }
