#!/usr/bin/env python3
"""
Clean Training Data Creator for HYPER_MEDUSA_NEURAL_VAULT

This script creates properly filtered training datasets that:
1. Only include official NBA (30) and WNBA (13) teams = 43 teams total
2. Separate NBA and WNBA data using league identifiers
3. Remove all international, college, and contaminated data
4. Utilize the full comprehensive dataset with proper team filtering

Key Features:
- Official team validation for NBA and WNBA
- League-specific filtering using file patterns and league identifiers
- Comprehensive data aggregation from all valid CSV files
- Proper team separation to prevent NBA/WNBA mixing
"""

import os
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import List, Dict, Tuple, Set
import glob
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CleanTrainingDataCreator:
    """Creates clean, filtered training datasets with official teams only"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.output_dir = Path("data/ml_training")
        self.output_dir.mkdir(exist_ok=True)
        
        # Official NBA teams (30 teams)
        self.NBA_TEAMS = {
            'ATL', 'BOS', 'BKN', 'CHA', 'CHI', 'CLE', 'DAL', 'DEN', 'DET', 'GSW',
            'HOU', 'IND', 'LAC', 'LAL', 'MEM', 'MIA', 'MIL', 'MIN', 'NOP', 'NYK',
            'OKC', 'ORL', 'PHI', 'PHX', 'POR', 'SAC', 'SAS', 'TOR', 'UTA', 'WAS'
        }
        
        # Official WNBA teams (13 teams)
        self.WNBA_TEAMS = {
            'ATL', 'CHI', 'CON', 'DAL', 'GSV', 'IND', 'LAS', 'LVA', 'MIN', 'NYL', 'PHX', 'SEA', 'WAS'
        }
        
        # Track statistics
        self.stats = {
            'nba_files': 0,
            'wnba_files': 0,
            'nba_records_raw': 0,
            'wnba_records_raw': 0,
            'nba_records_filtered': 0,
            'wnba_records_filtered': 0,
            'nba_teams_found': set(),
            'wnba_teams_found': set(),
            'contaminated_teams_removed': set()
        }
    
    def identify_league_from_file(self, csv_file: Path) -> str:
        """Identify league from filename patterns"""
        filename = csv_file.name.lower()
        
        # NBA patterns (league_id 00)
        if "_00_" in filename or filename.endswith("_00.csv"):
            return "NBA"
        # WNBA patterns (league_id 10)
        elif "_10_" in filename or filename.endswith("_10.csv") or "wnba_" in filename:
            return "WNBA"
        # Player stats with league identifiers
        elif "player_stats_" in filename:
            if "_00_" in filename:
                return "NBA"
            elif "_10_" in filename:
                return "WNBA"
        
        return "UNKNOWN"
    
    def filter_official_teams(self, df: pd.DataFrame, league: str) -> pd.DataFrame:
        """Filter dataframe to only include official NBA or WNBA teams"""
        if df.empty:
            return df
        
        # Find team abbreviation column
        team_cols = ['TEAM_ABBREVIATION', 'team_abbreviation', 'TEAM_ABBREV', 'team_abbrev']
        team_col = None
        for col in team_cols:
            if col in df.columns:
                team_col = col
                break
        
        if not team_col:
            logger.warning(f"No team column found in dataframe")
            return pd.DataFrame()
        
        # Standardize column name
        if team_col != 'TEAM_ABBREVIATION':
            df = df.rename(columns={team_col: 'TEAM_ABBREVIATION'})
        
        # Get initial team count
        initial_teams = set(df['TEAM_ABBREVIATION'].dropna().unique())
        initial_count = len(df)
        
        # Filter by official teams
        if league == "NBA":
            valid_teams = self.NBA_TEAMS
            filtered_df = df[df['TEAM_ABBREVIATION'].isin(valid_teams)].copy()
            self.stats['nba_teams_found'].update(filtered_df['TEAM_ABBREVIATION'].unique())
        elif league == "WNBA":
            valid_teams = self.WNBA_TEAMS
            filtered_df = df[df['TEAM_ABBREVIATION'].isin(valid_teams)].copy()
            self.stats['wnba_teams_found'].update(filtered_df['TEAM_ABBREVIATION'].unique())
        else:
            return pd.DataFrame()
        
        # Track contaminated teams removed
        final_teams = set(filtered_df['TEAM_ABBREVIATION'].dropna().unique())
        contaminated = initial_teams - final_teams
        self.stats['contaminated_teams_removed'].update(contaminated)
        
        final_count = len(filtered_df)
        
        if contaminated:
            logger.info(f"   🧹 Removed {len(contaminated)} contaminated teams: {sorted(list(contaminated))[:10]}...")
        
        logger.info(f"   📊 {league} filtering: {initial_count:,} → {final_count:,} records")
        logger.info(f"   🏀 Valid {league} teams found: {len(final_teams)}")
        
        return filtered_df
    
    def process_csv_file(self, csv_file: Path, league: str) -> pd.DataFrame:
        """Process and filter a single CSV file"""
        try:
            df = pd.read_csv(csv_file)
            
            if df.empty:
                return pd.DataFrame()
            
            # Track raw records
            if league == "NBA":
                self.stats['nba_records_raw'] += len(df)
            else:
                self.stats['wnba_records_raw'] += len(df)
            
            # Filter to official teams only
            filtered_df = self.filter_official_teams(df, league)
            
            if filtered_df.empty:
                logger.warning(f"   ⚠️ No valid {league} teams in {csv_file.name}")
                return pd.DataFrame()
            
            # Add metadata
            filtered_df['LEAGUE'] = league
            filtered_df['SOURCE_FILE'] = csv_file.name
            
            # Track filtered records
            if league == "NBA":
                self.stats['nba_records_filtered'] += len(filtered_df)
            else:
                self.stats['wnba_records_filtered'] += len(filtered_df)
            
            logger.info(f"✅ {csv_file.name}: {len(filtered_df):,} valid {league} records")
            return filtered_df
            
        except Exception as e:
            logger.error(f"❌ Error processing {csv_file}: {e}")
            return pd.DataFrame()
    
    def discover_and_filter_files(self) -> Dict[str, List[Path]]:
        """Discover and categorize basketball CSV files"""
        logger.info("🔍 Discovering basketball CSV files...")
        
        # Find all CSV files
        all_csv_files = list(self.data_dir.glob("**/*.csv"))
        logger.info(f"📁 Found {len(all_csv_files)} total CSV files")
        
        nba_files = []
        wnba_files = []
        unknown_files = []
        
        for csv_file in all_csv_files:
            # Skip existing training files
            if "ml_training" in str(csv_file):
                continue
                
            league = self.identify_league_from_file(csv_file)
            
            if league == "NBA":
                nba_files.append(csv_file)
            elif league == "WNBA":
                wnba_files.append(csv_file)
            else:
                unknown_files.append(csv_file)
        
        logger.info(f"🏀 NBA files identified: {len(nba_files)}")
        logger.info(f"🏀 WNBA files identified: {len(wnba_files)}")
        logger.info(f"❓ Unknown files: {len(unknown_files)}")
        
        return {"NBA": nba_files, "WNBA": wnba_files}
    
    def aggregate_league_data(self, files: List[Path], league: str) -> pd.DataFrame:
        """Aggregate and filter all files for a specific league"""
        logger.info(f"🔄 Processing {league} data from {len(files)} files...")
        
        all_dataframes = []
        
        for csv_file in files:
            filtered_df = self.process_csv_file(csv_file, league)
            if not filtered_df.empty:
                all_dataframes.append(filtered_df)
                
                if league == "NBA":
                    self.stats['nba_files'] += 1
                else:
                    self.stats['wnba_files'] += 1
        
        if not all_dataframes:
            logger.warning(f"⚠️ No valid {league} data found")
            return pd.DataFrame()
        
        # Combine all dataframes
        combined_df = pd.concat(all_dataframes, ignore_index=True, sort=False)
        
        # Remove duplicates
        initial_count = len(combined_df)
        combined_df = combined_df.drop_duplicates()
        final_count = len(combined_df)
        
        logger.info(f"📊 {league} aggregation complete:")
        logger.info(f"   📁 Files processed: {len(all_dataframes)}")
        logger.info(f"   📈 Final records: {final_count:,}")
        logger.info(f"   🗑️ Duplicates removed: {initial_count - final_count:,}")
        
        return combined_df
    
    def create_clean_datasets(self):
        """Create clean, filtered training datasets"""
        logger.info("🚀 Creating Clean Training Datasets")
        logger.info("🎯 Official Teams Only: 30 NBA + 13 WNBA = 43 Total")
        logger.info("=" * 60)
        
        # Discover files
        files_by_league = self.discover_and_filter_files()
        
        # Process each league
        for league, files in files_by_league.items():
            if not files:
                logger.warning(f"⚠️ No {league} files found")
                continue
            
            # Aggregate and filter data
            clean_data = self.aggregate_league_data(files, league)
            
            if clean_data.empty:
                logger.warning(f"⚠️ No clean {league} data generated")
                continue
            
            # Save clean training data
            output_file = self.output_dir / f"{league.lower()}_training_data.csv"
            clean_data.to_csv(output_file, index=False)
            
            logger.info(f"💾 Saved clean {league} training data: {output_file}")
            logger.info(f"   📊 Records: {len(clean_data):,}")
            
            if 'TEAM_ABBREVIATION' in clean_data.columns:
                unique_teams = clean_data['TEAM_ABBREVIATION'].nunique()
                teams_list = sorted(clean_data['TEAM_ABBREVIATION'].unique())
                logger.info(f"   🏀 Teams: {unique_teams} - {teams_list}")
        
        # Print comprehensive statistics
        self.print_final_statistics()
    
    def print_final_statistics(self):
        """Print comprehensive filtering statistics"""
        logger.info("\n" + "=" * 60)
        logger.info("📈 CLEAN TRAINING DATA STATISTICS")
        logger.info("=" * 60)
        
        logger.info(f"🏀 NBA:")
        logger.info(f"   📁 Files: {self.stats['nba_files']}")
        logger.info(f"   📊 Raw records: {self.stats['nba_records_raw']:,}")
        logger.info(f"   📊 Filtered records: {self.stats['nba_records_filtered']:,}")
        logger.info(f"   🏀 Teams found: {len(self.stats['nba_teams_found'])}/30")
        if self.stats['nba_teams_found']:
            logger.info(f"   🏀 Team list: {sorted(list(self.stats['nba_teams_found']))}")
        
        logger.info(f"\n🏀 WNBA:")
        logger.info(f"   📁 Files: {self.stats['wnba_files']}")
        logger.info(f"   📊 Raw records: {self.stats['wnba_records_raw']:,}")
        logger.info(f"   📊 Filtered records: {self.stats['wnba_records_filtered']:,}")
        logger.info(f"   🏀 Teams found: {len(self.stats['wnba_teams_found'])}/13")
        if self.stats['wnba_teams_found']:
            logger.info(f"   🏀 Team list: {sorted(list(self.stats['wnba_teams_found']))}")
        
        total_raw = self.stats['nba_records_raw'] + self.stats['wnba_records_raw']
        total_filtered = self.stats['nba_records_filtered'] + self.stats['wnba_records_filtered']
        total_teams = len(self.stats['nba_teams_found']) + len(self.stats['wnba_teams_found'])
        
        logger.info(f"\n📊 TOTAL:")
        logger.info(f"   📊 Raw records: {total_raw:,}")
        logger.info(f"   📊 Clean records: {total_filtered:,}")
        logger.info(f"   🧹 Records filtered out: {total_raw - total_filtered:,}")
        logger.info(f"   🏀 Official teams: {total_teams}/43")
        logger.info(f"   🗑️ Contaminated teams removed: {len(self.stats['contaminated_teams_removed'])}")

def main():
    """Main execution function"""
    logger.info("🎯 HYPER_MEDUSA_NEURAL_VAULT: Clean Training Data Creator")
    logger.info("🧹 Filtering to Official Teams Only: 30 NBA + 13 WNBA = 43 Total")
    
    creator = CleanTrainingDataCreator()
    creator.create_clean_datasets()
    
    logger.info("\n✅ Clean training datasets created successfully!")
    logger.info("🚀 Ready for proper team-separated neural training!")

if __name__ == "__main__":
    main()
