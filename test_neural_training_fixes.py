#!/usr/bin/env python3
"""
Test script to verify neural training pipeline fixes
Tests the improved synthetic data generation and constrained model outputs
"""

import sys
import os
import numpy as np
import torch
import torch.nn.functional as F

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from src.neural_cortex.neural_training_pipeline import (
    TrainingConfig, 
    EnhancedNeuralBasketballCore,
    BasketballDataset,
    NeuralTrainingPipeline
)

def test_synthetic_data_generation():
    """Test that synthetic data generation produces realistic values"""
    print("🧪 Testing synthetic data generation...")
    
    config = TrainingConfig(league="NBA", input_dim=20)
    dataset = BasketballDataset("./data", config, split="train")
    
    # Check data properties
    data = dataset.data
    labels = dataset.labels
    
    print(f"📊 Data shape: {data.shape}")
    print(f"📊 Data range: [{data.min():.3f}, {data.max():.3f}]")
    print(f"📊 Data mean: {data.mean():.3f}, std: {data.std():.3f}")
    print(f"📊 Label distribution: {np.bincount(labels)}")
    
    # Verify data is normalized and reasonable (updated for real data)
    assert data.std() < 10.0, f"Data standard deviation too high: {data.std():.3f} - may cause extreme predictions"
    assert abs(data.mean()) < 3.0, f"Data mean too far from zero: {data.mean():.3f} - may cause bias"
    assert data.min() > -20 and data.max() < 20, f"Data values too extreme: [{data.min():.3f}, {data.max():.3f}]"
    
    print("✅ Synthetic data generation test passed!")
    return True

def test_model_output_constraints():
    """Test that model outputs are constrained to reasonable ranges"""
    print("🧪 Testing model output constraints...")
    
    config = TrainingConfig(input_dim=248, hidden_dim=32, num_layers=2)  # Updated to match real data
    model = EnhancedNeuralBasketballCore(
        input_dim=config.input_dim,
        hidden_dim=config.hidden_dim,
        num_layers=config.num_layers,
        dropout_rate=config.dropout_rate
    )

    # Test with various input ranges (updated dimensions)
    test_inputs = [
        torch.randn(10, 248),  # Normal input
        torch.randn(10, 248) * 5,  # Large input
        torch.ones(10, 248) * 10,  # Extreme positive input
        torch.ones(10, 248) * -10,  # Extreme negative input
    ]
    
    model.eval()
    with torch.no_grad():
        for i, test_input in enumerate(test_inputs):
            print(f"  Testing input case {i+1}...")
            
            # Get raw logits
            logits = model(test_input)
            
            # Convert to probabilities
            probabilities = F.softmax(logits, dim=1)
            win_probs = probabilities[:, 1]
            
            print(f"    Win probability range: [{win_probs.min():.3f}, {win_probs.max():.3f}]")
            print(f"    Win probability mean: {win_probs.mean():.3f}")
            
            # Check constraints
            assert win_probs.min() >= 0.0, "Probabilities below 0"
            assert win_probs.max() <= 1.0, "Probabilities above 1"
            assert (win_probs < 0.01).sum() == 0, "Too many extreme low predictions"
            assert (win_probs > 0.99).sum() == 0, "Too many extreme high predictions"
            
            # Test constrained prediction method
            constrained_probs = model.predict_probabilities(test_input)
            constrained_win_probs = constrained_probs[:, 1]
            
            print(f"    Constrained range: [{constrained_win_probs.min():.3f}, {constrained_win_probs.max():.3f}]")
            
            # Verify constraints are applied
            assert constrained_win_probs.min() >= 0.1, "Constrained probabilities too low"
            assert constrained_win_probs.max() <= 0.9, "Constrained probabilities too high"
    
    print("✅ Model output constraints test passed!")
    return True

def test_training_stability():
    """Test that training is stable and doesn't produce extreme predictions"""
    print("🧪 Testing training stability...")
    
    # Create minimal training configuration
    config = TrainingConfig(
        league="NBA",
        input_dim=248,  # Updated to match real data dimensions
        hidden_dim=16,  # Very small for quick test
        num_layers=1,
        batch_size=32,
        num_epochs=2,  # Just 2 epochs for testing
        learning_rate=0.001
    )
    
    # Create pipeline
    pipeline = NeuralTrainingPipeline(config)
    
    # Prepare minimal data
    train_loader, val_loader, test_loader = pipeline.prepare_data()
    
    print(f"📊 Training batches: {len(train_loader)}")
    print(f"📊 Validation batches: {len(val_loader)}")
    
    # Test one training step
    pipeline.model = pipeline._build_model()
    pipeline.optimizer, pipeline.scheduler = pipeline._setup_optimizer()
    pipeline.criterion = torch.nn.CrossEntropyLoss()
    
    # Get a sample batch
    sample_batch = next(iter(train_loader))
    data, labels = sample_batch
    data = data.to(pipeline.device)
    labels = labels.squeeze().to(pipeline.device)
    
    # Forward pass
    outputs = pipeline.model(data)
    loss = pipeline.criterion(outputs, labels)
    
    print(f"📊 Sample loss: {loss.item():.4f}")
    
    # Check outputs are reasonable
    probabilities = F.softmax(outputs, dim=1)
    win_probs = probabilities[:, 1]
    
    print(f"📊 Prediction range: [{win_probs.min():.3f}, {win_probs.max():.3f}]")
    print(f"📊 Prediction mean: {win_probs.mean():.3f}")
    
    # Verify no extreme predictions
    extreme_count = ((win_probs < 0.05) | (win_probs > 0.95)).sum().item()
    print(f"📊 Extreme predictions: {extreme_count}/{len(win_probs)}")
    
    assert not torch.isnan(loss), "Loss is NaN"
    assert not torch.isinf(loss), "Loss is infinite"
    assert extreme_count < len(win_probs) * 0.1, "Too many extreme predictions"
    
    print("✅ Training stability test passed!")
    return True

def main():
    """Run all tests"""
    print("🚀 Testing Neural Training Pipeline Fixes")
    print("=" * 50)
    
    try:
        # Run tests
        test_synthetic_data_generation()
        print()
        
        test_model_output_constraints()
        print()
        
        test_training_stability()
        print()
        
        print("🎉 All tests passed! Neural training pipeline fixes are working correctly.")
        print("\n📋 Summary of fixes:")
        print("  ✅ Realistic synthetic data generation with proper scaling")
        print("  ✅ Model output constraints to prevent extreme predictions")
        print("  ✅ Conservative training configuration")
        print("  ✅ Prediction validation hooks")
        print("  ✅ Updated deprecated PyTorch functions")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
