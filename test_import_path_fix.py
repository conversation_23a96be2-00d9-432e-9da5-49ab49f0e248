#!/usr/bin/env python3
"""
Test vault_oracle.ai.temporal_models import fix
"""

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_temporal_models_import():
    """Test vault_oracle.ai.temporal_models import"""
    logger.info("🧪 Testing vault_oracle.ai.temporal_models import...")
    
    try:
        from vault_oracle.ai.temporal_models import detect_anomalies, TemporalPredictor
        logger.info("✅ vault_oracle.ai.temporal_models imported successfully")
        
        # Test detect_anomalies function
        test_data = [1, 2, 3, 10, 4, 5, 6]  # 10 is an outlier
        anomalies = detect_anomalies(test_data, z_thresh=2.0)
        logger.info(f"✅ detect_anomalies function works: found {len(anomalies)} anomalies")
        
        # Test TemporalPredictor class
        predictor = TemporalPredictor()
        prediction = predictor.predict_temporal_evolution(test_data)
        logger.info(f"✅ TemporalPredictor works: prediction confidence = {prediction.get('confidence', 'N/A')}")
        
        return True
    except ImportError as e:
        logger.error(f"❌ vault_oracle.ai.temporal_models import failed: {e}")
        return False

def test_medusa_core_import():
    """Test if medusa_core can now import without errors"""
    logger.info("🧪 Testing medusa_core import with fixed temporal models...")
    
    try:
        from vault_oracle.core.medusa_core import MedusaCore
        logger.info("✅ MedusaCore imported successfully")
        return True
    except Exception as e:
        logger.error(f"❌ MedusaCore import failed: {e}")
        return False

def test_vault_config_import():
    """Test if vault_config can now import without errors"""
    logger.info("🧪 Testing vault_config import with fixed temporal models...")
    
    try:
        from vault_oracle.core.vault_config import VaultConfig
        logger.info("✅ VaultConfig imported successfully")
        return True
    except Exception as e:
        logger.error(f"❌ VaultConfig import failed: {e}")
        return False

def main():
    """Run all import path fix tests"""
    logger.info("🚀 Starting Import Path Fix Tests")
    
    tests = [
        ("Temporal Models Import", test_temporal_models_import),
        ("MedusaCore Import", test_medusa_core_import),
        ("VaultConfig Import", test_vault_config_import)
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"❌ FAILED: {test_name} - {e}")
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🏆 ALL IMPORT PATH TESTS PASSED!")
        return True
    else:
        logger.warning(f"⚠️ {total - passed} tests failed")
        return False

if __name__ == "__main__":
    main()
