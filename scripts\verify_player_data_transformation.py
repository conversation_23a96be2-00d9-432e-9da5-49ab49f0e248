import sqlite3
import pandas as pd
import logging
from typing import Dict, Any


#!/usr/bin/env python3
"""
Verify Player Data Transformation
Check the massive improvement in player data percentages after comprehensive collection
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PlayerDataTransformationVerifier:
    """Verify the transformation in player data quality"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def get_comprehensive_data_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics after the transformation"""
        logger.info("📊 VERIFYING PLAYER DATA TRANSFORMATION")
        logger.info("=" * 50)
        
        conn = sqlite3.connect(self.db_path)
        
        # Overall statistics
        overall_query = """
        SELECT 
            league_name,
            COUNT(*) as total_records,
            COUNT(CASE WHEN player_name IS NOT NULL 
                  AND player_name != '' 
                  AND player_name != 'None' 
                  AND player_name != 'TEAM_RECORD' 
                  AND player_name != 'NEEDS_EXTRACTION' THEN 1 END) as player_records,
            COUNT(DISTINCT CASE WHEN player_name IS NOT NULL 
                  AND player_name != '' 
                  AND player_name != 'None' 
                  AND player_name != 'TEAM_RECORD' 
                  AND player_name != 'NEEDS_EXTRACTION' THEN player_name END) as unique_players
        FROM unified_nba_wnba_data
        GROUP BY league_name
        ORDER BY total_records DESC
        """
        
        overall_stats = pd.read_sql_query(overall_query, conn)
        
        # Individual player game logs specifically
        game_logs_query = """
        SELECT 
            league_name,
            COUNT(*) as game_log_records,
            COUNT(DISTINCT player_name) as players_with_game_logs,
            COUNT(DISTINCT game_id) as unique_games
        FROM unified_nba_wnba_data
        WHERE data_type = 'individual_player_game_log'
        GROUP BY league_name
        ORDER BY game_log_records DESC
        """
        
        game_logs_stats = pd.read_sql_query(game_logs_query, conn)
        
        # Player props relevant data
        props_query = """
        SELECT 
            league_name,
            stat_category,
            COUNT(*) as records,
            COUNT(DISTINCT player_name) as unique_players,
            AVG(CAST(stat_value AS FLOAT)) as avg_value,
            MIN(CAST(stat_value AS FLOAT)) as min_value,
            MAX(CAST(stat_value AS FLOAT)) as max_value
        FROM unified_nba_wnba_data
        WHERE stat_category IN ('points', 'rebounds', 'assists', 'steals', 'blocks', 'three_pointers_made')
        AND player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None' 
        AND player_name != 'TEAM_RECORD' 
        AND player_name != 'NEEDS_EXTRACTION'
        AND stat_value IS NOT NULL
        GROUP BY league_name, stat_category
        ORDER BY league_name, records DESC
        """
        
        props_stats = pd.read_sql_query(props_query, conn)
        
        # Top players by data volume
        top_players_query = """
        SELECT 
            league_name,
            player_name,
            COUNT(*) as total_records,
            COUNT(CASE WHEN data_type = 'individual_player_game_log' THEN 1 END) as game_logs,
            COUNT(DISTINCT stat_category) as stat_categories,
            MIN(season) as first_season,
            MAX(season) as last_season
        FROM unified_nba_wnba_data
        WHERE player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None' 
        AND player_name != 'TEAM_RECORD' 
        AND player_name != 'NEEDS_EXTRACTION'
        GROUP BY league_name, player_name
        HAVING COUNT(*) >= 50
        ORDER BY league_name, total_records DESC
        LIMIT 20
        """
        
        top_players = pd.read_sql_query(top_players_query, conn)
        
        conn.close()
        
        # Calculate percentages
        for _, row in overall_stats.iterrows():
            league = row['league_name']
            total = row['total_records']
            players = row['player_records']
            player_pct = (players / total * 100) if total > 0 else 0
            
            logger.info(f"📊 {league} TRANSFORMATION RESULTS:")
            logger.info(f"   Total records: {total:,}")
            logger.info(f"   Player records: {players:,} ({player_pct:.1f}%)")
            logger.info(f"   Unique players: {row['unique_players']:,}")
        
        logger.info(f"\n🎮 INDIVIDUAL GAME LOGS:")
        for _, row in game_logs_stats.iterrows():
            logger.info(f"   {row['league_name']}: {row['game_log_records']:,} game logs, {row['players_with_game_logs']:,} players")
        
        logger.info(f"\n🎯 PLAYER PROPS DATA QUALITY:")
        for _, row in props_stats.iterrows():
            logger.info(f"   {row['league_name']} | {row['stat_category']}: {row['records']:,} records, {row['unique_players']:,} players (avg: {row['avg_value']:.1f})")
        
        return {
            'overall_stats': overall_stats.to_dict('records'),
            'game_logs_stats': game_logs_stats.to_dict('records'),
            'props_stats': props_stats.to_dict('records'),
            'top_players': top_players.to_dict('records')
        }
    
    def compare_before_after(self) -> Dict[str, Any]:
        """Compare before and after transformation"""
        logger.info("\n⚖️ BEFORE vs AFTER COMPARISON")
        logger.info("=" * 35)
        
        # Known before stats
        before_stats = {
            'NBA': {'total': 41449, 'player_records': 35976, 'player_pct': 6.1, 'unique_players': 1320},
            'WNBA': {'total': 744873, 'player_records': 77975, 'player_pct': 10.3, 'unique_players': 1509}
        }
        
        # Get current stats
        current_stats = self.get_comprehensive_data_stats()
        
        comparison = {}
        
        for league_data in current_stats['overall_stats']:
            league = league_data['league_name']
            
            if league in before_stats:
                before = before_stats[league]
                after = {
                    'total': league_data['total_records'],
                    'player_records': league_data['player_records'],
                    'player_pct': (league_data['player_records'] / league_data['total_records'] * 100) if league_data['total_records'] > 0 else 0,
                    'unique_players': league_data['unique_players']
                }
                
                # Calculate improvements
                total_growth = after['total'] - before['total']
                player_growth = after['player_records'] - before['player_records']
                pct_improvement = after['player_pct'] - before['player_pct']
                player_growth_pct = ((after['unique_players'] - before['unique_players']) / before['unique_players'] * 100) if before['unique_players'] > 0 else 0
                
                comparison[league] = {
                    'before': before,
                    'after': after,
                    'improvements': {
                        'total_growth': total_growth,
                        'player_growth': player_growth,
                        'pct_improvement': pct_improvement,
                        'player_growth_pct': player_growth_pct
                    }
                }
                
                logger.info(f"📈 {league} TRANSFORMATION:")
                logger.info(f"   Total records: {before['total']:,} → {after['total']:,} (+{total_growth:,})")
                logger.info(f"   Player records: {before['player_records']:,} → {after['player_records']:,} (+{player_growth:,})")
                logger.info(f"   Player percentage: {before['player_pct']:.1f}% → {after['player_pct']:.1f}% (+{pct_improvement:+.1f}%)")
                logger.info(f"   Unique players: {before['unique_players']:,} → {after['unique_players']:,} (+{player_growth_pct:+.1f}%)")
        
        return comparison
    
    def assess_ml_readiness(self) -> Dict[str, Any]:
        """Assess readiness for ML training"""
        logger.info("\n🧠 ML TRAINING READINESS ASSESSMENT")
        logger.info("=" * 40)
        
        conn = sqlite3.connect(self.db_path)
        
        # Check data quality for ML training
        ml_readiness_query = """
        SELECT 
            league_name,
            COUNT(CASE WHEN data_type = 'individual_player_game_log' THEN 1 END) as game_logs,
            COUNT(DISTINCT CASE WHEN data_type = 'individual_player_game_log' THEN player_name END) as players_with_logs,
            COUNT(DISTINCT CASE WHEN data_type = 'individual_player_game_log' THEN season END) as seasons_covered,
            COUNT(DISTINCT CASE WHEN stat_category = 'points' THEN player_name END) as players_with_points,
            COUNT(DISTINCT CASE WHEN stat_category = 'rebounds' THEN player_name END) as players_with_rebounds,
            COUNT(DISTINCT CASE WHEN stat_category = 'assists' THEN player_name END) as players_with_assists
        FROM unified_nba_wnba_data
        WHERE player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None' 
        AND player_name != 'TEAM_RECORD' 
        AND player_name != 'NEEDS_EXTRACTION'
        GROUP BY league_name
        """
        
        ml_stats = pd.read_sql_query(ml_readiness_query, conn)
        conn.close()
        
        readiness_assessment = {}
        
        for _, row in ml_stats.iterrows():
            league = row['league_name']
            
            # Calculate readiness scores
            game_logs_score = min(100, (row['game_logs'] / 50000) * 100)  # Target: 50k+ game logs
            player_coverage_score = min(100, (row['players_with_logs'] / 1000) * 100)  # Target: 1k+ players
            season_coverage_score = min(100, (row['seasons_covered'] / 5) * 100)  # Target: 5+ seasons
            
            # Props data coverage
            props_coverage = (row['players_with_points'] + row['players_with_rebounds'] + row['players_with_assists']) / 3
            props_score = min(100, (props_coverage / 1000) * 100)  # Target: 1k+ players per prop
            
            overall_score = (game_logs_score + player_coverage_score + season_coverage_score + props_score) / 4
            
            readiness_assessment[league] = {
                'game_logs': row['game_logs'],
                'players_with_logs': row['players_with_logs'],
                'seasons_covered': row['seasons_covered'],
                'players_with_points': row['players_with_points'],
                'players_with_rebounds': row['players_with_rebounds'],
                'players_with_assists': row['players_with_assists'],
                'scores': {
                    'game_logs_score': game_logs_score,
                    'player_coverage_score': player_coverage_score,
                    'season_coverage_score': season_coverage_score,
                    'props_score': props_score,
                    'overall_score': overall_score
                }
            }
            
            logger.info(f"🎯 {league} ML READINESS:")
            logger.info(f"   Game logs: {row['game_logs']:,} (score: {game_logs_score:.0f}/100)")
            logger.info(f"   Players with logs: {row['players_with_logs']:,} (score: {player_coverage_score:.0f}/100)")
            logger.info(f"   Seasons covered: {row['seasons_covered']} (score: {season_coverage_score:.0f}/100)")
            logger.info(f"   Props coverage: {props_coverage:.0f} avg players (score: {props_score:.0f}/100)")
            logger.info(f"   OVERALL READINESS: {overall_score:.0f}/100")
            
            if overall_score >= 90:
                logger.info(f"   🏆 EXCELLENT - Ready for elite ML training!")
            elif overall_score >= 75:
                logger.info(f"   ✅ VERY GOOD - Ready for advanced ML training")
            elif overall_score >= 60:
                logger.info(f"   📈 GOOD - Ready for standard ML training")
            else:
                logger.info(f"   ⚠️ NEEDS IMPROVEMENT - More data recommended")
        
        return readiness_assessment

def main():
    """Run comprehensive verification of player data transformation"""
    
    verifier = PlayerDataTransformationVerifier()
    
    # Get comprehensive stats
    comprehensive_stats = verifier.get_comprehensive_data_stats()
    
    # Compare before and after
    comparison = verifier.compare_before_after()
    
    # Assess ML readiness
    ml_readiness = verifier.assess_ml_readiness()
    
    
    # Summary assessment
    total_improvement = 0
    leagues_ready = 0
    
    for league, data in comparison.items():
        improvement = data['improvements']['pct_improvement']
        total_improvement += improvement
        
        if league in ml_readiness and ml_readiness[league]['scores']['overall_score'] >= 75:
            leagues_ready += 1
    
    avg_improvement = total_improvement / len(comparison) if comparison else 0
    
    
    if avg_improvement >= 50:
    elif avg_improvement >= 30:
    elif avg_improvement >= 15:
    else:
    
    
    return {
        'comprehensive_stats': comprehensive_stats,
        'comparison': comparison,
        'ml_readiness': ml_readiness
    }

if __name__ == "__main__":
    main()
