import os
import sys
import asyncio
import logging
import json
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Set, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
import sqlite3
from concurrent.futures import ThreadPoolExecutor
import signal
    from enhanced_realtime_pipeline_integration import (
    from src.integrations.enhanced_realtime_pipeline import EnhancedRealTimePipeline
    from src.nba_ingestion.nba_real_time_pipeline import NBARealtimePipeline
    from src.data_integration.real_nba_integration import RealNBAIntegration
    from backend.infrastructure.realtime import connection_manager

#!/usr/bin/env python3
"""
Real-Time Data Pipeline Orchestrator - HYPER MEDUSA NEURAL VAULT
================================================================

Comprehensive orchestrator that coordinates all real-time data pipeline components
and provides a unified interface for real-time data processing.

Features:
- Unified pipeline management and coordination
- Intelligent data routing and prioritization
- Advanced error handling and recovery
- Performance monitoring and optimization
- Real-time data quality assurance
- Automated scaling and load balancing
- Integration with existing infrastructure
- Comprehensive logging and alerting

Components Orchestrated:
- Enhanced Real-Time Pipeline Integration
- NBA Real-Time Pipeline
- Live Real-Time Data Integrator
- Enhanced Real-Time Pipeline
- WebSocket infrastructure
- Market data integration
- Injury impact analytics
"""


# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import pipeline components
try:
        EnhancedRealTimePipelineIntegration, 
        create_enhanced_pipeline_integration,
        DataStreamType, DataPriority
    )
except ImportError as e:
    logging.warning(f"Some imports failed: {e}")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("REALTIME_ORCHESTRATOR")

class PipelineStatus(Enum):
    """Pipeline status states"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"
    MAINTENANCE = "maintenance"

class OrchestratorMode(Enum):
    """Orchestrator operation modes"""
    DEVELOPMENT = "development"
    PRODUCTION = "production"
    TESTING = "testing"
    MAINTENANCE = "maintenance"

@dataclass
class PipelineComponent:
    """Pipeline component configuration"""
    name: str
    instance: Any
    priority: int
    enabled: bool = True
    health_check: Optional[Callable] = None
    restart_on_failure: bool = True
    max_restart_attempts: int = 3
    restart_count: int = 0
    last_restart: Optional[datetime] = None
    status: PipelineStatus = PipelineStatus.STOPPED

@dataclass
class OrchestratorMetrics:
    """Orchestrator performance metrics"""
    total_components: int = 0
    running_components: int = 0
    failed_components: int = 0
    total_restarts: int = 0
    uptime_seconds: float = 0.0
    data_throughput: float = 0.0
    error_rate: float = 0.0
    last_health_check: Optional[datetime] = None
    system_load: float = 0.0

class RealTimePipelineOrchestrator:
    """
    Real-Time Data Pipeline Orchestrator
    
    Comprehensive orchestrator that manages and coordinates all real-time
    data pipeline components for optimal performance and reliability.
    """
    
    def __init__(self, mode: OrchestratorMode = OrchestratorMode.PRODUCTION):
        self.mode = mode
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Orchestrator state
        self.status = PipelineStatus.STOPPED
        self.start_time: Optional[datetime] = None
        self.components: Dict[str, PipelineComponent] = {}
        self.metrics = OrchestratorMetrics()
        
        # Management tasks
        self.management_tasks: Set[asyncio.Task] = set()
        self.shutdown_event = asyncio.Event()
        
        # Configuration
        self.health_check_interval = 30  # seconds
        self.restart_delay = 5  # seconds
        self.max_concurrent_restarts = 2
        self.performance_monitoring = True
        
        # Thread pool for blocking operations
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        self.logger.info(f"🎭 Real-Time Pipeline Orchestrator initialized in {mode.value} mode")
    
    async def initialize(self):
        """Initialize the orchestrator and register components"""
        try:
            self.logger.info("🔧 Initializing Real-Time Pipeline Orchestrator...")
            
            # Register pipeline components
            await self._register_components()
            
            # Setup signal handlers for graceful shutdown
            self._setup_signal_handlers()
            
            # Initialize metrics
            self.metrics.total_components = len(self.components)
            
            self.logger.info(f"✅ Orchestrator initialized with {len(self.components)} components")
            
        except Exception as e:
            self.logger.error(f"❌ Orchestrator initialization failed: {e}")
            raise
    
    async def _register_components(self):
        """Register all pipeline components"""
        try:
            # Enhanced Real-Time Pipeline Integration (Primary)
            enhanced_integration = await create_enhanced_pipeline_integration()
            self.components["enhanced_integration"] = PipelineComponent(
                name="Enhanced Real-Time Pipeline Integration",
                instance=enhanced_integration,
                priority=1,
                health_check=self._check_enhanced_integration_health
            )
            
            # Enhanced Real-Time Pipeline
            enhanced_pipeline = EnhancedRealTimePipeline()
            self.components["enhanced_pipeline"] = PipelineComponent(
                name="Enhanced Real-Time Pipeline",
                instance=enhanced_pipeline,
                priority=2,
                health_check=self._check_enhanced_pipeline_health
            )
            
            # Real NBA Integration
            nba_integration = RealNBAIntegration()
            self.components["nba_integration"] = PipelineComponent(
                name="Real NBA Integration",
                instance=nba_integration,
                priority=3,
                health_check=self._check_nba_integration_health
            )
            
            self.logger.info(f"✅ Registered {len(self.components)} pipeline components")
            
        except Exception as e:
            self.logger.error(f"❌ Component registration failed: {e}")
            raise
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            self.logger.info(f"📡 Received signal {signum}, initiating graceful shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def start(self):
        """Start the orchestrator and all pipeline components"""
        if self.status != PipelineStatus.STOPPED:
            self.logger.warning("⚠️ Orchestrator already running or in transition")
            return
        
        self.logger.info("🚀 Starting Real-Time Pipeline Orchestrator...")
        
        self.status = PipelineStatus.STARTING
        self.start_time = datetime.now(timezone.utc)
        
        try:
            # Start components in priority order
            sorted_components = sorted(
                self.components.items(), 
                key=lambda x: x[1].priority
            )
            
            for component_id, component in sorted_components:
                if component.enabled:
                    await self._start_component(component_id, component)
            
            # Start management tasks
            await self._start_management_tasks()
            
            self.status = PipelineStatus.RUNNING
            self.logger.info("✅ Real-Time Pipeline Orchestrator started successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Orchestrator startup failed: {e}")
            self.status = PipelineStatus.ERROR
            await self.shutdown()
            raise
    
    async def _start_component(self, component_id: str, component: PipelineComponent):
        """Start a single pipeline component"""
        try:
            self.logger.info(f"🔄 Starting component: {component.name}")
            
            component.status = PipelineStatus.STARTING
            
            # Start the component
            if hasattr(component.instance, 'start_pipeline'):
                await component.instance.start_pipeline()
            elif hasattr(component.instance, 'start'):
                await component.instance.start()
            elif hasattr(component.instance, 'initialize'):
                await component.instance.initialize()
            
            component.status = PipelineStatus.RUNNING
            self.metrics.running_components += 1
            
            self.logger.info(f"✅ Component started: {component.name}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start component {component.name}: {e}")
            component.status = PipelineStatus.ERROR
            self.metrics.failed_components += 1
            raise
    
    async def _start_management_tasks(self):
        """Start orchestrator management tasks"""
        tasks = [
            asyncio.create_task(self._health_monitor()),
            asyncio.create_task(self._performance_monitor()),
            asyncio.create_task(self._component_supervisor())
        ]
        
        for task in tasks:
            self.management_tasks.add(task)
        
        self.logger.info("✅ Management tasks started")
    
    async def _health_monitor(self):
        """Monitor health of all components"""
        self.logger.info("🏥 Starting health monitoring...")
        
        while not self.shutdown_event.is_set():
            try:
                self.metrics.last_health_check = datetime.now(timezone.utc)
                
                for component_id, component in self.components.items():
                    if component.status == PipelineStatus.RUNNING:
                        is_healthy = await self._check_component_health(component)
                        
                        if not is_healthy:
                            self.logger.warning(f"⚠️ Component unhealthy: {component.name}")
                            await self._handle_unhealthy_component(component_id, component)
                
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                self.logger.error(f"❌ Health monitoring error: {e}")
                await asyncio.sleep(self.health_check_interval)
    
    async def _check_component_health(self, component: PipelineComponent) -> bool:
        """Check health of a specific component"""
        try:
            if component.health_check:
                return await component.health_check(component.instance)
            else:
                # Default health check
                return hasattr(component.instance, 'is_running') and component.instance.is_running
                
        except Exception as e:
            self.logger.error(f"❌ Health check failed for {component.name}: {e}")
            return False
    
    async def _check_enhanced_integration_health(self, instance) -> bool:
        """Health check for enhanced integration"""
        try:
            return instance.is_running and instance.event_queue.qsize() < 8000
        except:
            return False
    
    async def _check_enhanced_pipeline_health(self, instance) -> bool:
        """Health check for enhanced pipeline"""
        try:
            return hasattr(instance, 'is_running') and instance.is_running
        except:
            return False
    
    async def _check_nba_integration_health(self, instance) -> bool:
        """Health check for NBA integration"""
        try:
            return hasattr(instance, 'processing_stats') and instance.processing_stats.get('errors', 0) < 10
        except:
            return False
    
    async def _handle_unhealthy_component(self, component_id: str, component: PipelineComponent):
        """Handle an unhealthy component"""
        if component.restart_on_failure and component.restart_count < component.max_restart_attempts:
            self.logger.info(f"🔄 Attempting to restart component: {component.name}")
            await self._restart_component(component_id, component)
        else:
            self.logger.error(f"❌ Component failed permanently: {component.name}")
            component.status = PipelineStatus.ERROR
            self.metrics.failed_components += 1
    
    async def _restart_component(self, component_id: str, component: PipelineComponent):
        """Restart a failed component"""
        try:
            component.status = PipelineStatus.STOPPING
            
            # Stop the component
            if hasattr(component.instance, 'stop_pipeline'):
                await component.instance.stop_pipeline()
            elif hasattr(component.instance, 'stop'):
                await component.instance.stop()
            
            # Wait before restart
            await asyncio.sleep(self.restart_delay)
            
            # Start the component
            await self._start_component(component_id, component)
            
            component.restart_count += 1
            component.last_restart = datetime.now(timezone.utc)
            self.metrics.total_restarts += 1
            
            self.logger.info(f"✅ Component restarted successfully: {component.name}")
            
        except Exception as e:
            self.logger.error(f"❌ Component restart failed: {component.name}: {e}")
            component.status = PipelineStatus.ERROR
    
    async def _performance_monitor(self):
        """Monitor orchestrator performance"""
        self.logger.info("📊 Starting performance monitoring...")
        
        while not self.shutdown_event.is_set():
            try:
                # Update uptime
                if self.start_time:
                    self.metrics.uptime_seconds = (
                        datetime.now(timezone.utc) - self.start_time
                    ).total_seconds()
                
                # Update component counts
                self.metrics.running_components = sum(
                    1 for comp in self.components.values() 
                    if comp.status == PipelineStatus.RUNNING
                )
                
                self.metrics.failed_components = sum(
                    1 for comp in self.components.values() 
                    if comp.status == PipelineStatus.ERROR
                )
                
                # Calculate error rate
                total_restarts = self.metrics.total_restarts
                if total_restarts > 0:
                    self.metrics.error_rate = total_restarts / self.metrics.uptime_seconds * 3600
                
                await asyncio.sleep(60)  # Update every minute
                
            except Exception as e:
                self.logger.error(f"❌ Performance monitoring error: {e}")
                await asyncio.sleep(60)
    
    async def _component_supervisor(self):
        """Supervise component lifecycle"""
        self.logger.info("👁️ Starting component supervision...")
        
        while not self.shutdown_event.is_set():
            try:
                # Check for components that need attention
                for component_id, component in self.components.items():
                    if component.status == PipelineStatus.ERROR and component.restart_on_failure:
                        # Reset restart count after some time
                        if (component.last_restart and 
                            (datetime.now(timezone.utc) - component.last_restart).total_seconds() > 3600):
                            component.restart_count = 0
                            self.logger.info(f"🔄 Reset restart count for {component.name}")
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"❌ Component supervision error: {e}")
                await asyncio.sleep(300)
    
    async def shutdown(self):
        """Shutdown the orchestrator gracefully"""
        if self.status == PipelineStatus.STOPPING:
            self.logger.warning("⚠️ Shutdown already in progress")
            return
        
        self.logger.info("🛑 Shutting down Real-Time Pipeline Orchestrator...")
        
        self.status = PipelineStatus.STOPPING
        self.shutdown_event.set()
        
        try:
            # Stop all components in reverse priority order
            sorted_components = sorted(
                self.components.items(), 
                key=lambda x: x[1].priority, 
                reverse=True
            )
            
            for component_id, component in sorted_components:
                if component.status == PipelineStatus.RUNNING:
                    await self._stop_component(component_id, component)
            
            # Cancel management tasks
            for task in self.management_tasks:
                task.cancel()
            
            # Wait for tasks to complete
            if self.management_tasks:
                await asyncio.gather(*self.management_tasks, return_exceptions=True)
            
            # Close thread pool
            self.thread_pool.shutdown(wait=True)
            
            self.status = PipelineStatus.STOPPED
            self.logger.info("✅ Real-Time Pipeline Orchestrator shutdown complete")
            
        except Exception as e:
            self.logger.error(f"❌ Shutdown error: {e}")
            self.status = PipelineStatus.ERROR
    
    async def _stop_component(self, component_id: str, component: PipelineComponent):
        """Stop a single component"""
        try:
            self.logger.info(f"🛑 Stopping component: {component.name}")
            
            component.status = PipelineStatus.STOPPING
            
            if hasattr(component.instance, 'stop_pipeline'):
                await component.instance.stop_pipeline()
            elif hasattr(component.instance, 'stop'):
                await component.instance.stop()
            
            component.status = PipelineStatus.STOPPED
            self.logger.info(f"✅ Component stopped: {component.name}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to stop component {component.name}: {e}")
            component.status = PipelineStatus.ERROR
    
    def get_status(self) -> Dict[str, Any]:
        """Get orchestrator status"""
        return {
            "orchestrator_status": self.status.value,
            "mode": self.mode.value,
            "uptime_seconds": self.metrics.uptime_seconds,
            "components": {
                component_id: {
                    "name": component.name,
                    "status": component.status.value,
                    "priority": component.priority,
                    "enabled": component.enabled,
                    "restart_count": component.restart_count,
                    "last_restart": component.last_restart.isoformat() if component.last_restart else None
                }
                for component_id, component in self.components.items()
            },
            "metrics": {
                "total_components": self.metrics.total_components,
                "running_components": self.metrics.running_components,
                "failed_components": self.metrics.failed_components,
                "total_restarts": self.metrics.total_restarts,
                "error_rate": round(self.metrics.error_rate, 3),
                "last_health_check": self.metrics.last_health_check.isoformat() if self.metrics.last_health_check else None
            }
        }

# Global orchestrator instance
_orchestrator = None

def get_orchestrator() -> RealTimePipelineOrchestrator:
    """Get or create global orchestrator"""
    global _orchestrator
    if _orchestrator is None:
        _orchestrator = RealTimePipelineOrchestrator()
    return _orchestrator

async def create_orchestrator(mode: OrchestratorMode = OrchestratorMode.PRODUCTION) -> RealTimePipelineOrchestrator:
    """Create and initialize orchestrator"""
    orchestrator = RealTimePipelineOrchestrator(mode)
    await orchestrator.initialize()
    return orchestrator
