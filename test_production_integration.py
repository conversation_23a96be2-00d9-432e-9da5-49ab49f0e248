#!/usr/bin/env python3
"""
PRODUCTION PROPS-TO-GAME INTEGRATION TEST
========================================

Test the integrated props-to-game system in the main HYPER MEDUSA NEURAL VAULT pipeline.
This validates that our proven strategy is working in the production environment.
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), ".")))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("PRODUCTION_INTEGRATION_TEST")

async def test_production_integration():
    """Test the production props-to-game integration"""
    logger.info("🚀 TESTING PRODUCTION PROPS-TO-GAME INTEGRATION")
    logger.info("=" * 60)
    
    try:
        # Import the enhanced ML prediction service
        from backend.services.ml_prediction_service import MLPredictionService
        
        # Initialize the service
        ml_service = MLPredictionService()
        await ml_service.initialize()
        
        # Test WNBA games (our validated league)
        test_games = [
            {
                'home_team': 'Las Vegas Aces',
                'away_team': 'New York Liberty',
                'league': 'WNBA'
            },
            {
                'home_team': 'Connecticut Sun',
                'away_team': 'Seattle Storm',
                'league': 'WNBA'
            },
            {
                'home_team': 'Chicago Sky',
                'away_team': 'Indiana Fever',
                'league': 'WNBA'
            }
        ]
        
        logger.info(f"Testing {len(test_games)} WNBA games with production integration")
        logger.info("-" * 60)
        
        results = []
        
        for i, game in enumerate(test_games, 1):
            logger.info(f"\n🎯 TEST {i}: {game['home_team']} vs {game['away_team']}")
            
            try:
                # Use the main prediction method (now enhanced with props integration)
                prediction = await ml_service.predict_game_winner(
                    home_team=game['home_team'],
                    away_team=game['away_team'],
                    league=game['league']
                )
                
                # Log results
                logger.info(f"   Winner: {prediction.get('predicted_winner')}")
                logger.info(f"   Home Win Prob: {prediction.get('home_win_probability', 0):.3f}")
                logger.info(f"   Confidence: {prediction.get('confidence', 0):.3f}")
                logger.info(f"   Model: {prediction.get('model', 'unknown')}")
                logger.info(f"   Integration Method: {prediction.get('integration_method', 'N/A')}")
                logger.info(f"   Improvement: {prediction.get('improvement_over_base', 0)*100:.1f}%")
                logger.info(f"   Success: {prediction.get('success', False)}")
                
                results.append({
                    'game': f"{game['home_team']} vs {game['away_team']}",
                    'prediction': prediction,
                    'success': prediction.get('success', False)
                })
                
            except Exception as e:
                logger.error(f"   ❌ Test {i} failed: {e}")
                results.append({
                    'game': f"{game['home_team']} vs {game['away_team']}",
                    'error': str(e),
                    'success': False
                })
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("📊 PRODUCTION INTEGRATION TEST RESULTS")
        logger.info("=" * 60)
        
        successful_tests = sum(1 for r in results if r.get('success', False))
        total_tests = len(results)
        
        logger.info(f"Tests Completed: {total_tests}")
        logger.info(f"Successful: {successful_tests}")
        logger.info(f"Success Rate: {successful_tests/total_tests*100:.1f}%")
        
        if successful_tests > 0:
            # Calculate average confidence and improvement
            successful_predictions = [r['prediction'] for r in results if r.get('success', False)]
            avg_confidence = sum(p.get('confidence', 0) for p in successful_predictions) / len(successful_predictions)
            avg_improvement = sum(p.get('improvement_over_base', 0) for p in successful_predictions) / len(successful_predictions)
            
            logger.info(f"Average Confidence: {avg_confidence:.3f}")
            logger.info(f"Average Improvement: {avg_improvement*100:.1f}%")
            
            # Check if using props integration
            props_integration_count = sum(1 for p in successful_predictions 
                                        if 'props' in p.get('model', '').lower() or 
                                           'props' in p.get('integration_method', '').lower())
            
            logger.info(f"Using Props Integration: {props_integration_count}/{successful_tests}")
            
            if props_integration_count > 0:
                logger.info("✅ PROPS-TO-GAME INTEGRATION IS WORKING IN PRODUCTION!")
            else:
                logger.warning("⚠️ Props integration not detected in results")
        
        logger.info("=" * 60)
        
        return results
        
    except Exception as e:
        logger.error(f"❌ Production integration test failed: {e}")
        return []

async def main():
    """Run the production integration test"""
    results = await test_production_integration()
    
    if results:
        print(f"\n🎯 Test completed with {len(results)} results")
        successful = sum(1 for r in results if r.get('success', False))
        if successful > 0:
            print(f"✅ {successful} successful predictions using enhanced system")
        else:
            print("❌ No successful predictions - check system configuration")
    else:
        print("❌ Test failed to run - check imports and dependencies")

if __name__ == "__main__":
    asyncio.run(main())
