#!/usr/bin/env python3
"""
Real Model Training Script for MEDUSA Basketball Intelligence System

This script trains actual NBA and WNBA models using the 6.3 million data files
available in the data directory, replacing all fallback predictions with real
trained models.

Usage:
    python train_real_models.py
"""

import asyncio
import logging
import os
import sys
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from models.unified_model_forge import UnifiedModelForge, ModelForgeConfig, ModelArchetype

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('model_training.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class RealModelTrainer:
    """Comprehensive real model trainer using 6.3M data files"""
    
    def __init__(self):
        self.logger = logger
        self.models_trained = []
        self.training_results = {}
        
    async def train_all_models(self):
        """Train all NBA and WNBA models using real data"""
        self.logger.info("🚀 Starting comprehensive real model training using 6.3M data files")
        
        # Create model configurations for different archetypes
        model_configs = [
            {
                'name': 'NBA_Basketball_Specialist',
                'league': 'NBA',
                'archetype': ModelArchetype.BASKETBALL_SPECIALIST,
                'description': 'NBA-specific basketball intelligence model'
            },
            {
                'name': 'WNBA_Basketball_Specialist', 
                'league': 'WNBA',
                'archetype': ModelArchetype.BASKETBALL_SPECIALIST,
                'description': 'WNBA-specific basketball intelligence model'
            },
            {
                'name': 'NBA_Neural_Ensemble',
                'league': 'NBA', 
                'archetype': ModelArchetype.NEURAL_ENSEMBLE,
                'description': 'NBA neural ensemble prediction model'
            },
            {
                'name': 'WNBA_Neural_Ensemble',
                'league': 'WNBA',
                'archetype': ModelArchetype.NEURAL_ENSEMBLE, 
                'description': 'WNBA neural ensemble prediction model'
            },
            {
                'name': 'NBA_Temporal_Predictor',
                'league': 'NBA',
                'archetype': ModelArchetype.TEMPORAL_PREDICTOR,
                'description': 'NBA temporal pattern prediction model'
            },
            {
                'name': 'WNBA_Temporal_Predictor',
                'league': 'WNBA',
                'archetype': ModelArchetype.TEMPORAL_PREDICTOR,
                'description': 'WNBA temporal pattern prediction model'
            }
        ]
        
        # Train each model configuration
        for config in model_configs:
            await self.train_single_model(config)
            
        # Generate training summary
        await self.generate_training_summary()
        
    async def train_single_model(self, config: dict):
        """Train a single model configuration"""
        model_name = config['name']
        league = config['league']
        archetype = config['archetype']
        
        self.logger.info(f"🏀 Training {model_name} ({league}) using {archetype.value}")
        
        try:
            # Create model forge with specific archetype
            forge_config = ModelForgeConfig(
                archetype=archetype,
                basketball_awareness=True,
                enable_quantum_features=True,
                temporal_integration=True,
                target_accuracy=0.75
            )
            
            forge = UnifiedModelForge(forge_config)
            
            # Train the model using real data
            result = await forge.forge_model(
                model_type=model_name.lower(),
                archetype=archetype,
                training_data=None  # Will load real data automatically
            )
            
            # Save the trained model
            model_path = await self.save_trained_model(result, model_name, league)
            
            # Record training results
            self.training_results[model_name] = {
                'league': league,
                'archetype': archetype.value,
                'model_path': str(model_path),
                'training_timestamp': datetime.now().isoformat(),
                'success': True,
                'performance_metrics': result.performance_metrics,
                'model_artifacts': result.model_artifacts
            }
            
            self.models_trained.append(model_name)
            self.logger.info(f"✅ Successfully trained {model_name}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to train {model_name}: {e}")
            self.training_results[model_name] = {
                'league': league,
                'archetype': archetype.value,
                'success': False,
                'error': str(e),
                'training_timestamp': datetime.now().isoformat()
            }
            
    async def save_trained_model(self, model_result: dict, model_name: str, league: str) -> Path:
        """Save trained model to appropriate directory"""
        
        # Create models directory structure
        models_dir = Path("models")
        league_dir = models_dir / league.lower()
        league_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate model filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_filename = f"{model_name.lower()}_{timestamp}.pkl"
        model_path = league_dir / model_filename
        
        # Also create a "latest" symlink/copy
        latest_path = league_dir / f"{model_name.lower()}_latest.pkl"
        
        try:
            # Save model using joblib if available
            import joblib
            joblib.dump(model_result, model_path)
            joblib.dump(model_result, latest_path)
            
            self.logger.info(f"💾 Saved {model_name} to {model_path}")
            return model_path
            
        except ImportError:
            # Fallback to pickle
            import pickle
            with open(model_path, 'wb') as f:
                pickle.dump(model_result, f)
            with open(latest_path, 'wb') as f:
                pickle.dump(model_result, f)
                
            self.logger.info(f"💾 Saved {model_name} to {model_path} (using pickle)")
            return model_path
            
    async def generate_training_summary(self):
        """Generate comprehensive training summary"""
        
        self.logger.info("📊 Generating training summary...")
        
        total_models = len(self.training_results)
        successful_models = len([r for r in self.training_results.values() if r.get('success', False)])
        failed_models = total_models - successful_models
        
        summary = {
            'training_session': {
                'timestamp': datetime.now().isoformat(),
                'total_models_attempted': total_models,
                'successful_models': successful_models,
                'failed_models': failed_models,
                'success_rate': f"{(successful_models/total_models)*100:.1f}%" if total_models > 0 else "0%"
            },
            'model_results': self.training_results,
            'data_sources_used': await self.get_data_sources_summary()
        }
        
        # Save summary to file
        import json
        summary_path = Path("model_training_summary.json")
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
            
        self.logger.info(f"📊 Training Summary:")
        self.logger.info(f"   Total Models: {total_models}")
        self.logger.info(f"   Successful: {successful_models}")
        self.logger.info(f"   Failed: {failed_models}")
        self.logger.info(f"   Success Rate: {(successful_models/total_models)*100:.1f}%")
        self.logger.info(f"   Summary saved to: {summary_path}")
        
    async def get_data_sources_summary(self) -> dict:
        """Get summary of data sources used"""
        
        data_dir = Path("data")
        data_summary = {
            'nba_files': [],
            'wnba_files': [],
            'total_files': 0,
            'total_size_mb': 0
        }
        
        if data_dir.exists():
            for file_path in data_dir.rglob("*.csv"):
                file_size = file_path.stat().st_size / (1024 * 1024)  # MB
                data_summary['total_size_mb'] += file_size
                data_summary['total_files'] += 1
                
                if 'nba' in str(file_path).lower():
                    data_summary['nba_files'].append(str(file_path))
                elif 'wnba' in str(file_path).lower():
                    data_summary['wnba_files'].append(str(file_path))
                    
        data_summary['total_size_mb'] = round(data_summary['total_size_mb'], 2)
        return data_summary

async def main():
    """Main training execution"""
    
    logger.info("🚀 MEDUSA Real Model Training - Using 6.3M Data Files")
    logger.info("=" * 60)
    
    # Verify data directory exists
    data_dir = Path("data")
    if not data_dir.exists():
        logger.error("❌ Data directory not found! Please ensure data files are available.")
        return
        
    # Count available data files
    csv_files = list(data_dir.rglob("*.csv"))
    logger.info(f"📁 Found {len(csv_files)} CSV data files")
    
    # Initialize trainer
    trainer = RealModelTrainer()
    
    # Start training
    await trainer.train_all_models()
    
    logger.info("🎉 Real model training completed!")
    logger.info("🔥 MEDUSA is now ready for real predictions!")

if __name__ == "__main__":
    asyncio.run(main())
