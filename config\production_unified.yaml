# 🚀 HYPER MEDUSA NEURAL VAULT - Unified Production Configuration
# ================================================================
# Single source of truth for all production configuration
# Environment variables override these defaults

# Application Configuration
app:
  name: "HYPER_MEDUSA_NEURAL_VAULT"
  version: "3.0.0-PRODUCTION"
  environment: "production"
  debug: false
  host: "0.0.0.0"
  port: 8000
  workers: 8
  timeout: 30
  max_requests: 10000
  max_requests_jitter: 100
  preload_app: true
  reload: false

# Database Configuration
database:
  url: "${DATABASE_URL}"
  provider: "postgresql"
  pool_size: 50
  max_overflow: 100
  pool_timeout: 60
  pool_recycle: 7200  # 2 hours
  echo: false  # Disable SQL logging in production
  pool_pre_ping: true
  connect_args:
    connect_timeout: 10
    command_timeout: 30

# Redis Configuration
redis:
  url: "${REDIS_URL}"
  password: "${REDIS_PASSWORD}"
  max_connections: 100
  socket_timeout: 10
  socket_connect_timeout: 10
  retry_on_timeout: true
  health_check_interval: 30

# Security Configuration
security:
  secret_key: "${SECRET_KEY}"
  jwt_secret: "${JWT_SECRET}"
  jwt_algorithm: "HS256"
  jwt_expiration: 3600  # 1 hour
  encryption_key: "${ENCRYPTION_KEY}"
  password_min_length: 12
  max_login_attempts: 3
  lockout_duration: 1800  # 30 minutes
  cors_enabled: true
  csrf_protection: true
  rate_limit: "100/minute"
  cors_origins:
    - "https://hypermedusa.ai"
    - "https://api.hypermedusa.ai"
    - "https://app.hypermedusa.ai"

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - 🧠 MEDUSA: %(message)s"
  file_path: "/var/log/hyper_medusa/production.log"
  max_bytes: 52428800  # 50MB
  backup_count: 10
  json_format: true
  structured_logging: true
  enable_audit_log: true
  audit_log_path: "/var/log/hyper_medusa/audit.log"

# Monitoring Configuration
monitoring:
  enabled: true
  prometheus_enabled: true
  prometheus_port: 9090
  grafana_enabled: true
  grafana_port: 3000
  grafana_url: "http://localhost:3000"
  grafana_token: "${GRAFANA_TOKEN}"
  health_check_interval: 30
  metrics_retention_days: 30
  alerting_enabled: true
  sentry_dsn: "${SENTRY_DSN}"
  alert_webhook_url: "${ALERT_WEBHOOK_URL}"
  alert_channels:
    - "console"
    - "file"
    - "oracle"
    - "grafana"

# External APIs
external_apis:
  nba_api:
    base_url: "https://stats.nba.com/stats"
    primary_key: "${NBA_API_KEY}"
    backup_key: "${NBA_API_KEY_BACKUP}"
    rate_limit_per_hour: 2000
    timeout_seconds: 5
    retry_attempts: 2
    cache_ttl_seconds: 10
  
  odds_api:
    key: "${ODDS_API_KEY}"
    base_url: "https://api.the-odds-api.com"
    timeout_seconds: 10
    retry_attempts: 3
    cache_ttl_seconds: 300

# Machine Learning Configuration
ml:
  model_path: "/app/models/production"
  batch_size: 64
  learning_rate: 0.0001
  epochs: 200
  validation_split: 0.15
  early_stopping_patience: 20
  device: "cuda"
  mixed_precision: true
  gradient_clipping: 1.0
  checkpoint_interval: 25
  model_cache_ttl_hours: 48
  drift_detection_enabled: true
  shap_explainability_enabled: false  # Disabled for performance

# Prediction Service Configuration
prediction:
  live_update_interval_seconds: 120
  confidence_threshold: "${MODEL_CONFIDENCE_THRESHOLD:0.75}"
  max_predictions_per_request: 200
  prediction_cache_ttl_minutes: 60
  enable_real_time: true
  enable_batch_processing: true
  max_batch_size: 1000

# Feature Engineering Configuration
feature_engineering:
  pipelines:
    - "player_stats"
    - "team_stats"
    - "matchup_history"
    - "injury_reports"
    - "weather_conditions"
  cache_ttl_hours: 24
  enable_advanced_features: true
  feature_selection_threshold: 0.05

# Caching Configuration
caching:
  enabled: true
  default_ttl: 300  # 5 minutes
  max_size: 10000
  eviction_policy: "lru"
  compression_enabled: true
  
  # Specific cache configurations
  prediction_cache_ttl: 3600  # 1 hour
  model_cache_ttl: 86400  # 24 hours
  api_cache_ttl: 600  # 10 minutes

# Performance Configuration
performance:
  max_workers: 8
  worker_timeout: 30
  enable_async_processing: true
  connection_pool_size: 50
  max_concurrent_requests: 1000
  request_timeout: 30
  enable_compression: true
  compression_level: 6

# Business Logic Configuration
business:
  subscription_tiers:
    free:
      api_calls_per_day: 100
      predictions_per_day: 10
      features: ["basic_predictions"]
    
    pro:
      api_calls_per_day: 2000
      predictions_per_day: 50
      monthly_price: 49.99
      features: ["advanced_analytics", "portfolio_optimization"]
    
    enterprise:
      api_calls_per_day: -1  # Unlimited
      predictions_per_day: -1  # Unlimited
      monthly_price: 199.99
      features: ["medusa_supreme_access", "custom_models", "priority_support"]

# Autonomous System Configuration
autonomous:
  enabled: true
  monitoring_interval: 30
  decision_threshold: 0.85
  auto_scaling_enabled: true
  self_healing_enabled: true
  learning_rate_adaptation: true

# Backup and Recovery Configuration
backup:
  enabled: true
  interval_hours: 6
  retention_days: 30
  backup_location: "/var/backups/hyper_medusa"
  encryption_enabled: true
  compression_enabled: true
  verify_backups: true

# Circuit Breaker Configuration
circuit_breaker:
  enabled: true
  failure_threshold: 5
  timeout_seconds: 60
  half_open_max_calls: 3
  
# Rate Limiting Configuration
rate_limiting:
  enabled: true
  default_limit: "100/minute"
  burst_limit: "200/minute"
  tier_limits:
    free: "50/minute"
    pro: "200/minute"
    enterprise: "1000/minute"

# Feature Flags
feature_flags:
  enable_real_time_predictions: true
  enable_betting_integration: true
  enable_advanced_analytics: true
  enable_websockets: true
  enable_autonomous_system: true
  enable_multi_league: true
  enable_ensemble_router: true
  enable_quantum_features: true
  enable_cognitive_spires: true

# Vault Specific Configuration
vault:
  security_level: "maximum"
  neural_threat_detection: true
  quantum_encryption_enabled: true
  temporal_flux_threshold: 0.95
  enable_basketball_intelligence: true
  enable_cognitive_spires: true
  vigil_cycle_seconds: 300  # 5 minutes

# League Configuration
leagues:
  nba:
    enabled: true
    season_start_month: 10
    season_end_month: 6
    teams_count: 30
  
  wnba:
    enabled: true
    season_start_month: 5
    season_end_month: 10
    teams_count: 13  # Including Golden State Valkyries expansion 2025

# Deployment Configuration
deployment:
  strategy: "blue_green"
  health_check_path: "/health"
  readiness_check_path: "/ready"
  liveness_check_path: "/alive"
  graceful_shutdown_timeout: 30
  max_startup_time: 120
