# 🔒 HYPER MEDUSA NEURAL VAULT - Production Environment Variables
# ======================================================================
# SECURITY NOTICE: Replace all placeholder values with secure credentials

# Application Configuration
APP_ENV=production
DEBUG=false
LOG_LEVEL=INFO

# Security Configuration (REQUIRED)
SECRET_KEY=S496k-RIqcAVGIiHm4UUpUTdKDd_DzFEb7jMrK_1F0k
JWT_SECRET=O_MtesziKemEkNFXN2HW5EJXWgK5hYuc8LERKNwEx2A
ENCRYPTION_KEY=Pr4v303tHMLJHsGnG5nU7dX1Zrv6vAbtozDbpBK5hfw
JWT_ALGORITHM=HS256
JWT_EXPIRATION=3600

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/hyper_medusa_prod
DATABASE_POOL_SIZE=50
DATABASE_MAX_OVERFLOW=100

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your_secure_redis_password
REDIS_MAX_CONNECTIONS=100

# External API Keys
NBA_API_KEY=your_nba_api_key
ODDS_API_KEY=your_odds_api_key
SENTRY_DSN=your_sentry_dsn
GRAFANA_TOKEN=your_grafana_token

# Monitoring Configuration
ENABLE_METRICS=true
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000
ALERT_WEBHOOK_URL=your_alert_webhook_url
