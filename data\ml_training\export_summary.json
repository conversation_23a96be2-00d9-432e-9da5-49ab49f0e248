{"export_timestamp": "2025-06-30T01:18:53.803890", "exported_files": [{"filename": "nba_training_data.csv", "filepath": "data\\ml_training\\nba_training_data.csv", "records": 1000, "columns": ["player_id", "player_name", "team_abbreviation", "season", "season_type", "league_name", "league_id", "stat_value", "rank_position", "high_performer", "top_10_rank", "random_feature_1", "random_feature_2", "win_prediction", "total_score_prediction", "over_under_prediction"], "file_size_mb": 0.09993171691894531}, {"filename": "wnba_training_data.csv", "filepath": "data\\ml_training\\wnba_training_data.csv", "records": 1000, "columns": ["player_id", "player_name", "team_abbreviation", "season", "season_type", "league_name", "league_id", "stat_value", "rank_position", "high_performer", "top_10_rank", "random_feature_1", "random_feature_2", "win_prediction", "total_score_prediction", "over_under_prediction"], "file_size_mb": 0.10088539123535156}], "total_records": 2000}