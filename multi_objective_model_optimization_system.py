import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tu<PERSON>, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging
import warnings
import math
import random
from abc import ABC, abstractmethod
import json
    import optuna
    from optuna.samplers import NSGAIISampler, TPESampler
    from optuna.pruners import Median<PERSON><PERSON>er, HyperbandPruner
    from sklearn.model_selection import cross_val_score, StratifiedKFold
    from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
    from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.svm import SVC
    import matplotlib.pyplot as plt
    import seaborn as sns
            import time

#!/usr/bin/env python3
"""
multi_objective_model_optimization_system.py
============================================

🎯 Multi-Objective Model Optimization System - HYPER MEDUSA NEURAL VAULT
Advanced multi-objective optimization targeting accuracy, precision, recall, and F1-score
simultaneously with Pareto frontier analysis for optimal model selection.

Features:
- NSGA-II (Non-dominated Sorting Genetic Algorithm II) implementation
- Pareto frontier analysis and visualization
- Multi-objective hyperparameter optimization
- Basketball-specific objective functions
- Advanced model selection strategies
- Comprehensive performance evaluation

Author: HYPER MEDUSA NEURAL VAULT
"""


# Advanced optimization libraries
try:
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False

try:
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False

warnings.filterwarnings('ignore')
logger = logging.getLogger(__name__)

class OptimizationObjective(Enum):
    """Multi-objective optimization objectives"""
    ACCURACY = "accuracy"
    PRECISION = "precision"
    RECALL = "recall"
    F1_SCORE = "f1_score"
    ROC_AUC = "roc_auc"
    BASKETBALL_INTELLIGENCE = "basketball_intelligence"
    MODEL_COMPLEXITY = "model_complexity"
    INFERENCE_SPEED = "inference_speed"

@dataclass
class MultiObjectiveConfig:
    """Configuration for multi-objective optimization"""
    objectives: List[OptimizationObjective] = field(default_factory=lambda: [
        OptimizationObjective.ACCURACY,
        OptimizationObjective.PRECISION,
        OptimizationObjective.RECALL,
        OptimizationObjective.F1_SCORE
    ])
    n_trials: int = 100
    n_generations: int = 50
    population_size: int = 50
    crossover_prob: float = 0.9
    mutation_prob: float = 0.1
    enable_basketball_intelligence: bool = True
    enable_pareto_visualization: bool = True
    cv_folds: int = 5
    random_state: int = 42

@dataclass
class ParetoSolution:
    """Individual solution on the Pareto frontier"""
    parameters: Dict[str, Any]
    objectives: Dict[OptimizationObjective, float]
    rank: int = 0
    crowding_distance: float = 0.0
    dominates: List[int] = field(default_factory=list)
    dominated_by: int = 0
    
    def dominates_solution(self, other: 'ParetoSolution') -> bool:
        """Check if this solution dominates another (all objectives better or equal, at least one strictly better)"""
        better_in_all = True
        strictly_better_in_one = False
        
        for obj in self.objectives:
            if obj in other.objectives:
                self_val = self.objectives[obj]
                other_val = other.objectives[obj]
                
                # For all objectives, higher is better (we convert minimization to maximization)
                if self_val < other_val:
                    better_in_all = False
                    break
                elif self_val > other_val:
                    strictly_better_in_one = True
        
        return better_in_all and strictly_better_in_one

@dataclass
class MultiObjectiveResult:
    """Result from multi-objective optimization"""
    pareto_frontier: List[ParetoSolution]
    best_compromise_solution: ParetoSolution
    optimization_history: List[Dict[str, Any]]
    convergence_metrics: Dict[str, float]
    basketball_insights: Dict[str, Any]
    execution_time: float
    total_evaluations: int

class MultiObjectiveModelOptimizationSystem:
    """
    🎯 Multi-Objective Model Optimization System
    
    Advanced multi-objective optimization system that simultaneously optimizes
    multiple performance metrics using NSGA-II algorithm and Pareto frontier analysis.
    
    Key Features:
    - NSGA-II implementation for multi-objective optimization
    - Pareto frontier identification and analysis
    - Basketball-specific objective functions
    - Advanced model selection strategies
    - Comprehensive performance evaluation
    """
    
    def __init__(self, config: Optional[MultiObjectiveConfig] = None):
        """Initialize the Multi-Objective Model Optimization System"""
        self.config = config or MultiObjectiveConfig()
        self.logger = logger.getChild(self.__class__.__name__)
        self.optimization_history = []
        self.pareto_frontier = []
        
        # Initialize random state
        np.random.seed(self.config.random_state)
        random.seed(self.config.random_state)
        
        self.logger.info(f"🎯 Multi-Objective Model Optimization System initialized")
        self.logger.info(f"🎯 Objectives: {[obj.value for obj in self.config.objectives]}")
    
    async def optimize_model(self, 
                           X: np.ndarray, 
                           y: np.ndarray,
                           model_type: str = "ensemble",
                           search_space: Optional[Dict[str, Any]] = None) -> MultiObjectiveResult:
        """
        Perform multi-objective optimization for model selection and hyperparameter tuning
        
        Args:
            X: Feature matrix
            y: Target vector
            model_type: Type of model to optimize
            search_space: Parameter search space
            
        Returns:
            MultiObjectiveResult with Pareto frontier and optimization details
        """
        start_time = datetime.now()
        self.logger.info(f"🎯 Starting multi-objective optimization for {model_type}")
        
        # Define default search space if not provided
        if search_space is None:
            search_space = self._get_default_search_space(model_type)
        
        # Initialize population
        population = await self._initialize_population(search_space)
        
        # Evaluate initial population
        evaluated_population = await self._evaluate_population(population, X, y, model_type)
        
        # Evolution loop
        for generation in range(self.config.n_generations):
            self.logger.info(f"🎯 Generation {generation + 1}/{self.config.n_generations}")
            
            # Non-dominated sorting
            fronts = self._non_dominated_sorting(evaluated_population)
            
            # Calculate crowding distance
            for front in fronts:
                self._calculate_crowding_distance(front)
            
            # Selection, crossover, and mutation
            new_population = await self._evolve_population(evaluated_population, fronts, X, y, model_type)
            evaluated_population = new_population
            
            # Track convergence
            self._track_convergence(generation, fronts)
        
        # Final non-dominated sorting to get Pareto frontier
        final_fronts = self._non_dominated_sorting(evaluated_population)
        pareto_frontier = final_fronts[0] if final_fronts else []
        
        # Select best compromise solution
        best_compromise = self._select_best_compromise_solution(pareto_frontier)
        
        # Generate basketball insights
        basketball_insights = await self._generate_basketball_insights(pareto_frontier, X, y)
        
        # Calculate execution time
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # Create result
        result = MultiObjectiveResult(
            pareto_frontier=pareto_frontier,
            best_compromise_solution=best_compromise,
            optimization_history=self.optimization_history,
            convergence_metrics=self._calculate_convergence_metrics(),
            basketball_insights=basketball_insights,
            execution_time=execution_time,
            total_evaluations=len(self.optimization_history)
        )
        
        self.logger.info(f"🎯 Multi-objective optimization completed in {execution_time:.2f}s")
        self.logger.info(f"🎯 Pareto frontier size: {len(pareto_frontier)}")
        self.logger.info(f"🎯 Best compromise solution objectives: {best_compromise.objectives}")
        
        # Generate visualization if enabled
        if self.config.enable_pareto_visualization and PLOTTING_AVAILABLE:
            await self._visualize_pareto_frontier(result)
        
        return result
    
    def _get_default_search_space(self, model_type: str) -> Dict[str, Any]:
        """Get default search space for model type"""
        if model_type == "random_forest":
            return {
                'n_estimators': (10, 200),
                'max_depth': (3, 20),
                'min_samples_split': (2, 20),
                'min_samples_leaf': (1, 10),
                'max_features': ['sqrt', 'log2', None]
            }
        elif model_type == "gradient_boosting":
            return {
                'n_estimators': (50, 300),
                'learning_rate': (0.01, 0.3),
                'max_depth': (3, 10),
                'min_samples_split': (2, 20),
                'min_samples_leaf': (1, 10)
            }
        elif model_type == "logistic_regression":
            return {
                'C': (0.001, 100.0),
                'penalty': ['l1', 'l2', 'elasticnet'],
                'solver': ['liblinear', 'saga'],
                'max_iter': (100, 1000)
            }
        else:  # ensemble
            return {
                'rf_n_estimators': (10, 200),
                'rf_max_depth': (3, 20),
                'gb_n_estimators': (50, 300),
                'gb_learning_rate': (0.01, 0.3),
                'ensemble_weights': [(0.3, 0.7), (0.5, 0.5), (0.7, 0.3)]
            }
    
    async def _initialize_population(self, search_space: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Initialize random population"""
        population = []
        
        for _ in range(self.config.population_size):
            individual = {}
            for param, bounds in search_space.items():
                if isinstance(bounds, tuple) and len(bounds) == 2:
                    if isinstance(bounds[0], (int, float)) and isinstance(bounds[1], (int, float)):
                        if isinstance(bounds[0], int) and isinstance(bounds[1], int):
                            individual[param] = np.random.randint(bounds[0], bounds[1] + 1)
                        else:
                            individual[param] = np.random.uniform(bounds[0], bounds[1])
                elif isinstance(bounds, list):
                    individual[param] = np.random.choice(bounds)
                else:
                    individual[param] = bounds
            
            population.append(individual)
        
        return population
    
    async def _evaluate_population(self, 
                                 population: List[Dict[str, Any]], 
                                 X: np.ndarray, 
                                 y: np.ndarray,
                                 model_type: str) -> List[ParetoSolution]:
        """Evaluate population and convert to ParetoSolution objects"""
        evaluated_population = []
        
        for individual in population:
            objectives = await self._evaluate_individual(individual, X, y, model_type)
            
            solution = ParetoSolution(
                parameters=individual.copy(),
                objectives=objectives
            )
            
            evaluated_population.append(solution)
        
        return evaluated_population
    
    async def _evaluate_individual(self, 
                                 parameters: Dict[str, Any], 
                                 X: np.ndarray, 
                                 y: np.ndarray,
                                 model_type: str) -> Dict[OptimizationObjective, float]:
        """Evaluate individual solution across all objectives"""
        objectives = {}
        
        try:
            # Create and train model
            model = self._create_model(model_type, parameters)
            
            # Cross-validation evaluation
            cv = StratifiedKFold(n_splits=self.config.cv_folds, shuffle=True, random_state=self.config.random_state)
            
            # Calculate each objective
            for objective in self.config.objectives:
                if objective == OptimizationObjective.ACCURACY:
                    scores = cross_val_score(model, X, y, cv=cv, scoring='accuracy')
                    objectives[objective] = np.mean(scores)
                
                elif objective == OptimizationObjective.PRECISION:
                    scores = cross_val_score(model, X, y, cv=cv, scoring='precision_weighted')
                    objectives[objective] = np.mean(scores)
                
                elif objective == OptimizationObjective.RECALL:
                    scores = cross_val_score(model, X, y, cv=cv, scoring='recall_weighted')
                    objectives[objective] = np.mean(scores)
                
                elif objective == OptimizationObjective.F1_SCORE:
                    scores = cross_val_score(model, X, y, cv=cv, scoring='f1_weighted')
                    objectives[objective] = np.mean(scores)
                
                elif objective == OptimizationObjective.ROC_AUC:
                    try:
                        scores = cross_val_score(model, X, y, cv=cv, scoring='roc_auc_ovr_weighted')
                        objectives[objective] = np.mean(scores)
                    except:
                        objectives[objective] = 0.5  # Default for multiclass issues
                
                elif objective == OptimizationObjective.BASKETBALL_INTELLIGENCE:
                    objectives[objective] = self._calculate_basketball_intelligence_score(parameters, X, y)
                
                elif objective == OptimizationObjective.MODEL_COMPLEXITY:
                    objectives[objective] = 1.0 - self._calculate_model_complexity(parameters)  # Invert for maximization
                
                elif objective == OptimizationObjective.INFERENCE_SPEED:
                    objectives[objective] = self._calculate_inference_speed_score(model, X)
            
            # Track evaluation
            self.optimization_history.append({
                'parameters': parameters.copy(),
                'objectives': objectives.copy(),
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            self.logger.warning(f"🎯 Evaluation failed for parameters {parameters}: {e}")
            # Return poor scores for failed evaluations
            for objective in self.config.objectives:
                objectives[objective] = 0.0
        
        return objectives

    def _create_model(self, model_type: str, parameters: Dict[str, Any]):
        """Create model instance with given parameters"""
        if model_type == "random_forest":
            return RandomForestClassifier(
                n_estimators=parameters.get('n_estimators', 100),
                max_depth=parameters.get('max_depth', None),
                min_samples_split=parameters.get('min_samples_split', 2),
                min_samples_leaf=parameters.get('min_samples_leaf', 1),
                max_features=parameters.get('max_features', 'sqrt'),
                random_state=self.config.random_state
            )
        elif model_type == "gradient_boosting":
            return GradientBoostingClassifier(
                n_estimators=parameters.get('n_estimators', 100),
                learning_rate=parameters.get('learning_rate', 0.1),
                max_depth=parameters.get('max_depth', 3),
                min_samples_split=parameters.get('min_samples_split', 2),
                min_samples_leaf=parameters.get('min_samples_leaf', 1),
                random_state=self.config.random_state
            )
        elif model_type == "logistic_regression":
            return LogisticRegression(
                C=parameters.get('C', 1.0),
                penalty=parameters.get('penalty', 'l2'),
                solver=parameters.get('solver', 'liblinear'),
                max_iter=parameters.get('max_iter', 1000),
                random_state=self.config.random_state
            )
        else:  # ensemble - simplified for this implementation
            return RandomForestClassifier(
                n_estimators=parameters.get('rf_n_estimators', 100),
                max_depth=parameters.get('rf_max_depth', None),
                random_state=self.config.random_state
            )

    def _calculate_basketball_intelligence_score(self, parameters: Dict[str, Any], X: np.ndarray, y: np.ndarray) -> float:
        """Calculate basketball intelligence score based on parameters and data characteristics"""
        score = 0.5  # Base score

        # Reward complexity that matches basketball data patterns
        if 'n_estimators' in parameters:
            # Basketball games have complex patterns, reward moderate complexity
            n_est = parameters['n_estimators']
            if 50 <= n_est <= 150:
                score += 0.2

        if 'max_depth' in parameters:
            # Basketball decisions have moderate depth
            depth = parameters['max_depth']
            if depth and 5 <= depth <= 15:
                score += 0.2

        # Reward ensemble approaches for basketball prediction
        if any(key.startswith('ensemble') for key in parameters.keys()):
            score += 0.1

        return min(1.0, score)

    def _calculate_model_complexity(self, parameters: Dict[str, Any]) -> float:
        """Calculate model complexity score (0-1, higher = more complex)"""
        complexity = 0.0

        if 'n_estimators' in parameters:
            complexity += min(1.0, parameters['n_estimators'] / 200.0) * 0.3

        if 'max_depth' in parameters and parameters['max_depth']:
            complexity += min(1.0, parameters['max_depth'] / 20.0) * 0.3

        if 'C' in parameters:
            # Higher C = more complex for logistic regression
            complexity += min(1.0, np.log10(parameters['C'] + 1) / 2.0) * 0.4

        return complexity

    def _calculate_inference_speed_score(self, model, X: np.ndarray) -> float:
        """Calculate inference speed score (higher = faster)"""
        try:

            # Fit model on small sample for speed test
            sample_size = min(100, len(X))
            X_sample = X[:sample_size]
            y_sample = np.random.randint(0, 2, sample_size)

            model.fit(X_sample, y_sample)

            # Time prediction
            start_time = time.time()
            _ = model.predict(X_sample)
            prediction_time = time.time() - start_time

            # Convert to score (faster = higher score)
            speed_score = max(0.0, 1.0 - prediction_time * 10)  # Penalize slow predictions
            return min(1.0, speed_score)

        except Exception:
            return 0.5  # Default score if speed test fails

    def _non_dominated_sorting(self, population: List[ParetoSolution]) -> List[List[ParetoSolution]]:
        """Perform non-dominated sorting (NSGA-II algorithm)"""
        # Reset domination info
        for solution in population:
            solution.dominates = []
            solution.dominated_by = 0

        # Calculate domination relationships
        for i, solution_i in enumerate(population):
            for j, solution_j in enumerate(population):
                if i != j:
                    if solution_i.dominates_solution(solution_j):
                        solution_i.dominates.append(j)
                    elif solution_j.dominates_solution(solution_i):
                        solution_i.dominated_by += 1

        # Create fronts
        fronts = []
        current_front = []

        # First front (non-dominated solutions)
        for i, solution in enumerate(population):
            if solution.dominated_by == 0:
                solution.rank = 0
                current_front.append(solution)

        fronts.append(current_front)

        # Subsequent fronts
        front_index = 0
        while front_index < len(fronts) and len(fronts[front_index]) > 0:
            next_front = []
            for solution in fronts[front_index]:
                for dominated_index in solution.dominates:
                    dominated_solution = population[dominated_index]
                    dominated_solution.dominated_by -= 1
                    if dominated_solution.dominated_by == 0:
                        dominated_solution.rank = front_index + 1
                        next_front.append(dominated_solution)

            if next_front:
                fronts.append(next_front)
                front_index += 1
            else:
                break

        return fronts

    def _calculate_crowding_distance(self, front: List[ParetoSolution]):
        """Calculate crowding distance for solutions in a front"""
        if len(front) <= 2:
            for solution in front:
                solution.crowding_distance = float('inf')
            return

        # Initialize distances
        for solution in front:
            solution.crowding_distance = 0.0

        # Calculate distance for each objective
        for objective in self.config.objectives:
            # Sort by objective value
            front.sort(key=lambda x: x.objectives.get(objective, 0.0))

            # Set boundary solutions to infinite distance
            front[0].crowding_distance = float('inf')
            front[-1].crowding_distance = float('inf')

            # Calculate distances for intermediate solutions
            obj_min = front[0].objectives.get(objective, 0.0)
            obj_max = front[-1].objectives.get(objective, 0.0)

            if obj_max - obj_min > 0:
                for i in range(1, len(front) - 1):
                    distance = (front[i + 1].objectives.get(objective, 0.0) -
                              front[i - 1].objectives.get(objective, 0.0)) / (obj_max - obj_min)
                    front[i].crowding_distance += distance

    async def _evolve_population(self,
                               population: List[ParetoSolution],
                               fronts: List[List[ParetoSolution]],
                               X: np.ndarray,
                               y: np.ndarray,
                               model_type: str) -> List[ParetoSolution]:
        """Evolve population using selection, crossover, and mutation"""
        # Select parents using tournament selection
        parents = self._tournament_selection(population, fronts)

        # Generate offspring through crossover and mutation
        offspring_params = []
        for i in range(0, len(parents), 2):
            if i + 1 < len(parents):
                child1, child2 = self._crossover(parents[i].parameters, parents[i + 1].parameters)
                child1 = self._mutate(child1)
                child2 = self._mutate(child2)
                offspring_params.extend([child1, child2])
            else:
                child = self._mutate(parents[i].parameters.copy())
                offspring_params.append(child)

        # Evaluate offspring
        offspring = await self._evaluate_population(offspring_params, X, y, model_type)

        # Combine parents and offspring
        combined_population = population + offspring

        # Environmental selection (select best solutions for next generation)
        return self._environmental_selection(combined_population)

    def _tournament_selection(self, population: List[ParetoSolution], fronts: List[List[ParetoSolution]]) -> List[ParetoSolution]:
        """Tournament selection based on rank and crowding distance"""
        selected = []

        for _ in range(self.config.population_size):
            # Tournament size of 2
            candidate1 = random.choice(population)
            candidate2 = random.choice(population)

            # Select based on rank first, then crowding distance
            if candidate1.rank < candidate2.rank:
                selected.append(candidate1)
            elif candidate1.rank > candidate2.rank:
                selected.append(candidate2)
            else:
                # Same rank, select based on crowding distance
                if candidate1.crowding_distance > candidate2.crowding_distance:
                    selected.append(candidate1)
                else:
                    selected.append(candidate2)

        return selected

    def _crossover(self, parent1: Dict[str, Any], parent2: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Simulated binary crossover for real parameters, uniform crossover for categorical"""
        if random.random() > self.config.crossover_prob:
            return parent1.copy(), parent2.copy()

        child1 = parent1.copy()
        child2 = parent2.copy()

        for param in parent1.keys():
            if param in parent2:
                val1 = parent1[param]
                val2 = parent2[param]

                if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    # Simulated binary crossover for numerical parameters
                    if random.random() <= 0.5:
                        beta = 2.0 * random.random()
                        if beta <= 1.0:
                            beta = beta ** (1.0 / 3.0)
                        else:
                            beta = (1.0 / (2.0 - beta)) ** (1.0 / 3.0)

                        child1[param] = 0.5 * ((1 + beta) * val1 + (1 - beta) * val2)
                        child2[param] = 0.5 * ((1 - beta) * val1 + (1 + beta) * val2)

                        # Ensure integer parameters remain integers
                        if isinstance(val1, int):
                            child1[param] = int(round(child1[param]))
                            child2[param] = int(round(child2[param]))
                else:
                    # Uniform crossover for categorical parameters
                    if random.random() <= 0.5:
                        child1[param] = val2
                        child2[param] = val1

        return child1, child2

    def _mutate(self, individual: Dict[str, Any]) -> Dict[str, Any]:
        """Polynomial mutation for real parameters, random reset for categorical"""
        if random.random() > self.config.mutation_prob:
            return individual

        mutated = individual.copy()

        for param, value in individual.items():
            if random.random() <= 0.1:  # 10% chance to mutate each parameter
                if isinstance(value, (int, float)):
                    # Polynomial mutation for numerical parameters
                    eta = 20.0  # Distribution index
                    delta = random.random()

                    if delta <= 0.5:
                        delta_q = (2.0 * delta) ** (1.0 / (eta + 1.0)) - 1.0
                    else:
                        delta_q = 1.0 - (2.0 * (1.0 - delta)) ** (1.0 / (eta + 1.0))

                    # Apply mutation with 10% perturbation
                    mutated_value = value + 0.1 * abs(value) * delta_q

                    # Ensure integer parameters remain integers
                    if isinstance(value, int):
                        mutated[param] = int(round(mutated_value))
                    else:
                        mutated[param] = mutated_value
                else:
                    # Random reset for categorical parameters
                    # This would need the original search space, simplified for now
                    pass

        return mutated

    def _environmental_selection(self, population: List[ParetoSolution]) -> List[ParetoSolution]:
        """Environmental selection to maintain population size"""
        # Non-dominated sorting
        fronts = self._non_dominated_sorting(population)

        # Calculate crowding distance for each front
        for front in fronts:
            self._calculate_crowding_distance(front)

        # Select solutions for next generation
        selected = []
        front_index = 0

        while front_index < len(fronts) and len(selected) + len(fronts[front_index]) <= self.config.population_size:
            selected.extend(fronts[front_index])
            front_index += 1

        # If we need to select some solutions from the next front
        if len(selected) < self.config.population_size and front_index < len(fronts):
            remaining_slots = self.config.population_size - len(selected)
            last_front = fronts[front_index]

            # Sort by crowding distance (descending)
            last_front.sort(key=lambda x: x.crowding_distance, reverse=True)
            selected.extend(last_front[:remaining_slots])

        return selected[:self.config.population_size]

    def _select_best_compromise_solution(self, pareto_frontier: List[ParetoSolution]) -> ParetoSolution:
        """Select best compromise solution from Pareto frontier"""
        if not pareto_frontier:
            return ParetoSolution(parameters={}, objectives={})

        # Use weighted sum approach to find compromise solution
        weights = {obj: 1.0 / len(self.config.objectives) for obj in self.config.objectives}

        best_solution = None
        best_score = -float('inf')

        for solution in pareto_frontier:
            # Calculate weighted sum score
            score = sum(weights[obj] * solution.objectives.get(obj, 0.0) for obj in self.config.objectives)

            if score > best_score:
                best_score = score
                best_solution = solution

        return best_solution or pareto_frontier[0]

    def _track_convergence(self, generation: int, fronts: List[List[ParetoSolution]]):
        """Track convergence metrics"""
        if fronts:
            pareto_size = len(fronts[0])
            avg_objectives = {}

            for obj in self.config.objectives:
                values = [sol.objectives.get(obj, 0.0) for sol in fronts[0]]
                avg_objectives[obj.value] = np.mean(values) if values else 0.0

            convergence_info = {
                'generation': generation,
                'pareto_frontier_size': pareto_size,
                'average_objectives': avg_objectives,
                'timestamp': datetime.now().isoformat()
            }

            self.optimization_history.append(convergence_info)

    def _calculate_convergence_metrics(self) -> Dict[str, float]:
        """Calculate convergence metrics from optimization history"""
        if len(self.optimization_history) < 2:
            return {'convergence_rate': 0.0, 'diversity_metric': 0.0}

        # Simple convergence metrics
        convergence_rate = 0.0
        diversity_metric = 0.0

        try:
            # Calculate improvement rate in last few generations
            recent_history = [h for h in self.optimization_history if 'average_objectives' in h]
            if len(recent_history) >= 2:
                first_gen = recent_history[0]['average_objectives']
                last_gen = recent_history[-1]['average_objectives']

                improvements = []
                for obj in self.config.objectives:
                    obj_name = obj.value
                    if obj_name in first_gen and obj_name in last_gen:
                        improvement = last_gen[obj_name] - first_gen[obj_name]
                        improvements.append(improvement)

                convergence_rate = np.mean(improvements) if improvements else 0.0
                diversity_metric = np.std(improvements) if len(improvements) > 1 else 0.0

        except Exception as e:
            self.logger.warning(f"🎯 Error calculating convergence metrics: {e}")

        return {
            'convergence_rate': convergence_rate,
            'diversity_metric': diversity_metric
        }

    async def _generate_basketball_insights(self,
                                          pareto_frontier: List[ParetoSolution],
                                          X: np.ndarray,
                                          y: np.ndarray) -> Dict[str, Any]:
        """Generate basketball-specific insights from optimization results"""
        insights = {
            'pareto_frontier_analysis': {},
            'basketball_optimization_patterns': {},
            'model_recommendations': {},
            'performance_trade_offs': {}
        }

        if not pareto_frontier:
            return insights

        try:
            # Analyze Pareto frontier
            insights['pareto_frontier_analysis'] = {
                'frontier_size': len(pareto_frontier),
                'objective_ranges': {},
                'best_single_objectives': {}
            }

            # Calculate objective ranges and best single objectives
            for obj in self.config.objectives:
                values = [sol.objectives.get(obj, 0.0) for sol in pareto_frontier]
                if values:
                    insights['pareto_frontier_analysis']['objective_ranges'][obj.value] = {
                        'min': min(values),
                        'max': max(values),
                        'mean': np.mean(values),
                        'std': np.std(values)
                    }

                    # Find best solution for this single objective
                    best_idx = np.argmax(values)
                    insights['pareto_frontier_analysis']['best_single_objectives'][obj.value] = {
                        'value': values[best_idx],
                        'parameters': pareto_frontier[best_idx].parameters
                    }

            # Basketball-specific optimization patterns
            insights['basketball_optimization_patterns'] = {
                'ensemble_preference': self._analyze_ensemble_preference(pareto_frontier),
                'complexity_vs_performance': self._analyze_complexity_performance_trade_off(pareto_frontier),
                'basketball_intelligence_correlation': self._analyze_basketball_intelligence_correlation(pareto_frontier)
            }

            # Model recommendations
            insights['model_recommendations'] = {
                'high_accuracy_model': self._get_recommendation_by_objective(pareto_frontier, OptimizationObjective.ACCURACY),
                'balanced_model': self._get_balanced_recommendation(pareto_frontier),
                'fast_inference_model': self._get_recommendation_by_objective(pareto_frontier, OptimizationObjective.INFERENCE_SPEED),
                'basketball_optimized_model': self._get_recommendation_by_objective(pareto_frontier, OptimizationObjective.BASKETBALL_INTELLIGENCE)
            }

            # Performance trade-offs
            insights['performance_trade_offs'] = self._analyze_performance_trade_offs(pareto_frontier)

        except Exception as e:
            self.logger.error(f"🎯 Error generating basketball insights: {e}")
            insights['error'] = str(e)

        return insights

    def _analyze_ensemble_preference(self, pareto_frontier: List[ParetoSolution]) -> Dict[str, Any]:
        """Analyze if ensemble methods are preferred in Pareto frontier"""
        ensemble_count = 0
        total_count = len(pareto_frontier)

        for solution in pareto_frontier:
            if any('ensemble' in str(key).lower() for key in solution.parameters.keys()):
                ensemble_count += 1

        return {
            'ensemble_solutions_count': ensemble_count,
            'ensemble_percentage': (ensemble_count / total_count * 100) if total_count > 0 else 0,
            'ensemble_preferred': ensemble_count > total_count * 0.5
        }

    def _analyze_complexity_performance_trade_off(self, pareto_frontier: List[ParetoSolution]) -> Dict[str, Any]:
        """Analyze trade-off between model complexity and performance"""
        complexity_scores = []
        performance_scores = []

        for solution in pareto_frontier:
            complexity = solution.objectives.get(OptimizationObjective.MODEL_COMPLEXITY, 0.5)
            accuracy = solution.objectives.get(OptimizationObjective.ACCURACY, 0.0)

            complexity_scores.append(1.0 - complexity)  # Convert back to complexity (higher = more complex)
            performance_scores.append(accuracy)

        correlation = 0.0
        if len(complexity_scores) > 1 and len(performance_scores) > 1:
            correlation = np.corrcoef(complexity_scores, performance_scores)[0, 1]

        return {
            'complexity_performance_correlation': correlation,
            'trade_off_strength': abs(correlation),
            'complexity_benefits_performance': correlation > 0.1
        }

    def _analyze_basketball_intelligence_correlation(self, pareto_frontier: List[ParetoSolution]) -> Dict[str, Any]:
        """Analyze correlation between basketball intelligence and other objectives"""
        correlations = {}

        basketball_scores = [sol.objectives.get(OptimizationObjective.BASKETBALL_INTELLIGENCE, 0.0) for sol in pareto_frontier]

        for obj in self.config.objectives:
            if obj != OptimizationObjective.BASKETBALL_INTELLIGENCE:
                obj_scores = [sol.objectives.get(obj, 0.0) for sol in pareto_frontier]

                if len(basketball_scores) > 1 and len(obj_scores) > 1:
                    correlation = np.corrcoef(basketball_scores, obj_scores)[0, 1]
                    correlations[obj.value] = correlation

        return {
            'objective_correlations': correlations,
            'strongest_correlation': max(correlations.items(), key=lambda x: abs(x[1])) if correlations else None,
            'basketball_intelligence_beneficial': any(corr > 0.1 for corr in correlations.values())
        }

    def _get_recommendation_by_objective(self, pareto_frontier: List[ParetoSolution], objective: OptimizationObjective) -> Dict[str, Any]:
        """Get recommendation for best solution by specific objective"""
        if not pareto_frontier:
            return {}

        best_solution = max(pareto_frontier, key=lambda x: x.objectives.get(objective, 0.0))

        return {
            'parameters': best_solution.parameters,
            'objectives': best_solution.objectives,
            'objective_value': best_solution.objectives.get(objective, 0.0)
        }

    def _get_balanced_recommendation(self, pareto_frontier: List[ParetoSolution]) -> Dict[str, Any]:
        """Get recommendation for most balanced solution"""
        if not pareto_frontier:
            return {}

        # Find solution with minimum variance across objectives
        best_solution = None
        min_variance = float('inf')

        for solution in pareto_frontier:
            objective_values = [solution.objectives.get(obj, 0.0) for obj in self.config.objectives]
            variance = np.var(objective_values)

            if variance < min_variance:
                min_variance = variance
                best_solution = solution

        return {
            'parameters': best_solution.parameters if best_solution else {},
            'objectives': best_solution.objectives if best_solution else {},
            'objective_variance': min_variance
        }

    def _analyze_performance_trade_offs(self, pareto_frontier: List[ParetoSolution]) -> Dict[str, Any]:
        """Analyze performance trade-offs in Pareto frontier"""
        trade_offs = {}

        # Analyze pairwise trade-offs between objectives
        objectives = list(self.config.objectives)

        for i, obj1 in enumerate(objectives):
            for j, obj2 in enumerate(objectives[i+1:], i+1):
                obj1_values = [sol.objectives.get(obj1, 0.0) for sol in pareto_frontier]
                obj2_values = [sol.objectives.get(obj2, 0.0) for sol in pareto_frontier]

                if len(obj1_values) > 1 and len(obj2_values) > 1:
                    correlation = np.corrcoef(obj1_values, obj2_values)[0, 1]
                    trade_offs[f"{obj1.value}_vs_{obj2.value}"] = {
                        'correlation': correlation,
                        'trade_off_exists': correlation < -0.1,
                        'synergy_exists': correlation > 0.1
                    }

        return trade_offs

    async def _visualize_pareto_frontier(self, result: MultiObjectiveResult):
        """Generate Pareto frontier visualization"""
        if not PLOTTING_AVAILABLE or len(result.pareto_frontier) == 0:
            return

        try:
            # Create visualization for 2D case (first two objectives)
            if len(self.config.objectives) >= 2:
                obj1 = self.config.objectives[0]
                obj2 = self.config.objectives[1]

                x_values = [sol.objectives.get(obj1, 0.0) for sol in result.pareto_frontier]
                y_values = [sol.objectives.get(obj2, 0.0) for sol in result.pareto_frontier]

                plt.figure(figsize=(10, 8))
                plt.scatter(x_values, y_values, c='red', s=50, alpha=0.7, label='Pareto Frontier')

                # Highlight best compromise solution
                if result.best_compromise_solution:
                    best_x = result.best_compromise_solution.objectives.get(obj1, 0.0)
                    best_y = result.best_compromise_solution.objectives.get(obj2, 0.0)
                    plt.scatter([best_x], [best_y], c='gold', s=100, marker='*', label='Best Compromise')

                plt.xlabel(f'{obj1.value.replace("_", " ").title()}')
                plt.ylabel(f'{obj2.value.replace("_", " ").title()}')
                plt.title('🎯 Multi-Objective Optimization - Pareto Frontier')
                plt.legend()
                plt.grid(True, alpha=0.3)

                # Save plot
                plt.savefig('pareto_frontier_visualization.png', dpi=300, bbox_inches='tight')
                plt.close()

                self.logger.info("🎯 Pareto frontier visualization saved as 'pareto_frontier_visualization.png'")

        except Exception as e:
            self.logger.warning(f"🎯 Error creating visualization: {e}")

# Factory function for easy instantiation
def create_multi_objective_optimizer(objectives: Optional[List[OptimizationObjective]] = None,
                                   n_trials: int = 100,
                                   enable_basketball_intelligence: bool = True) -> MultiObjectiveModelOptimizationSystem:
    """Create a Multi-Objective Model Optimization System with specified configuration"""
    config = MultiObjectiveConfig(
        objectives=objectives or [
            OptimizationObjective.ACCURACY,
            OptimizationObjective.PRECISION,
            OptimizationObjective.RECALL,
            OptimizationObjective.F1_SCORE
        ],
        n_trials=n_trials,
        enable_basketball_intelligence=enable_basketball_intelligence
    )

    return MultiObjectiveModelOptimizationSystem(config)

# Example usage and testing
async def main():
    """Example usage of Multi-Objective Model Optimization System"""
    # Create sample data
    np.random.seed(42)
    X = np.random.randn(1000, 10)
    y = np.random.randint(0, 2, 1000)

    # Create optimizer
    optimizer = create_multi_objective_optimizer(
        objectives=[
            OptimizationObjective.ACCURACY,
            OptimizationObjective.F1_SCORE,
            OptimizationObjective.BASKETBALL_INTELLIGENCE
        ],
        n_trials=50
    )

    # Run optimization
    result = await optimizer.optimize_model(X, y, model_type="random_forest")


if __name__ == "__main__":
    asyncio.run(main())
