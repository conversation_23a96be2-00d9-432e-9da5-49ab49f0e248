import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
import requests
import time
import json
from datetime import datetime
import sys
import os
from collections import defaultdict
from smart_duplicate_prevention_system import SmartDuplicatePreventionSystem


#!/usr/bin/env python3
"""
Phase 2 - Team Analytics Collection
Collect comprehensive team-level analytics with high success rates
"""


# Add the scripts directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase2TeamAnalyticsCollector:
    """Collect comprehensive team analytics with high success rates"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }
        
        # Phase 2 Team Analytics Endpoints
        self.phase2_endpoints = {
            'teamdashboardbygeneralsplits': {
                'description': 'Team general performance splits',
                'success_rate': 'HIGH',
                'params': {
                    'TeamID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0
                }
            },
            'teamdashboardbyshootingsplits': {
                'description': 'Team shooting splits and efficiency',
                'success_rate': 'HIGH',
                'params': {
                    'TeamID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0
                }
            },
            'teamplayerdashboard': {
                'description': 'Team player dashboard and rotations',
                'success_rate': 'HIGH',
                'params': {
                    'TeamID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0
                }
            },
            'teamgamelog': {
                'description': 'Team game-by-game performance logs',
                'success_rate': 'HIGH',
                'params': {
                    'TeamID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None
                }
            },
            'teamdashboardbyopponent': {
                'description': 'Team performance vs specific opponents',
                'success_rate': 'HIGH',
                'params': {
                    'TeamID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0
                }
            },
            'teamdashboardbylastngames': {
                'description': 'Team performance in recent games',
                'success_rate': 'HIGH',
                'params': {
                    'TeamID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 10
                }
            }
        }
        
        # Priority seasons
        self.seasons = {
            'NBA': ['2024-25', '2023-24', '2022-23', '2021-22', '2020-21'],
            'WNBA': ['2025', '2024', '2023', '2022', '2021']
        }
        
        # Team IDs
        self.team_ids = {
            'NBA': [
                1610612737, 1610612738, 1610612751, 1610612766, 1610612741, 1610612739,
                1610612742, 1610612743, 1610612765, 1610612744, 1610612745, 1610612754,
                1610612746, 1610612747, 1610612763, 1610612748, 1610612749, 1610612750,
                1610612740, 1610612752, 1610612760, 1610612753, 1610612755, 1610612756,
                1610612757, 1610612758, 1610612759, 1610612761, 1610612762, 1610612764
            ],
            'WNBA': [
                1611661313, 1611661314, 1611661315, 1611661316, 1611661317, 1611661318,
                1611661319, 1611661320, 1611661321, 1611661322, 1611661323, 1611661324,
                1611661325  # 13 WNBA teams including Golden State Valkyries
            ]
        }
        
        # Initialize duplicate prevention
        logger.info("🔍 Initializing Smart Duplicate Prevention System...")
        self.duplicate_prevention = SmartDuplicatePreventionSystem(db_path)
        logger.info("✅ Duplicate prevention system ready")
        
    def collect_team_endpoint_data(self, endpoint: str, team_id: int, league: str, season: str) -> List[Dict[str, Any]]:
        """Collect data from a team endpoint"""
        endpoint_config = self.phase2_endpoints[endpoint]
        
        # Check if we already have this data
        if self.duplicate_prevention.check_endpoint_data_exists(league, str(team_id), season, endpoint):
            return []
        
        collected_data = []
        
        try:
            logger.info(f"   📊 Collecting {endpoint} for team {team_id} {league} {season}...")
            
            url = f"{self.base_url}/{endpoint}"
            params = endpoint_config['params'].copy()
            
            # Set team and season parameters
            params['TeamID'] = team_id
            params['Season'] = season
            params['LeagueID'] = '00' if league == 'NBA' else '10'
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                for result_set_idx, result_set in enumerate(data['resultSets']):
                    headers = result_set.get('headers', [])
                    rows = result_set.get('rowSet', [])
                    result_set_name = result_set.get('name', f'ResultSet_{result_set_idx}')
                    
                    for row_idx, row in enumerate(rows):
                        if len(row) == len(headers):
                            row_dict = dict(zip(headers, row))
                            
                            # Create comprehensive record
                            record = {
                                'league_name': league,
                                'league_id': '00' if league == 'NBA' else '10',
                                'season': season,
                                'team_id': team_id,
                                'endpoint': endpoint,
                                'result_set_name': result_set_name,
                                'data_category': f'phase2_team_{endpoint}',
                                'data_type': f'{endpoint}_team_data',
                                'source_file': f"{endpoint}_team_{team_id}_{season}.json",
                                'source_table': endpoint,
                                'raw_data': json.dumps(row_dict),
                                'created_at': datetime.now().isoformat()
                            }
                            
                            # Extract key information
                            self._extract_team_data_fields(record, row_dict, endpoint)
                            
                            collected_data.append(record)
            
            time.sleep(0.6)  # Rate limiting
            
        except Exception as e:
            logger.warning(f"⚠️ Error collecting {endpoint} for team {team_id} {league} {season}: {e}")
        
        logger.info(f"✅ Collected {len(collected_data)} {endpoint} records for team {team_id} {league} {season}")
        return collected_data
    
    def _extract_team_data_fields(self, record: Dict[str, Any], row_dict: Dict[str, Any], endpoint: str) -> None:
        """Extract key fields from team data"""
        
        # Common extractions
        record['player_id'] = row_dict.get('PLAYER_ID') or row_dict.get('TEAM_ID') or record['team_id']
        record['player_name'] = row_dict.get('PLAYER_NAME') or row_dict.get('TEAM_NAME') or f"TEAM_{record['team_id']}"
        record['team_abbreviation'] = row_dict.get('TEAM_ABBREVIATION')
        
        # Endpoint-specific extractions
        if endpoint == 'teamdashboardbygeneralsplits':
            record['stat_category'] = 'team_general'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['games_played'] = row_dict.get('GP', 0)
            record['wins'] = row_dict.get('W', 0)
            record['losses'] = row_dict.get('L', 0)
            
        elif endpoint == 'teamdashboardbyshootingsplits':
            record['stat_category'] = 'team_shooting'
            record['stat_value'] = row_dict.get('FG_PCT', 0)
            record['field_goal_attempts'] = row_dict.get('FGA', 0)
            record['three_point_percentage'] = row_dict.get('FG3_PCT', 0)
            
        elif endpoint == 'teamplayerdashboard':
            record['stat_category'] = 'team_players'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['minutes'] = row_dict.get('MIN', 0)
            record['player_name'] = row_dict.get('PLAYER_NAME', record['player_name'])
            
        elif endpoint == 'teamgamelog':
            record['stat_category'] = 'team_games'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['game_id'] = row_dict.get('Game_ID')
            record['game_date'] = row_dict.get('GAME_DATE')
            record['matchup'] = row_dict.get('MATCHUP')
            
        elif endpoint == 'teamdashboardbyopponent':
            record['stat_category'] = 'team_vs_opponent'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['opponent_team'] = row_dict.get('VS_TEAM_ABBREVIATION')
            
        elif endpoint == 'teamdashboardbylastngames':
            record['stat_category'] = 'team_recent'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['last_n_games'] = 10
    
    def insert_team_data(self, data_batch: List[Dict[str, Any]]) -> int:
        """Insert team data with duplicate prevention"""
        if not data_batch:
            return 0
        
        # Filter duplicates
        new_records, duplicate_stats = self.duplicate_prevention.filter_new_data_only(data_batch)
        
        if duplicate_stats['duplicates'] > 0:
            logger.info(f"   🔍 Filtered out {duplicate_stats['duplicates']} duplicates, inserting {duplicate_stats['new_records']} new records")
        
        if not new_records:
            return 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        
        for record in new_records:
            try:
                cursor.execute("""
                    INSERT INTO unified_nba_wnba_data (
                        source_file, source_table, data_category, season, league_id, league_name,
                        season_type, player_id, player_name, team_id, team_abbreviation,
                        stat_category, stat_value, game_id, game_date, data_type, raw_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['source_file'], record['source_table'], record['data_category'],
                    record['season'], record['league_id'], record['league_name'],
                    'Regular Season', record['player_id'], record['player_name'],
                    record.get('team_id'), record.get('team_abbreviation'),
                    record.get('stat_category'), record.get('stat_value'),
                    record.get('game_id'), record.get('game_date'),
                    record['data_type'], record['raw_data'], record['created_at']
                ))
                inserted_count += 1
                
            except Exception as e:
                logger.warning(f"⚠️ Error inserting record: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        return inserted_count
    
    def collect_phase2_team_data(self, league: str) -> Dict[str, Any]:
        """Collect all Phase 2 team data for a league"""
        logger.info(f"🚀 PHASE 2 TEAM ANALYTICS COLLECTION - {league}")
        logger.info("=" * 65)
        
        start_time = datetime.now()
        total_records = 0
        
        for season in self.seasons[league]:
            logger.info(f"\n📅 Processing {league} {season}...")
            
            for team_id in self.team_ids[league]:
                logger.info(f"\n🏀 Processing Team {team_id}...")
                
                # Collect data from each endpoint
                for endpoint, config in self.phase2_endpoints.items():
                    endpoint_data = self.collect_team_endpoint_data(endpoint, team_id, league, season)
                    
                    if endpoint_data:
                        inserted = self.insert_team_data(endpoint_data)
                        total_records += inserted
                        if inserted > 0:
                            logger.info(f"   ✅ Inserted {inserted:,} records")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎉 PHASE 2 {league} TEAM COLLECTION COMPLETE!")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   📊 Total records: {total_records:,}")
        
        return {
            'success': True,
            'league': league,
            'duration': str(duration),
            'total_records': total_records
        }

def main():
    """Execute Phase 2 team analytics collection"""
    
    collector = Phase2TeamAnalyticsCollector()
    
    # Start with NBA
    nba_results = collector.collect_phase2_team_data('NBA')
    
    if nba_results['success']:
        
        # Then WNBA
        wnba_results = collector.collect_phase2_team_data('WNBA')
        
        if wnba_results['success']:
            
            total_records = nba_results['total_records'] + wnba_results['total_records']
            
            
    
    return nba_results

if __name__ == "__main__":
    main()
