#!/usr/bin/env python3
"""
Test EnsembleTrainingResult attribute handling and validation
"""

import asyncio
import numpy as np
import logging
from datetime import datetime
from ensemble_model_training_infrastructure import (
    EnsembleModelTrainingInfrastructure,
    EnsembleTrainingConfig,
    EnsembleTrainingResult,
    ModelPerformance
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ensemble_result_attributes():
    """Test EnsembleTrainingResult attribute handling"""
    logger.info("🧪 Testing EnsembleTrainingResult attribute handling...")
    
    # Test 1: Valid result creation
    logger.info("Test 1: Valid result creation")
    try:
        config = EnsembleTrainingConfig(
            base_models=['random_forest'],
            meta_model='logistic_regression',
            cv_folds=3,
            random_state=42
        )
        
        valid_result = EnsembleTrainingResult(
            ensemble_id="test_ensemble",
            training_timestamp=datetime.now(),
            config=config,
            base_model_performances=[
                ModelPerformance(
                    model_name="test_model",
                    accuracy=0.8, auc_roc=0.85, f1_score=0.82,
                    precision=0.8, recall=0.84, training_time=10.0,
                    prediction_time=0.1, memory_usage=100.0
                )
            ],
            ensemble_performance=ModelPerformance(
                model_name="ensemble",
                accuracy=0.85, auc_roc=0.9, f1_score=0.87,
                precision=0.85, recall=0.89, training_time=15.0,
                prediction_time=0.2, memory_usage=150.0
            ),
            model_paths={"test_model": "/path/to/model"},
            feature_engineering_stats={"feature_count": 10},
            training_summary={"total_time": 25.0},
            success=True
        )
        
        # Test validation
        is_valid = valid_result.validate()
        logger.info(f"✅ Valid result validation: {is_valid}")
        
        # Test attribute access
        logger.info(f"✅ Ensemble ID: {valid_result.ensemble_id}")
        logger.info(f"✅ Success: {valid_result.success}")
        logger.info(f"✅ Base model count: {valid_result.training_summary['base_model_count']}")
        
    except Exception as e:
        logger.error(f"❌ Valid result test failed: {e}")
        return False
    
    # Test 2: Result with missing attributes (should be handled by __post_init__)
    logger.info("\nTest 2: Result with minimal attributes")
    try:
        minimal_result = EnsembleTrainingResult(
            ensemble_id="minimal",
            training_timestamp=datetime.now(),
            config=config,
            base_model_performances=[],  # Empty list
            ensemble_performance=ModelPerformance(
                model_name="minimal",
                accuracy=0.0, auc_roc=0.0, f1_score=0.0,
                precision=0.0, recall=0.0, training_time=0.0,
                prediction_time=0.0, memory_usage=0.0
            ),
            model_paths={},  # Empty dict
            feature_engineering_stats={},  # Empty dict
            training_summary={},  # Empty dict - should be filled by __post_init__
            success=False
        )
        
        # Check that __post_init__ filled in missing training_summary keys
        required_keys = [
            'total_training_time', 'successful_base_models', 'ensemble_improvement',
            'target_accuracy_achieved', 'target_auc_achieved', 'base_model_count',
            'feature_count', 'target_type'
        ]
        
        for key in required_keys:
            if key not in minimal_result.training_summary:
                logger.error(f"❌ Missing required key in training_summary: {key}")
                return False
            else:
                logger.info(f"✅ Found required key: {key} = {minimal_result.training_summary[key]}")
        
        is_valid = minimal_result.validate()
        logger.info(f"✅ Minimal result validation: {is_valid}")
        
    except Exception as e:
        logger.error(f"❌ Minimal result test failed: {e}")
        return False
    
    # Test 3: Actual ensemble training with error handling
    logger.info("\nTest 3: Actual ensemble training with small dataset")
    try:
        # Create small test dataset
        np.random.seed(42)
        X = np.random.randn(50, 5)  # Small dataset to potentially trigger errors
        y = np.random.randint(0, 2, 50)
        
        config = EnsembleTrainingConfig(
            base_models=['random_forest'],
            meta_model='logistic_regression',
            cv_folds=2,  # Small CV to speed up test
            random_state=42,
            target_accuracy=0.6
        )
        
        ensemble = EnsembleModelTrainingInfrastructure(config)
        result = await ensemble.train_ensemble(X, y)
        
        # Test that result has all expected attributes
        logger.info(f"✅ Training result success: {result.success}")
        logger.info(f"✅ Training result ensemble_id: {result.ensemble_id}")
        logger.info(f"✅ Training result has {len(result.base_model_performances)} base models")
        
        # Test validation
        is_valid = result.validate()
        logger.info(f"✅ Training result validation: {is_valid}")
        
        # Test all training_summary keys are present
        for key in required_keys:
            if key not in result.training_summary:
                logger.error(f"❌ Missing key in actual training result: {key}")
                return False
            else:
                logger.info(f"✅ Training result has key: {key} = {result.training_summary[key]}")
        
    except Exception as e:
        logger.error(f"❌ Actual training test failed: {e}")
        return False
    
    logger.info("🎯 All EnsembleTrainingResult attribute tests passed!")
    return True

async def main():
    """Run all tests"""
    success = await test_ensemble_result_attributes()
    if success:
        logger.info("🏆 ALL TESTS PASSED - EnsembleTrainingResult attribute issues are FIXED!")
    else:
        logger.error("❌ TESTS FAILED - EnsembleTrainingResult still has attribute issues")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
