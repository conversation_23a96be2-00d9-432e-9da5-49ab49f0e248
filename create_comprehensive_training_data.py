#!/usr/bin/env python3
"""
Comprehensive Training Data Creator for HYPER_MEDUSA_NEURAL_VAULT

This script aggregates all 795 CSV files into comprehensive training datasets
that utilize the full 4.9 million records for optimal model training.

Key Features:
- Processes all NBA (league_id 00) and WNBA (league_id 10) CSV files
- Maintains team diversity and proper data structure
- Creates comprehensive training datasets for neural training pipeline
- Replaces limited 1,000-record training files with full datasets
"""

import os
import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import List, Dict, Tuple
import glob
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveDataAggregator:
    """Aggregates all basketball CSV files into comprehensive training datasets"""
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.output_dir = Path("data/ml_training")
        self.output_dir.mkdir(exist_ok=True)
        
        # Track statistics
        self.stats = {
            'nba_files': 0,
            'wnba_files': 0,
            'nba_records': 0,
            'wnba_records': 0,
            'nba_teams': set(),
            'wnba_teams': set()
        }
    
    def discover_all_csv_files(self) -> Dict[str, List[Path]]:
        """Discover all basketball CSV files organized by league"""
        logger.info("🔍 Discovering all basketball CSV files...")
        
        # Find all CSV files in data directory
        all_csv_files = list(self.data_dir.glob("**/*.csv"))
        logger.info(f"📁 Found {len(all_csv_files)} total CSV files")
        
        nba_files = []
        wnba_files = []
        
        for csv_file in all_csv_files:
            filename = csv_file.name
            
            # NBA files (league_id 00)
            if "_00_" in filename or filename.endswith("_00.csv"):
                nba_files.append(csv_file)
            # WNBA files (league_id 10) 
            elif "_10_" in filename or filename.endswith("_10.csv") or "wnba_" in filename.lower():
                wnba_files.append(csv_file)
            # Player stats files with year patterns
            elif "player_stats_" in filename:
                if "_00_" in filename:
                    nba_files.append(csv_file)
                elif "_10_" in filename:
                    wnba_files.append(csv_file)
        
        logger.info(f"🏀 NBA files: {len(nba_files)}")
        logger.info(f"🏀 WNBA files: {len(wnba_files)}")
        
        return {"NBA": nba_files, "WNBA": wnba_files}
    
    def process_csv_file(self, csv_file: Path, league: str) -> pd.DataFrame:
        """Process a single CSV file and standardize columns"""
        try:
            df = pd.read_csv(csv_file)
            
            if df.empty:
                return pd.DataFrame()
            
            # Standardize team abbreviation column
            team_cols = ['TEAM_ABBREVIATION', 'team_abbreviation', 'TEAM_ABBREV', 'team_abbrev']
            team_col = None
            for col in team_cols:
                if col in df.columns:
                    team_col = col
                    break
            
            if team_col and team_col != 'TEAM_ABBREVIATION':
                df = df.rename(columns={team_col: 'TEAM_ABBREVIATION'})
            
            # Add league identifier
            df['LEAGUE'] = league
            df['SOURCE_FILE'] = csv_file.name
            
            # Track teams
            if 'TEAM_ABBREVIATION' in df.columns:
                teams = set(df['TEAM_ABBREVIATION'].dropna().unique())
                if league == "NBA":
                    self.stats['nba_teams'].update(teams)
                else:
                    self.stats['wnba_teams'].update(teams)
            
            logger.info(f"✅ {csv_file.name}: {len(df)} records")
            return df
            
        except Exception as e:
            logger.error(f"❌ Error processing {csv_file}: {e}")
            return pd.DataFrame()
    
    def aggregate_league_data(self, files: List[Path], league: str) -> pd.DataFrame:
        """Aggregate all files for a specific league"""
        logger.info(f"🔄 Aggregating {league} data from {len(files)} files...")
        
        all_dataframes = []
        total_records = 0
        
        for csv_file in files:
            df = self.process_csv_file(csv_file, league)
            if not df.empty:
                all_dataframes.append(df)
                total_records += len(df)
                
                if league == "NBA":
                    self.stats['nba_files'] += 1
                    self.stats['nba_records'] += len(df)
                else:
                    self.stats['wnba_files'] += 1
                    self.stats['wnba_records'] += len(df)
        
        if not all_dataframes:
            logger.warning(f"⚠️ No valid data found for {league}")
            return pd.DataFrame()
        
        # Combine all dataframes
        combined_df = pd.concat(all_dataframes, ignore_index=True, sort=False)
        
        # Remove duplicates
        initial_count = len(combined_df)
        combined_df = combined_df.drop_duplicates()
        final_count = len(combined_df)
        
        logger.info(f"📊 {league} aggregation complete:")
        logger.info(f"   📁 Files processed: {len([df for df in all_dataframes if not df.empty])}")
        logger.info(f"   📈 Total records: {final_count:,}")
        logger.info(f"   🗑️ Duplicates removed: {initial_count - final_count:,}")
        
        return combined_df
    
    def create_comprehensive_datasets(self):
        """Create comprehensive training datasets from all CSV files"""
        logger.info("🚀 Creating Comprehensive Training Datasets")
        logger.info("=" * 60)
        
        # Discover all files
        files_by_league = self.discover_all_csv_files()
        
        # Process each league
        for league, files in files_by_league.items():
            if not files:
                logger.warning(f"⚠️ No files found for {league}")
                continue
            
            # Aggregate data
            combined_data = self.aggregate_league_data(files, league)
            
            if combined_data.empty:
                logger.warning(f"⚠️ No data aggregated for {league}")
                continue
            
            # Save comprehensive training data
            output_file = self.output_dir / f"{league.lower()}_training_data.csv"
            combined_data.to_csv(output_file, index=False)
            
            logger.info(f"💾 Saved {league} training data: {output_file}")
            logger.info(f"   📊 Records: {len(combined_data):,}")
            
            if 'TEAM_ABBREVIATION' in combined_data.columns:
                unique_teams = combined_data['TEAM_ABBREVIATION'].nunique()
                logger.info(f"   🏀 Unique teams: {unique_teams}")
        
        # Print final statistics
        self.print_final_statistics()
    
    def print_final_statistics(self):
        """Print comprehensive statistics"""
        logger.info("\n" + "=" * 60)
        logger.info("📈 COMPREHENSIVE TRAINING DATA STATISTICS")
        logger.info("=" * 60)
        
        logger.info(f"🏀 NBA:")
        logger.info(f"   📁 Files: {self.stats['nba_files']}")
        logger.info(f"   📊 Records: {self.stats['nba_records']:,}")
        logger.info(f"   🏀 Teams: {len(self.stats['nba_teams'])}")
        if self.stats['nba_teams']:
            logger.info(f"   🏀 Team List: {sorted(list(self.stats['nba_teams']))[:10]}...")
        
        logger.info(f"\n🏀 WNBA:")
        logger.info(f"   📁 Files: {self.stats['wnba_files']}")
        logger.info(f"   📊 Records: {self.stats['wnba_records']:,}")
        logger.info(f"   🏀 Teams: {len(self.stats['wnba_teams'])}")
        if self.stats['wnba_teams']:
            logger.info(f"   🏀 Team List: {sorted(list(self.stats['wnba_teams']))}")
        
        total_records = self.stats['nba_records'] + self.stats['wnba_records']
        total_files = self.stats['nba_files'] + self.stats['wnba_files']
        
        logger.info(f"\n📊 TOTAL:")
        logger.info(f"   📁 Files: {total_files}")
        logger.info(f"   📊 Records: {total_records:,}")
        logger.info(f"   🏀 Total Teams: {len(self.stats['nba_teams']) + len(self.stats['wnba_teams'])}")

def main():
    """Main execution function"""
    logger.info("🎯 HYPER_MEDUSA_NEURAL_VAULT: Comprehensive Training Data Creator")
    logger.info("🎯 Utilizing Full 4.9M Record Dataset for Maximum Model Performance")
    
    aggregator = ComprehensiveDataAggregator()
    aggregator.create_comprehensive_datasets()
    
    logger.info("\n✅ Comprehensive training datasets created successfully!")
    logger.info("🚀 Ready for full dataset neural training with maximum team diversity!")

if __name__ == "__main__":
    main()
