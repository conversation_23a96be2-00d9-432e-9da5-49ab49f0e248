#!/usr/bin/env python3
"""
Prepare Prediction API for WNBA Training Completion
"""

import sys
import asyncio
from pathlib import Path
from datetime import datetime

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

try:
    from backend.services.ml_prediction_service import MLPredictionService
    from src.api.prediction_api import app as prediction_app
    from src.api.ml_prediction_api import app as ml_prediction_app
    print("✅ Prediction services imported successfully")
except ImportError as e:
    print(f"⚠️ Import error: {e}")

async def test_prediction_services():
    """Test prediction services readiness"""
    print("🧪 TESTING PREDICTION SERVICES")
    print("=" * 40)
    
    try:
        # Test ML Prediction Service
        ml_service = MLPredictionService()
        print("✅ MLPredictionService initialized")
        
        # Test sample prediction
        sample_game = {
            "home_team": "Las Vegas Aces",
            "away_team": "New York Liberty",
            "league": "WNBA",
            "game_date": "2024-07-05",
            "season": "2024"
        }
        
        print(f"🏀 Testing prediction: {sample_game['away_team']} @ {sample_game['home_team']}")
        
        # This will use fallback logic until models are trained
        prediction = await ml_service.predict_game(sample_game)
        
        print("✅ PREDICTION SERVICE WORKING!")
        print(f"🎯 Result: {prediction}")
        
        return True
        
    except Exception as e:
        print(f"⚠️ Prediction service test failed: {e}")
        print("🔄 Will use fallback predictions until training completes")
        return False

def check_api_endpoints():
    """Check available API endpoints"""
    print("\n🌐 CHECKING API ENDPOINTS")
    print("=" * 40)
    
    endpoints = [
        "src/api/prediction_api.py",
        "src/api/ml_prediction_api.py", 
        "backend/routers/predictions.py",
        "backend/api/expert_endpoints.py"
    ]
    
    available_endpoints = []
    
    for endpoint in endpoints:
        if Path(endpoint).exists():
            available_endpoints.append(endpoint)
            print(f"✅ {endpoint}")
        else:
            print(f"❌ {endpoint}")
    
    print(f"\n📊 Available endpoints: {len(available_endpoints)}/{len(endpoints)}")
    return available_endpoints

def prepare_wnba_prediction_config():
    """Prepare WNBA-specific prediction configuration"""
    print("\n⚙️ PREPARING WNBA PREDICTION CONFIG")
    print("=" * 40)
    
    wnba_config = {
        "league": "WNBA",
        "active_season": True,
        "model_path": "./models/neural_core/wnba",
        "data_source": "data/ml_training/wnba_training_data.csv",
        "teams": [
            "Las Vegas Aces", "New York Liberty", "Connecticut Sun",
            "Seattle Storm", "Minnesota Lynx", "Indiana Fever",
            "Phoenix Mercury", "Chicago Sky", "Atlanta Dream",
            "Washington Mystics", "Dallas Wings", "Los Angeles Sparks"
        ],
        "prediction_types": [
            "game_winner", "point_spread", "total_points",
            "player_props", "live_betting"
        ],
        "confidence_threshold": 0.75,
        "api_endpoints": {
            "game_prediction": "/api/vault/v1/predict/game",
            "player_props": "/api/vault/v1/predict/player-prop", 
            "tonight_games": "/predict/tonight/WNBA",
            "batch_predictions": "/predict/batch"
        }
    }
    
    print("✅ WNBA Configuration prepared:")
    print(f"🏀 Teams: {len(wnba_config['teams'])} WNBA teams")
    print(f"🎯 Prediction types: {len(wnba_config['prediction_types'])}")
    print(f"🌐 API endpoints: {len(wnba_config['api_endpoints'])}")
    
    return wnba_config

async def prepare_live_prediction_demo():
    """Prepare live prediction demonstration"""
    print("\n🎯 PREPARING LIVE PREDICTION DEMO")
    print("=" * 40)
    
    # Sample WNBA games for testing
    sample_games = [
        {
            "home_team": "Las Vegas Aces",
            "away_team": "New York Liberty", 
            "game_date": "2024-07-05",
            "league": "WNBA"
        },
        {
            "home_team": "Connecticut Sun",
            "away_team": "Seattle Storm",
            "game_date": "2024-07-05", 
            "league": "WNBA"
        },
        {
            "home_team": "Minnesota Lynx",
            "away_team": "Indiana Fever",
            "game_date": "2024-07-05",
            "league": "WNBA"
        }
    ]
    
    print("🏀 Sample games prepared for testing:")
    for i, game in enumerate(sample_games, 1):
        print(f"   {i}. {game['away_team']} @ {game['home_team']}")
    
    return sample_games

async def main():
    """Main preparation function"""
    print("🚀 PREPARING PREDICTION API FOR WNBA TRAINING COMPLETION")
    print("🔥 Getting ready for live WNBA predictions!")
    print("=" * 60)
    
    # Test prediction services
    services_ready = await test_prediction_services()
    
    # Check API endpoints
    available_endpoints = check_api_endpoints()
    
    # Prepare WNBA config
    wnba_config = prepare_wnba_prediction_config()
    
    # Prepare demo games
    demo_games = await prepare_live_prediction_demo()
    
    print("\n" + "="*60)
    print("📋 PREDICTION API PREPARATION SUMMARY")
    print("="*60)
    print(f"✅ Prediction Services: {'Ready' if services_ready else 'Fallback Mode'}")
    print(f"✅ API Endpoints: {len(available_endpoints)} available")
    print(f"✅ WNBA Configuration: Prepared")
    print(f"✅ Demo Games: {len(demo_games)} ready for testing")
    print(f"✅ Training Status: WNBA model training in progress")
    
    print("\n🎯 NEXT STEPS:")
    print("1. ⏳ Wait for WNBA training to complete (~60 minutes)")
    print("2. 🧪 Test predictions with trained WNBA model")
    print("3. 🌐 Launch prediction API server")
    print("4. 🏀 Start making live WNBA predictions!")
    
    print("\n👑 THE QUEEN WILL BE READY FOR WNBA SEASON!")

if __name__ == "__main__":
    asyncio.run(main())
