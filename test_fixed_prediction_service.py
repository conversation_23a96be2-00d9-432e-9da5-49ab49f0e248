#!/usr/bin/env python3
"""
Test the fixed unified neural prediction service
"""

import sys
sys.path.append('.')

import asyncio
import numpy as np
from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

async def test_fixed_predictions():
    """Test the fixed prediction service"""
    print("🧪 TESTING FIXED UNIFIED NEURAL PREDICTION SERVICE")
    print("=" * 60)
    
    # Initialize service
    service = UnifiedNeuralPredictionService()
    
    # Load models
    print("📥 Loading models...")
    await service.initialize()
    
    # Test game data
    game_data = {
        'home_team': 'Las Vegas Aces',
        'away_team': 'Seattle Storm',
        'date': '2025-07-05',
        'season': '2025'
    }
    
    # Test players with different tiers
    test_players = [
        {
            'name': "A'ja <PERSON>",
            'team': 'Las Vegas Aces',
            'position': 'C',
            'tier': 1  # Elite
        },
        {
            'name': "<PERSON>", 
            'team': 'New York Liberty',
            'position': 'G',
            'tier': 1  # Elite
        },
        {
            'name': "<PERSON>",
            'team': 'Las Vegas Aces', 
            'position': 'G',
            'tier': 2  # Good
        },
        {
            'name': "Bench Player",
            'team': 'Seattle Storm',
            'position': 'F',
            'tier': 4  # Bench
        }
    ]
    
    print(f"🎯 Testing predictions for {len(test_players)} players...")
    
    # Get unified predictions
    result = await service.predict_unified(game_data, test_players)
    
    print("\n" + "=" * 60)
    print("🏀 GAME OUTCOME PREDICTION")
    print("=" * 60)
    print(f"Home Team Win Probability: {result.home_win_probability:.1%}")
    print(f"Away Team Win Probability: {result.away_win_probability:.1%}")
    print(f"Predicted Spread: {result.predicted_spread:.1f}")
    print(f"Predicted Total: {result.predicted_total:.1f}")
    print(f"Confidence: {result.game_confidence:.1%}")
    
    print("\n" + "=" * 60)
    print("👤 PLAYER PROPS PREDICTIONS")
    print("=" * 60)
    
    for player_name, props in result.player_props.items():
        print(f"\n🏀 {player_name}")
        print("-" * 40)
        
        # Find player tier for context
        player_tier = next((p['tier'] for p in test_players if p['name'] == player_name), 3)
        tier_names = {1: "Elite", 2: "Good", 3: "Average", 4: "Bench"}
        print(f"   Tier: {tier_names.get(player_tier, 'Unknown')}")
        
        for prop_type, prediction in props.items():
            confidence = result.props_confidence[player_name][prop_type]
            print(f"   {prop_type.capitalize():<8}: {prediction:>5.1f} (confidence: {confidence:.1%})")
    
    print("\n" + "=" * 60)
    print("🔍 ANALYSIS")
    print("=" * 60)
    
    # Check for identical predictions (the original problem)
    points_predictions = [props.get('points', 0) for props in result.player_props.values()]
    rebounds_predictions = [props.get('rebounds', 0) for props in result.player_props.values()]
    
    points_unique = len(set(f"{p:.1f}" for p in points_predictions))
    rebounds_unique = len(set(f"{r:.1f}" for r in rebounds_predictions))
    
    print(f"Points predictions diversity: {points_unique}/{len(points_predictions)} unique values")
    print(f"Rebounds predictions diversity: {rebounds_unique}/{len(rebounds_predictions)} unique values")
    
    if points_unique == 1:
        print("❌ STILL IDENTICAL POINTS PREDICTIONS!")
    else:
        print("✅ Points predictions are now diverse")
        
    if rebounds_unique == 1:
        print("❌ STILL IDENTICAL REBOUNDS PREDICTIONS!")
    else:
        print("✅ Rebounds predictions are now diverse")
    
    # Check confidence diversity
    all_confidences = []
    for player_confidences in result.props_confidence.values():
        all_confidences.extend(player_confidences.values())
    
    confidence_unique = len(set(f"{c:.2f}" for c in all_confidences))
    print(f"Confidence diversity: {confidence_unique}/{len(all_confidences)} unique values")
    
    if confidence_unique == 1:
        print("❌ STILL IDENTICAL CONFIDENCE VALUES!")
    else:
        print("✅ Confidence values are now diverse")
    
    print(f"\nConfidence range: {min(all_confidences):.1%} - {max(all_confidences):.1%}")

if __name__ == "__main__":
    asyncio.run(test_fixed_predictions())
