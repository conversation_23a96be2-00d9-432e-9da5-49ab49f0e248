import os
import sys
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

#!/usr/bin/env python3
"""
Legacy Cleanup Executor - HYPER MEDUSA NEURAL VAULT
==================================================

Execute targeted cleanup of legacy systems based on analysis results.
Focus on high-impact, low-risk cleanup first.
"""


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LegacyCleanupExecutor:
    """Execute safe cleanup of legacy systems"""
    
    def __init__(self, dry_run: bool = True):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.dry_run = dry_run
        self.project_root = Path(".")
        self.backup_dir = Path("legacy_backup") / datetime.now().strftime("%Y%m%d_%H%M%S")
        self.cleanup_stats = {
            'files_removed': 0,
            'files_migrated': 0,
            'files_backed_up': 0,
            'space_freed': 0
        }
    
    def execute_priority_cleanup(self) -> Dict[str, Any]:
        """Execute high-priority, low-risk cleanup"""
        self.logger.info("🧹 EXECUTING PRIORITY LEGACY CLEANUP")
        self.logger.info("=" * 45)
        
        if self.dry_run:
            self.logger.info("🔍 DRY RUN MODE - No files will be actually removed")
        
        # Phase 1: Remove deprecated files
        self._cleanup_deprecated_files()
        
        # Phase 2: Remove empty files
        self._cleanup_empty_files()
        
        # Phase 3: Remove obsolete scripts
        self._cleanup_obsolete_scripts()
        
        # Phase 4: Migrate legacy collectors
        self._migrate_legacy_collectors()
        
        # Phase 5: Clean up duplicate test files
        self._cleanup_duplicate_tests()
        
        return self.cleanup_stats
    
    def _cleanup_deprecated_files(self):
        """Remove explicitly deprecated files"""
        self.logger.info("\n📁 CLEANING UP DEPRECATED FILES")
        self.logger.info("=" * 35)
        
        deprecated_files = [
            "DEPRECATED_FILES_REMOVED.txt",
            "deprecated_*",
            "*_deprecated.*"
        ]
        
        for pattern in deprecated_files:
            for file_path in self.project_root.glob(pattern):
                self._safe_remove_file(file_path, "Deprecated file")
    
    def _cleanup_empty_files(self):
        """Remove empty or nearly empty files"""
        self.logger.info("\n📄 CLEANING UP EMPTY FILES")
        self.logger.info("=" * 30)
        
        # Focus on obviously empty files first
        empty_patterns = [
            "**/*.csv",  # Empty CSV files
            "**/*.py"    # Empty Python files
        ]
        
        for pattern in empty_patterns:
            for file_path in self.project_root.glob(pattern):
                if self._is_safe_empty_file(file_path):
                    self._safe_remove_file(file_path, "Empty file")
    
    def _cleanup_obsolete_scripts(self):
        """Remove obsolete one-time scripts"""
        self.logger.info("\n🛠️ CLEANING UP OBSOLETE SCRIPTS")
        self.logger.info("=" * 35)
        
        obsolete_scripts = [
            "scripts/remove_empty_files.py",
            "scripts/validate_no_duplicates.py", 
            "scripts/comprehensive_data_ingestion_verification.py",
            "tools/find_csvs.py"
        ]
        
        for script_path in obsolete_scripts:
            file_path = self.project_root / script_path
            if file_path.exists():
                self._safe_remove_file(file_path, "Obsolete script")
    
    def _migrate_legacy_collectors(self):
        """Migrate legacy data collectors"""
        self.logger.info("\n🔄 MIGRATING LEGACY COLLECTORS")
        self.logger.info("=" * 35)
        
        legacy_collectors = [
            "smart_10year_collector.py",
            "smart_incremental_collector.py"
        ]
        
        for collector_name in legacy_collectors:
            collector_path = self.project_root / collector_name
            if collector_path.exists():
                self._migrate_collector(collector_path)
    
    def _cleanup_duplicate_tests(self):
        """Clean up duplicate test files"""
        self.logger.info("\n🧪 CLEANING UP DUPLICATE TESTS")
        self.logger.info("=" * 35)
        
        # Find test files in non-standard locations
        test_patterns = [
            "test_*.py",
            "*_test.py"
        ]
        
        for pattern in test_patterns:
            for test_file in self.project_root.rglob(pattern):
                # Keep tests in proper test directories, remove others
                if not self._is_in_proper_test_location(test_file):
                    self._safe_remove_file(test_file, "Misplaced test file")
    
    def _safe_remove_file(self, file_path: Path, reason: str):
        """Safely remove a file with backup"""
        try:
            if not file_path.exists():
                return
            
            file_size = file_path.stat().st_size
            
            if self.dry_run:
                self.logger.info(f"🔍 Would remove: {file_path} ({reason}) - {file_size} bytes")
                self.cleanup_stats['files_removed'] += 1
                self.cleanup_stats['space_freed'] += file_size
                return
            
            # Create backup if file is not empty
            if file_size > 0:
                self._backup_file(file_path)
            
            # Remove the file
            file_path.unlink()
            
            self.logger.info(f"✅ Removed: {file_path} ({reason}) - {file_size} bytes")
            self.cleanup_stats['files_removed'] += 1
            self.cleanup_stats['space_freed'] += file_size
            
        except Exception as e:
            self.logger.error(f"❌ Failed to remove {file_path}: {e}")
    
    def _backup_file(self, file_path: Path):
        """Create backup of file before removal"""
        try:
            # Create backup directory if needed
            self.backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Create backup with relative path structure
            relative_path = file_path.relative_to(self.project_root)
            backup_path = self.backup_dir / relative_path
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Copy file to backup
            shutil.copy2(file_path, backup_path)
            self.cleanup_stats['files_backed_up'] += 1
            
        except Exception as e:
            self.logger.warning(f"⚠️ Failed to backup {file_path}: {e}")
    
    def _migrate_collector(self, collector_path: Path):
        """Migrate a legacy collector to new system"""
        try:
            migration_note = f"""
# MIGRATION NOTICE - {datetime.now().strftime('%Y-%m-%d')}
# This legacy collector has been superseded by:
# src/integrations/live_realtime_data_integrator.py
# 
# The new system provides:
# - Real-time data integration
# - Live NBA/WNBA API support
# - Unified prediction pipeline
# - Better error handling and performance
#
# Original file backed up to: {self.backup_dir / collector_path.name}
"""
            
            if self.dry_run:
                self.logger.info(f"🔍 Would migrate: {collector_path}")
                self.cleanup_stats['files_migrated'] += 1
                return
            
            # Backup original
            self._backup_file(collector_path)
            
            # Replace with migration notice
            collector_path.write_text(migration_note)
            
            self.logger.info(f"✅ Migrated: {collector_path}")
            self.cleanup_stats['files_migrated'] += 1
            
        except Exception as e:
            self.logger.error(f"❌ Failed to migrate {collector_path}: {e}")
    
    def _is_safe_empty_file(self, file_path: Path) -> bool:
        """Check if file is safe to remove (empty and not important)"""
        try:
            # Skip if file is too large (not empty)
            if file_path.stat().st_size > 100:
                return False
            
            # Skip important files even if empty
            important_patterns = [
                '__init__.py',
                'requirements.txt',
                'README',
                'LICENSE',
                '.env',
                'config'
            ]
            
            filename = file_path.name.lower()
            if any(pattern.lower() in filename for pattern in important_patterns):
                return False
            
            # Skip files in important directories
            important_dirs = [
                'src',
                'backend', 
                'vault_oracle',
                '.git',
                '.venv'
            ]
            
            path_str = str(file_path).lower()
            if any(f"/{dir_name}/" in path_str or f"\\{dir_name}\\" in path_str for dir_name in important_dirs):
                return False
            
            return True
            
        except:
            return False
    
    def _is_in_proper_test_location(self, test_file: Path) -> bool:
        """Check if test file is in proper test directory"""
        proper_test_dirs = [
            'tests',
            'test',
            'testing'
        ]
        
        path_parts = test_file.parts
        return any(test_dir in path_parts for test_dir in proper_test_dirs)
    
    def generate_cleanup_report(self) -> str:
        """Generate cleanup report"""
        report = f"""
# Legacy Cleanup Report - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Cleanup Statistics
- Files removed: {self.cleanup_stats['files_removed']:,}
- Files migrated: {self.cleanup_stats['files_migrated']:,}
- Files backed up: {self.cleanup_stats['files_backed_up']:,}
- Space freed: {self.cleanup_stats['space_freed'] / 1024 / 1024:.2f} MB

## Actions Taken
1. ✅ Removed deprecated files
2. ✅ Cleaned up empty files
3. ✅ Removed obsolete scripts
4. ✅ Migrated legacy collectors
5. ✅ Cleaned up duplicate tests

## Backup Location
All removed files backed up to: {self.backup_dir}

## Next Steps
1. Review migration of remaining legacy components
2. Update documentation references
3. Test system functionality after cleanup
4. Remove backup files after verification (optional)

## Migration Status
- Legacy data collectors: ✅ Migrated to live_realtime_data_integrator.py
- Obsolete scripts: ✅ Removed
- Empty files: ✅ Cleaned up
- Duplicate tests: ✅ Consolidated
"""
        return report

def main():
    """Execute legacy cleanup with user confirmation"""
    
    # First run in dry-run mode
    executor = LegacyCleanupExecutor(dry_run=True)
    stats = executor.execute_priority_cleanup()
    
    
    # Ask for confirmation
    response = input(f"\n❓ Proceed with actual cleanup? (y/N): ").strip().lower()
    
    if response == 'y':
        executor = LegacyCleanupExecutor(dry_run=False)
        final_stats = executor.execute_priority_cleanup()
        
        # Generate report
        report = executor.generate_cleanup_report()
        
        # Save report
        report_path = Path("legacy_cleanup_report.md")
        report_path.write_text(report)
        
        
        return final_stats
    else:
        return stats

if __name__ == "__main__":
    main()
