import json
import logging
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np
    import sys


#!/usr/bin/env python3
"""
ENHANCED VALIDATION SYSTEM
==========================

Advanced validation system to achieve and exceed 65% baseline accuracy.
Includes confidence-based filtering, model ensemble validation, and accuracy optimization.
"""


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("ENHANCED_VALIDATION")

@dataclass
class EnhancedValidationMetrics:
    """Enhanced validation metrics with confidence analysis"""
    total_predictions: int
    correct_predictions: int
    accuracy: float
    high_confidence_accuracy: float
    medium_confidence_accuracy: float
    low_confidence_accuracy: float
    confidence_threshold_high: float = 0.75
    confidence_threshold_medium: float = 0.60
    baseline_target: float = 0.65
    
    def baseline_achieved(self) -> bool:
        return self.accuracy >= self.baseline_target
    
    def get_optimization_recommendations(self) -> List[str]:
        """Get recommendations to improve accuracy"""
        recommendations = []
        
        if self.high_confidence_accuracy < self.baseline_target:
            recommendations.append("🔧 High-confidence predictions underperforming - review model calibration")
        
        if self.medium_confidence_accuracy > self.high_confidence_accuracy:
            recommendations.append("⚡ Medium-confidence predictions outperforming high - adjust confidence thresholds")
        
        if self.low_confidence_accuracy > 0.5:
            recommendations.append("📈 Low-confidence predictions showing edge - consider ensemble weighting")
        
        if self.accuracy < self.baseline_target:
            deficit = (self.baseline_target - self.accuracy) * 100
            recommendations.append(f"🎯 Need {deficit:.1f}% improvement to reach baseline")
        
        return recommendations

class EnhancedValidationSystem:
    """Enhanced validation system with optimization capabilities"""
    
    def __init__(self, database_path: str = "medusa_master.db"):
        self.database_path = database_path
        self.baseline_target = 0.65
        self.validation_history = []
        
    def analyze_confidence_performance(self, results: List[Dict[str, Any]]) -> EnhancedValidationMetrics:
        """Analyze performance by confidence levels"""
        if not results:
            return EnhancedValidationMetrics(0, 0, 0.0, 0.0, 0.0, 0.0)
        
        # Separate by confidence levels
        high_conf = [r for r in results if r.get('confidence', 0) >= 0.75]
        medium_conf = [r for r in results if 0.60 <= r.get('confidence', 0) < 0.75]
        low_conf = [r for r in results if r.get('confidence', 0) < 0.60]
        
        def calc_accuracy(subset):
            if not subset:
                return 0.0
            return sum(1 for r in subset if r.get('correct', False)) / len(subset)
        
        total_correct = sum(1 for r in results if r.get('correct', False))
        overall_accuracy = total_correct / len(results)
        
        return EnhancedValidationMetrics(
            total_predictions=len(results),
            correct_predictions=total_correct,
            accuracy=overall_accuracy,
            high_confidence_accuracy=calc_accuracy(high_conf),
            medium_confidence_accuracy=calc_accuracy(medium_conf),
            low_confidence_accuracy=calc_accuracy(low_conf)
        )
    
    def optimize_prediction_filtering(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Find optimal confidence thresholds for filtering predictions"""
        if not results:
            return {"optimal_threshold": 0.5, "filtered_accuracy": 0.0}
        
        # Test different confidence thresholds
        thresholds = np.arange(0.5, 0.95, 0.05)
        best_threshold = 0.5
        best_accuracy = 0.0
        threshold_results = []
        
        for threshold in thresholds:
            filtered_results = [r for r in results if r.get('confidence', 0) >= threshold]
            
            if filtered_results:
                accuracy = sum(1 for r in filtered_results if r.get('correct', False)) / len(filtered_results)
                threshold_results.append({
                    'threshold': threshold,
                    'accuracy': accuracy,
                    'predictions_kept': len(filtered_results),
                    'predictions_filtered': len(results) - len(filtered_results)
                })
                
                if accuracy > best_accuracy and len(filtered_results) >= len(results) * 0.3:  # Keep at least 30%
                    best_accuracy = accuracy
                    best_threshold = threshold
        
        return {
            'optimal_threshold': best_threshold,
            'filtered_accuracy': best_accuracy,
            'threshold_analysis': threshold_results
        }
    
    def simulate_ensemble_validation(self, base_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Simulate ensemble model validation"""
        logger.info("🔬 Simulating Ensemble Model Validation")
        
        ensemble_results = []
        
        for result in base_results:
            # Simulate multiple model predictions
            base_pred = result.get('predicted_home_win_prob', 0.5)
            
            # Simulate 3 different models with slight variations
            model1_pred = max(0.1, min(0.9, base_pred + np.random.normal(0, 0.05)))
            model2_pred = max(0.1, min(0.9, base_pred + np.random.normal(0, 0.08)))
            model3_pred = max(0.1, min(0.9, base_pred + np.random.normal(0, 0.06)))
            
            # Ensemble prediction (weighted average)
            ensemble_pred = (model1_pred * 0.4 + model2_pred * 0.35 + model3_pred * 0.25)
            
            # Determine if ensemble prediction is correct
            actual_home_win = result.get('actual_home_win', False)
            ensemble_home_win = ensemble_pred > 0.5
            ensemble_correct = ensemble_home_win == actual_home_win
            
            # Calculate ensemble confidence (agreement between models)
            predictions = [model1_pred, model2_pred, model3_pred]
            ensemble_confidence = 1.0 - np.std(predictions)  # Higher std = lower confidence
            
            ensemble_result = {
                **result,
                'ensemble_prediction': ensemble_pred,
                'ensemble_correct': ensemble_correct,
                'ensemble_confidence': ensemble_confidence,
                'model_predictions': predictions
            }
            ensemble_results.append(ensemble_result)
        
        # Calculate ensemble accuracy
        ensemble_accuracy = sum(1 for r in ensemble_results if r['ensemble_correct']) / len(ensemble_results)
        base_accuracy = sum(1 for r in base_results if r.get('correct', False)) / len(base_results)
        
        improvement = ensemble_accuracy - base_accuracy
        
        logger.info(f"   Base Model Accuracy: {base_accuracy*100:.1f}%")
        logger.info(f"   Ensemble Accuracy: {ensemble_accuracy*100:.1f}%")
        logger.info(f"   Improvement: {improvement*100:.1f}%")
        
        return {
            'base_accuracy': base_accuracy,
            'ensemble_accuracy': ensemble_accuracy,
            'improvement': improvement,
            'ensemble_results': ensemble_results
        }
    
    def generate_accuracy_improvement_plan(self, metrics: EnhancedValidationMetrics) -> Dict[str, Any]:
        """Generate a plan to improve accuracy to baseline"""
        plan = {
            'current_accuracy': metrics.accuracy,
            'target_accuracy': metrics.baseline_target,
            'improvement_needed': metrics.baseline_target - metrics.accuracy,
            'strategies': [],
            'expected_impact': {}
        }
        
        if metrics.accuracy < metrics.baseline_target:
            deficit = (metrics.baseline_target - metrics.accuracy) * 100
            
            # Strategy 1: Confidence-based filtering
            if metrics.high_confidence_accuracy > metrics.accuracy:
                impact = (metrics.high_confidence_accuracy - metrics.accuracy) * 100
                plan['strategies'].append({
                    'name': 'Confidence-Based Filtering',
                    'description': f'Filter predictions below 75% confidence',
                    'expected_improvement': f'{impact:.1f}%',
                    'implementation': 'Apply confidence threshold of 0.75'
                })
                plan['expected_impact']['confidence_filtering'] = impact
            
            # Strategy 2: Ensemble modeling
            ensemble_improvement = 0.03  # Conservative 3% improvement estimate
            plan['strategies'].append({
                'name': 'Ensemble Model Integration',
                'description': 'Combine multiple prediction models',
                'expected_improvement': f'{ensemble_improvement*100:.1f}%',
                'implementation': 'Implement weighted ensemble of 3+ models'
            })
            plan['expected_impact']['ensemble'] = ensemble_improvement * 100
            
            # Strategy 3: Feature engineering
            feature_improvement = 0.02  # Conservative 2% improvement estimate
            plan['strategies'].append({
                'name': 'Advanced Feature Engineering',
                'description': 'Add temporal and interaction features',
                'expected_improvement': f'{feature_improvement*100:.1f}%',
                'implementation': 'Implement advanced feature extraction'
            })
            plan['expected_impact']['feature_engineering'] = feature_improvement * 100
            
            # Calculate total expected improvement
            total_improvement = sum(plan['expected_impact'].values())
            plan['total_expected_improvement'] = total_improvement
            plan['projected_accuracy'] = (metrics.accuracy * 100) + total_improvement
            
        return plan
    
    def run_enhanced_validation(self, validation_results_file: str) -> Dict[str, Any]:
        """Run enhanced validation analysis on existing results"""
        logger.info("🚀 RUNNING ENHANCED VALIDATION ANALYSIS")
        logger.info("=" * 60)
        
        # Load validation results
        try:
            with open(validation_results_file, 'r') as f:
                data = json.load(f)
        except FileNotFoundError:
            logger.error(f"❌ Validation results file not found: {validation_results_file}")
            return {}
        
        # Extract all prediction results
        all_results = []
        if 'game_predictions' in data and 'results' in data['game_predictions']:
            all_results.extend(data['game_predictions']['results'])
        
        if 'player_props' in data and 'results' in data['player_props']:
            # Convert player props to standard format
            for prop in data['player_props']['results']:
                all_results.append({
                    'correct': prop['correct'],
                    'confidence': prop['confidence'],
                    'prediction_type': 'player_prop'
                })
        
        # Analyze confidence performance
        metrics = self.analyze_confidence_performance(all_results)
        
        # Find optimal filtering thresholds
        optimization = self.optimize_prediction_filtering(all_results)
        
        # Simulate ensemble performance
        game_results = data.get('game_predictions', {}).get('results', [])
        ensemble_analysis = self.simulate_ensemble_validation(game_results) if game_results else {}
        
        # Generate improvement plan
        improvement_plan = self.generate_accuracy_improvement_plan(metrics)
        
        # Log enhanced results
        logger.info("📊 ENHANCED VALIDATION RESULTS")
        logger.info("=" * 60)
        logger.info(f"Overall Accuracy: {metrics.accuracy*100:.1f}%")
        logger.info(f"High Confidence (≥75%): {metrics.high_confidence_accuracy*100:.1f}%")
        logger.info(f"Medium Confidence (60-75%): {metrics.medium_confidence_accuracy*100:.1f}%")
        logger.info(f"Low Confidence (<60%): {metrics.low_confidence_accuracy*100:.1f}%")
        
        if optimization:
            logger.info(f"\n🎯 OPTIMIZATION ANALYSIS")
            logger.info(f"Optimal Confidence Threshold: {optimization['optimal_threshold']:.2f}")
            logger.info(f"Filtered Accuracy: {optimization['filtered_accuracy']*100:.1f}%")
        
        if ensemble_analysis:
            logger.info(f"\n🔬 ENSEMBLE ANALYSIS")
            logger.info(f"Ensemble Improvement: +{ensemble_analysis['improvement']*100:.1f}%")
        
        logger.info(f"\n📈 IMPROVEMENT PLAN")
        for strategy in improvement_plan['strategies']:
            logger.info(f"  • {strategy['name']}: +{strategy['expected_improvement']}")
        
        if improvement_plan.get('projected_accuracy'):
            logger.info(f"Projected Accuracy: {improvement_plan['projected_accuracy']:.1f}%")
        
        # Recommendations
        recommendations = metrics.get_optimization_recommendations()
        if recommendations:
            logger.info(f"\n💡 RECOMMENDATIONS")
            for rec in recommendations:
                logger.info(f"  {rec}")
        
        if metrics.baseline_achieved():
            logger.info("\n✅ BASELINE ACCURACY ACHIEVED!")
        else:
            logger.info(f"\n⚠️ Need {(metrics.baseline_target - metrics.accuracy)*100:.1f}% improvement for baseline")
        
        return {
            'metrics': {
                'accuracy': metrics.accuracy,
                'high_confidence_accuracy': metrics.high_confidence_accuracy,
                'medium_confidence_accuracy': metrics.medium_confidence_accuracy,
                'low_confidence_accuracy': metrics.low_confidence_accuracy,
                'baseline_achieved': metrics.baseline_achieved()
            },
            'optimization': optimization,
            'ensemble_analysis': ensemble_analysis,
            'improvement_plan': improvement_plan,
            'recommendations': recommendations
        }

def main():
    """Main enhanced validation routine"""
    
    if len(sys.argv) < 2:
        return
    
    results_file = sys.argv[1]
    
    system = EnhancedValidationSystem()
    analysis = system.run_enhanced_validation(results_file)
    
    # Save enhanced analysis
    output_file = f"enhanced_validation_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(analysis, f, indent=2)
    

if __name__ == "__main__":
    main()
