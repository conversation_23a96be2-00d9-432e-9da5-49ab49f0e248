import sqlite3
import pandas as pd
import logging
import os
from pathlib import Path
from datetime import datetime
import re

#!/usr/bin/env python3
"""
Enhanced Basketball Analytics Migration System
=============================================

Comprehensive migration system for all basketball analytics data including:
- Shot analytics (distance-based, zone-based)
- Clutch performance statistics
- Advanced analytics and efficiency metrics
- Defensive statistics
- Catch & shoot analytics
- Pull-up shot analytics
"""


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedBasketballMigration:
    """Enhanced migration system for comprehensive basketball analytics"""
    
    def __init__(self, db_path="hyper_medusa_consolidated.db"):
        self.db_path = db_path
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()
        
        # Analytics table definitions
        self.analytics_tables = {
            'shot_analytics': {
                'pattern': r'shots_(5ft|8ft|by_zone)',
                'description': 'Shot analytics by distance and zone'
            },
            'clutch_analytics': {
                'pattern': r'clutch_\d+min_\d+pt',
                'description': 'Clutch performance statistics'
            },
            'advanced_stats': {
                'pattern': r'advanced_stats',
                'description': 'Advanced efficiency and analytics'
            },
            'defense_stats': {
                'pattern': r'defense_stats',
                'description': 'Defensive performance metrics'
            },
            'catch_shoot': {
                'pattern': r'catch_shoot',
                'description': 'Catch and shoot analytics'
            },
            'pullup_shot': {
                'pattern': r'pullup_shot',
                'description': 'Pull-up shot analytics'
            },
            'bio_stats': {
                'pattern': r'bio_stats',
                'description': 'Player biographical statistics'
            }
        }
        
        self.processed_files = 0
        self.total_records = 0
        
    def create_analytics_tables(self):
        """Create specialized analytics tables"""
        
        # Shot Analytics Table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS shot_analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                player_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                age REAL,
                nickname TEXT,
                season TEXT,
                league TEXT,
                shot_type TEXT,
                distance_range TEXT,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                data_source TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Clutch Analytics Table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS clutch_analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                player_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                age REAL,
                season TEXT,
                league TEXT,
                clutch_type TEXT,
                gp INTEGER,
                wins INTEGER,
                losses INTEGER,
                win_pct REAL,
                minutes REAL,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                ftm INTEGER,
                fta INTEGER,
                ft_pct REAL,
                points INTEGER,
                rebounds INTEGER,
                assists INTEGER,
                steals INTEGER,
                blocks INTEGER,
                turnovers INTEGER,
                plus_minus INTEGER,
                fantasy_pts REAL,
                data_source TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Advanced Analytics Table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS advanced_analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                player_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                age REAL,
                season TEXT,
                league TEXT,
                gp INTEGER,
                minutes REAL,
                off_rating REAL,
                def_rating REAL,
                net_rating REAL,
                ast_pct REAL,
                reb_pct REAL,
                usg_pct REAL,
                ts_pct REAL,
                efg_pct REAL,
                pie REAL,
                pace REAL,
                data_source TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Defense Analytics Table
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS defense_analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                player_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                age REAL,
                season TEXT,
                league TEXT,
                gp INTEGER,
                minutes REAL,
                def_rating REAL,
                steals INTEGER,
                blocks INTEGER,
                defensive_rebounds INTEGER,
                deflections INTEGER,
                contested_shots INTEGER,
                data_source TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Shooting Analytics Table (catch & shoot, pullup)
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS shooting_analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_id TEXT,
                player_name TEXT,
                team_id TEXT,
                team_abbreviation TEXT,
                age REAL,
                season TEXT,
                league TEXT,
                shot_category TEXT,
                gp INTEGER,
                fgm INTEGER,
                fga INTEGER,
                fg_pct REAL,
                fg3m INTEGER,
                fg3a INTEGER,
                fg3_pct REAL,
                efg_pct REAL,
                data_source TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        logger.info("✅ Created specialized analytics tables")
    
    def determine_analytics_table(self, file_path):
        """Determine which analytics table a file belongs to"""
        filename = os.path.basename(file_path).lower()
        
        for table_name, config in self.analytics_tables.items():
            if re.search(config['pattern'], filename):
                return table_name
        
        return None
    
    def extract_metadata(self, file_path):
        """Extract season and league from filename"""
        filename = os.path.basename(file_path)
        
        # Extract season
        season_match = re.search(r'(\d{4})', filename)
        season = season_match.group(1) if season_match else 'unknown'
        
        # Extract league
        league = 'WNBA' if 'wnba' in filename.lower() else 'NBA'
        
        return season, league
    
    def process_analytics_file(self, file_path, table_type):
        """Process analytics file and insert into appropriate table"""
        try:
            df = pd.read_csv(file_path)
            if df.empty:
                logger.warning(f"Empty file: {file_path}")
                return 0
            
            season, league = self.extract_metadata(file_path)
            records_inserted = 0
            
            if table_type == 'shot_analytics':
                records_inserted = self.process_shot_analytics(df, file_path, season, league)
            elif table_type == 'clutch_analytics':
                records_inserted = self.process_clutch_analytics(df, file_path, season, league)
            elif table_type == 'advanced_stats':
                records_inserted = self.process_advanced_analytics(df, file_path, season, league)
            elif table_type == 'defense_stats':
                records_inserted = self.process_defense_analytics(df, file_path, season, league)
            elif table_type in ['catch_shoot', 'pullup_shot']:
                records_inserted = self.process_shooting_analytics(df, file_path, season, league, table_type)
            
            logger.info(f"✅ Processed {records_inserted} records from {file_path}")
            return records_inserted
            
        except Exception as e:
            logger.error(f"❌ Error processing {file_path}: {e}")
            return 0
    
    def process_shot_analytics(self, df, file_path, season, league):
        """Process shot analytics data"""
        records = 0
        filename = os.path.basename(file_path)
        
        # Determine shot type from filename
        if '5ft' in filename:
            shot_type = 'distance_breakdown'
        elif '8ft' in filename:
            shot_type = 'distance_breakdown'
        elif 'by_zone' in filename:
            shot_type = 'zone_breakdown'
        else:
            shot_type = 'general'
        
        for _, row in df.iterrows():
            try:
                # Extract basic player info
                player_id = str(row.get('PLAYER_ID', ''))
                player_name = str(row.get('PLAYER_NAME', ''))
                team_id = str(row.get('TEAM_ID', ''))
                team_abbr = str(row.get('TEAM_ABBREVIATION', ''))
                age = float(row.get('AGE', 0)) if pd.notna(row.get('AGE')) else None
                nickname = str(row.get('NICKNAME', ''))
                
                # For shot analytics, we need to process multiple distance ranges
                # This is a simplified version - in production, you'd parse all distance columns
                fgm = int(row.get('FGM', 0)) if pd.notna(row.get('FGM')) else 0
                fga = int(row.get('FGA', 0)) if pd.notna(row.get('FGA')) else 0
                fg_pct = float(row.get('FG_PCT', 0)) if pd.notna(row.get('FG_PCT')) else 0
                
                self.cursor.execute('''
                    INSERT INTO shot_analytics 
                    (player_id, player_name, team_id, team_abbreviation, age, nickname, 
                     season, league, shot_type, fgm, fga, fg_pct, data_source)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (player_id, player_name, team_id, team_abbr, age, nickname,
                      season, league, shot_type, fgm, fga, fg_pct, filename))
                
                records += 1
                
            except Exception as e:
                logger.warning(f"Error processing row in {file_path}: {e}")
                continue
        
        return records

    def process_clutch_analytics(self, df, file_path, season, league):
        """Process clutch performance analytics"""
        records = 0
        filename = os.path.basename(file_path)

        # Extract clutch type from filename
        clutch_match = re.search(r'clutch_(\d+min_\d+pt)', filename)
        clutch_type = clutch_match.group(1) if clutch_match else 'unknown'

        for _, row in df.iterrows():
            try:
                self.cursor.execute('''
                    INSERT INTO clutch_analytics
                    (player_id, player_name, team_id, team_abbreviation, age, season, league,
                     clutch_type, gp, wins, losses, win_pct, minutes, fgm, fga, fg_pct,
                     fg3m, fg3a, fg3_pct, ftm, fta, ft_pct, points, rebounds, assists,
                     steals, blocks, turnovers, plus_minus, fantasy_pts, data_source)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    str(row.get('PLAYER_ID', '')),
                    str(row.get('PLAYER_NAME', '')),
                    str(row.get('TEAM_ID', '')),
                    str(row.get('TEAM_ABBREVIATION', '')),
                    float(row.get('AGE', 0)) if pd.notna(row.get('AGE')) else None,
                    season, league, clutch_type,
                    int(row.get('GP', 0)) if pd.notna(row.get('GP')) else 0,
                    int(row.get('W', 0)) if pd.notna(row.get('W')) else 0,
                    int(row.get('L', 0)) if pd.notna(row.get('L')) else 0,
                    float(row.get('W_PCT', 0)) if pd.notna(row.get('W_PCT')) else 0,
                    float(row.get('MIN', 0)) if pd.notna(row.get('MIN')) else 0,
                    int(row.get('FGM', 0)) if pd.notna(row.get('FGM')) else 0,
                    int(row.get('FGA', 0)) if pd.notna(row.get('FGA')) else 0,
                    float(row.get('FG_PCT', 0)) if pd.notna(row.get('FG_PCT')) else 0,
                    int(row.get('FG3M', 0)) if pd.notna(row.get('FG3M')) else 0,
                    int(row.get('FG3A', 0)) if pd.notna(row.get('FG3A')) else 0,
                    float(row.get('FG3_PCT', 0)) if pd.notna(row.get('FG3_PCT')) else 0,
                    int(row.get('FTM', 0)) if pd.notna(row.get('FTM')) else 0,
                    int(row.get('FTA', 0)) if pd.notna(row.get('FTA')) else 0,
                    float(row.get('FT_PCT', 0)) if pd.notna(row.get('FT_PCT')) else 0,
                    int(row.get('PTS', 0)) if pd.notna(row.get('PTS')) else 0,
                    int(row.get('REB', 0)) if pd.notna(row.get('REB')) else 0,
                    int(row.get('AST', 0)) if pd.notna(row.get('AST')) else 0,
                    int(row.get('STL', 0)) if pd.notna(row.get('STL')) else 0,
                    int(row.get('BLK', 0)) if pd.notna(row.get('BLK')) else 0,
                    int(row.get('TOV', 0)) if pd.notna(row.get('TOV')) else 0,
                    int(row.get('PLUS_MINUS', 0)) if pd.notna(row.get('PLUS_MINUS')) else 0,
                    float(row.get('WNBA_FANTASY_PTS', 0)) if pd.notna(row.get('WNBA_FANTASY_PTS')) else 0,
                    filename
                ))
                records += 1
            except Exception as e:
                logger.warning(f"Error processing clutch row: {e}")
                continue

        return records

    def process_advanced_analytics(self, df, file_path, season, league):
        """Process advanced analytics data"""
        records = 0
        filename = os.path.basename(file_path)

        for _, row in df.iterrows():
            try:
                self.cursor.execute('''
                    INSERT INTO advanced_analytics
                    (player_id, player_name, team_id, team_abbreviation, age, season, league,
                     gp, minutes, off_rating, def_rating, net_rating, ast_pct, reb_pct,
                     usg_pct, ts_pct, efg_pct, pie, pace, data_source)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    str(row.get('PLAYER_ID', '')),
                    str(row.get('PLAYER_NAME', '')),
                    str(row.get('TEAM_ID', '')),
                    str(row.get('TEAM_ABBREVIATION', '')),
                    float(row.get('AGE', 0)) if pd.notna(row.get('AGE')) else None,
                    season, league,
                    int(row.get('GP', 0)) if pd.notna(row.get('GP')) else 0,
                    float(row.get('MIN', 0)) if pd.notna(row.get('MIN')) else 0,
                    float(row.get('OFF_RATING', 0)) if pd.notna(row.get('OFF_RATING')) else 0,
                    float(row.get('DEF_RATING', 0)) if pd.notna(row.get('DEF_RATING')) else 0,
                    float(row.get('NET_RATING', 0)) if pd.notna(row.get('NET_RATING')) else 0,
                    float(row.get('AST_PCT', 0)) if pd.notna(row.get('AST_PCT')) else 0,
                    float(row.get('REB_PCT', 0)) if pd.notna(row.get('REB_PCT')) else 0,
                    float(row.get('USG_PCT', 0)) if pd.notna(row.get('USG_PCT')) else 0,
                    float(row.get('TS_PCT', 0)) if pd.notna(row.get('TS_PCT')) else 0,
                    float(row.get('EFG_PCT', 0)) if pd.notna(row.get('EFG_PCT')) else 0,
                    float(row.get('PIE', 0)) if pd.notna(row.get('PIE')) else 0,
                    float(row.get('PACE', 0)) if pd.notna(row.get('PACE')) else 0,
                    filename
                ))
                records += 1
            except Exception as e:
                logger.warning(f"Error processing advanced row: {e}")
                continue

        return records

    def process_defense_analytics(self, df, file_path, season, league):
        """Process defensive analytics data"""
        records = 0
        filename = os.path.basename(file_path)

        for _, row in df.iterrows():
            try:
                self.cursor.execute('''
                    INSERT INTO defense_analytics
                    (player_id, player_name, team_id, team_abbreviation, age, season, league,
                     gp, minutes, def_rating, steals, blocks, defensive_rebounds, data_source)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    str(row.get('PLAYER_ID', '')),
                    str(row.get('PLAYER_NAME', '')),
                    str(row.get('TEAM_ID', '')),
                    str(row.get('TEAM_ABBREVIATION', '')),
                    float(row.get('AGE', 0)) if pd.notna(row.get('AGE')) else None,
                    season, league,
                    int(row.get('GP', 0)) if pd.notna(row.get('GP')) else 0,
                    float(row.get('MIN', 0)) if pd.notna(row.get('MIN')) else 0,
                    float(row.get('DEF_RATING', 0)) if pd.notna(row.get('DEF_RATING')) else 0,
                    int(row.get('STL', 0)) if pd.notna(row.get('STL')) else 0,
                    int(row.get('BLK', 0)) if pd.notna(row.get('BLK')) else 0,
                    int(row.get('DREB', 0)) if pd.notna(row.get('DREB')) else 0,
                    filename
                ))
                records += 1
            except Exception as e:
                logger.warning(f"Error processing defense row: {e}")
                continue

        return records

    def process_shooting_analytics(self, df, file_path, season, league, shot_category):
        """Process shooting analytics (catch & shoot, pullup)"""
        records = 0
        filename = os.path.basename(file_path)

        for _, row in df.iterrows():
            try:
                self.cursor.execute('''
                    INSERT INTO shooting_analytics
                    (player_id, player_name, team_id, team_abbreviation, age, season, league,
                     shot_category, gp, fgm, fga, fg_pct, fg3m, fg3a, fg3_pct, efg_pct, data_source)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    str(row.get('PLAYER_ID', '')),
                    str(row.get('PLAYER_NAME', '')),
                    str(row.get('TEAM_ID', '')),
                    str(row.get('TEAM_ABBREVIATION', '')),
                    float(row.get('AGE', 0)) if pd.notna(row.get('AGE')) else None,
                    season, league, shot_category,
                    int(row.get('GP', 0)) if pd.notna(row.get('GP')) else 0,
                    int(row.get('FGM', 0)) if pd.notna(row.get('FGM')) else 0,
                    int(row.get('FGA', 0)) if pd.notna(row.get('FGA')) else 0,
                    float(row.get('FG_PCT', 0)) if pd.notna(row.get('FG_PCT')) else 0,
                    int(row.get('FG3M', 0)) if pd.notna(row.get('FG3M')) else 0,
                    int(row.get('FG3A', 0)) if pd.notna(row.get('FG3A')) else 0,
                    float(row.get('FG3_PCT', 0)) if pd.notna(row.get('FG3_PCT')) else 0,
                    float(row.get('EFG_PCT', 0)) if pd.notna(row.get('EFG_PCT')) else 0,
                    filename
                ))
                records += 1
            except Exception as e:
                logger.warning(f"Error processing shooting row: {e}")
                continue

        return records

    def run_enhanced_migration(self):
        """Run the enhanced migration for all analytics files"""
        logger.info("🚀 Starting Enhanced Basketball Analytics Migration")

        # Create analytics tables
        self.create_analytics_tables()

        # Find all CSV files in data directories
        data_dirs = [
            'data/wnba_10year_historical',
            'data/nba_10year_historical',
            'data'
        ]

        analytics_files = []
        for data_dir in data_dirs:
            if os.path.exists(data_dir):
                for root, dirs, files in os.walk(data_dir):
                    for file in files:
                        if file.endswith('.csv'):
                            file_path = os.path.join(root, file)
                            table_type = self.determine_analytics_table(file_path)
                            if table_type:
                                analytics_files.append((file_path, table_type))

        logger.info(f"📊 Found {len(analytics_files)} analytics files to process")

        # Process analytics files
        for file_path, table_type in analytics_files:
            try:
                records = self.process_analytics_file(file_path, table_type)
                self.total_records += records
                self.processed_files += 1

                if self.processed_files % 10 == 0:
                    self.conn.commit()
                    logger.info(f"💾 Committed batch - {self.processed_files} files processed")

            except Exception as e:
                logger.error(f"❌ Failed to process {file_path}: {e}")

        # Final commit
        self.conn.commit()

        # Generate summary
        self.generate_analytics_summary()

        logger.info(f"✅ Enhanced migration completed!")
        logger.info(f"📈 Total files processed: {self.processed_files}")
        logger.info(f"📊 Total records inserted: {self.total_records}")

    def generate_analytics_summary(self):
        """Generate summary of analytics data"""
        logger.info("📋 Generating Analytics Summary")

        # Count records in each analytics table
        tables = ['shot_analytics', 'clutch_analytics', 'advanced_analytics',
                 'defense_analytics', 'shooting_analytics']

        for table in tables:
            try:
                count = self.cursor.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                logger.info(f"  {table}: {count:,} records")
            except Exception as e:
                logger.warning(f"Could not count {table}: {e}")

    def close(self):
        """Close database connection"""
        self.conn.close()

def main():
    """Main execution function"""
    migration = EnhancedBasketballMigration()

    try:
        migration.run_enhanced_migration()
    except Exception as e:
        logger.error(f"Migration failed: {e}")
    finally:
        migration.close()

if __name__ == "__main__":
    main()
