import os
import shutil
import psutil
import logging
from pathlib import Path
    import gc
    import sys

#!/usr/bin/env python3
"""
EMERGENCY MEMORY FIX - RESTORE 65% BASELINE
===========================================

CRITICAL: Memory at 95.1% - Need to free 4.7GB immediately!
Found: legacy_backup consuming 3.17GB + other large directories
"""


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("EMERGENCY_FIX")

def check_memory():
    """Check current memory usage"""
    memory = psutil.virtual_memory()
    return memory.percent

def emergency_cleanup():
    """Emergency cleanup to restore 65% baseline"""
    
    initial_memory = check_memory()
    logger.error(f"🚨 STARTING EMERGENCY CLEANUP - Memory: {initial_memory:.1f}%")
    
    # 1. REMOVE LEGACY BACKUP (3.17GB)
    legacy_backup = Path("legacy_backup/20250701_093625")
    if legacy_backup.exists():
        logger.info(f"🗑️ Removing legacy backup: {legacy_backup}")
        try:
            shutil.rmtree(legacy_backup)
            logger.info("✅ Legacy backup removed - freed ~3.17GB")
        except Exception as e:
            logger.error(f"❌ Failed to remove legacy backup: {e}")
    
    # 2. CLEAR GOOGLE API CACHE (83.6MB)
    google_cache = Path(".venv/Lib/site-packages/googleapiclient/discovery_cache")
    if google_cache.exists():
        logger.info(f"🗑️ Clearing Google API cache: {google_cache}")
        try:
            shutil.rmtree(google_cache)
            logger.info("✅ Google API cache cleared")
        except Exception as e:
            logger.error(f"❌ Failed to clear Google cache: {e}")
    
    # 3. CLEAR PYTHON CACHE
    logger.info("🧹 Clearing Python __pycache__ directories...")
    for root, dirs, files in os.walk('.'):
        if '__pycache__' in dirs:
            cache_path = os.path.join(root, '__pycache__')
            try:
                shutil.rmtree(cache_path)
                dirs.remove('__pycache__')  # Don't recurse into removed dir
            except Exception as e:
                logger.warning(f"⚠️ Could not remove {cache_path}: {e}")
    
    # 4. CHECK MEMORY AFTER CLEANUP
    final_memory = check_memory()
    memory_freed = initial_memory - final_memory
    
    logger.info(f"📊 CLEANUP COMPLETE")
    logger.info(f"Initial Memory: {initial_memory:.1f}%")
    logger.info(f"Final Memory: {final_memory:.1f}%")
    logger.info(f"Memory Freed: {memory_freed:.1f}%")
    
    if final_memory <= 65.0:
        logger.info("✅ BASELINE RESTORED! Memory back to 65% target")
        return True
    else:
        logger.warning(f"⚠️ Still above baseline. Current: {final_memory:.1f}% (target: 65%)")
        return False

def additional_optimizations():
    """Additional memory optimizations"""
    logger.info("🔧 APPLYING ADDITIONAL OPTIMIZATIONS...")
    
    # Force garbage collection
    collected = gc.collect()
    logger.info(f"🧹 Garbage collection: {collected} objects collected")
    
    # Clear import caches
    if hasattr(sys, '_clear_type_cache'):
        sys._clear_type_cache()
        logger.info("🧹 Python type cache cleared")
    
    # Check final memory
    final_memory = check_memory()
    logger.info(f"📊 Final memory after optimizations: {final_memory:.1f}%")
    
    return final_memory

if __name__ == "__main__":
    
    # Emergency cleanup
    baseline_restored = emergency_cleanup()
    
    # Additional optimizations
    final_memory = additional_optimizations()
    
    # Final status
    if final_memory <= 65.0:
    elif final_memory < 90.0:
    else:
    
