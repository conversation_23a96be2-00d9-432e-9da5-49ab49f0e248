import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
from collections import Counter

#!/usr/bin/env python3
"""
Investigate Team Data Quality Issues
Analyze why we have 214 unique teams instead of 43 (30 NBA + 13 WNBA)
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TeamDataQualityInvestigator:
    """Investigate team data quality issues"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
        # Official NBA teams (30 teams)
        self.official_nba_teams = {
            'ATL', 'BOS', 'BKN', 'CHA', 'CHI', 'CLE', 'DAL', 'DEN', 'DET', 'GSW',
            'HOU', 'IND', 'LAC', 'LAL', 'MEM', 'MIA', 'MIL', 'MIN', 'NOP', 'NYK',
            'OKC', 'ORL', 'PHI', 'PHX', 'POR', 'SAC', 'SAS', 'TOR', 'UTA', 'WAS'
        }
        
        # Official WNBA teams (13 teams as of 2025)
        self.official_wnba_teams = {
            'ATL', 'CHI', 'CONN', 'DAL', 'IND', 'LAS', 'MIN', 'NY', 'PHX', 'SEA', 'WAS', 'GSV', 'TOR'
        }
        
    def analyze_team_abbreviations(self) -> Dict[str, Any]:
        """Analyze all team abbreviations in the database"""
        logger.info("🔍 ANALYZING TEAM ABBREVIATIONS")
        logger.info("=" * 40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get all unique team abbreviations
            team_analysis = conn.execute("""
                SELECT 
                    COALESCE(team_abbreviation, 'NULL') as team_abbrev,
                    league_name,
                    COUNT(*) as record_count,
                    COUNT(DISTINCT season) as seasons,
                    MIN(season) as first_season,
                    MAX(season) as last_season
                FROM unified_nba_wnba_data 
                WHERE team_abbreviation IS NOT NULL AND team_abbreviation != ''
                GROUP BY team_abbreviation, league_name
                ORDER BY record_count DESC
            """).fetchall()
            
            logger.info(f"📊 TOTAL UNIQUE TEAM ABBREVIATIONS: {len(team_analysis)}")
            
            nba_teams = []
            wnba_teams = []
            unknown_teams = []
            
            for team_abbrev, league, count, seasons, first, last in team_analysis:
                team_info = {
                    'abbreviation': team_abbrev,
                    'league': league,
                    'records': count,
                    'seasons': seasons,
                    'first_season': first,
                    'last_season': last
                }
                
                if league == 'NBA':
                    nba_teams.append(team_info)
                elif league == 'WNBA':
                    wnba_teams.append(team_info)
                else:
                    unknown_teams.append(team_info)
            
            logger.info(f"\n🏀 NBA TEAMS FOUND: {len(nba_teams)}")
            logger.info(f"🏀 WNBA TEAMS FOUND: {len(wnba_teams)}")
            logger.info(f"❓ UNKNOWN LEAGUE TEAMS: {len(unknown_teams)}")
            
            # Show top teams by record count
            logger.info(f"\n📈 TOP 20 TEAMS BY RECORD COUNT:")
            for i, (team_abbrev, league, count, seasons, first, last) in enumerate(team_analysis[:20]):
                logger.info(f"   {i+1:2d}. {team_abbrev} ({league}): {count:,} records, {seasons} seasons ({first}-{last})")
            
            conn.close()
            
            return {
                'total_teams': len(team_analysis),
                'nba_teams': nba_teams,
                'wnba_teams': wnba_teams,
                'unknown_teams': unknown_teams,
                'all_teams': team_analysis
            }
            
        except Exception as e:
            logger.error(f"❌ Error analyzing team abbreviations: {e}")
            return {'error': str(e)}
    
    def identify_invalid_teams(self, team_data: Dict[str, Any]) -> Dict[str, Any]:
        """Identify invalid/duplicate team abbreviations"""
        logger.info("\n🔍 IDENTIFYING INVALID TEAMS")
        logger.info("=" * 35)
        
        invalid_nba_teams = []
        invalid_wnba_teams = []
        duplicate_teams = []
        
        # Check NBA teams
        nba_abbreviations = set()
        for team in team_data['nba_teams']:
            abbrev = team['abbreviation']
            
            if abbrev in nba_abbreviations:
                duplicate_teams.append(team)
                logger.info(f"🔄 Duplicate NBA team: {abbrev}")
            else:
                nba_abbreviations.add(abbrev)
            
            if abbrev not in self.official_nba_teams:
                invalid_nba_teams.append(team)
                logger.info(f"❌ Invalid NBA team: {abbrev} ({team['records']:,} records)")
        
        # Check WNBA teams
        wnba_abbreviations = set()
        for team in team_data['wnba_teams']:
            abbrev = team['abbreviation']
            
            if abbrev in wnba_abbreviations:
                duplicate_teams.append(team)
                logger.info(f"🔄 Duplicate WNBA team: {abbrev}")
            else:
                wnba_abbreviations.add(abbrev)
            
            if abbrev not in self.official_wnba_teams:
                invalid_wnba_teams.append(team)
                logger.info(f"❌ Invalid WNBA team: {abbrev} ({team['records']:,} records)")
        
        # Check for common issues
        logger.info(f"\n📊 TEAM VALIDATION SUMMARY:")
        logger.info(f"   Valid NBA teams: {len([t for t in team_data['nba_teams'] if t['abbreviation'] in self.official_nba_teams])}/30")
        logger.info(f"   Valid WNBA teams: {len([t for t in team_data['wnba_teams'] if t['abbreviation'] in self.official_wnba_teams])}/13")
        logger.info(f"   Invalid NBA teams: {len(invalid_nba_teams)}")
        logger.info(f"   Invalid WNBA teams: {len(invalid_wnba_teams)}")
        logger.info(f"   Duplicate teams: {len(duplicate_teams)}")
        logger.info(f"   Unknown league teams: {len(team_data['unknown_teams'])}")
        
        return {
            'invalid_nba_teams': invalid_nba_teams,
            'invalid_wnba_teams': invalid_wnba_teams,
            'duplicate_teams': duplicate_teams,
            'valid_nba_count': len([t for t in team_data['nba_teams'] if t['abbreviation'] in self.official_nba_teams]),
            'valid_wnba_count': len([t for t in team_data['wnba_teams'] if t['abbreviation'] in self.official_wnba_teams])
        }
    
    def analyze_problematic_patterns(self, team_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze patterns in problematic team data"""
        logger.info("\n🔍 ANALYZING PROBLEMATIC PATTERNS")
        logger.info("=" * 40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Find unusual team abbreviations
            unusual_teams = conn.execute("""
                SELECT 
                    team_abbreviation,
                    league_name,
                    COUNT(*) as count,
                    GROUP_CONCAT(DISTINCT source_table) as source_tables,
                    GROUP_CONCAT(DISTINCT data_category) as data_categories
                FROM unified_nba_wnba_data 
                WHERE team_abbreviation IS NOT NULL 
                AND team_abbreviation != ''
                AND (
                    LENGTH(team_abbreviation) > 4 
                    OR team_abbreviation LIKE '%-%'
                    OR team_abbreviation LIKE '%_%'
                    OR team_abbreviation LIKE '% %'
                    OR team_abbreviation REGEXP '[0-9]'
                )
                GROUP BY team_abbreviation, league_name
                ORDER BY count DESC
                LIMIT 20
            """).fetchall()
            
            logger.info(f"🚨 UNUSUAL TEAM ABBREVIATIONS:")
            for team_abbrev, league, count, sources, categories in unusual_teams:
                logger.info(f"   {team_abbrev} ({league}): {count:,} records")
                logger.info(f"      Sources: {sources}")
                logger.info(f"      Categories: {categories}")
            
            # Check for player IDs in team field
            player_id_teams = conn.execute("""
                SELECT 
                    team_abbreviation,
                    COUNT(*) as count
                FROM unified_nba_wnba_data 
                WHERE team_abbreviation REGEXP '^[0-9]+$'
                AND LENGTH(team_abbreviation) > 4
                GROUP BY team_abbreviation
                ORDER BY count DESC
                LIMIT 10
            """).fetchall()
            
            if player_id_teams:
                logger.info(f"\n🏀 PLAYER IDs IN TEAM FIELD:")
                for team_abbrev, count in player_id_teams:
                    logger.info(f"   {team_abbrev}: {count:,} records (likely player ID)")
            
            # Check for game IDs in team field
            game_id_teams = conn.execute("""
                SELECT 
                    team_abbreviation,
                    COUNT(*) as count
                FROM unified_nba_wnba_data 
                WHERE team_abbreviation LIKE '00%' 
                OR team_abbreviation LIKE '10%'
                GROUP BY team_abbreviation
                ORDER BY count DESC
                LIMIT 10
            """).fetchall()
            
            if game_id_teams:
                logger.info(f"\n🎮 GAME IDs IN TEAM FIELD:")
                for team_abbrev, count in game_id_teams:
                    logger.info(f"   {team_abbrev}: {count:,} records (likely game ID)")
            
            conn.close()
            
            return {
                'unusual_teams': unusual_teams,
                'player_id_teams': player_id_teams,
                'game_id_teams': game_id_teams
            }
            
        except Exception as e:
            logger.error(f"❌ Error analyzing problematic patterns: {e}")
            return {'error': str(e)}
    
    def generate_team_cleanup_plan(self, team_data: Dict[str, Any], 
                                 invalid_data: Dict[str, Any], 
                                 patterns: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a plan to clean up team data"""
        logger.info("\n🔧 GENERATING TEAM CLEANUP PLAN")
        logger.info("=" * 40)
        
        cleanup_actions = []
        
        # Action 1: Remove records with player IDs in team field
        if patterns.get('player_id_teams'):
            player_id_count = sum(count for _, count in patterns['player_id_teams'])
            cleanup_actions.append({
                'action': 'remove_player_ids_from_team_field',
                'description': f'Remove {player_id_count:,} records with player IDs in team field',
                'priority': 'HIGH'
            })
        
        # Action 2: Remove records with game IDs in team field
        if patterns.get('game_id_teams'):
            game_id_count = sum(count for _, count in patterns['game_id_teams'])
            cleanup_actions.append({
                'action': 'remove_game_ids_from_team_field',
                'description': f'Remove {game_id_count:,} records with game IDs in team field',
                'priority': 'HIGH'
            })
        
        # Action 3: Fix invalid team abbreviations
        invalid_count = len(invalid_data['invalid_nba_teams']) + len(invalid_data['invalid_wnba_teams'])
        if invalid_count > 0:
            cleanup_actions.append({
                'action': 'fix_invalid_team_abbreviations',
                'description': f'Fix {invalid_count} invalid team abbreviations',
                'priority': 'MEDIUM'
            })
        
        # Action 4: Remove duplicate team entries
        if invalid_data['duplicate_teams']:
            cleanup_actions.append({
                'action': 'remove_duplicate_teams',
                'description': f'Remove {len(invalid_data["duplicate_teams"])} duplicate team entries',
                'priority': 'MEDIUM'
            })
        
        logger.info(f"📋 CLEANUP PLAN ({len(cleanup_actions)} actions):")
        for i, action in enumerate(cleanup_actions, 1):
            logger.info(f"   {i}. [{action['priority']}] {action['description']}")
        
        # Estimate final team count
        estimated_final_teams = invalid_data['valid_nba_count'] + invalid_data['valid_wnba_count']
        logger.info(f"\n🎯 ESTIMATED FINAL TEAM COUNT: {estimated_final_teams}")
        logger.info(f"   Expected: 43 (30 NBA + 13 WNBA)")
        logger.info(f"   Current valid: {estimated_final_teams}")
        logger.info(f"   Gap: {43 - estimated_final_teams} teams")
        
        return {
            'cleanup_actions': cleanup_actions,
            'estimated_final_teams': estimated_final_teams,
            'expected_teams': 43
        }

def main():
    """Execute team data quality investigation"""
    
    investigator = TeamDataQualityInvestigator()
    
    # Step 1: Analyze team abbreviations
    team_data = investigator.analyze_team_abbreviations()
    
    if 'error' in team_data:
        return
    
    # Step 2: Identify invalid teams
    invalid_data = investigator.identify_invalid_teams(team_data)
    
    # Step 3: Analyze problematic patterns
    patterns = investigator.analyze_problematic_patterns(team_data)
    
    # Step 4: Generate cleanup plan
    cleanup_plan = investigator.generate_team_cleanup_plan(team_data, invalid_data, patterns)
    
    
    if cleanup_plan['estimated_final_teams'] <= 43:
    else:
    
    return {
        'team_data': team_data,
        'invalid_data': invalid_data,
        'patterns': patterns,
        'cleanup_plan': cleanup_plan
    }

if __name__ == "__main__":
    main()
