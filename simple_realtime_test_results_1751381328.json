{"total_tests": 5, "passed_tests": 2, "failed_tests": 3, "success_rate": 40.0, "total_time_seconds": 1.65, "test_results": {"Database Connectivity": {"status": "PASSED", "details": {"success": true, "message": "Database connectivity successful", "total_tables": 13, "expected_tables": ["games", "players", "teams", "player_game_stats"], "missing_tables": [], "table_counts": {"games": 135816, "players": 24153, "teams": 4021, "player_game_stats": 26213}}, "timestamp": "2025-07-01T10:48:47.046877"}, "Existing Infrastructure": {"status": "FAILED", "details": {"success": false, "error": "expected an indented block after function definition on line 94 (nba_real_time_pipeline.py, line 95)", "message": "Infrastructure test failed"}, "timestamp": "2025-07-01T10:48:47.052541"}, "Real-Time Data Simulation": {"status": "PASSED", "details": {"success": true, "message": "Real-time data simulation successful", "events_processed": 10, "processing_time_seconds": 1.087, "throughput_events_per_second": 9.2, "sample_event": {"event_id": "sim_event_000", "timestamp": "2025-07-01T14:48:47.053834+00:00", "type": "live_game_update", "data": {"game_id": "sim_game_0", "home_score": 80, "away_score": 75, "quarter": 1, "time_remaining": "12:00"}}}, "timestamp": "2025-07-01T10:48:48.140970"}, "Performance Baseline": {"status": "FAILED", "details": {"success": false, "error": "no such column: p.name", "message": "Performance baseline test failed"}, "timestamp": "2025-07-01T10:48:48.153626"}, "Integration Readiness": {"status": "FAILED", "details": {"success": false, "error": "cannot access local variable 'sqlite3' where it is not associated with a value", "message": "Integration readiness test failed"}, "timestamp": "2025-07-01T10:48:48.155039"}}, "overall_status": "FAILED"}