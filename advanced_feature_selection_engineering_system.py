import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging
import warnings
import json
import random
from abc import ABC, abstractmethod
    from sklearn.feature_selection import (
    from sklearn.preprocessing import StandardScaler, RobustScaler, PolynomialFeatures
    from sklearn.decomposition import PCA, FastICA, TruncatedSVD
    from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier
    from sklearn.linear_model import LassoCV, ElasticNetCV
    from sklearn.model_selection import cross_val_score, StratifiedKFold
    from sklearn.metrics import accuracy_score, f1_score, roc_auc_score
    import xgboost as xgb
    from scipy import stats
    from scipy.stats import pearsonr, spearmanr
    import scipy.sparse as sp
    from src.autonomous.autonomous_feature_alchemist import Autonomous<PERSON>eature<PERSON>lchemist
    from autonomous_feature_engineering_system import AutonomousFeatureEngineeringSystem
    from src.optimization.optimal_prediction_engine import OptimalPredictionEngine
    from backend.utils.feature_engineering import FeatureEngineeringIntegration
                        from sklearn.ensemble import RandomForestClassifier
                        from sklearn.ensemble import RandomForestRegressor

#!/usr/bin/env python3
"""
🎯 HYPER MEDUSA NEURAL VAULT - Advanced Feature Selection & Engineering System
Elite feature selection and engineering targeting maximum predictive power

This system implements cutting-edge feature selection and engineering strategies:
- Multi-method feature selection with mutual information and RFE
- Basketball-specific feature engineering with domain expertise
- Automated feature quality assessment and ranking
- Real-time feature importance monitoring and adaptation
- Integration with existing HMNV feature systems

Author: HYPER MEDUSA NEURAL VAULT
Version: 1.0 Expert Edition
"""


# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Advanced feature selection and engineering libraries
try:
        SelectKBest, SelectPercentile, SelectFromModel, RFE, RFECV,
        f_classif, f_regression, mutual_info_classif, mutual_info_regression,
        VarianceThreshold, chi2
    )
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

# Statistical libraries
try:
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False

# HYPER MEDUSA integrations
try:
    HMNV_INTEGRATION = True
except ImportError:
    HMNV_INTEGRATION = False

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# =============================================================================
# FEATURE SELECTION ENUMS AND DATACLASSES
# =============================================================================

class FeatureSelectionMethod(Enum):
    """Advanced feature selection methods"""
    MUTUAL_INFORMATION = "mutual_information"
    RECURSIVE_FEATURE_ELIMINATION = "rfe"
    UNIVARIATE_STATISTICAL = "univariate"
    MODEL_BASED_SELECTION = "model_based"
    VARIANCE_THRESHOLD = "variance"
    CORRELATION_FILTER = "correlation"
    BASKETBALL_INTELLIGENCE = "basketball_intelligence"
    ENSEMBLE_SELECTION = "ensemble"
    QUANTUM_INSPIRED = "quantum"
    MULTI_OBJECTIVE = "multi_objective"

class FeatureEngineeringStrategy(Enum):
    """Feature engineering strategies"""
    POLYNOMIAL_FEATURES = "polynomial"
    INTERACTION_FEATURES = "interaction"
    TEMPORAL_FEATURES = "temporal"
    BASKETBALL_SPECIFIC = "basketball"
    STATISTICAL_FEATURES = "statistical"
    DOMAIN_EXPERT = "domain_expert"
    AUTOMATED_DISCOVERY = "automated"
    QUANTUM_ENHANCED = "quantum_enhanced"

@dataclass
class FeatureImportanceScore:
    """Feature importance scoring"""
    feature_name: str
    importance_score: float
    selection_method: FeatureSelectionMethod
    confidence: float
    basketball_relevance: float = 0.0
    statistical_significance: float = 0.0

@dataclass
class FeatureSelectionConfig:
    """Configuration for feature selection system"""
    methods: List[FeatureSelectionMethod] = field(default_factory=lambda: [
        FeatureSelectionMethod.MUTUAL_INFORMATION,
        FeatureSelectionMethod.RECURSIVE_FEATURE_ELIMINATION,
        FeatureSelectionMethod.BASKETBALL_INTELLIGENCE
    ])
    engineering_strategies: List[FeatureEngineeringStrategy] = field(default_factory=lambda: [
        FeatureEngineeringStrategy.BASKETBALL_SPECIFIC,
        FeatureEngineeringStrategy.INTERACTION_FEATURES,
        FeatureEngineeringStrategy.TEMPORAL_FEATURES
    ])
    max_features: int = 50
    min_features: int = 10
    correlation_threshold: float = 0.95
    variance_threshold: float = 0.01
    mutual_info_threshold: float = 0.1
    enable_basketball_intelligence: bool = True
    enable_ensemble_selection: bool = True
    cross_validation_folds: int = 5
    random_state: int = 42

@dataclass
class FeatureSelectionResult:
    """Results from feature selection process"""
    selected_features: List[str]
    feature_scores: List[FeatureImportanceScore]
    original_feature_count: int
    selected_feature_count: int
    selection_methods_used: List[FeatureSelectionMethod]
    engineering_strategies_used: List[FeatureEngineeringStrategy]
    basketball_insights: Dict[str, Any]
    performance_metrics: Dict[str, float]
    execution_time: float
    convergence_metrics: Dict[str, float]

# =============================================================================
# ADVANCED FEATURE SELECTION & ENGINEERING SYSTEM
# =============================================================================

class AdvancedFeatureSelectionEngineeringSystem:
    """
    🎯 Advanced Feature Selection & Engineering System
    
    Elite feature selection and engineering system that maximizes predictive power
    through multi-method selection, basketball-specific engineering, and automated
    quality assessment.
    """
    
    def __init__(self, config: Optional[FeatureSelectionConfig] = None):
        self.config = config or FeatureSelectionConfig()
        self.logger = logging.getLogger(self.__class__.__name__)
        self.feature_history = []
        self.basketball_features_cache = {}
        
        # Initialize HMNV integrations
        self.feature_alchemist = None
        self.feature_engineering_system = None
        self.optimal_engine = None
        self.feature_integration = None
        
        if HMNV_INTEGRATION:
            try:
                self.feature_alchemist = AutonomousFeatureAlchemist()
                self.feature_engineering_system = AutonomousFeatureEngineeringSystem()
                self.optimal_engine = OptimalPredictionEngine()
                self.feature_integration = FeatureEngineeringIntegration()
                self.logger.info("🎯 HMNV feature systems integrated successfully")
            except Exception as e:
                self.logger.warning(f"🎯 HMNV integration partial: {e}")
        
        self.logger.info("🎯 Advanced Feature Selection & Engineering System initialized")
        self.logger.info(f"🎯 Methods: {[m.value for m in self.config.methods]}")
        self.logger.info(f"🎯 Engineering strategies: {[s.value for s in self.config.engineering_strategies]}")
    
    async def select_and_engineer_features(self, 
                                         X: pd.DataFrame, 
                                         y: pd.Series,
                                         basketball_context: Optional[Dict[str, Any]] = None) -> FeatureSelectionResult:
        """
        Perform comprehensive feature selection and engineering
        """
        start_time = datetime.now()
        self.logger.info(f"🎯 Starting advanced feature selection and engineering")
        self.logger.info(f"🎯 Input: {X.shape[0]} samples, {X.shape[1]} features")
        
        # Step 1: Feature Engineering
        X_engineered = await self._comprehensive_feature_engineering(X, y, basketball_context)
        
        # Step 2: Multi-method Feature Selection
        selected_features, feature_scores = await self._multi_method_feature_selection(X_engineered, y)
        
        # Step 3: Basketball Intelligence Enhancement
        if self.config.enable_basketball_intelligence:
            selected_features, feature_scores = await self._basketball_intelligence_enhancement(
                X_engineered, y, selected_features, feature_scores, basketball_context
            )
        
        # Step 4: Final Validation and Optimization
        final_features = await self._final_feature_validation(X_engineered, y, selected_features)
        
        # Step 5: Generate Basketball Insights
        basketball_insights = await self._generate_basketball_insights(
            X_engineered, y, final_features, basketball_context
        )
        
        # Step 6: Calculate Performance Metrics
        performance_metrics = await self._calculate_performance_metrics(
            X_engineered[final_features], y
        )
        
        # Step 7: Calculate Convergence Metrics
        convergence_metrics = self._calculate_convergence_metrics()
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        result = FeatureSelectionResult(
            selected_features=final_features,
            feature_scores=feature_scores,
            original_feature_count=X.shape[1],
            selected_feature_count=len(final_features),
            selection_methods_used=self.config.methods,
            engineering_strategies_used=self.config.engineering_strategies,
            basketball_insights=basketball_insights,
            performance_metrics=performance_metrics,
            execution_time=execution_time,
            convergence_metrics=convergence_metrics
        )
        
        self.logger.info(f"🎯 Feature selection completed in {execution_time:.2f}s")
        self.logger.info(f"🎯 Features: {X.shape[1]} → {len(final_features)} (reduction: {(1-len(final_features)/X.shape[1])*100:.1f}%)")
        
        return result
    
    async def _comprehensive_feature_engineering(self, 
                                               X: pd.DataFrame, 
                                               y: pd.Series,
                                               basketball_context: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Comprehensive feature engineering using multiple strategies"""
        X_engineered = X.copy()
        
        for strategy in self.config.engineering_strategies:
            try:
                if strategy == FeatureEngineeringStrategy.BASKETBALL_SPECIFIC:
                    X_engineered = await self._engineer_basketball_features(X_engineered, basketball_context)
                elif strategy == FeatureEngineeringStrategy.INTERACTION_FEATURES:
                    X_engineered = await self._engineer_interaction_features(X_engineered)
                elif strategy == FeatureEngineeringStrategy.TEMPORAL_FEATURES:
                    X_engineered = await self._engineer_temporal_features(X_engineered)
                elif strategy == FeatureEngineeringStrategy.STATISTICAL_FEATURES:
                    X_engineered = await self._engineer_statistical_features(X_engineered)
                elif strategy == FeatureEngineeringStrategy.AUTOMATED_DISCOVERY:
                    X_engineered = await self._automated_feature_discovery(X_engineered, y)
                
            except Exception as e:
                self.logger.warning(f"🎯 Feature engineering strategy {strategy.value} failed: {e}")
        
        self.logger.info(f"🎯 Feature engineering: {X.shape[1]} → {X_engineered.shape[1]} features")
        return X_engineered

    async def _engineer_basketball_features(self,
                                          X: pd.DataFrame,
                                          basketball_context: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """Engineer basketball-specific features"""
        X_basketball = X.copy()

        try:
            # Use existing HMNV basketball feature engineering if available
            if self.feature_integration:
                # Generate basketball features using existing integration
                for idx, row in X.iterrows():
                    try:
                        basketball_features = self.feature_integration.generate_matchup_features(
                            row.to_dict(),
                            league=basketball_context.get('league', 'NBA') if basketball_context else 'NBA'
                        )
                        # Add basketball features to the row
                        for i, feature_value in enumerate(basketball_features):
                            X_basketball.loc[idx, f'basketball_feature_{i}'] = feature_value
                    except Exception as e:

            # Basketball efficiency metrics
            if all(col in X.columns for col in ['points', 'field_goals_attempted', 'free_throws_attempted']):
                X_basketball['true_shooting_pct'] = (
                    X['points'] / (2 * (X['field_goals_attempted'] + 0.44 * X['free_throws_attempted']))
                ).fillna(0)

            if all(col in X.columns for col in ['field_goals_made', 'three_pointers_made', 'field_goals_attempted']):
                X_basketball['effective_fg_pct'] = (
                    (X['field_goals_made'] + 0.5 * X['three_pointers_made']) / X['field_goals_attempted']
                ).fillna(0)

            # Basketball impact metrics
            if all(col in X.columns for col in ['points', 'total_rebounds', 'assists', 'steals', 'blocks']):
                X_basketball['player_impact_estimate'] = (
                    X['points'] + X['total_rebounds'] + X['assists'] * 1.5 +
                    X['steals'] * 2 + X['blocks'] * 2
                )

            # Team performance ratios
            if all(col in X.columns for col in ['offensive_rating', 'defensive_rating']):
                X_basketball['net_rating'] = X['offensive_rating'] - X['defensive_rating']
                X_basketball['efficiency_ratio'] = X['offensive_rating'] / (X['defensive_rating'] + 1e-6)

            # Momentum and form features
            if 'win_streak' in X.columns and 'loss_streak' in X.columns:
                X_basketball['momentum_indicator'] = X['win_streak'] - X['loss_streak']
                X_basketball['form_stability'] = 1 / (1 + abs(X['win_streak'] - X['loss_streak']))

            self.logger.info(f"🏀 Generated {X_basketball.shape[1] - X.shape[1]} basketball-specific features")

        except Exception as e:
            self.logger.error(f"Basketball feature engineering failed: {e}")

        return X_basketball

    async def _engineer_interaction_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Engineer interaction features between important variables"""
        X_interaction = X.copy()

        try:
            # Get numeric columns
            numeric_cols = X.select_dtypes(include=[np.number]).columns.tolist()

            if len(numeric_cols) >= 2:
                # Create top interaction features (limit to avoid explosion)
                important_cols = numeric_cols[:10]  # Limit to top 10 numeric features

                for i, col1 in enumerate(important_cols):
                    for j, col2 in enumerate(important_cols[i+1:], i+1):
                        if j < i + 3:  # Limit interactions per feature
                            # Multiplicative interaction
                            X_interaction[f'{col1}_x_{col2}'] = X[col1] * X[col2]

                            # Ratio interaction (avoid division by zero)
                            X_interaction[f'{col1}_div_{col2}'] = X[col1] / (X[col2] + 1e-6)

                            # Difference interaction
                            X_interaction[f'{col1}_diff_{col2}'] = X[col1] - X[col2]

            self.logger.info(f"🔗 Generated {X_interaction.shape[1] - X.shape[1]} interaction features")

        except Exception as e:
            self.logger.error(f"Interaction feature engineering failed: {e}")

        return X_interaction

    async def _engineer_temporal_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Engineer temporal and trend features"""
        X_temporal = X.copy()

        try:
            # Get numeric columns for temporal analysis
            numeric_cols = X.select_dtypes(include=[np.number]).columns.tolist()

            for col in numeric_cols[:5]:  # Limit to top 5 columns
                # Rolling statistics (if we have enough data)
                if len(X) >= 5:
                    X_temporal[f'{col}_rolling_mean_3'] = X[col].rolling(window=3, min_periods=1).mean()
                    X_temporal[f'{col}_rolling_std_3'] = X[col].rolling(window=3, min_periods=1).std().fillna(0)

                # Lag features
                if len(X) >= 2:
                    X_temporal[f'{col}_lag_1'] = X[col].shift(1).fillna(X[col].mean())

                # Trend features
                X_temporal[f'{col}_trend'] = X[col].diff().fillna(0)

                # Momentum features
                X_temporal[f'{col}_momentum'] = X[col] - X[col].rolling(window=3, min_periods=1).mean()

            self.logger.info(f"⏰ Generated {X_temporal.shape[1] - X.shape[1]} temporal features")

        except Exception as e:
            self.logger.error(f"Temporal feature engineering failed: {e}")

        return X_temporal

    async def _engineer_statistical_features(self, X: pd.DataFrame) -> pd.DataFrame:
        """Engineer statistical features"""
        X_stats = X.copy()

        try:
            # Get numeric columns
            numeric_cols = X.select_dtypes(include=[np.number]).columns.tolist()

            if len(numeric_cols) >= 2:
                # Statistical aggregations across features
                X_stats['feature_mean'] = X[numeric_cols].mean(axis=1)
                X_stats['feature_std'] = X[numeric_cols].std(axis=1).fillna(0)
                X_stats['feature_max'] = X[numeric_cols].max(axis=1)
                X_stats['feature_min'] = X[numeric_cols].min(axis=1)
                X_stats['feature_range'] = X_stats['feature_max'] - X_stats['feature_min']

                # Percentile features
                X_stats['feature_median'] = X[numeric_cols].median(axis=1)
                X_stats['feature_q75'] = X[numeric_cols].quantile(0.75, axis=1)
                X_stats['feature_q25'] = X[numeric_cols].quantile(0.25, axis=1)
                X_stats['feature_iqr'] = X_stats['feature_q75'] - X_stats['feature_q25']

                # Skewness and kurtosis
                X_stats['feature_skew'] = X[numeric_cols].skew(axis=1).fillna(0)
                X_stats['feature_kurtosis'] = X[numeric_cols].kurtosis(axis=1).fillna(0)

            self.logger.info(f"📊 Generated {X_stats.shape[1] - X.shape[1]} statistical features")

        except Exception as e:
            self.logger.error(f"Statistical feature engineering failed: {e}")

        return X_stats

    async def _automated_feature_discovery(self, X: pd.DataFrame, y: pd.Series) -> pd.DataFrame:
        """Automated feature discovery using existing HMNV systems"""
        X_discovered = X.copy()

        try:
            # Use existing AutoML feature discovery if available
            if self.feature_alchemist:
                # Use the autonomous feature alchemist for discovery
                discovered_features = await self.feature_alchemist.comprehensive_feature_engineering(
                    X, y, enable_basketball_intelligence=True
                )

                # Merge discovered features
                for col in discovered_features.columns:
                    if col not in X_discovered.columns:
                        X_discovered[col] = discovered_features[col]

            # Use existing feature engineering system if available
            if self.feature_engineering_system:
                enhanced_data = await self.feature_engineering_system.engineer_features(
                    X, target_column=y.name if y.name else 'target'
                )

                # Merge enhanced features
                for col in enhanced_data.columns:
                    if col not in X_discovered.columns and col != y.name:
                        X_discovered[col] = enhanced_data[col]

            self.logger.info(f"🤖 Automated discovery: {X_discovered.shape[1] - X.shape[1]} new features")

        except Exception as e:
            self.logger.error(f"Automated feature discovery failed: {e}")

        return X_discovered

    async def _multi_method_feature_selection(self,
                                            X: pd.DataFrame,
                                            y: pd.Series) -> Tuple[List[str], List[FeatureImportanceScore]]:
        """Multi-method feature selection combining multiple algorithms"""
        all_feature_scores = []
        method_results = {}

        for method in self.config.methods:
            try:
                if method == FeatureSelectionMethod.MUTUAL_INFORMATION:
                    scores = await self._mutual_information_selection(X, y)
                elif method == FeatureSelectionMethod.RECURSIVE_FEATURE_ELIMINATION:
                    scores = await self._recursive_feature_elimination(X, y)
                elif method == FeatureSelectionMethod.UNIVARIATE_STATISTICAL:
                    scores = await self._univariate_statistical_selection(X, y)
                elif method == FeatureSelectionMethod.MODEL_BASED_SELECTION:
                    scores = await self._model_based_selection(X, y)
                elif method == FeatureSelectionMethod.VARIANCE_THRESHOLD:
                    scores = await self._variance_threshold_selection(X, y)
                elif method == FeatureSelectionMethod.CORRELATION_FILTER:
                    scores = await self._correlation_filter_selection(X, y)
                elif method == FeatureSelectionMethod.BASKETBALL_INTELLIGENCE:
                    scores = await self._basketball_intelligence_selection(X, y)
                else:
                    continue

                all_feature_scores.extend(scores)
                method_results[method] = scores

            except Exception as e:
                self.logger.warning(f"🎯 Feature selection method {method.value} failed: {e}")

        # Ensemble feature selection - combine results from all methods
        if self.config.enable_ensemble_selection:
            selected_features = await self._ensemble_feature_selection(method_results, X.columns.tolist())
        else:
            # Simple aggregation - select features that appear in multiple methods
            feature_vote_counts = {}
            for score in all_feature_scores:
                if score.feature_name in feature_vote_counts:
                    feature_vote_counts[score.feature_name] += score.importance_score
                else:
                    feature_vote_counts[score.feature_name] = score.importance_score

            # Select top features based on aggregated scores
            sorted_features = sorted(feature_vote_counts.items(), key=lambda x: x[1], reverse=True)
            n_select = min(self.config.max_features, len(sorted_features))
            selected_features = [f[0] for f in sorted_features[:n_select]]

        self.logger.info(f"🎯 Multi-method selection: {len(selected_features)} features selected")
        return selected_features, all_feature_scores

    async def _mutual_information_selection(self, X: pd.DataFrame, y: pd.Series) -> List[FeatureImportanceScore]:
        """Mutual information based feature selection"""
        scores = []

        try:
            if not SKLEARN_AVAILABLE:
                return scores

            # Get numeric features only
            numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
            if not numeric_features:
                return scores

            X_numeric = X[numeric_features].fillna(0)

            # Determine if classification or regression
            if len(np.unique(y)) <= 10:  # Classification
                mi_scores = mutual_info_classif(X_numeric, y, random_state=self.config.random_state)
            else:  # Regression
                mi_scores = mutual_info_regression(X_numeric, y, random_state=self.config.random_state)

            for feature, score in zip(numeric_features, mi_scores):
                if score > self.config.mutual_info_threshold:
                    scores.append(FeatureImportanceScore(
                        feature_name=feature,
                        importance_score=score,
                        selection_method=FeatureSelectionMethod.MUTUAL_INFORMATION,
                        confidence=min(score * 2, 1.0),  # Normalize confidence
                        statistical_significance=score
                    ))

            self.logger.info(f"🧠 Mutual information: {len(scores)} features above threshold")

        except Exception as e:
            self.logger.error(f"Mutual information selection failed: {e}")

        return scores

    async def _recursive_feature_elimination(self, X: pd.DataFrame, y: pd.Series) -> List[FeatureImportanceScore]:
        """Recursive feature elimination with cross-validation"""
        scores = []

        try:
            if not SKLEARN_AVAILABLE:
                return scores

            # Get numeric features only
            numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
            if len(numeric_features) < 2:
                return scores

            X_numeric = X[numeric_features].fillna(0)

            # Use appropriate estimator based on problem type
            if len(np.unique(y)) <= 10:  # Classification
                estimator = RandomForestClassifier(n_estimators=50, random_state=self.config.random_state)
            else:  # Regression
                estimator = xgb.XGBRegressor(n_estimators=50, random_state=self.config.random_state)

            # Determine number of features to select
            n_features_to_select = min(self.config.max_features, len(numeric_features) // 2)

            # Perform RFE
            rfe = RFE(estimator, n_features_to_select=n_features_to_select)
            rfe.fit(X_numeric, y)

            # Get feature rankings and importance
            for i, (feature, selected, ranking) in enumerate(zip(numeric_features, rfe.support_, rfe.ranking_)):
                if selected:
                    # Calculate importance score (inverse of ranking, normalized)
                    importance_score = 1.0 / ranking
                    scores.append(FeatureImportanceScore(
                        feature_name=feature,
                        importance_score=importance_score,
                        selection_method=FeatureSelectionMethod.RECURSIVE_FEATURE_ELIMINATION,
                        confidence=importance_score,
                        statistical_significance=importance_score
                    ))

            self.logger.info(f"🔄 RFE: {len(scores)} features selected")

        except Exception as e:
            self.logger.error(f"RFE selection failed: {e}")

        return scores

    async def _univariate_statistical_selection(self, X: pd.DataFrame, y: pd.Series) -> List[FeatureImportanceScore]:
        """Univariate statistical feature selection"""
        scores = []

        try:
            if not SKLEARN_AVAILABLE:
                return scores

            # Get numeric features only
            numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
            if not numeric_features:
                return scores

            X_numeric = X[numeric_features].fillna(0)

            # Use appropriate scoring function
            if len(np.unique(y)) <= 10:  # Classification
                score_func = f_classif
            else:  # Regression
                score_func = f_regression

            # Perform univariate selection
            k_best = min(self.config.max_features, len(numeric_features))
            selector = SelectKBest(score_func=score_func, k=k_best)
            selector.fit(X_numeric, y)

            # Get feature scores
            feature_scores = selector.scores_
            selected_features = selector.get_support()

            for i, (feature, selected, score) in enumerate(zip(numeric_features, selected_features, feature_scores)):
                if selected and not np.isnan(score):
                    # Normalize score
                    normalized_score = score / (np.max(feature_scores) + 1e-6)
                    scores.append(FeatureImportanceScore(
                        feature_name=feature,
                        importance_score=normalized_score,
                        selection_method=FeatureSelectionMethod.UNIVARIATE_STATISTICAL,
                        confidence=normalized_score,
                        statistical_significance=score
                    ))

            self.logger.info(f"📈 Univariate: {len(scores)} features selected")

        except Exception as e:
            self.logger.error(f"Univariate selection failed: {e}")

        return scores

    async def _model_based_selection(self, X: pd.DataFrame, y: pd.Series) -> List[FeatureImportanceScore]:
        """Model-based feature selection using feature importance"""
        scores = []

        try:
            if not SKLEARN_AVAILABLE:
                return scores

            # Get numeric features only
            numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
            if len(numeric_features) < 2:
                return scores

            X_numeric = X[numeric_features].fillna(0)

            # Use appropriate model based on problem type
            if len(np.unique(y)) <= 10:  # Classification
                model = ExtraTreesClassifier(n_estimators=100, random_state=self.config.random_state)
            else:  # Regression
                model = xgb.XGBRegressor(n_estimators=100, random_state=self.config.random_state)

            # Fit model and get feature importance
            model.fit(X_numeric, y)

            if hasattr(model, 'feature_importances_'):
                importances = model.feature_importances_

                for feature, importance in zip(numeric_features, importances):
                    if importance > 0.001:  # Threshold for meaningful importance
                        scores.append(FeatureImportanceScore(
                            feature_name=feature,
                            importance_score=importance,
                            selection_method=FeatureSelectionMethod.MODEL_BASED_SELECTION,
                            confidence=importance,
                            statistical_significance=importance
                        ))

            self.logger.info(f"🌳 Model-based: {len(scores)} important features")

        except Exception as e:
            self.logger.error(f"Model-based selection failed: {e}")

        return scores

    async def _variance_threshold_selection(self, X: pd.DataFrame, y: pd.Series) -> List[FeatureImportanceScore]:
        """Variance threshold feature selection"""
        scores = []

        try:
            if not SKLEARN_AVAILABLE:
                return scores

            # Get numeric features only
            numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
            if not numeric_features:
                return scores

            X_numeric = X[numeric_features].fillna(0)

            # Apply variance threshold
            selector = VarianceThreshold(threshold=self.config.variance_threshold)
            selector.fit(X_numeric)

            selected_features = selector.get_support()
            variances = selector.variances_

            for feature, selected, variance in zip(numeric_features, selected_features, variances):
                if selected:
                    # Normalize variance score
                    normalized_score = min(variance / (np.max(variances) + 1e-6), 1.0)
                    scores.append(FeatureImportanceScore(
                        feature_name=feature,
                        importance_score=normalized_score,
                        selection_method=FeatureSelectionMethod.VARIANCE_THRESHOLD,
                        confidence=normalized_score,
                        statistical_significance=variance
                    ))

            self.logger.info(f"📊 Variance threshold: {len(scores)} features above threshold")

        except Exception as e:
            self.logger.error(f"Variance threshold selection failed: {e}")

        return scores

    async def _correlation_filter_selection(self, X: pd.DataFrame, y: pd.Series) -> List[FeatureImportanceScore]:
        """Correlation-based feature selection"""
        scores = []

        try:
            # Get numeric features only
            numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
            if not numeric_features:
                return scores

            X_numeric = X[numeric_features].fillna(0)

            # Calculate correlation with target
            target_correlations = {}
            for feature in numeric_features:
                try:
                    corr, p_value = pearsonr(X_numeric[feature], y)
                    if not np.isnan(corr):
                        target_correlations[feature] = abs(corr)
                except:
                    target_correlations[feature] = 0.0

            # Remove highly correlated features among themselves
            correlation_matrix = X_numeric.corr().abs()
            selected_features = set(numeric_features)

            for i in range(len(numeric_features)):
                for j in range(i+1, len(numeric_features)):
                    feature1, feature2 = numeric_features[i], numeric_features[j]

                    if (feature1 in selected_features and feature2 in selected_features and
                        correlation_matrix.loc[feature1, feature2] > self.config.correlation_threshold):

                        # Remove the feature with lower target correlation
                        if target_correlations[feature1] < target_correlations[feature2]:
                            selected_features.discard(feature1)
                        else:
                            selected_features.discard(feature2)

            # Create scores for selected features
            for feature in selected_features:
                correlation = target_correlations[feature]
                if correlation > 0.1:  # Minimum correlation threshold
                    scores.append(FeatureImportanceScore(
                        feature_name=feature,
                        importance_score=correlation,
                        selection_method=FeatureSelectionMethod.CORRELATION_FILTER,
                        confidence=correlation,
                        statistical_significance=correlation
                    ))

            self.logger.info(f"🔗 Correlation filter: {len(scores)} features selected")

        except Exception as e:
            self.logger.error(f"Correlation filter selection failed: {e}")

        return scores

    async def _basketball_intelligence_selection(self, X: pd.DataFrame, y: pd.Series) -> List[FeatureImportanceScore]:
        """Basketball intelligence-based feature selection"""
        scores = []

        try:
            # Basketball-specific feature importance
            basketball_features = {
                'true_shooting_pct': 0.95,
                'effective_fg_pct': 0.90,
                'player_impact_estimate': 0.85,
                'net_rating': 0.80,
                'efficiency_ratio': 0.75,
                'momentum_indicator': 0.70,
                'offensive_rating': 0.65,
                'defensive_rating': 0.65,
                'pace': 0.60,
                'form_stability': 0.55
            }

            # Check which basketball features are present
            for feature in X.columns:
                basketball_relevance = 0.0

                # Direct basketball feature match
                if feature in basketball_features:
                    basketball_relevance = basketball_features[feature]

                # Pattern matching for basketball features
                elif any(keyword in feature.lower() for keyword in
                        ['basketball', 'points', 'rebounds', 'assists', 'steals', 'blocks',
                         'shooting', 'efficiency', 'rating', 'pace', 'win', 'loss']):
                    basketball_relevance = 0.50

                # Generated basketball features
                elif feature.startswith('basketball_feature_'):
                    basketball_relevance = 0.60

                if basketball_relevance > 0.3:
                    scores.append(FeatureImportanceScore(
                        feature_name=feature,
                        importance_score=basketball_relevance,
                        selection_method=FeatureSelectionMethod.BASKETBALL_INTELLIGENCE,
                        confidence=basketball_relevance,
                        basketball_relevance=basketball_relevance,
                        statistical_significance=basketball_relevance
                    ))

            self.logger.info(f"🏀 Basketball intelligence: {len(scores)} relevant features")

        except Exception as e:
            self.logger.error(f"Basketball intelligence selection failed: {e}")

        return scores

    async def _ensemble_feature_selection(self,
                                        method_results: Dict[FeatureSelectionMethod, List[FeatureImportanceScore]],
                                        all_features: List[str]) -> List[str]:
        """Ensemble feature selection combining multiple methods"""
        feature_ensemble_scores = {}

        # Aggregate scores from all methods
        for method, scores in method_results.items():
            method_weight = self._get_method_weight(method)

            for score in scores:
                if score.feature_name not in feature_ensemble_scores:
                    feature_ensemble_scores[score.feature_name] = 0.0

                # Weighted aggregation
                feature_ensemble_scores[score.feature_name] += score.importance_score * method_weight

        # Sort features by ensemble score
        sorted_features = sorted(feature_ensemble_scores.items(), key=lambda x: x[1], reverse=True)

        # Select top features
        n_select = min(self.config.max_features, max(self.config.min_features, len(sorted_features)))
        selected_features = [f[0] for f in sorted_features[:n_select]]

        self.logger.info(f"🎯 Ensemble selection: {len(selected_features)} features from {len(method_results)} methods")
        return selected_features

    def _get_method_weight(self, method: FeatureSelectionMethod) -> float:
        """Get weight for each selection method"""
        weights = {
            FeatureSelectionMethod.BASKETBALL_INTELLIGENCE: 1.0,
            FeatureSelectionMethod.MUTUAL_INFORMATION: 0.9,
            FeatureSelectionMethod.RECURSIVE_FEATURE_ELIMINATION: 0.8,
            FeatureSelectionMethod.MODEL_BASED_SELECTION: 0.7,
            FeatureSelectionMethod.UNIVARIATE_STATISTICAL: 0.6,
            FeatureSelectionMethod.CORRELATION_FILTER: 0.5,
            FeatureSelectionMethod.VARIANCE_THRESHOLD: 0.4
        }
        return weights.get(method, 0.5)

    async def _basketball_intelligence_enhancement(self,
                                                 X: pd.DataFrame,
                                                 y: pd.Series,
                                                 selected_features: List[str],
                                                 feature_scores: List[FeatureImportanceScore],
                                                 basketball_context: Optional[Dict[str, Any]] = None) -> Tuple[List[str], List[FeatureImportanceScore]]:
        """Enhance feature selection with basketball intelligence"""
        enhanced_features = selected_features.copy()
        enhanced_scores = feature_scores.copy()

        try:
            # Basketball domain expertise enhancement
            basketball_priority_features = [
                'true_shooting_pct', 'effective_fg_pct', 'player_impact_estimate',
                'net_rating', 'efficiency_ratio', 'momentum_indicator'
            ]

            # Ensure basketball priority features are included if available
            for feature in basketball_priority_features:
                if feature in X.columns and feature not in enhanced_features:
                    enhanced_features.append(feature)
                    enhanced_scores.append(FeatureImportanceScore(
                        feature_name=feature,
                        importance_score=0.8,
                        selection_method=FeatureSelectionMethod.BASKETBALL_INTELLIGENCE,
                        confidence=0.8,
                        basketball_relevance=0.9
                    ))

            # Use existing HMNV basketball intelligence if available
            if self.optimal_engine and basketball_context:
                try:
                    # Get basketball-enhanced features from optimal engine
                    basketball_enhanced = await self.optimal_engine.optimal_feature_selection(
                        X[enhanced_features], y
                    )
                    enhanced_features = basketball_enhanced.columns.tolist()
                except Exception as e:

            # Limit to max features
            if len(enhanced_features) > self.config.max_features:
                # Prioritize basketball features
                basketball_features = [f for f in enhanced_features if any(
                    keyword in f.lower() for keyword in ['basketball', 'true_shooting', 'efficiency', 'rating']
                )]
                other_features = [f for f in enhanced_features if f not in basketball_features]

                # Keep all basketball features and top other features
                n_other = self.config.max_features - len(basketball_features)
                enhanced_features = basketball_features + other_features[:max(0, n_other)]

            self.logger.info(f"🏀 Basketball enhancement: {len(enhanced_features)} features optimized")

        except Exception as e:
            self.logger.error(f"Basketball intelligence enhancement failed: {e}")

        return enhanced_features, enhanced_scores

    async def _final_feature_validation(self,
                                      X: pd.DataFrame,
                                      y: pd.Series,
                                      selected_features: List[str]) -> List[str]:
        """Final validation and optimization of selected features"""
        validated_features = []

        try:
            # Ensure features exist in dataframe
            available_features = [f for f in selected_features if f in X.columns]

            if not available_features:
                self.logger.warning("No valid features found, using top numeric features")
                numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
                available_features = numeric_features[:self.config.min_features]

            # Remove features with too many missing values
            for feature in available_features:
                missing_ratio = X[feature].isnull().sum() / len(X)
                if missing_ratio < 0.5:  # Keep features with less than 50% missing
                    validated_features.append(feature)

            # Ensure minimum number of features
            if len(validated_features) < self.config.min_features:
                numeric_features = X.select_dtypes(include=[np.number]).columns.tolist()
                additional_features = [f for f in numeric_features if f not in validated_features]
                needed = self.config.min_features - len(validated_features)
                validated_features.extend(additional_features[:needed])

            # Final cross-validation check if possible
            if SKLEARN_AVAILABLE and len(validated_features) >= 2:
                try:
                    X_val = X[validated_features].fillna(0)

                    # Quick validation with simple model
                    if len(np.unique(y)) <= 10:  # Classification
                        model = RandomForestClassifier(n_estimators=10, random_state=42)
                    else:  # Regression
                        model = RandomForestRegressor(n_estimators=10, random_state=42)

                    # Cross-validation score
                    cv_scores = cross_val_score(model, X_val, y, cv=3, scoring='accuracy' if len(np.unique(y)) <= 10 else 'r2')
                    avg_score = np.mean(cv_scores)

                    self.logger.info(f"🎯 Validation score: {avg_score:.3f}")

                except Exception as e:

            self.logger.info(f"✅ Final validation: {len(validated_features)} features validated")

        except Exception as e:
            self.logger.error(f"Feature validation failed: {e}")
            # Fallback to original features
            validated_features = selected_features

        return validated_features

    async def _generate_basketball_insights(self,
                                          X: pd.DataFrame,
                                          y: pd.Series,
                                          selected_features: List[str],
                                          basketball_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Generate basketball-specific insights from feature selection"""
        insights = {
            'total_features_analyzed': X.shape[1],
            'features_selected': len(selected_features),
            'basketball_features_count': 0,
            'feature_categories': {},
            'top_basketball_features': [],
            'feature_engineering_impact': {},
            'recommendations': []
        }

        try:
            # Categorize features
            categories = {
                'efficiency': ['efficiency', 'rating', 'true_shooting', 'effective_fg'],
                'performance': ['points', 'rebounds', 'assists', 'steals', 'blocks'],
                'team_metrics': ['pace', 'net_rating', 'offensive', 'defensive'],
                'momentum': ['momentum', 'streak', 'form', 'trend'],
                'interaction': ['_x_', '_div_', '_diff_'],
                'temporal': ['rolling', 'lag', 'trend'],
                'statistical': ['mean', 'std', 'max', 'min', 'median']
            }

            for feature in selected_features:
                for category, keywords in categories.items():
                    if any(keyword in feature.lower() for keyword in keywords):
                        if category not in insights['feature_categories']:
                            insights['feature_categories'][category] = 0
                        insights['feature_categories'][category] += 1

                        if category in ['efficiency', 'performance', 'team_metrics']:
                            insights['basketball_features_count'] += 1

            # Top basketball features
            basketball_features = [f for f in selected_features if any(
                keyword in f.lower() for keyword in ['basketball', 'efficiency', 'rating', 'points', 'rebounds']
            )]
            insights['top_basketball_features'] = basketball_features[:10]

            # Generate recommendations
            if insights['basketball_features_count'] < len(selected_features) * 0.3:
                insights['recommendations'].append("Consider adding more basketball-specific features")

            if 'efficiency' not in insights['feature_categories']:
                insights['recommendations'].append("Include efficiency metrics for better basketball predictions")

            if 'temporal' in insights['feature_categories']:
                insights['recommendations'].append("Temporal features detected - good for trend analysis")

            self.logger.info(f"🏀 Generated insights: {insights['basketball_features_count']} basketball features")

        except Exception as e:
            self.logger.error(f"Basketball insights generation failed: {e}")

        return insights

    async def _calculate_performance_metrics(self, X: pd.DataFrame, y: pd.Series) -> Dict[str, float]:
        """Calculate performance metrics for selected features"""
        metrics = {
            'feature_count': X.shape[1],
            'sample_count': X.shape[0],
            'missing_data_ratio': 0.0,
            'feature_correlation_avg': 0.0,
            'estimated_accuracy': 0.0
        }

        try:
            # Missing data ratio
            total_cells = X.shape[0] * X.shape[1]
            missing_cells = X.isnull().sum().sum()
            metrics['missing_data_ratio'] = missing_cells / total_cells if total_cells > 0 else 0.0

            # Average feature correlation
            numeric_features = X.select_dtypes(include=[np.number]).columns
            if len(numeric_features) > 1:
                corr_matrix = X[numeric_features].corr().abs()
                # Get upper triangle (excluding diagonal)
                upper_triangle = corr_matrix.where(np.triu(np.ones(corr_matrix.shape), k=1).astype(bool))
                metrics['feature_correlation_avg'] = upper_triangle.stack().mean()

            # Quick accuracy estimation
            if SKLEARN_AVAILABLE and len(X.columns) >= 2:
                try:
                    X_clean = X.fillna(0)
                    if len(np.unique(y)) <= 10:  # Classification
                        model = RandomForestClassifier(n_estimators=20, random_state=42)
                        scores = cross_val_score(model, X_clean, y, cv=3, scoring='accuracy')
                    else:  # Regression
                        model = RandomForestRegressor(n_estimators=20, random_state=42)
                        scores = cross_val_score(model, X_clean, y, cv=3, scoring='r2')

                    metrics['estimated_accuracy'] = np.mean(scores)
                except Exception:
                    metrics['estimated_accuracy'] = 0.5  # Default estimate

            self.logger.info(f"📊 Performance metrics calculated: {metrics['estimated_accuracy']:.3f} estimated accuracy")

        except Exception as e:
            self.logger.error(f"Performance metrics calculation failed: {e}")

        return metrics

    def _calculate_convergence_metrics(self) -> Dict[str, float]:
        """Calculate convergence metrics for the feature selection process"""
        return {
            'selection_stability': 0.85,  # Placeholder - would track across multiple runs
            'method_agreement': 0.78,     # Placeholder - agreement between methods
            'basketball_alignment': 0.92  # Placeholder - alignment with basketball domain knowledge
        }


# =============================================================================
# CONVENIENCE FUNCTIONS
# =============================================================================

def create_advanced_feature_selector(
    max_features: int = 50,
    enable_basketball_intelligence: bool = True,
    methods: Optional[List[FeatureSelectionMethod]] = None
) -> AdvancedFeatureSelectionEngineeringSystem:
    """Create an advanced feature selection system with specified configuration"""

    if methods is None:
        methods = [
            FeatureSelectionMethod.MUTUAL_INFORMATION,
            FeatureSelectionMethod.RECURSIVE_FEATURE_ELIMINATION,
            FeatureSelectionMethod.BASKETBALL_INTELLIGENCE,
            FeatureSelectionMethod.MODEL_BASED_SELECTION
        ]

    config = FeatureSelectionConfig(
        methods=methods,
        max_features=max_features,
        enable_basketball_intelligence=enable_basketball_intelligence,
        enable_ensemble_selection=True
    )

    return AdvancedFeatureSelectionEngineeringSystem(config)


async def main():
    """Example usage of the Advanced Feature Selection & Engineering System"""

    # Create sample basketball data
    np.random.seed(42)
    n_samples = 1000

    # Basketball-style features
    data = {
        'points': np.random.normal(100, 15, n_samples),
        'field_goals_attempted': np.random.normal(80, 10, n_samples),
        'free_throws_attempted': np.random.normal(20, 5, n_samples),
        'field_goals_made': np.random.normal(40, 8, n_samples),
        'three_pointers_made': np.random.normal(12, 4, n_samples),
        'total_rebounds': np.random.normal(45, 8, n_samples),
        'assists': np.random.normal(25, 6, n_samples),
        'steals': np.random.normal(8, 3, n_samples),
        'blocks': np.random.normal(5, 2, n_samples),
        'offensive_rating': np.random.normal(110, 10, n_samples),
        'defensive_rating': np.random.normal(105, 8, n_samples),
        'pace': np.random.normal(100, 5, n_samples),
        'win_streak': np.random.randint(0, 10, n_samples),
        'loss_streak': np.random.randint(0, 8, n_samples)
    }

    X = pd.DataFrame(data)

    # Create target variable (win probability)
    y = (
        0.3 * (X['points'] - X['points'].mean()) / X['points'].std() +
        0.2 * (X['offensive_rating'] - X['defensive_rating']) / 10 +
        0.1 * (X['win_streak'] - X['loss_streak']) +
        np.random.normal(0, 0.1, n_samples)
    )
    y = (y > y.median()).astype(int)  # Convert to binary classification

    # Create feature selector
    selector = create_advanced_feature_selector(
        max_features=20,
        enable_basketball_intelligence=True
    )

    # Perform feature selection and engineering
    basketball_context = {'league': 'NBA', 'season': '2023-24'}
    result = await selector.select_and_engineer_features(X, y, basketball_context)

    # Display results

    for key, value in result.basketball_insights.items():

    for key, value in result.performance_metrics.items():

    for i, feature in enumerate(result.selected_features[:10], 1):



if __name__ == "__main__":
    asyncio.run(main())
