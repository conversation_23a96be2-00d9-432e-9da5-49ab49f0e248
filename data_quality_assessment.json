{"timestamp": "2025-07-01T10:15:31.000354", "total_tables": 10, "average_quality": 0.8610695952899416, "tables": {"sqlite_sequence": {"records": 0, "overall_score": 0.16, "quality_level": "CRITICAL", "anomalies": 0, "recommendations": ["Improve data completeness (current: 0.0%, target: 85.0%)", "Address data consistency issues (current: 0.0%, target: 80.0%)", "Remove duplicate records (current uniqueness: 0.0%, target: 95.0%)", "Update data sources for more recent data (current: 0.0%, target: 70.0%)"]}, "teams": {"records": 4021, "overall_score": 0.9548868440686397, "quality_level": "PRISTINE", "anomalies": 0, "recommendations": ["Improve data completeness (current: 77.4%, target: 85.0%)"]}, "players": {"records": 24153, "overall_score": 0.7460759497285266, "quality_level": "FAIR", "anomalies": 3, "recommendations": ["Improve data completeness (current: 67.5%, target: 85.0%)", "Address data consistency issues (current: 5.5%, target: 80.0%)", "Investigate and resolve 3 detected anomalies"]}, "games": {"records": 135816, "overall_score": 0.8481785160314936, "quality_level": "GOOD", "anomalies": 0, "recommendations": ["Improve data completeness (current: 34.1%, target: 85.0%)"]}, "player_game_stats": {"records": 26213, "overall_score": 0.9593031810824509, "quality_level": "PRISTINE", "anomalies": 2, "recommendations": ["Investigate and resolve 2 detected anomalies"]}, "shot_analytics": {"records": 10, "overall_score": 0.975, "quality_level": "PRISTINE", "anomalies": 0, "recommendations": ["Data quality is excellent - maintain current standards"]}, "clutch_analytics": {"records": 1458, "overall_score": 1.0, "quality_level": "PRISTINE", "anomalies": 7, "recommendations": ["Investigate and resolve 7 detected anomalies"]}, "shooting_analytics": {"records": 20, "overall_score": 0.9894736842105264, "quality_level": "PRISTINE", "anomalies": 0, "recommendations": ["Data quality is excellent - maintain current standards"]}, "advanced_analytics": {"records": 1733, "overall_score": 1.0, "quality_level": "PRISTINE", "anomalies": 2, "recommendations": ["Investigate and resolve 2 detected anomalies"]}, "defense_analytics": {"records": 1733, "overall_score": 0.9777777777777779, "quality_level": "PRISTINE", "anomalies": 3, "recommendations": ["Investigate and resolve 3 detected anomalies"]}}}