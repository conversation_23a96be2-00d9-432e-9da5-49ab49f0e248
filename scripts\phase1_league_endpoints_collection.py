import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
import requests
import time
import json
from datetime import datetime
import sys
import os
from collections import defaultdict
from smart_duplicate_prevention_system import SmartDuplicatePreventionSystem


#!/usr/bin/env python3
"""
Phase 1 - High Success League Endpoints Collection
Collect league-wide data with 90% success rate for maximum data acquisition
"""


# Add the scripts directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase1LeagueEndpointsCollector:
    """Collect high-success league endpoints for maximum data acquisition"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }
        
        # Phase 1 High-Success League Endpoints
        self.phase1_endpoints = {
            'leaguedashplayerstats': {
                'description': 'League-wide player statistics',
                'success_rate': 'HIGH',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0
                }
            },
            'leaguedashteamstats': {
                'description': 'League-wide team statistics',
                'success_rate': 'HIGH',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0
                }
            },
            'leaguedashplayershotlocations': {
                'description': 'League player shot locations',
                'success_rate': 'HIGH',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0,
                    'DistanceRange': '5ft Range'
                }
            },
            'leaguedashteamclutch': {
                'description': 'League-wide team clutch stats',
                'success_rate': 'HIGH',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'ClutchTime': 'Last 5 Minutes',
                    'AheadBehind': 'Ahead or Behind',
                    'PointDiff': 5,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0
                }
            },
            'leaguedashlineups': {
                'description': 'League lineup statistics',
                'success_rate': 'HIGH',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0,
                    'GroupQuantity': 5
                }
            },
            'leagueleaders': {
                'description': 'League statistical leaders',
                'success_rate': 'HIGH',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'PerMode': 'PerGame',
                    'StatCategory': 'PTS',
                    'Scope': 'S',
                    'ActiveFlag': 'No'
                }
            }
        }
        
        # Priority seasons (recent first)
        self.seasons = {
            'NBA': ['2024-25', '2023-24', '2022-23', '2021-22', '2020-21'],
            'WNBA': ['2025', '2024', '2023', '2022', '2021']
        }
        
        # Initialize duplicate prevention
        logger.info("🔍 Initializing Smart Duplicate Prevention System...")
        self.duplicate_prevention = SmartDuplicatePreventionSystem(db_path)
        logger.info("✅ Duplicate prevention system ready")
        
    def collect_league_endpoint_data(self, endpoint: str, league: str, season: str) -> List[Dict[str, Any]]:
        """Collect data from a league endpoint"""
        endpoint_config = self.phase1_endpoints[endpoint]
        
        # Check if we already have this data
        if self.duplicate_prevention.check_endpoint_data_exists(league, 'LEAGUE', season, endpoint):
            logger.info(f"   ⏭️ Skipping {endpoint} for {league} {season} - already exists")
            return []
        
        collected_data = []
        
        try:
            logger.info(f"   📊 Collecting {endpoint} for {league} {season}...")
            
            url = f"{self.base_url}/{endpoint}"
            params = endpoint_config['params'].copy()
            
            # Set league and season parameters
            params['Season'] = season
            params['LeagueID'] = '00' if league == 'NBA' else '10'
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                for result_set_idx, result_set in enumerate(data['resultSets']):
                    headers = result_set.get('headers', [])
                    rows = result_set.get('rowSet', [])
                    result_set_name = result_set.get('name', f'ResultSet_{result_set_idx}')
                    
                    for row_idx, row in enumerate(rows):
                        if len(row) == len(headers):
                            row_dict = dict(zip(headers, row))
                            
                            # Create comprehensive record
                            record = {
                                'league_name': league,
                                'league_id': '00' if league == 'NBA' else '10',
                                'season': season,
                                'endpoint': endpoint,
                                'result_set_name': result_set_name,
                                'data_category': f'phase1_league_{endpoint}',
                                'data_type': f'{endpoint}_league_data',
                                'source_file': f"{endpoint}_{league}_{season}.json",
                                'source_table': endpoint,
                                'raw_data': json.dumps(row_dict),
                                'created_at': datetime.now().isoformat()
                            }
                            
                            # Extract key information
                            self._extract_league_data_fields(record, row_dict, endpoint)
                            
                            collected_data.append(record)
            
            time.sleep(0.6)  # Rate limiting
            
        except Exception as e:
            logger.warning(f"⚠️ Error collecting {endpoint} for {league} {season}: {e}")
        
        logger.info(f"✅ Collected {len(collected_data)} {endpoint} records for {league} {season}")
        return collected_data
    
    def _extract_league_data_fields(self, record: Dict[str, Any], row_dict: Dict[str, Any], endpoint: str) -> None:
        """Extract key fields from league data"""
        
        # Common extractions
        record['player_id'] = row_dict.get('PLAYER_ID') or row_dict.get('TEAM_ID') or 'LEAGUE'
        record['player_name'] = row_dict.get('PLAYER_NAME') or row_dict.get('TEAM_NAME') or 'LEAGUE_DATA'
        record['team_id'] = row_dict.get('TEAM_ID')
        record['team_abbreviation'] = row_dict.get('TEAM_ABBREVIATION')
        
        # Endpoint-specific extractions
        if endpoint == 'leaguedashplayerstats':
            record['stat_category'] = 'player_stats'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['games_played'] = row_dict.get('GP', 0)
            record['minutes'] = row_dict.get('MIN', 0)
            
        elif endpoint == 'leaguedashteamstats':
            record['stat_category'] = 'team_stats'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['wins'] = row_dict.get('W', 0)
            record['losses'] = row_dict.get('L', 0)
            
        elif endpoint == 'leaguedashplayershotlocations':
            record['stat_category'] = 'shot_locations'
            record['stat_value'] = row_dict.get('FG_PCT', 0)
            record['shot_attempts'] = row_dict.get('FGA', 0)
            record['shot_distance'] = row_dict.get('SHOT_ZONE_RANGE', '')
            
        elif endpoint == 'leaguedashteamclutch':
            record['stat_category'] = 'team_clutch'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['clutch_games'] = row_dict.get('GP', 0)
            
        elif endpoint == 'leaguedashlineups':
            record['stat_category'] = 'lineups'
            record['stat_value'] = row_dict.get('PLUS_MINUS', 0)
            record['lineup_minutes'] = row_dict.get('MIN', 0)
            
        elif endpoint == 'leagueleaders':
            record['stat_category'] = 'league_leaders'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['rank'] = row_dict.get('RANK', 0)
    
    def insert_league_data(self, data_batch: List[Dict[str, Any]]) -> int:
        """Insert league data with duplicate prevention"""
        if not data_batch:
            return 0
        
        # Filter duplicates
        new_records, duplicate_stats = self.duplicate_prevention.filter_new_data_only(data_batch)
        
        if duplicate_stats['duplicates'] > 0:
            logger.info(f"   🔍 Filtered out {duplicate_stats['duplicates']} duplicates, inserting {duplicate_stats['new_records']} new records")
        
        if not new_records:
            return 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        
        for record in new_records:
            try:
                cursor.execute("""
                    INSERT INTO unified_nba_wnba_data (
                        source_file, source_table, data_category, season, league_id, league_name,
                        season_type, player_id, player_name, team_id, team_abbreviation,
                        stat_category, stat_value, data_type, raw_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['source_file'], record['source_table'], record['data_category'],
                    record['season'], record['league_id'], record['league_name'],
                    'Regular Season', record['player_id'], record['player_name'],
                    record.get('team_id'), record.get('team_abbreviation'),
                    record.get('stat_category'), record.get('stat_value'),
                    record['data_type'], record['raw_data'], record['created_at']
                ))
                inserted_count += 1
                
            except Exception as e:
                logger.warning(f"⚠️ Error inserting record: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        return inserted_count
    
    def collect_phase1_league_data(self, league: str) -> Dict[str, Any]:
        """Collect all Phase 1 league data for a league"""
        logger.info(f"🚀 PHASE 1 LEAGUE ENDPOINTS COLLECTION - {league}")
        logger.info("=" * 65)
        
        start_time = datetime.now()
        total_records = 0
        
        for season in self.seasons[league]:
            logger.info(f"\n📅 Processing {league} {season}...")
            
            # Collect data from each endpoint
            for endpoint, config in self.phase1_endpoints.items():
                logger.info(f"\n🎯 Collecting {endpoint} ({config['description']})...")
                
                endpoint_data = self.collect_league_endpoint_data(endpoint, league, season)
                
                if endpoint_data:
                    inserted = self.insert_league_data(endpoint_data)
                    total_records += inserted
                    logger.info(f"   ✅ Inserted {inserted:,} records")
                else:
                    logger.info(f"   ⏭️ No new data for {endpoint}")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎉 PHASE 1 {league} LEAGUE COLLECTION COMPLETE!")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   📊 Total records: {total_records:,}")
        
        return {
            'success': True,
            'league': league,
            'duration': str(duration),
            'total_records': total_records
        }

def main():
    """Execute Phase 1 league endpoints collection"""
    
    collector = Phase1LeagueEndpointsCollector()
    
    # Start with NBA
    nba_results = collector.collect_phase1_league_data('NBA')
    
    if nba_results['success']:
        
        # Then WNBA
        wnba_results = collector.collect_phase1_league_data('WNBA')
        
        if wnba_results['success']:
            
            total_records = nba_results['total_records'] + wnba_results['total_records']
            
            
    
    return nba_results

if __name__ == "__main__":
    main()
