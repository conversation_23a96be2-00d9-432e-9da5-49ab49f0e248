#!/usr/bin/env python3
"""
Debug what's actually saved in the model checkpoints
"""

import torch
import numpy as np
from pathlib import Path

def debug_checkpoint_contents():
    """Debug what's saved in model checkpoints"""
    print("🔍 DEBUGGING MODEL CHECKPOINT CONTENTS")
    print("=" * 60)
    
    # Check a player props model
    points_model_path = Path("models/player_props/wnba_points/best_points_model.pt")
    
    if points_model_path.exists():
        print(f"📊 Loading checkpoint: {points_model_path}")
        checkpoint = torch.load(points_model_path, map_location='cpu')
        
        print(f"\n🔑 Checkpoint keys: {list(checkpoint.keys())}")
        
        for key, value in checkpoint.items():
            if key == 'model_state_dict':
                print(f"   {key}: {type(value)} with {len(value)} parameters")
                # Show first few parameter names
                param_names = list(value.keys())[:5]
                print(f"      First 5 params: {param_names}")
            elif key == 'config':
                print(f"   {key}: {type(value)}")
                if hasattr(value, '__dict__'):
                    print(f"      Config attrs: {list(value.__dict__.keys())}")
            else:
                print(f"   {key}: {type(value)} = {value}")
        
        # Check if scalers are saved anywhere
        scaler_keys = [k for k in checkpoint.keys() if 'scaler' in k.lower()]
        print(f"\n🔍 Scaler-related keys: {scaler_keys}")
        
        if not scaler_keys:
            print("❌ NO SCALERS FOUND IN CHECKPOINT!")
            print("   This explains why we get 'No scaler found' warnings")
        
    else:
        print(f"❌ Model file not found: {points_model_path}")
    
    # Check game model too
    game_model_path = Path("models/wnba_neural_models/best_model.pt")
    if game_model_path.exists():
        print(f"\n📊 Loading game model checkpoint: {game_model_path}")
        game_checkpoint = torch.load(game_model_path, map_location='cpu')
        print(f"🔑 Game model keys: {list(game_checkpoint.keys())}")
        
        scaler_keys = [k for k in game_checkpoint.keys() if 'scaler' in k.lower()]
        print(f"🔍 Game model scaler keys: {scaler_keys}")

def debug_feature_preparation():
    """Debug how features are being prepared"""
    print("\n" + "=" * 60)
    print("🔍 DEBUGGING FEATURE PREPARATION")
    print("=" * 60)
    
    # Test the feature preparation with different players
    test_players = [
        {"name": "A'ja Wilson", "team": "Las Vegas Aces", "position": "C", "tier": 1},
        {"name": "Sabrina Ionescu", "team": "New York Liberty", "position": "G", "tier": 1},
        {"name": "Kelsey Plum", "team": "Las Vegas Aces", "position": "G", "tier": 2}
    ]
    
    # Import the unified service
    import sys
    sys.path.append('.')
    from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService
    
    service = UnifiedNeuralPredictionService("WNBA")
    
    print("🧪 Testing feature preparation for different players:")
    
    for player in test_players:
        print(f"\n👤 {player['name']}:")
        
        # Test feature preparation
        features = service._prepare_player_features(player, "points")
        print(f"   Feature vector shape: {features.shape}")
        print(f"   Feature vector (first 10): {features[:10]}")
        print(f"   Feature vector (last 5): {features[-5:]}")
        print(f"   Feature vector sum: {np.sum(features):.3f}")
        print(f"   Feature vector mean: {np.mean(features):.3f}")
        print(f"   Feature vector std: {np.std(features):.3f}")

if __name__ == "__main__":
    debug_checkpoint_contents()
    debug_feature_preparation()
