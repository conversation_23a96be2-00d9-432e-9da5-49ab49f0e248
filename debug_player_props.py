#!/usr/bin/env python3
"""
Debug script to investigate player props prediction issues
"""

import torch
import numpy as np
import logging

# Setup debug logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def debug_player_props_model():
    """Debug a single player props model to see raw outputs"""
    
    # Load the points model
    model_path = "models/player_props/wnba_points/best_points_model.pt"
    checkpoint = torch.load(model_path, map_location='cpu')
    
    print("🔍 Checkpoint keys:", list(checkpoint.keys()))
    print("🔍 Config:", checkpoint.get('config', {}))
    
    # Load model architecture
    from src.neural_cortex.player_props_neural_pipeline import PlayerPropsNeuralNetwork, PlayerPropsConfig
    
    # Recreate config
    config_dict = checkpoint['config']

    # Create a simple config object with just the needed attributes
    class SimpleConfig:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    config = SimpleConfig(**config_dict)

    # Create model with individual parameters
    model = PlayerPropsNeuralNetwork(
        input_dim=config.input_dim,
        hidden_dim=config.hidden_dim,
        num_layers=config.num_layers,
        dropout_rate=config.dropout_rate,
        output_activation=config.output_activation,
        use_batch_norm=config.use_batch_norm
    )
    model.load_state_dict(checkpoint['model_state_dict'])
    model.eval()
    
    print(f"🔍 Model input dim: {config.input_dim}")
    print(f"🔍 Model output dim: {config.output_dim}")
    
    # Test with different input patterns
    test_cases = [
        np.zeros(32),  # All zeros
        np.ones(32),   # All ones  
        np.random.normal(0, 1, 32),  # Standard normal (like scaled features)
        np.random.uniform(0, 1, 32), # Uniform [0,1]
        np.full(32, 0.5),  # All 0.5 (like our current features)
    ]
    
    print("\n🔍 Testing different input patterns:")
    for i, features in enumerate(test_cases):
        features_tensor = torch.FloatTensor(features).unsqueeze(0)
        
        with torch.no_grad():
            prediction = model(features_tensor)
            pred_value = prediction.item()
        
        print(f"   Test {i+1}: input_mean={np.mean(features):.3f}, input_std={np.std(features):.3f} → prediction={pred_value:.6f}")
    
    # Test with realistic basketball features
    print("\n🔍 Testing with realistic basketball features:")
    realistic_features = np.array([
        15.0,  # stat_value (points baseline)
        50.0,  # rank_position
        0.75,  # player_consistency
        2.0,   # player_tier
        1.0,   # position_encoded
        0.5,   # opponent_strength
        1.0,   # home_advantage
        1.0,   # rest_days
        0.0,   # back_to_back
        0.6,   # season_progress
        0.0,   # recent_form
        0.0,   # hot_streak
        0.0,   # cold_streak
        # Fill remaining 19 features
        *([0.5] * 19)
    ])
    
    # Test raw features
    features_tensor = torch.FloatTensor(realistic_features).unsqueeze(0)
    with torch.no_grad():
        prediction = model(features_tensor)
        pred_raw = prediction.item()
    
    print(f"   Raw realistic features: prediction={pred_raw:.6f}")
    
    # Test scaled features (standardized)
    features_scaled = (realistic_features - np.mean(realistic_features)) / np.std(realistic_features)
    features_tensor = torch.FloatTensor(features_scaled).unsqueeze(0)
    with torch.no_grad():
        prediction = model(features_tensor)
        pred_scaled = prediction.item()
    
    print(f"   Scaled realistic features: prediction={pred_scaled:.6f}")
    
    # Test what happens with typical training-like scaled features
    # During training, features are standardized to mean=0, std=1
    training_like_features = np.random.normal(0, 1, 32)
    features_tensor = torch.FloatTensor(training_like_features).unsqueeze(0)
    with torch.no_grad():
        prediction = model(features_tensor)
        pred_training = prediction.item()
    
    print(f"   Training-like scaled features: prediction={pred_training:.6f}")

if __name__ == "__main__":
    debug_player_props_model()
