#!/usr/bin/env python3
"""
Comprehensive testing script for all WNBA player props models against historical games
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import time
import numpy as np
from datetime import datetime
from src.neural_cortex.player_props_neural_pipeline import PlayerPropsConfig, PlayerPropsTrainingPipeline

# All player props to test
PLAYER_PROPS = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']

def load_trained_model(prop_type: str):
    """Load a trained player props model"""
    config = PlayerPropsConfig(
        prop_type=prop_type,
        league='WNBA',
        num_epochs=1,  # Not training, just loading
        output_activation='linear',
        dropout_rate=0.2,
        hidden_dim=256,
        num_layers=3
    )
    
    pipeline = PlayerPropsTrainingPipeline(config)
    return pipeline, config

async def test_single_prop_model(prop_type: str) -> dict:
    """Test a single player props model against historical data"""
    print(f"\n🏀 ===== TESTING {prop_type.upper()} MODEL =====")
    start_time = time.time()
    
    try:
        # Load the trained model
        pipeline, config = load_trained_model(prop_type)
        
        print(f"📊 Loading {prop_type} model from: {config.model_save_path}")
        
        # Prepare test data (same as training data for comprehensive evaluation)
        print(f"📊 Preparing test dataset...")

        # Load data using the same pipeline (not async) - we need this to set up scalers
        _ = pipeline.prepare_data()

        # Load the trained model manually
        import torch
        from pathlib import Path

        best_model_path = Path(config.model_save_path) / f"best_{prop_type}_model.pt"
        if not best_model_path.exists():
            return {
                'prop_type': prop_type,
                'success': False,
                'error': f'Trained model not found at {best_model_path}'
            }

        # Load the checkpoint
        checkpoint = torch.load(best_model_path, map_location='cpu')

        # Build the model first (it's None by default)
        pipeline.model = pipeline._build_model()
        pipeline.model.load_state_dict(checkpoint['model_state_dict'])
        pipeline.model.eval()

        print(f"✅ Model loaded successfully from {best_model_path}")

        # Get test data (use full dataset for comprehensive evaluation)
        # Create a new dataset to get all the data
        from src.neural_cortex.player_props_neural_pipeline import PlayerPropsDataset
        full_dataset = PlayerPropsDataset(
            data_path="", config=config, split="full"
        )

        # Extract all features and targets
        features = full_dataset.data
        targets = full_dataset.targets

        print(f"📊 Test dataset: {len(features)} samples")
        print(f"🎯 Target range: {targets.min():.2f} to {targets.max():.2f}")
        print(f"🎯 Target mean: {targets.mean():.2f}, std: {targets.std():.2f}")

        # Make predictions on full dataset
        print(f"🔮 Making predictions...")
        # Scale features using the pipeline's scaler (fitted during prepare_data)
        scaled_features = pipeline.feature_scaler.transform(features)

        # Make predictions using the model directly with scaled features
        import torch
        with torch.no_grad():
            features_tensor = torch.FloatTensor(scaled_features)
            predictions_tensor = pipeline.model(features_tensor)
            scaled_predictions = predictions_tensor.numpy().flatten()

            # Unscale predictions back to original scale
            predictions = pipeline.target_scaler.inverse_transform(scaled_predictions.reshape(-1, 1)).flatten()
        
        # Calculate comprehensive metrics
        mae = np.mean(np.abs(predictions - targets))
        mse = np.mean((predictions - targets) ** 2)
        rmse = np.sqrt(mse)
        
        # R² score
        ss_res = np.sum((targets - predictions) ** 2)
        ss_tot = np.sum((targets - np.mean(targets)) ** 2)
        r2 = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
        
        # Accuracy metrics for different thresholds
        errors = np.abs(predictions - targets)
        accuracy_1 = np.mean(errors <= 1.0) * 100
        accuracy_2 = np.mean(errors <= 2.0) * 100
        accuracy_25pct = np.mean(errors <= (targets * 0.25)) * 100
        
        # Prediction bias
        bias = np.mean(predictions - targets)
        
        # Distribution analysis
        pred_mean = np.mean(predictions)
        pred_std = np.std(predictions)
        target_mean = np.mean(targets)
        target_std = np.std(targets)
        
        # Error distribution
        error_percentiles = np.percentile(errors, [25, 50, 75, 90, 95])
        
        test_time = time.time() - start_time
        
        print(f"\n✅ {prop_type.upper()} TESTING COMPLETED!")
        print(f"⏱️  Test Time: {test_time:.1f}s")
        print(f"📊 Samples Tested: {len(features):,}")
        print(f"📊 MAE: {mae:.3f}")
        print(f"📊 RMSE: {rmse:.3f}")
        print(f"📊 R²: {r2:.3f}")
        print(f"🎯 Accuracy (±1 unit): {accuracy_1:.1f}%")
        print(f"🎯 Accuracy (±2 units): {accuracy_2:.1f}%")
        print(f"🎯 Accuracy (±25%): {accuracy_25pct:.1f}%")
        print(f"📈 Prediction Bias: {bias:+.3f}")
        print(f"📊 Pred vs Target: {pred_mean:.2f}±{pred_std:.2f} vs {target_mean:.2f}±{target_std:.2f}")
        
        # Quality assessment
        if r2 > 0.6:
            quality = "EXCELLENT"
            emoji = "🏆"
        elif r2 > 0.4:
            quality = "GOOD"
            emoji = "✅"
        elif r2 > 0.2:
            quality = "FAIR"
            emoji = "⚠️"
        else:
            quality = "POOR"
            emoji = "❌"
        
        print(f"{emoji} {quality}: R² = {r2:.3f}")
        
        return {
            'prop_type': prop_type,
            'success': True,
            'test_time': test_time,
            'samples_tested': len(features),
            'metrics': {
                'mae': mae,
                'rmse': rmse,
                'r2': r2,
                'accuracy_1': accuracy_1,
                'accuracy_2': accuracy_2,
                'accuracy_25pct': accuracy_25pct,
                'bias': bias,
                'pred_mean': pred_mean,
                'pred_std': pred_std,
                'target_mean': target_mean,
                'target_std': target_std,
                'error_percentiles': error_percentiles.tolist()
            },
            'quality': quality,
            'config': config
        }
        
    except Exception as e:
        print(f"❌ ERROR testing {prop_type}: {e}")
        import traceback
        traceback.print_exc()
        return {
            'prop_type': prop_type,
            'success': False,
            'error': str(e),
            'test_time': time.time() - start_time
        }

async def test_all_player_props():
    """Test all player props models against historical data"""
    print("🚀 ===== WNBA PLAYER PROPS COMPREHENSIVE TESTING =====")
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Props to test: {', '.join(PLAYER_PROPS)}")
    print(f"📊 Testing against full historical dataset")
    
    overall_start = time.time()
    results = []
    
    # Test each prop model
    for prop_type in PLAYER_PROPS:
        result = await test_single_prop_model(prop_type)
        results.append(result)
        
        # Brief pause between tests
        await asyncio.sleep(1)
    
    # Summary report
    total_time = time.time() - overall_start
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"\n🏆 ===== TESTING SUMMARY =====")
    print(f"⏱️  Total Time: {total_time:.1f}s ({total_time/60:.1f} minutes)")
    print(f"✅ Successful: {len(successful_tests)}/{len(PLAYER_PROPS)}")
    print(f"❌ Failed: {len(failed_tests)}")
    
    if successful_tests:
        print(f"\n📊 ===== MODEL PERFORMANCE SUMMARY =====")
        
        # Performance table
        print(f"{'Prop':<8} {'R²':<6} {'MAE':<6} {'±1':<6} {'±2':<6} {'Bias':<7} {'Quality'}")
        print("-" * 55)
        
        total_samples = 0
        for result in successful_tests:
            metrics = result['metrics']
            prop = result['prop_type']
            quality = result['quality']
            total_samples = result['samples_tested']  # Same for all
            
            print(f"{prop:<8} {metrics['r2']:<6.3f} {metrics['mae']:<6.3f} "
                  f"{metrics['accuracy_1']:<6.1f} {metrics['accuracy_2']:<6.1f} "
                  f"{metrics['bias']:<+7.3f} {quality}")
        
        # Overall statistics
        avg_r2 = np.mean([r['metrics']['r2'] for r in successful_tests])
        avg_mae = np.mean([r['metrics']['mae'] for r in successful_tests])
        avg_acc_1 = np.mean([r['metrics']['accuracy_1'] for r in successful_tests])
        avg_acc_2 = np.mean([r['metrics']['accuracy_2'] for r in successful_tests])
        avg_bias = np.mean([r['metrics']['bias'] for r in successful_tests])
        
        print("-" * 55)
        print(f"{'AVERAGE':<8} {avg_r2:<6.3f} {avg_mae:<6.3f} "
              f"{avg_acc_1:<6.1f} {avg_acc_2:<6.1f} {avg_bias:<+7.3f}")
        
        # Best and worst models
        best_model = max(successful_tests, key=lambda x: x['metrics']['r2'])
        worst_model = min(successful_tests, key=lambda x: x['metrics']['r2'])
        
        print(f"\n🏆 Best Model: {best_model['prop_type']} (R² = {best_model['metrics']['r2']:.3f})")
        print(f"⚠️  Worst Model: {worst_model['prop_type']} (R² = {worst_model['metrics']['r2']:.3f})")
        
        # Dataset info
        print(f"\n📊 Dataset: {total_samples:,} WNBA samples tested")
        print(f"📈 Overall System Performance:")
        print(f"   • Average R²: {avg_r2:.3f} ({avg_r2*100:.1f}% variance explained)")
        print(f"   • Average MAE: {avg_mae:.3f}")
        print(f"   • Average ±1 accuracy: {avg_acc_1:.1f}%")
        print(f"   • Average ±2 accuracy: {avg_acc_2:.1f}%")
        print(f"   • Average bias: {avg_bias:+.3f}")
        
        # Quality distribution
        quality_counts = {}
        for result in successful_tests:
            quality = result['quality']
            quality_counts[quality] = quality_counts.get(quality, 0) + 1
        
        print(f"\n🎯 Quality Distribution:")
        for quality, count in quality_counts.items():
            print(f"   • {quality}: {count} models")
    
    if failed_tests:
        print(f"\n❌ ===== FAILED TESTS =====")
        for result in failed_tests:
            print(f"{result['prop_type']}: {result['error']}")
    
    print(f"\n📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return results

if __name__ == "__main__":
    try:
        results = asyncio.run(test_all_player_props())
        
        # Final status
        successful_count = sum(1 for r in results if r['success'])
        if successful_count == len(PLAYER_PROPS):
            print(f"\n🎉 SUCCESS: All {len(PLAYER_PROPS)} player props models tested successfully!")
            print("🚀 WNBA Player Props Neural System validation COMPLETE!")
        else:
            print(f"\n⚠️  PARTIAL SUCCESS: {successful_count}/{len(PLAYER_PROPS)} models tested")
            
    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
