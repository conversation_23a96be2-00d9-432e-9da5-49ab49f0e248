import pandas as pd
import sqlite3
import logging
from pathlib import Path
from datetime import datetime
import uuid
import hashlib

#!/usr/bin/env python3
"""
Simple migration runner for HYPER MEDUSA NEURAL VAULT
Consolidates CSV data into SQLite database
"""


# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_database_schema(conn):
    """Create database tables"""
    logger.info("Creating database schema...")
    
    # Teams table
    conn.execute("""
        CREATE TABLE IF NOT EXISTS teams (
            id TEXT PRIMARY KEY,
            name TEXT,
            abbreviation TEXT,
            city TEXT,
            conference TEXT,
            division TEXT,
            league TEXT,
            season TEXT,
            wins INTEGER DEFAULT 0,
            losses INTEGER DEFAULT 0,
            win_percentage REAL DEFAULT 0.0,
            games_played INTEGER DEFAULT 0,
            points_per_game REAL DEFAULT 0.0,
            points_allowed_per_game REAL DEFAULT 0.0,
            net_rating REAL DEFAULT 0.0,
            offensive_rating REAL DEFAULT 0.0,
            defensive_rating REAL DEFAULT 0.0,
            pace REAL DEFAULT 0.0,
            true_shooting_percentage REAL DEFAULT 0.0,
            effective_field_goal_percentage REAL DEFAULT 0.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Players table
    conn.execute("""
        CREATE TABLE IF NOT EXISTS players (
            id TEXT PRIMARY KEY,
            full_name TEXT,
            first_name TEXT,
            last_name TEXT,
            position TEXT,
            height TEXT,
            weight INTEGER,
            birth_date TIMESTAMP,
            years_experience INTEGER,
            college TEXT,
            country TEXT,
            mythic_roster_id TEXT,
            league TEXT,
            season TEXT,
            is_active BOOLEAN DEFAULT 1,
            jersey_number INTEGER,
            points_per_game REAL DEFAULT 0.0,
            rebounds_per_game REAL DEFAULT 0.0,
            assists_per_game REAL DEFAULT 0.0,
            steals_per_game REAL DEFAULT 0.0,
            blocks_per_game REAL DEFAULT 0.0,
            field_goal_percentage REAL DEFAULT 0.0,
            three_point_percentage REAL DEFAULT 0.0,
            free_throw_percentage REAL DEFAULT 0.0,
            player_efficiency_rating REAL DEFAULT 0.0,
            usage_rate REAL DEFAULT 0.0,
            true_shooting_percentage REAL DEFAULT 0.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Games table
    conn.execute("""
        CREATE TABLE IF NOT EXISTS games (
            id TEXT PRIMARY KEY,
            date TIMESTAMP,
            season TEXT,
            status TEXT,
            home_team_id TEXT,
            away_team_id TEXT,
            league TEXT,
            home_score INTEGER DEFAULT 0,
            away_score INTEGER DEFAULT 0,
            attendance INTEGER,
            arena TEXT,
            broadcast_network TEXT,
            spread_home REAL,
            spread_away REAL,
            total_points REAL,
            moneyline_home REAL,
            moneyline_away REAL,
            over_under_result TEXT,
            spread_result_home TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Player game stats table
    conn.execute("""
        CREATE TABLE IF NOT EXISTS player_game_stats (
            id TEXT PRIMARY KEY,
            hero_id TEXT,
            titan_clash_id TEXT,
            date TIMESTAMP,
            season TEXT,
            league TEXT,
            minutes_played REAL,
            points INTEGER DEFAULT 0,
            field_goals_made INTEGER DEFAULT 0,
            field_goals_attempted INTEGER DEFAULT 0,
            field_goal_percentage REAL DEFAULT 0.0,
            three_pointers_made INTEGER DEFAULT 0,
            three_pointers_attempted INTEGER DEFAULT 0,
            three_point_percentage REAL DEFAULT 0.0,
            free_throws_made INTEGER DEFAULT 0,
            free_throws_attempted INTEGER DEFAULT 0,
            free_throw_percentage REAL DEFAULT 0.0,
            offensive_rebounds INTEGER DEFAULT 0,
            defensive_rebounds INTEGER DEFAULT 0,
            total_rebounds INTEGER DEFAULT 0,
            assists INTEGER DEFAULT 0,
            steals INTEGER DEFAULT 0,
            blocks INTEGER DEFAULT 0,
            turnovers INTEGER DEFAULT 0,
            personal_fouls INTEGER DEFAULT 0,
            plus_minus REAL DEFAULT 0.0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Create indexes
    conn.execute("CREATE INDEX IF NOT EXISTS idx_teams_league_season ON teams(league, season)")
    conn.execute("CREATE INDEX IF NOT EXISTS idx_players_league_season ON players(league, season)")
    conn.execute("CREATE INDEX IF NOT EXISTS idx_games_date_teams ON games(date, home_team_id, away_team_id)")
    conn.execute("CREATE INDEX IF NOT EXISTS idx_player_stats_hero_game ON player_game_stats(hero_id, titan_clash_id)")
    
    conn.commit()
    logger.info("✅ Database schema created successfully")

def generate_id(data_dict):
    """Generate consistent ID from data"""
    # Create a hash from key fields
    key_fields = []
    for key in sorted(data_dict.keys()):
        if data_dict[key] is not None:
            key_fields.append(f"{key}:{data_dict[key]}")
    
    hash_input = "|".join(key_fields)
    return hashlib.md5(hash_input.encode()).hexdigest()

def consolidate_csv_files():
    """Consolidate all CSV files into SQLite database"""
    logger.info("🚀 Starting CSV data consolidation...")
    
    # Connect to SQLite database
    db_path = "hyper_medusa_consolidated.db"
    conn = sqlite3.connect(db_path)
    
    try:
        # Create schema
        create_database_schema(conn)
        
        # Find all CSV files
        data_dir = Path("data")
        csv_files = list(data_dir.rglob("*.csv"))
        
        logger.info(f"Found {len(csv_files)} CSV files to process")
        
        total_records = 0
        processed_files = 0
        
        for csv_file in csv_files:
            try:
                logger.info(f"Processing: {csv_file}")
                
                # Read CSV
                df = pd.read_csv(csv_file)
                
                if df.empty:
                    logger.warning(f"Empty file: {csv_file}")
                    continue
                
                # Determine table based on file content and path
                table_name = determine_table_name(csv_file, df)
                
                if table_name:
                    # Process and insert data
                    records_inserted = process_dataframe(conn, df, table_name, csv_file)
                    total_records += records_inserted
                    processed_files += 1
                    
                    logger.info(f"✅ Inserted {records_inserted} records into {table_name}")
                else:
                    logger.warning(f"Could not determine table for: {csv_file}")
                    
            except Exception as e:
                logger.error(f"❌ Error processing {csv_file}: {e}")
                continue
        
        logger.info(f"🎉 Consolidation complete!")
        logger.info(f"📊 Processed {processed_files} files")
        logger.info(f"📈 Total records inserted: {total_records}")
        logger.info(f"💾 Database saved to: {db_path}")
        
    finally:
        conn.close()

def determine_table_name(file_path, df):
    """Determine which table the CSV data belongs to"""
    file_name = file_path.name.lower()
    columns = [col.lower() for col in df.columns]

    # Game data indicators (check first as it's most specific)
    if any(keyword in file_name for keyword in ['game_finder', 'schedule', 'matchup']) or \
       any(col in columns for col in ['game_id', 'game_date', 'matchup', 'wl', 'season_id']):
        return 'games'

    # Player stats indicators (check before general player data)
    elif any(keyword in file_name for keyword in ['player_stats', 'advanced_player', 'box_score']) or \
         (any(col in columns for col in ['player_id', 'player_name']) and
          any(col in columns for col in ['pts', 'points', 'rebounds', 'assists', 'minutes', 'min'])):
        return 'player_game_stats'

    # Player data indicators
    elif any(keyword in file_name for keyword in ['player', 'roster']) or \
         any(col in columns for col in ['player_name', 'player_id', 'position', 'height', 'weight']):
        return 'players'

    # Team data indicators
    elif any(keyword in file_name for keyword in ['team']) or \
         any(col in columns for col in ['team_name', 'abbreviation', 'conference']):
        return 'teams'

    return None

def process_dataframe(conn, df, table_name, file_path):
    """Process dataframe and insert into appropriate table"""
    records_inserted = 0
    
    for _, row in df.iterrows():
        try:
            # Convert row to dict and clean
            data = row.to_dict()
            
            # Generate ID
            record_id = generate_id(data)
            data['id'] = record_id
            data['created_at'] = datetime.now()
            data['updated_at'] = datetime.now()
            
            # Map columns based on table
            mapped_data = map_columns(data, table_name, file_path)
            
            if mapped_data:
                # Insert into database
                insert_record(conn, table_name, mapped_data)
                records_inserted += 1
                
        except Exception as e:
            logger.warning(f"Error processing row in {file_path}: {e}")
            continue
    
    return records_inserted

def map_columns(data, table_name, file_path):
    """Map CSV columns to database columns"""
    mapped = {}

    for key, value in data.items():
        if pd.isna(value):
            continue

        # Clean column name
        clean_key = key.lower().replace(' ', '_').replace('-', '_')

        # Table-specific mapping logic
        if table_name == 'teams':
            if clean_key == 'team_name':
                mapped['name'] = str(value)
            elif clean_key in ['team_abbreviation', 'abbreviation']:
                mapped['abbreviation'] = str(value)
            elif clean_key == 'season_id':
                mapped['season'] = str(value)
            elif 'league' in clean_key:
                mapped['league'] = str(value)

        elif table_name == 'players':
            if clean_key == 'player_name':
                mapped['full_name'] = str(value)
            elif clean_key == 'position':
                mapped['position'] = str(value)
            elif clean_key == 'team_abbreviation':
                mapped['team_abbreviation'] = str(value)
            elif 'league' in clean_key:
                mapped['league'] = str(value)

        elif table_name == 'games':
            if clean_key == 'game_id':
                mapped['id'] = str(value)  # Use game_id as primary key
            elif clean_key == 'game_date':
                mapped['date'] = str(value)
            elif clean_key == 'season_id':
                mapped['season'] = str(value)
            elif clean_key == 'team_id':
                # For games, we need to determine home/away
                mapped['home_team_id'] = str(value)  # Simplified
            elif clean_key == 'pts':
                mapped['home_score'] = int(value) if str(value).isdigit() else 0

        elif table_name == 'player_game_stats':
            if clean_key == 'player_id':
                mapped['hero_id'] = str(value)
            elif clean_key == 'game_id':
                mapped['titan_clash_id'] = str(value)
            elif clean_key == 'game_date':
                mapped['date'] = str(value)
            elif clean_key == 'season_id':
                mapped['season'] = str(value)
            elif clean_key in ['pts', 'points']:
                mapped['points'] = int(value) if str(value).isdigit() else 0
            elif clean_key in ['min', 'minutes']:
                mapped['minutes_played'] = float(value) if str(value).replace('.','').isdigit() else 0.0
            elif clean_key == 'reb':
                mapped['total_rebounds'] = int(value) if str(value).isdigit() else 0
            elif clean_key == 'ast':
                mapped['assists'] = int(value) if str(value).isdigit() else 0

        # Always include the original data with cleaned key for flexibility
        if clean_key not in mapped:
            mapped[clean_key] = value

    # Ensure required fields
    if 'id' in data:
        mapped['id'] = data['id']
    if 'created_at' in data:
        mapped['created_at'] = data['created_at']
    if 'updated_at' in data:
        mapped['updated_at'] = data['updated_at']

    return mapped

def insert_record(conn, table_name, data):
    """Insert record into database with dynamic column handling"""
    # Get existing table columns
    cursor = conn.execute(f"PRAGMA table_info({table_name})")
    existing_columns = {row[1] for row in cursor.fetchall()}

    # Filter data to only include columns that exist in the table
    filtered_data = {}
    for key, value in data.items():
        if key in existing_columns:
            filtered_data[key] = value

    if not filtered_data:
        return  # No valid columns to insert

    columns = list(filtered_data.keys())
    placeholders = ['?' for _ in columns]
    values = list(filtered_data.values())

    query = f"INSERT OR REPLACE INTO {table_name} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"

    try:
        conn.execute(query, values)
        conn.commit()
    except Exception as e:
        logger.warning(f"Error inserting into {table_name}: {e}")
        # Try to add missing columns dynamically
        for key in data.keys():
            if key not in existing_columns:
                try:
                    conn.execute(f"ALTER TABLE {table_name} ADD COLUMN {key} TEXT")
                    logger.info(f"Added column {key} to {table_name}")
                except:
                    pass  # Column might already exist or be invalid
        conn.commit()

if __name__ == "__main__":
    consolidate_csv_files()
