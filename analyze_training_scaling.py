#!/usr/bin/env python3
"""
Analyze the actual scaling parameters used during training
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path
import logging

# Add project root to path
sys.path.append('.')

# Removed unused imports - analyzing data directly from CSV files
from sklearn.preprocessing import StandardScaler

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def analyze_scaling_for_prop(prop_type: str):
    """Analyze the actual scaling parameters for a specific prop type"""
    print(f"\n🔍 Analyzing scaling for {prop_type}...")

    # Load WNBA data directly from CSV files
    data_dir = Path("data/wnba_10year_historical")

    # Load all available WNBA data files
    all_targets = []

    for csv_file in data_dir.glob("*.csv"):
        if "stats" in csv_file.name.lower():
            try:
                df = pd.read_csv(csv_file)
                print(f"   Loading {csv_file.name}: {len(df)} records")

                # Map prop_type to column name
                prop_column_map = {
                    'points': ['PTS', 'Points', 'points'],
                    'rebounds': ['REB', 'Rebounds', 'rebounds', 'TRB'],
                    'assists': ['AST', 'Assists', 'assists'],
                    'steals': ['STL', 'Steals', 'steals'],
                    'blocks': ['BLK', 'Blocks', 'blocks'],
                    'threes': ['3PM', '3P', 'FG3M', 'threes']
                }

                # Find the correct column
                target_col = None
                for possible_col in prop_column_map.get(prop_type, []):
                    if possible_col in df.columns:
                        target_col = possible_col
                        break

                if target_col:
                    targets = df[target_col].dropna().values
                    # Filter out negative values and extreme outliers
                    targets = targets[(targets >= 0) & (targets <= 100)]
                    all_targets.extend(targets)
                    print(f"     Found {len(targets)} valid {prop_type} values in column '{target_col}'")

            except Exception as e:
                print(f"   Error loading {csv_file.name}: {e}")

    if not all_targets:
        print(f"❌ No {prop_type} data found!")
        return None, None

    targets = np.array(all_targets)

    print(f"📊 Raw {prop_type} targets:")
    print(f"   Count: {len(targets)}")
    print(f"   Range: {np.min(targets):.2f} to {np.max(targets):.2f}")
    print(f"   Mean: {np.mean(targets):.2f}")
    print(f"   Std: {np.std(targets):.2f}")
    print(f"   Median: {np.median(targets):.2f}")

    # Apply the same scaling as during training
    target_scaler = StandardScaler()
    targets_scaled = target_scaler.fit_transform(targets.reshape(-1, 1)).flatten()

    print(f"📊 Scaled {prop_type} targets:")
    print(f"   Mean: {np.mean(targets_scaled):.6f}")
    print(f"   Std: {np.std(targets_scaled):.6f}")
    print(f"   Range: {np.min(targets_scaled):.6f} to {np.max(targets_scaled):.6f}")

    print(f"📊 StandardScaler parameters for {prop_type}:")
    print(f"   scaler.mean_[0]: {target_scaler.mean_[0]:.6f}")
    print(f"   scaler.scale_[0]: {target_scaler.scale_[0]:.6f}")

    # Test unscaling with some example values (including the problematic ones we saw)
    test_scaled_values = [-2.5, -1.0, 0.0, 1.0, 2.5, -2.48, -3.14]
    print(f"📊 Test unscaling for {prop_type}:")
    for scaled_val in test_scaled_values:
        unscaled = target_scaler.inverse_transform([[scaled_val]])[0][0]
        print(f"   scaled={scaled_val:.2f} → unscaled={unscaled:.2f}")

    return target_scaler.mean_[0], target_scaler.scale_[0]

def main():
    """Analyze scaling for all prop types"""
    print("🔍 ANALYZING ACTUAL TRAINING SCALING PARAMETERS")
    print("=" * 60)
    
    prop_types = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']
    scaling_params = {}
    
    for prop_type in prop_types:
        try:
            mean, scale = analyze_scaling_for_prop(prop_type)
            scaling_params[prop_type] = {'mean': mean, 'scale': scale}
        except Exception as e:
            print(f"❌ Error analyzing {prop_type}: {e}")
    
    print("\n📊 SUMMARY OF ACTUAL SCALING PARAMETERS:")
    print("=" * 60)
    for prop_type, params in scaling_params.items():
        print(f"{prop_type:>8}: mean={params['mean']:>8.3f}, scale={params['scale']:>8.3f}")
    
    print("\n🔧 CORRECTED UNSCALING CODE:")
    print("=" * 60)
    for prop_type, params in scaling_params.items():
        print(f"elif prop_type == \"{prop_type}\":")
        print(f"    target_mean, target_std = {params['mean']:.3f}, {params['scale']:.3f}")

if __name__ == "__main__":
    main()
