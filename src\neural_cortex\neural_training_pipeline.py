import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, random_split, WeightedRandomSampler
# Optional tensorboard import
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    SummaryWriter = None
    TENSORBOARD_AVAILABLE = False
import numpy as np
import pandas as pd
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Union
from dataclasses import dataclass, field
from pathlib import Path
import json
import pickle
from collections import defaultdict
# import matplotlib.pyplot as plt
# import seaborn as sns
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import warnings
from .neural_basketball_core import NeuralBasketballCore, GameState, PredictionOutput
from ..data.basketball_data_loader import BasketballDataLoader


#!/usr/bin/env python3
"""
 HYPER MEDUSA NEURAL VAULT - Neural Training Pipeline
═══════════════════════════════════════════════════════════════════════════════════
Advanced PyTorch training pipeline for basketball neural networks.
Implements comprehensive training, validation, and model optimization
for the Cognitive Basketball Cortex and Neural Basketball Core.

Features:
- Real basketball data integration
- Multi-modal training (player stats, team metrics, game context)
- Advanced optimization techniques
- Model checkpointing and versioning
- Performance monitoring and visualization
- GPU acceleration support
- Distributed training capabilities

 Elite Basketball Intelligence Training System
"""


warnings.filterwarnings('ignore')

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


import sys
import logging

# Ensure sys.stdout uses UTF-8 encoding (Python 3.7+)
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')

logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        "%(asctime)s %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# Import neural models
try:
    NEURAL_CORE_AVAILABLE = True
except ImportError as e:
    logger.warning(f" Neural Basketball Core not available: {e}")
    NEURAL_CORE_AVAILABLE = False

# Import data sources
try:
    DATA_LOADER_AVAILABLE = True
except ImportError:
    DATA_LOADER_AVAILABLE = False
    logger.warning(" Basketball Data Loader not available")

@dataclass
class TrainingConfig:
    """Training configuration parameters - Optimized for realistic basketball predictions"""
    # League configuration
    league: str = "NBA" # NBA or WNBA
    # Model parameters - Conservative settings to prevent extreme predictions
    model_type: str = "neural_basketball_core"
    input_dim: int = 20  # Will be adjusted based on actual data
    hidden_dim: int = 32  # VERY CONSERVATIVE to prevent overfitting
    num_layers: int = 2  # Minimal layers for stability
    dropout_rate: float = 0.8  # VERY HIGH dropout for maximum regularization
    # Training parameters - Designed for stable, realistic predictions
    batch_size: int = 512  # LARGE batches for stable gradients
    learning_rate: float = 0.00005  # VERY LOW learning rate for stability
    num_epochs: int = 10  # MINIMAL epochs to prevent overfitting
    early_stopping_patience: int = 1  # IMMEDIATE early stopping
    weight_decay: float = 5e-2  # VERY HIGH regularization
    # Class imbalance handling
    use_focal_loss: bool = True  # Use focal loss for class imbalance
    focal_alpha: float = 0.25  # Focal loss alpha parameter
    focal_gamma: float = 2.0  # Focal loss gamma parameter
    # Data parameters
    train_split: float = 0.8  # More training data since we have 3.9M records
    val_split: float = 0.1
    test_split: float = 0.1
    sequence_length: int = 10
    # Optimization
    optimizer: str = "adamw"
    scheduler: str = "cosine"
    gradient_clip: float = 1.0
    mixed_precision: bool = True
    # Hardware
    device: str = "auto" # auto, cuda, cpu
    num_workers: int = 4
    pin_memory: bool = True
    # Paths
    data_path: str = "./data"  # Updated to use clean training data location
    model_save_path: str = "./models/neural_core"
    tensorboard_path: str = "./logs/tensorboard"
    # Advanced features
    use_data_augmentation: bool = True
    use_curriculum_learning: bool = True
    use_adversarial_training: bool = False
    ensemble_size: int = 5
    # Database integration
    use_database: bool = True
    max_samples: int = None  # No limit - use all available data for maximum accuracy

@dataclass
class TrainingMetrics:
    """Training metrics tracking"""
    epoch: int = 0
    train_loss: float = 0.0
    val_loss: float = 0.0
    train_accuracy: float = 0.0
    val_accuracy: float = 0.0
    train_f1: float = 0.0
    val_f1: float = 0.0
    learning_rate: float = 0.0
    gpu_memory: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)


class FocalLoss(nn.Module):
    """Focal Loss for addressing class imbalance"""

    def __init__(self, alpha: float = 0.25, gamma: float = 2.0, reduction: str = 'mean'):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            inputs: Predictions from model (before softmax) [N, C]
            targets: Ground truth labels [N]
        """
        # Compute cross entropy
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')

        # Compute probabilities
        pt = torch.exp(-ce_loss)

        # Compute focal loss
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


class BasketballDataset(Dataset):
    """Basketball dataset for neural training - supports both NBA and WNBA"""

    def __init__(self, data_path: str, config: TrainingConfig, split: str = "train", fitted_scaler=None, temporal_indices=None):
        self.config = config
        self.split = split
        self.data_path = Path(data_path)
        self.league = config.league.upper() # Store league (NBA or WNBA)
        self.temporal_indices = temporal_indices  # For temporal splitting

        # Load and preprocess data
        self.data, self.labels, self.features = self._load_basketball_data()

        # Feature scaling
        if fitted_scaler is not None: # Use pre-fitted scaler for validation/test data
            self.scaler = fitted_scaler
            self.data = self.scaler.transform(self.data)
        else:
            # Fit scaler on training data
            self.scaler = StandardScaler()
            if split == "train":
                self.data = self.scaler.fit_transform(self.data)
            else:
                # This shouldn't happen now, but keep as fallback
                self.data = self.scaler.fit_transform(self.data)
        
        logger.info(f" {split.upper()} dataset loaded: {len(self.data)} samples, {self.data.shape[1]} features")

    def _load_basketball_data(self) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Load real basketball data or generate synthetic data for specified league"""
        # Try to load real data for the specific league
        real_data = self._load_real_data()
        if real_data:
            return real_data
        
        # Generate synthetic basketball data
        logger.warning(f" Real {self.league} data not available, generating synthetic basketball data")
        return self._generate_synthetic_data()

    def _load_real_data(self) -> Optional[Tuple[np.ndarray, np.ndarray, List[str]]]:
        """Load real basketball data from clean training datasets for specified league"""
        try:
            all_dataframes = []
            total_records = 0

            # 1. Load from clean training datasets (107k+ clean records available)
            clean_training_file = self.data_path / "ml_training" / f"{self.league.lower()}_training_data.csv"

            if clean_training_file.exists():
                logger.info(f"📂 Loading clean {self.league} training data from {clean_training_file}")
                df = pd.read_csv(clean_training_file)

                # Verify this is the correct league data
                if 'LEAGUE' in df.columns:
                    league_df = df[df['LEAGUE'] == self.league]
                    if len(league_df) > 0:
                        all_dataframes.append(league_df)
                        total_records += len(league_df)
                        logger.info(f"✅ Loaded {len(league_df)} clean {self.league} records")
                    else:
                        logger.warning(f"⚠️ No {self.league} records found in clean training data")
                else:
                    # Assume the file is already league-specific
                    all_dataframes.append(df)
                    total_records += len(df)
                    logger.info(f"✅ Loaded {len(df)} clean {self.league} records")
            else:
                logger.warning(f"⚠️ Clean training file not found: {clean_training_file}")

                # Fallback to original CSV loading method
                league_data_files = list(self.data_path.glob(f"*{self.league.lower()}*.csv"))
                general_data_files = list(self.data_path.glob("*.csv"))

                if league_data_files:
                    logger.info(f"📂 Fallback: Found {len(league_data_files)} {self.league} CSV files")
                    csv_data = self._load_from_csv(league_data_files)
                    if csv_data:
                        csv_df = self._convert_to_dataframe(csv_data)
                        all_dataframes.append(csv_df)
                        total_records += len(csv_df)
                        logger.info(f"✅ Loaded {len(csv_df)} records from CSV files")
                elif general_data_files:
                    logger.info(f"📂 Fallback: Found {len(general_data_files)} general CSV files, filtering for {self.league}")
                    csv_data = self._load_from_csv(general_data_files, filter_league=True)
                    if csv_data:
                        csv_df = self._convert_to_dataframe(csv_data)
                        all_dataframes.append(csv_df)
                        total_records += len(csv_df)
                        logger.info(f"✅ Loaded {len(csv_df)} records from filtered CSV files")

            # 2. Load from database (340k+ records available)
            if DATA_LOADER_AVAILABLE:
                loader = BasketballDataLoader()
                db_df = loader.load_training_data(league=self.league, data_source="database")
                if db_df is not None and not db_df.empty:
                    all_dataframes.append(db_df)
                    total_records += len(db_df)
                    logger.info(f"✅ Loaded {len(db_df)} records from database")

            # 3. Combine all data sources
            if all_dataframes:
                logger.info(f"🔗 Combining {len(all_dataframes)} data sources with {total_records} total records")
                combined_df = pd.concat(all_dataframes, ignore_index=True)

                # Remove duplicates to ensure data quality
                initial_count = len(combined_df)
                combined_df = combined_df.drop_duplicates()
                final_count = len(combined_df)

                if initial_count != final_count:
                    logger.info(f"🧹 Removed {initial_count - final_count} duplicate records")

                logger.info(f"🎯 Final dataset: {final_count} unique records for {self.league} training")
                return self._process_dataloader_data(combined_df)

            return None
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: load real data: {e}")
            return None

    def _convert_to_dataframe(self, data_tuple: Tuple[np.ndarray, np.ndarray, List[str]]) -> pd.DataFrame:
        """Convert data tuple back to DataFrame for combining with other sources"""
        try:
            features, labels, feature_names = data_tuple

            # Create DataFrame from features
            df = pd.DataFrame(features, columns=feature_names)

            # Add labels column
            df['target'] = labels

            return df
        except Exception as e:
            logger.error(f"❌ Error converting data tuple to DataFrame: {e}")
            return pd.DataFrame()

    def _process_dataloader_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Process DataFrame from BasketballDataLoader into training format"""
        try:
            logger.info(f"🔧 Processing {len(df)} records from BasketballDataLoader for {self.league}")

            # Select feature columns (exclude target and metadata columns)
            exclude_columns = [
                'player_id', 'player_name', 'team_abbreviation', 'season',
                'season_type', 'league_name', 'league_id', 'source_file',
                'ingestion_timestamp', 'data_hash', 'source_table', 'data_category',
                'data_type', 'win_prediction', 'elite_performer', 'top_tier'
            ]


            # Initial feature selection by exclusion
            feature_columns = [col for col in df.columns if col not in exclude_columns]

            # Filter to only numeric columns
            numeric_feature_columns = df[feature_columns].select_dtypes(include=[np.number]).columns.tolist()
            if not numeric_feature_columns:
                logger.warning("No numeric feature columns found, using all numeric columns in DataFrame")
                numeric_feature_columns = df.select_dtypes(include=[np.number]).columns.tolist()
                # Remove any remaining non-feature columns
                numeric_feature_columns = [col for col in numeric_feature_columns if col not in exclude_columns]

            # Ensure we have enough features for the model
            if len(numeric_feature_columns) < 10:
                logger.warning(f"Only {len(numeric_feature_columns)} numeric features found, padding with engineered features")
                for i in range(10 - len(numeric_feature_columns)):
                    new_col = f'engineered_feature_{i}'
                    df[new_col] = np.random.normal(0, 1, len(df))
                    numeric_feature_columns.append(new_col)

            # Extract features and handle extreme values
            X = df[numeric_feature_columns].fillna(0).values.astype(np.float32)

            # Handle extreme values and outliers in real data
            # Clip extreme outliers (beyond 5 standard deviations)
            for i in range(X.shape[1]):
                col_data = X[:, i]
                if col_data.std() > 0:
                    mean_val = col_data.mean()
                    std_val = col_data.std()
                    # Clip to 3 standard deviations to prevent extreme values
                    X[:, i] = np.clip(col_data, mean_val - 3*std_val, mean_val + 3*std_val)

            # Apply robust scaling to handle different feature scales
            from sklearn.preprocessing import RobustScaler
            scaler = RobustScaler()
            X = scaler.fit_transform(X)

            # Final clipping to ensure no extreme values
            X = np.clip(X, -5, 5)

            # Create target variable (use win_prediction if available, otherwise create one)
            if 'win_prediction' in df.columns:
                y = df['win_prediction'].fillna(0).values.astype(np.int64)
            elif 'elite_performer' in df.columns:
                y = df['elite_performer'].fillna(0).values.astype(np.int64)
            else:
                # Create binary target based on stat_value or rank_position
                if 'stat_value' in df.columns:
                    stat_values = df['stat_value'].fillna(0)
                    median_val = stat_values.median() if len(stat_values) > 0 else 0
                    y = (stat_values > median_val).astype(np.int64)
                elif 'rank_position' in df.columns:
                    rank_values = df['rank_position'].fillna(50)
                    y = (rank_values <= 10).astype(np.int64)  # Top 10 performers
                else:
                    # Random binary target as fallback
                    y = np.random.randint(0, 2, len(df)).astype(np.int64)

            # Validate data shapes
            if X.shape[0] != y.shape[0]:
                logger.error(f"Shape mismatch: X={X.shape}, y={y.shape}")
                min_samples = min(X.shape[0], y.shape[0])
                X = X[:min_samples]
                y = y[:min_samples]


            # Apply temporal indices if specified
            if self.temporal_indices is not None:
                start_idx, end_idx = self.temporal_indices
                X = X[start_idx:end_idx]
                y = y[start_idx:end_idx]
                logger.info(f"🕒 Applied temporal split: indices {start_idx}:{end_idx}")

            logger.info(f"✅ Processed data: {X.shape[0]} samples, {X.shape[1]} features")
            logger.info(f"📊 Target distribution: {np.bincount(y)}")
            logger.info(f"🔧 Numeric feature columns: {numeric_feature_columns[:5]}{'...' if len(numeric_feature_columns) > 5 else ''}")

            return X, y, numeric_feature_columns

        except Exception as e:
            logger.error(f"❌ Error processing dataloader data: {e}")
            logger.warning("🔄 Attempting to use clean CSV data as fallback...")

            # Try to load clean CSV data as fallback
            try:
                return self._load_clean_csv_fallback()
            except Exception as fallback_error:
                logger.error(f"❌ Fallback also failed: {fallback_error}")
                # Return minimal valid data only as last resort
                logger.warning("⚠️ Using synthetic data as last resort - this will result in poor performance!")
                return np.random.randn(1000, 20).astype(np.float32), np.random.randint(0, 2, 1000).astype(np.int64), [f"feature_{i}" for i in range(20)]

    def _load_from_csv(self, data_files: List[Path]) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Load data from CSV files"""
        all_data = []
        for file in data_files:
            df = pd.read_csv(file)
            all_data.append(df)
        
        combined_df = pd.concat(all_data, ignore_index=True)

        # Separate features and labels - create labels from existing data if needed
        label_columns = ['outcome', 'win', 'result', 'winner', 'high_performer', 'above_average_performer']
        label_col = None
        for col in label_columns:
            if col in combined_df.columns:
                label_col = col
                break

        # If no existing label column, create one from performance metrics
        if label_col is None:
            if 'stat_value' in combined_df.columns:
                # Create binary classification based on performance
                median_performance = combined_df['stat_value'].median()
                combined_df['performance_label'] = (combined_df['stat_value'] > median_performance).astype(int)
                label_col = 'performance_label'
                logger.info(f"✅ Created performance_label from stat_value (threshold: {median_performance:.2f})")
            elif 'points' in combined_df.columns:
                # Create binary classification based on points
                median_points = combined_df['points'].median()
                combined_df['performance_label'] = (combined_df['points'] > median_points).astype(int)
                label_col = 'performance_label'
                logger.info(f"✅ Created performance_label from points (threshold: {median_points:.2f})")
            else:
                raise ValueError("No label column found and cannot create labels from available data")

        # Handle string columns before selecting numeric features
        logger.info(f"🔧 Processing {len(combined_df.columns)} total columns...")

        # Identify and log string columns
        string_columns = combined_df.select_dtypes(include=['object']).columns.tolist()
        if string_columns:
            logger.info(f"⚠️ Found {len(string_columns)} string columns that will be excluded: {string_columns[:5]}...")

            # Try to encode categorical string columns if they have few unique values
            for col in string_columns[:]:  # Copy list to modify during iteration
                if col != label_col and combined_df[col].nunique() <= 50:  # Reasonable number for categorical
                    try:
                        le = LabelEncoder()
                        combined_df[f"{col}_encoded"] = le.fit_transform(combined_df[col].fillna('unknown'))
                        logger.info(f"✅ Encoded categorical column: {col} -> {col}_encoded")
                        string_columns.remove(col)
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to encode {col}: {e}")

        # Select only numeric features for training
        numeric_columns = combined_df.select_dtypes(include=[np.number]).columns.tolist()
        features = [col for col in numeric_columns if col != label_col]

        if not features:
            logger.error(f"❌ No numeric features found! Available columns: {combined_df.columns.tolist()}")
            raise ValueError("No numeric features found for training")

        logger.info(f"✅ Selected {len(features)} numeric features: {features[:5]}...")

        # Extract feature matrix and handle NaN values
        X = combined_df[features].values
        X = np.nan_to_num(X, nan=0.0, posinf=1.0, neginf=-1.0)  # Handle NaN/inf values
        X = X.astype(np.float32)

        y = combined_df[label_col].values

        # Encode labels if they're strings
        if y.dtype == 'object':
            le = LabelEncoder()
            y = le.fit_transform(y)

        return X, y.astype(np.int64), features

    def _load_clean_csv_fallback(self) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Load clean CSV data as fallback when database processing fails"""
        logger.info(f"🔄 Loading clean {self.league} CSV data as fallback...")

        # Try to load clean CSV file
        clean_csv_path = self.data_path / "ml_training" / f"{self.league.lower()}_training_data.csv"
        if not clean_csv_path.exists():
            raise FileNotFoundError(f"Clean CSV file not found: {clean_csv_path}")

        df = pd.read_csv(clean_csv_path)
        logger.info(f"✅ Loaded {len(df)} records from clean CSV")

        # Remove string columns that can't be converted to numeric
        string_columns = df.select_dtypes(include=['object']).columns.tolist()
        logger.info(f"🔧 Removing {len(string_columns)} string columns: {string_columns[:5]}...")

        # Keep only numeric columns
        numeric_df = df.select_dtypes(include=[np.number])

        if numeric_df.empty:
            raise ValueError("No numeric columns found in clean CSV data")

        # Create target variable from available data
        if 'home_win' in numeric_df.columns:
            target_col = 'home_win'
        elif 'win' in numeric_df.columns:
            target_col = 'win'
        elif 'result' in numeric_df.columns:
            target_col = 'result'
        elif 'W' in numeric_df.columns:  # Win column
            target_col = 'W'
        else:
            # Create meaningful target based on basketball performance metrics
            if 'PTS' in numeric_df.columns:  # Points scored
                # High performance: above median points
                median_pts = numeric_df['PTS'].median()
                numeric_df['target'] = (numeric_df['PTS'] > median_pts).astype(int)
                target_col = 'target'
                logger.info(f"✅ Created performance target based on points (threshold: {median_pts:.1f})")
            elif 'FG_PCT' in numeric_df.columns and 'REB' in numeric_df.columns:
                # Composite performance: good shooting + rebounding
                fg_median = numeric_df['FG_PCT'].median()
                reb_median = numeric_df['REB'].median()
                numeric_df['target'] = ((numeric_df['FG_PCT'] > fg_median) & (numeric_df['REB'] > reb_median)).astype(int)
                target_col = 'target'
                logger.info(f"✅ Created composite performance target (FG%>{fg_median:.3f} & REB>{reb_median:.1f})")
            elif any(col in numeric_df.columns for col in ['PLUS_MINUS', 'NET_RATING']):
                # Use plus/minus or net rating
                pm_col = 'PLUS_MINUS' if 'PLUS_MINUS' in numeric_df.columns else 'NET_RATING'
                numeric_df['target'] = (numeric_df[pm_col] > 0).astype(int)
                target_col = 'target'
                logger.info(f"✅ Created target based on {pm_col} > 0")
            else:
                # Create random target as last resort
                numeric_df['target'] = np.random.randint(0, 2, len(numeric_df))
                target_col = 'target'
                logger.warning("⚠️ Created random target variable - model performance will be poor")

        # Separate features and target
        feature_cols = [col for col in numeric_df.columns if col != target_col]

        if not feature_cols:
            raise ValueError("No feature columns available after removing target")

        X = numeric_df[feature_cols].values.astype(np.float32)
        y = numeric_df[target_col].values.astype(np.int64)

        # Handle NaN values
        X = np.nan_to_num(X, nan=0.0, posinf=1.0, neginf=-1.0)

        logger.info(f"✅ Clean CSV fallback: {X.shape[0]} samples, {X.shape[1]} features")
        return X, y, feature_cols

    def _generate_synthetic_data(self) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Generate synthetic basketball data that matches real data patterns"""
        n_samples = 10000 if self.split == "train" else 2000
        n_features = self.config.input_dim

        # Generate realistic basketball features that match real data patterns
        features = [
            # Team efficiency metrics (normalized 0-1)
            'offensive_efficiency', 'defensive_efficiency', 'pace_factor', 'effective_fg_pct',
            'true_shooting_pct', 'turnover_rate', 'offensive_rebound_pct',
            'defensive_rebound_pct', 'assist_rate', 'steal_rate', 'block_rate',
            # Player performance metrics (standardized)
            'avg_points_per_game', 'avg_rebounds_per_game', 'avg_assists_per_game',
            'avg_steals_per_game', 'avg_blocks_per_game', 'avg_minutes_played',
            'field_goal_percentage', 'three_point_percentage', 'free_throw_percentage', 'plus_minus_rating',
            # Game context factors (normalized)
            'home_court_advantage', 'days_rest', 'back_to_back_games', 'travel_fatigue',
            'venue_altitude', 'weather_impact', 'injury_impact', 'roster_changes',
            # Team momentum (recent performance)
            'recent_win_percentage', 'recent_point_differential', 'head_to_head_record',
            'season_form', 'playoff_position', 'motivation_factor', 'crowd_support',
            # Advanced analytics (scaled appropriately)
            'net_efficiency_rating', 'clutch_performance', 'fourth_quarter_performance',
            'fast_break_efficiency', 'paint_scoring_rate', 'second_chance_conversion'
        ]

        # Pad or trim features to match input_dim
        while len(features) < n_features:
            features.append(f'engineered_feature_{len(features)}')
        features = features[:n_features]

        # Generate realistic data that matches actual basketball statistics
        np.random.seed(42 if self.split == "train" else 123)

        # Create realistic team strength distributions
        team_strength_home = np.random.beta(2, 2, n_samples)  # Beta distribution for bounded values
        team_strength_away = np.random.beta(2, 2, n_samples)

        data = np.zeros((n_samples, n_features))

        for i, feature in enumerate(features):
            if 'efficiency' in feature or 'rating' in feature:
                # Efficiency metrics: realistic basketball range (0.8-1.2 normalized)
                base_value = 0.9 + (team_strength_home - 0.5) * 0.4
                data[:, i] = np.clip(base_value + np.random.normal(0, 0.05, n_samples), 0.7, 1.3)

            elif 'percentage' in feature or 'pct' in feature or 'rate' in feature:
                # Percentage stats: realistic basketball percentages (0.3-0.7)
                if 'three_point' in feature:
                    data[:, i] = np.clip(np.random.beta(3, 5, n_samples), 0.2, 0.5)  # 3PT% range
                elif 'free_throw' in feature:
                    data[:, i] = np.clip(np.random.beta(8, 3, n_samples), 0.6, 0.95)  # FT% range
                else:
                    data[:, i] = np.clip(np.random.beta(4, 4, n_samples), 0.35, 0.65)  # FG% range

            elif 'points' in feature or 'rebounds' in feature or 'assists' in feature:
                # Per-game stats: realistic basketball ranges
                if 'points' in feature:
                    base_stat = 15 + team_strength_home * 20  # 15-35 points range
                elif 'rebounds' in feature:
                    base_stat = 8 + team_strength_home * 8   # 8-16 rebounds range
                else:  # assists
                    base_stat = 4 + team_strength_home * 8   # 4-12 assists range
                data[:, i] = np.clip(base_stat + np.random.normal(0, 2, n_samples), 0, 50)

            elif 'advantage' in feature or 'support' in feature:
                # Binary/categorical factors (0-1 range)
                data[:, i] = np.random.beta(2, 2, n_samples)

            elif 'rest' in feature or 'fatigue' in feature or 'impact' in feature:
                # Impact factors (0-1 normalized)
                data[:, i] = np.random.exponential(0.3, n_samples)
                data[:, i] = np.clip(data[:, i], 0, 1)

            else:
                # Generic features: standardized normal distribution
                data[:, i] = np.random.normal(0, 1, n_samples)

        # Normalize all features to prevent extreme values
        from sklearn.preprocessing import StandardScaler
        scaler = StandardScaler()
        data = scaler.fit_transform(data)

        # Generate realistic labels based on team performance differential
        performance_diff = np.mean(data[:, :5], axis=1)  # Use first 5 features as performance proxy
        # Apply realistic sigmoid with moderate slope to prevent extreme probabilities
        win_prob = 1 / (1 + np.exp(-performance_diff * 2))  # Reduced from 5 to 2 for more realistic spread
        # Add some randomness to prevent perfect correlation
        win_prob = np.clip(win_prob + np.random.normal(0, 0.1, n_samples), 0.1, 0.9)
        labels = (np.random.random(n_samples) < win_prob).astype(np.int64)

        logger.info(f"🎯 Generated {n_samples} realistic basketball samples")
        logger.info(f"📊 Feature ranges: min={data.min():.3f}, max={data.max():.3f}, std={data.std():.3f}")
        logger.info(f"📊 Label distribution: {np.bincount(labels)}")

        return data.astype(np.float32), labels, features

    def __len__(self) -> int:
        return len(self.data)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        sample = torch.FloatTensor(self.data[idx])
        label = torch.LongTensor([self.labels[idx]])

        # Data augmentation for training
        if self.split == "train" and self.config.use_data_augmentation:
            sample = self._augment_sample(sample)
        
        return sample, label

    def _augment_sample(self, sample: torch.Tensor) -> torch.Tensor:
        """Apply data augmentation to training samples"""
        # Add small amount of noise
        noise = torch.randn_like(sample) * 0.01
        sample = sample + noise

        # Random feature dropout (set some features to 0)
        if np.random.random() < 0.1:
            dropout_mask = torch.rand_like(sample) > 0.05
            sample = sample * dropout_mask
        
        return sample


class StratifiedBasketballDataset(Dataset):
    """Stratified Basketball dataset for proper train/val/test splitting"""

    def __init__(self, data: np.ndarray, labels: np.ndarray, features: List[str],
                 config: TrainingConfig, split: str = "train", fitted_scaler=None):
        self.config = config
        self.split = split
        self.features = features

        # Store data and labels
        self.data = data.astype(np.float32)
        self.labels = labels.astype(np.int64)

        # Feature scaling
        if fitted_scaler is not None:
            self.scaler = fitted_scaler
            self.data = self.scaler.transform(self.data)
        else:
            # Fit scaler on training data
            self.scaler = StandardScaler()
            if split == "train":
                self.data = self.scaler.fit_transform(self.data)
            else:
                # This shouldn't happen with stratified split, but keep as fallback
                self.data = self.scaler.fit_transform(self.data)

        logger.info(f" {split.upper()} stratified dataset: {len(self.data)} samples, {self.data.shape[1]} features")
        logger.info(f" {split.upper()} class distribution: {dict(zip(*np.unique(self.labels, return_counts=True)))}")

    def __len__(self) -> int:
        return len(self.data)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        sample = torch.FloatTensor(self.data[idx])
        label = torch.LongTensor([self.labels[idx]])

        # Data augmentation for training
        if self.split == "train" and self.config.use_data_augmentation:
            sample = self._augment_sample(sample)

        return sample, label

    def _augment_sample(self, sample: torch.Tensor) -> torch.Tensor:
        """Apply data augmentation to training samples"""
        # Add small amount of noise
        noise = torch.randn_like(sample) * 0.01
        sample = sample + noise

        # Random feature dropout (10% chance)
        if torch.rand(1) < 0.1:
            mask = torch.rand_like(sample) > 0.1
            sample = sample * mask

        return sample


class NeuralTrainingPipeline:
    """Complete neural training pipeline for basketball models"""
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.device = self._setup_device()
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.scaler = None
        self.writer = None
        # Training state
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.training_history = []
        # Setup directories
        self._setup_directories()
        logger.info(f" Neural Training Pipeline initialized on device: {self.device}")

    def _setup_device(self) -> torch.device:
        """Setup training device (CPU/GPU)"""
        if self.config.device == "auto":
            if torch.cuda.is_available():
                device = torch.device("cuda")
                logger.info(f" GPU detected: {torch.cuda.get_device_name()}")
            else:
                device = torch.device("cpu")
                logger.info(" MEDUSA VAULT: 💻 Using CPU for training")
        else:
            device = torch.device(self.config.device)
        return device

    def _setup_directories(self):
        """Setup training directories"""
        dirs = [
            self.config.model_save_path,
            self.config.tensorboard_path,
            os.path.dirname(self.config.data_path)
        ]
        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
        # Setup TensorBoard (optional)
        if TENSORBOARD_AVAILABLE:
            self.writer = SummaryWriter(self.config.tensorboard_path)
        else:
            self.writer = None
            logger.warning("⚠️ TensorBoard not available - metrics logging disabled")

    def _build_model(self) -> nn.Module:
        """Build the neural model for training"""
        if self.config.model_type == "neural_basketball_core":
            model = EnhancedNeuralBasketballCore(
                input_dim=self.config.input_dim,
                hidden_dim=self.config.hidden_dim,
                num_layers=self.config.num_layers,
                dropout_rate=self.config.dropout_rate
            )
        else:
            raise ValueError(f"Unknown model type: {self.config.model_type}")
        return model.to(self.device)

    def _setup_optimizer(self) -> Tuple[optim.Optimizer, optim.lr_scheduler._LRScheduler]:
        """Setup optimizer and learning rate scheduler"""
        # Optimizer
        if self.config.optimizer.lower() == "adamw":
            optimizer = optim.AdamW(
                self.model.parameters(),
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay
            )
        elif self.config.optimizer.lower() == "adam":
            optimizer = optim.Adam(
                self.model.parameters(),
                lr=self.config.learning_rate,
                weight_decay=self.config.weight_decay
            )
        else:
            raise ValueError(f"Unknown optimizer: {self.config.optimizer}")
        # Scheduler
        if self.config.scheduler.lower() == "cosine":
            scheduler = optim.lr_scheduler.CosineAnnealingLR(
                optimizer, T_max=self.config.num_epochs
            )
        elif self.config.scheduler.lower() == "step":
            scheduler = optim.lr_scheduler.StepLR(
                optimizer, step_size=30, gamma=0.1
            )
        else:
            scheduler = optim.lr_scheduler.ConstantLR(optimizer)
        return optimizer, scheduler

    def prepare_data(self) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """Prepare training, validation, and test data loaders with proper stratified splitting"""
        logger.info(" MEDUSA VAULT: Preparing datasets with stratified splitting to prevent data leakage...")

        # Load all data first for proper stratified splitting
        full_dataset = BasketballDataset(
            self.config.data_path, self.config, split="full"
        )

        # Check if we have data and labels for stratified split
        if hasattr(full_dataset, 'data') and hasattr(full_dataset, 'labels') and len(full_dataset.data) > 0:
            # Get data and labels
            X_full = full_dataset.data
            y_full = full_dataset.labels

            # Check class distribution
            unique_classes, class_counts = np.unique(y_full, return_counts=True)
            logger.info(f"📊 Full dataset class distribution: {dict(zip(unique_classes, class_counts))}")

            # Ensure we have at least 2 classes for stratified split
            if len(unique_classes) < 2:
                logger.warning("⚠️ Only one class found, creating balanced synthetic targets")
                # Create balanced binary targets based on feature statistics
                feature_median = np.median(X_full, axis=0).mean()
                sample_medians = np.median(X_full, axis=1)
                y_full = (sample_medians > feature_median).astype(np.int64)
                unique_classes, class_counts = np.unique(y_full, return_counts=True)
                logger.info(f"📊 Synthetic class distribution: {dict(zip(unique_classes, class_counts))}")

            # Stratified split: 70% train, 15% val, 15% test
            from sklearn.model_selection import train_test_split

            # First split: 70% train, 30% temp (val+test)
            X_train, X_temp, y_train, y_temp = train_test_split(
                X_full, y_full, test_size=0.3, random_state=42, stratify=y_full
            )

            # Second split: 15% val, 15% test from the 30% temp
            X_val, X_test, y_val, y_test = train_test_split(
                X_temp, y_temp, test_size=0.5, random_state=42, stratify=y_temp
            )

            logger.info(f"📊 Stratified split: Train={len(X_train)}, Val={len(X_val)}, Test={len(X_test)}")
            logger.info(f"📊 Train class distribution: {dict(zip(*np.unique(y_train, return_counts=True)))}")
            logger.info(f"📊 Val class distribution: {dict(zip(*np.unique(y_val, return_counts=True)))}")
            logger.info(f"📊 Test class distribution: {dict(zip(*np.unique(y_test, return_counts=True)))}")

            # Create datasets with stratified data
            train_dataset = StratifiedBasketballDataset(X_train, y_train, full_dataset.features, self.config, "train")
            val_dataset = StratifiedBasketballDataset(X_val, y_val, full_dataset.features, self.config, "val", train_dataset.scaler)
            test_dataset = StratifiedBasketballDataset(X_test, y_test, full_dataset.features, self.config, "test", train_dataset.scaler)

        else:
            # Fallback to original method if stratified split fails
            logger.warning("⚠️ Stratified split failed, using random split")
            train_dataset = BasketballDataset(
                self.config.data_path, self.config, split="train"
            )
            val_dataset = BasketballDataset(
                self.config.data_path, self.config, split="val", fitted_scaler=train_dataset.scaler
            )
            test_dataset = BasketballDataset(
                self.config.data_path, self.config, split="test", fitted_scaler=train_dataset.scaler
            )
        # Create data loaders with class balancing for training
        from torch.utils.data import WeightedRandomSampler

        # Calculate class weights for balanced sampling
        if hasattr(train_dataset, 'labels') and len(train_dataset.labels) > 0:
            class_counts = torch.bincount(torch.tensor(train_dataset.labels))
            class_weights = 1.0 / class_counts.float()
            sample_weights = class_weights[torch.tensor(train_dataset.labels)]

            # Create weighted sampler for balanced training
            sampler = WeightedRandomSampler(
                weights=sample_weights,
                num_samples=len(sample_weights),
                replacement=True
            )

            train_loader = DataLoader(
                train_dataset,
                batch_size=self.config.batch_size,
                sampler=sampler,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=True
            )
            logger.info(f"✅ Applied class balancing: {class_counts.tolist()}")
        else:
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.config.batch_size,
                shuffle=True,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=True
            )
            logger.warning("⚠️ Could not apply class balancing")
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=self.config.num_workers,
            pin_memory=self.config.pin_memory
        )
        test_loader = DataLoader(
            test_dataset,
            batch_size=self.config.batch_size,
            shuffle=False,
            num_workers=self.config.num_workers,
            pin_memory=self.config.pin_memory
        )
        logger.info(f" Data loaders ready: Train={len(train_loader)}, Val={len(val_loader)}, Test={len(test_loader)} batches")
        return train_loader, val_loader, test_loader

    def _add_prediction_validation_hooks(self):
        """Add hooks to validate model predictions during training"""
        def validation_hook(_module, _input_tensor, output):
            """Hook to validate model outputs"""
            if output.dim() == 2 and output.size(1) == 2:
                # Binary classification outputs
                probabilities = F.softmax(output, dim=1)
                win_probs = probabilities[:, 1]

                # Check for extreme predictions
                extreme_low = (win_probs < 0.01).sum().item()
                extreme_high = (win_probs > 0.99).sum().item()

                if extreme_low > 0 or extreme_high > 0:
                    logger.warning(f"Extreme predictions: {extreme_low} < 1%, {extreme_high} > 99%")

                # Log prediction distribution
                mean_prob = win_probs.mean().item()
                std_prob = win_probs.std().item()
                if std_prob > 0.4:  # Very high variance indicates potential instability
                    logger.warning(f"High prediction variance: mean={mean_prob:.3f}, std={std_prob:.3f}")

        # Register hook on the final output layer
        self.model.win_probability_head.register_forward_hook(validation_hook)

    def train_epoch(self, train_loader: DataLoader) -> TrainingMetrics:
        """Train for one epoch"""
        self.model.train()
        total_loss = 0.0
        total_samples = 0
        correct_predictions = 0
        all_predictions = []
        all_labels = []

        for batch_idx, (data, labels) in enumerate(train_loader):
            data = data.to(self.device)
            labels = labels.squeeze().to(self.device)
            # Validate input data to prevent extreme values
            data = torch.clamp(data, -10, 10)  # Prevent extreme input values

            # Forward pass
            if self.config.mixed_precision and self.scaler:
                with torch.amp.autocast('cuda'):  # Updated deprecated function
                    outputs = self.model(data)
                    loss = self.criterion(outputs, labels)
            else:
                outputs = self.model(data)
                loss = self.criterion(outputs, labels)

            # Validate loss to prevent training instability
            if torch.isnan(loss) or torch.isinf(loss):
                logger.warning(f"Invalid loss detected: {loss.item()}, skipping batch")
                continue

            # Backward pass
            self.optimizer.zero_grad()
            if self.config.mixed_precision and self.scaler:
                self.scaler.scale(loss).backward()
                self.scaler.unscale_(self.optimizer)
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clip)
                self.scaler.step(self.optimizer)
                self.scaler.update()
            else:
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), self.config.gradient_clip)
                self.optimizer.step()
            
            # Validate outputs for extreme predictions
            probabilities = F.softmax(outputs, dim=1)
            extreme_predictions = (probabilities[:, 1] < 0.05) | (probabilities[:, 1] > 0.95)
            if extreme_predictions.any():
                logger.warning(f"Extreme predictions detected in {extreme_predictions.sum().item()} samples")

            # Statistics
            total_loss += loss.item() * data.size(0)
            total_samples += data.size(0)
            predictions = torch.argmax(outputs, dim=1)
            correct_predictions += (predictions == labels).sum().item()
            all_predictions.extend(predictions.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

            # Log progress
            if batch_idx % 50 == 0:
                logger.info(f" Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}")
        
        # Calculate metrics
        avg_loss = total_loss / total_samples
        accuracy = correct_predictions / total_samples
        f1 = f1_score(all_labels, all_predictions, average='weighted')
        
        return TrainingMetrics(
            epoch=self.current_epoch,
            train_loss=avg_loss,
            train_accuracy=accuracy,
            train_f1=f1,
            learning_rate=self.optimizer.param_groups[0]['lr'],
            gpu_memory=torch.cuda.memory_allocated() / 1e9 if torch.cuda.is_available() else 0.0
        )

    def validate_epoch(self, val_loader: DataLoader) -> TrainingMetrics:
        """Validate for one epoch"""
        self.model.eval()
        total_loss = 0.0
        total_samples = 0
        correct_predictions = 0
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for data, labels in val_loader:
                data = data.to(self.device)
                labels = labels.squeeze().to(self.device)
                outputs = self.model(data)
                loss = self.criterion(outputs, labels)
                total_loss += loss.item() * data.size(0)
                total_samples += data.size(0)
                predictions = torch.argmax(outputs, dim=1)
                correct_predictions += (predictions == labels).sum().item()
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.cpu().numpy())
        
        # Calculate metrics
        avg_loss = total_loss / total_samples
        accuracy = correct_predictions / total_samples
        f1 = f1_score(all_labels, all_predictions, average='weighted')
        
        return TrainingMetrics(
            epoch=self.current_epoch,
            val_loss=avg_loss,
            val_accuracy=accuracy,
            val_f1=f1
        )

    def train(self) -> Dict[str, Any]:
        """Complete training loop"""
        logger.info(" MEDUSA VAULT: Starting neural training pipeline...")

        # Prepare data first to determine input dimensions
        train_loader, val_loader, test_loader = self.prepare_data()

        # Get actual input dimension from data and update model accordingly
        sample_batch = next(iter(train_loader))
        actual_input_dim = sample_batch[0].shape[1]

        # Always update config with actual input dimension to handle real data
        if actual_input_dim != self.config.input_dim:
            logger.info(f"🔧 Updating input_dim from {self.config.input_dim} to {actual_input_dim} based on actual data")
            self.config.input_dim = actual_input_dim
        else:
            logger.info(f"✅ Input dimension matches expected: {actual_input_dim}")

        # Setup model and optimizer after determining correct dimensions
        self.model = self._build_model()
        self.optimizer, self.scheduler = self._setup_optimizer()
        if self.config.mixed_precision:
            self.scaler = torch.amp.GradScaler('cuda')  # Updated deprecated function

        # Initialize loss function
        if hasattr(self.config, 'use_focal_loss') and self.config.use_focal_loss:
            self.criterion = FocalLoss(
                alpha=getattr(self.config, 'focal_alpha', 0.25),
                gamma=getattr(self.config, 'focal_gamma', 2.0)
            )
            logger.info(f" Using Focal Loss (alpha={self.criterion.alpha}, gamma={self.criterion.gamma})")
        else:
            self.criterion = nn.CrossEntropyLoss()
            logger.info(" Using Cross Entropy Loss")

        logger.info(f" Model: {sum(p.numel() for p in self.model.parameters())} parameters")

        # Add prediction validation hook
        self._add_prediction_validation_hooks()

        # Training loop
        for epoch in range(self.config.num_epochs):
            self.current_epoch = epoch
            start_time = datetime.now()
            # Train epoch
            train_metrics = self.train_epoch(train_loader)
            # Validate epoch
            val_metrics = self.validate_epoch(val_loader)
            # Combine metrics
            epoch_metrics = TrainingMetrics(
                epoch=epoch,
                train_loss=train_metrics.train_loss,
                val_loss=val_metrics.val_loss,
                train_accuracy=train_metrics.train_accuracy,
                val_accuracy=train_metrics.val_accuracy,
                train_f1=train_metrics.train_f1,
                val_f1=val_metrics.val_f1,
                learning_rate=train_metrics.learning_rate,
                gpu_memory=train_metrics.gpu_memory
            )
            self.training_history.append(epoch_metrics)
            # Log to TensorBoard
            self._log_metrics(epoch_metrics)
            # Scheduler step
            self.scheduler.step()
            # Enhanced early stopping with overfitting detection
            val_acc_improved = val_metrics.val_accuracy > getattr(self, 'best_val_acc', 0)
            val_loss_improved = val_metrics.val_loss < self.best_val_loss

            # Check for overfitting (train acc much higher than val acc)
            overfitting_gap = train_metrics.train_accuracy - val_metrics.val_accuracy
            is_overfitting = overfitting_gap > 0.02  # VERY STRICT 2% gap threshold (was 5%)

            if val_loss_improved and val_acc_improved:
                self.best_val_loss = val_metrics.val_loss
                self.best_val_acc = val_metrics.val_accuracy
                self.patience_counter = 0
                self._save_checkpoint(epoch, is_best=True)
                logger.info(f" ✅ New best model saved (Val Acc: {val_metrics.val_accuracy:.3f})")
            else:
                self.patience_counter += 1

            # Regular checkpoint
            if epoch % 10 == 0:
                self._save_checkpoint(epoch, is_best=False)

            elapsed = datetime.now() - start_time
            overfitting_warning = " ⚠️ OVERFITTING DETECTED" if is_overfitting else ""
            logger.info(
                f" Epoch {epoch:3d}/{self.config.num_epochs} | "
                f"Train Loss: {train_metrics.train_loss:.4f} | "
                f"Val Loss: {val_metrics.val_loss:.4f} | "
                f"Train Acc: {train_metrics.train_accuracy:.3f} | "
                f"Val Acc: {val_metrics.val_accuracy:.3f} | "
                f"Gap: {overfitting_gap:.3f} | "
                f"Time: {elapsed.total_seconds():.1f}s{overfitting_warning}"
            )

            # Enhanced early stopping with overfitting protection
            if self.patience_counter >= self.config.early_stopping_patience:
                logger.info(f" Early stopping triggered after {epoch + 1} epochs")
                break
            elif is_overfitting and epoch > 2:  # VERY EARLY overfitting detection (was 5)
                logger.warning(f" ⚠️ Stopping due to overfitting at epoch {epoch + 1}")
                break

        # Final evaluation on test set
        test_metrics = self.evaluate(test_loader)
        logger.info(" MEDUSA VAULT: Training completed!")
        logger.info(f" Final test accuracy: {test_metrics['accuracy']:.3f}")
        logger.info(f" Final test F1-score: {test_metrics['f1_score']:.3f}")

        # Save final results
        results = {
            'config': self.config.__dict__,
            'training_history': [m.__dict__ for m in self.training_history],
            'test_metrics': test_metrics,
            'best_val_loss': self.best_val_loss,
            'total_epochs': len(self.training_history)
        }
        results_path = Path(self.config.model_save_path) / "training_results.json"
        with open(results_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        return results

    def evaluate(self, test_loader: DataLoader) -> Dict[str, float]:
        """Evaluate model on test set"""
        self.model.eval()
        all_predictions = []
        all_labels = []
        with torch.no_grad():
            for data, labels in test_loader:
                data = data.to(self.device)
                labels = labels.squeeze()
                outputs = self.model(data)
                predictions = torch.argmax(outputs, dim=1)
                all_predictions.extend(predictions.cpu().numpy())
                all_labels.extend(labels.numpy())
        # Calculate comprehensive metrics
        accuracy = accuracy_score(all_labels, all_predictions)
        precision = precision_score(all_labels, all_predictions, average='weighted')
        recall = recall_score(all_labels, all_predictions, average='weighted')
        f1 = f1_score(all_labels, all_predictions, average='weighted')
        return {
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1
        }

    def _log_metrics(self, metrics: TrainingMetrics):
        """Log metrics to TensorBoard"""
        if self.writer:
            self.writer.add_scalar('Loss/Train', metrics.train_loss, metrics.epoch)
            self.writer.add_scalar('Loss/Validation', metrics.val_loss, metrics.epoch)
            self.writer.add_scalar('Accuracy/Train', metrics.train_accuracy, metrics.epoch)
            self.writer.add_scalar('Accuracy/Validation', metrics.val_accuracy, metrics.epoch)
            self.writer.add_scalar('F1/Train', metrics.train_f1, metrics.epoch)
            self.writer.add_scalar('F1/Validation', metrics.val_f1, metrics.epoch)
            self.writer.add_scalar('Learning_Rate', metrics.learning_rate, metrics.epoch)
            if torch.cuda.is_available():
                self.writer.add_scalar('GPU_Memory_GB', metrics.gpu_memory, metrics.epoch)

    def _save_checkpoint(self, epoch: int, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_loss': self.best_val_loss,
            'config': self.config.__dict__
        }
        if self.scaler:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()

        # Regular checkpoint
        checkpoint_path = Path(self.config.model_save_path) / f"checkpoint_epoch_{epoch}.pt"
        torch.save(checkpoint, checkpoint_path)

        # Best model
        if is_best:
            best_path = Path(self.config.model_save_path) / "best_model.pt"
            torch.save(checkpoint, best_path)
            logger.info(f" Best model saved at epoch {epoch}")

class EnhancedNeuralBasketballCore(nn.Module):
    """Enhanced Neural Basketball Core with constrained outputs to prevent extreme predictions"""
    def __init__(self, input_dim: int = 128, hidden_dim: int = 64,
                 num_layers: int = 2, dropout_rate: float = 0.7):
        super().__init__()
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers

        # Simplified architecture to prevent overfitting
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )

        # Reduced complexity with stronger regularization
        self.hidden_layers = nn.ModuleList()
        for _ in range(num_layers):  # Fixed unused variable warning
            layer = nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            )
            self.hidden_layers.append(layer)

        # Simplified attention mechanism
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_dim,
            num_heads=4,  # Reduced from 8
            dropout=dropout_rate,
            batch_first=True
        )

        # Output head for binary classification with constrained initialization
        self.win_probability_head = nn.Linear(hidden_dim, 2)

        # Additional prediction heads (for future use)
        self.spread_head = nn.Linear(hidden_dim, 1)
        self.total_points_head = nn.Linear(hidden_dim, 1)
        self.confidence_head = nn.Linear(hidden_dim, 1)

        self._init_weights()

    def _init_weights(self):
        """Initialize model weights with conservative values to prevent extreme outputs"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                # Use smaller initialization to prevent extreme predictions
                nn.init.xavier_uniform_(module.weight, gain=0.5)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)

        # Special initialization for output layer to encourage balanced predictions
        with torch.no_grad():
            self.win_probability_head.weight.data *= 0.1
            self.win_probability_head.bias.data.zero_()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass with gradient clipping to prevent extreme outputs"""
        # Input validation and clipping
        x = torch.clamp(x, -5, 5)  # Prevent extreme input values

        # Input projection
        x = self.input_projection(x)

        # Hidden layers with residual connections (but only if dimensions match)
        for i, layer in enumerate(self.hidden_layers):
            if i == 0:
                x = layer(x)
            else:
                residual = x
                x = layer(x)
                x = x + residual  # Residual connection

        # Self-attention with gradient clipping
        x_unsqueezed = x.unsqueeze(1)
        attended, _ = self.attention(x_unsqueezed, x_unsqueezed, x_unsqueezed)
        x = attended.squeeze(1)

        # Apply gradient clipping to prevent extreme gradients
        x = torch.clamp(x, -3, 3)

        # Output with constrained logits
        win_prob_logits = self.win_probability_head(x)

        # Constrain logits to prevent extreme probabilities
        win_prob_logits = torch.clamp(win_prob_logits, -3, 3)

        return win_prob_logits

    def predict_probabilities(self, x: torch.Tensor) -> torch.Tensor:
        """Get constrained probability predictions"""
        logits = self.forward(x)
        probabilities = F.softmax(logits, dim=1)

        # Ensure probabilities are within reasonable bounds (0.1 to 0.9)
        probabilities = torch.clamp(probabilities, 0.1, 0.9)
        probabilities = probabilities / probabilities.sum(dim=1, keepdim=True)  # Renormalize

        return probabilities

    def validate_predictions(self, predictions: torch.Tensor) -> torch.Tensor:
        """Validate and constrain predictions to prevent extreme values"""
        # Ensure predictions are within reasonable basketball probability ranges
        if predictions.dim() == 2 and predictions.size(1) == 2:
            # Binary classification logits
            probabilities = F.softmax(predictions, dim=1)
            # Constrain to reasonable win probability range (10% to 90%)
            probabilities = torch.clamp(probabilities, 0.1, 0.9)
            probabilities = probabilities / probabilities.sum(dim=1, keepdim=True)
            return probabilities
        else:
            # Single value predictions
            return torch.clamp(predictions, -3, 3)

def create_training_config(**kwargs) -> TrainingConfig:
    """Create a training configuration with custom parameters"""
    config = TrainingConfig()
    # Update with provided parameters
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)
        else:
            logger.warning(f" Unknown config parameter: {key}")
    return config

def main():
    """Main training function - supports both NBA and WNBA"""
    logger.info(" MEDUSA VAULT: HYPER MEDUSA NEURAL TRAINING PIPELINE ")
    logger.info(" MEDUSA VAULT: =" * 60)
    # Train models for both lea
    leagues = ["NBA", "WNBA"]
    for league in leagues:
        logger.info(f" Training {league} Neural Models...")
        logger.info(" MEDUSA VAULT: -" * 40)
        # Create league-specific training configuration
        config = create_training_config(
            league=league,
            batch_size=64,
            learning_rate=0.001,
            num_epochs=50,
            hidden_dim=512,
            use_data_augmentation=True,
            mixed_precision=True,
            model_save_path=f"./models/neural_core/{league.lower()}"
        )
        # Initialize training pipeline
        pipeline = NeuralTrainingPipeline(config)
        # Start training
        results = pipeline.train()
        logger.info(f" {league} training pipeline completed successfully!")
        logger.info(f" Best validation loss: {results['best_val_loss']:.4f}")
        logger.info(f" Test accuracy: {results['test_metrics']['accuracy']:.3f}")
        logger.info(" MEDUSA VAULT: ")

if __name__ == "__main__":
    main()
