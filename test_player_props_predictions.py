#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Player Props Prediction Testing
Test the trained player props models with sample predictions
"""

import sys
import os
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.services.player_props_prediction_service import (
    PlayerPropsPredictor, 
    PlayerPropPrediction
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sample_player_data():
    """Create sample player data for testing"""
    
    # Sample WNBA players with realistic stats
    players = [
        {
            'player_id': 'player_001',
            'player_name': 'A\'ja <PERSON>',
            'recent_avg': 22.8,  # Recent points average
            'season_avg': 21.5,  # Season points average
            'rank_position': 5,   # Top 5 player
            'consistency': 0.85,  # High consistency
            'tier': 4,           # Elite tier
            'position_encoded': 4, # Center
            'recent_form': 0.2,   # Good recent form
            'hot_streak': 1,      # On hot streak
            'cold_streak': 0
        },
        {
            'player_id': 'player_002', 
            'player_name': '<PERSON>reanna <PERSON>',
            'recent_avg': 19.5,
            'season_avg': 18.9,
            'rank_position': 8,
            'consistency': 0.82,
            'tier': 4,
            'position_encoded': 3, # Forward
            'recent_form': 0.1,
            'hot_streak': 0,
            'cold_streak': 0
        },
        {
            'player_id': 'player_003',
            'player_name': 'Sabrina Ionescu', 
            'recent_avg': 16.2,
            'season_avg': 15.8,
            'rank_position': 12,
            'consistency': 0.78,
            'tier': 3,
            'position_encoded': 1, # Guard
            'recent_form': -0.1,
            'hot_streak': 0,
            'cold_streak': 0
        },
        {
            'player_id': 'player_004',
            'player_name': 'Alyssa Thomas',
            'recent_avg': 12.5,
            'season_avg': 13.1,
            'rank_position': 18,
            'consistency': 0.75,
            'tier': 3,
            'position_encoded': 3, # Forward
            'recent_form': -0.2,
            'hot_streak': 0,
            'cold_streak': 1
        },
        {
            'player_id': 'player_005',
            'player_name': 'Kelsey Plum',
            'recent_avg': 17.8,
            'season_avg': 16.9,
            'rank_position': 10,
            'consistency': 0.73,
            'tier': 3,
            'position_encoded': 1, # Guard
            'recent_form': 0.3,
            'hot_streak': 1,
            'cold_streak': 0
        }
    ]
    
    return players

def create_sample_game_context():
    """Create sample game context data"""
    return {
        'opponent_strength': 0.6,  # Moderate opponent
        'home_advantage': 1,       # Home game
        'rest_days': 1,           # 1 day rest
        'back_to_back': 0,        # Not back-to-back
        'season_progress': 0.4    # Mid-season
    }

def create_sample_lines():
    """Create sample betting lines for props"""
    return {
        'points': {
            'player_001': 21.5,  # A'ja Wilson points line
            'player_002': 18.5,  # Breanna Stewart points line
            'player_003': 15.5,  # Sabrina Ionescu points line
            'player_004': 12.5,  # Alyssa Thomas points line
            'player_005': 16.5   # Kelsey Plum points line
        },
        'rebounds': {
            'player_001': 9.5,   # A'ja Wilson rebounds line
            'player_002': 8.5,   # Breanna Stewart rebounds line
            'player_003': 4.5,   # Sabrina Ionescu rebounds line
            'player_004': 7.5,   # Alyssa Thomas rebounds line
            'player_005': 3.5    # Kelsey Plum rebounds line
        },
        'assists': {
            'player_001': 2.5,   # A'ja Wilson assists line
            'player_002': 3.5,   # Breanna Stewart assists line
            'player_003': 6.5,   # Sabrina Ionescu assists line
            'player_004': 5.5,   # Alyssa Thomas assists line
            'player_005': 4.5    # Kelsey Plum assists line
        }
    }

async def test_single_player_predictions():
    """Test predictions for a single player across multiple props"""
    
    print("🏀 SINGLE PLAYER MULTI-PROP PREDICTION TEST")
    print("=" * 50)
    
    # Initialize predictor
    predictor = PlayerPropsPredictor(league="WNBA")
    
    # Load models
    load_results = predictor.load_all_models()
    loaded_models = [prop for prop, success in load_results.items() if success]
    
    print(f"📊 Loaded models: {loaded_models}")
    
    if not loaded_models:
        print("❌ No models loaded successfully")
        return
    
    # Get sample data
    players = create_sample_player_data()
    game_context = create_sample_game_context()
    lines = create_sample_lines()
    
    # Test with A'ja Wilson
    player = players[0]  # A'ja Wilson
    print(f"\n🌟 Testing predictions for: {player['player_name']}")
    print("-" * 30)
    
    for prop_type in loaded_models:
        try:
            line_value = lines[prop_type].get(player['player_id'])
            
            prediction = predictor.predict_prop(
                player_data=player,
                prop_type=prop_type,
                game_context=game_context,
                line_value=line_value
            )
            
            print(f"📊 {prop_type.upper()}:")
            print(f"   Prediction: {prediction.predicted_value:.1f}")
            print(f"   Line: {line_value}")
            print(f"   Confidence: {prediction.confidence_score:.1%}")
            print(f"   Over: {prediction.over_probability:.1%}")
            print(f"   Under: {prediction.under_probability:.1%}")
            print(f"   Recommendation: {prediction.recommendation}")
            print(f"   Factors: {', '.join(prediction.factors)}")
            print()
            
        except Exception as e:
            print(f"❌ Error predicting {prop_type}: {e}")

async def test_multiple_players_single_prop():
    """Test predictions for multiple players on a single prop"""
    
    print("\n🏀 MULTIPLE PLAYERS SINGLE PROP TEST")
    print("=" * 50)
    
    # Initialize predictor
    predictor = PlayerPropsPredictor(league="WNBA")
    
    # Load points model
    if not predictor.load_model("points"):
        print("❌ Failed to load points model")
        return
    
    # Get sample data
    players = create_sample_player_data()
    game_context = create_sample_game_context()
    lines = create_sample_lines()
    
    print("📊 POINTS PREDICTIONS:")
    print("-" * 30)
    
    for player in players:
        try:
            line_value = lines['points'].get(player['player_id'])
            
            prediction = predictor.predict_prop(
                player_data=player,
                prop_type="points",
                game_context=game_context,
                line_value=line_value
            )
            
            print(f"🏀 {player['player_name']:<18} | "
                  f"Pred: {prediction.predicted_value:5.1f} | "
                  f"Line: {line_value:4.1f} | "
                  f"Conf: {prediction.confidence_score:4.0%} | "
                  f"{prediction.recommendation:>5}")
            
        except Exception as e:
            print(f"❌ Error for {player['player_name']}: {e}")

async def test_model_info():
    """Test model information retrieval"""
    
    print("\n🏀 MODEL INFORMATION TEST")
    print("=" * 50)
    
    # Initialize predictor
    predictor = PlayerPropsPredictor(league="WNBA")
    
    # Load all models
    load_results = predictor.load_all_models()
    
    print("📊 Available Models:")
    for prop_type in predictor.get_available_models():
        info = predictor.get_model_info(prop_type)
        print(f"   {prop_type}: {info['parameters']} parameters, {info['input_dim']} features")

async def test_edge_cases():
    """Test edge cases and error handling"""
    
    print("\n🏀 EDGE CASES TEST")
    print("=" * 50)
    
    # Initialize predictor
    predictor = PlayerPropsPredictor(league="WNBA")
    
    # Test with minimal data
    minimal_player = {
        'player_id': 'test_player',
        'player_name': 'Test Player'
    }
    
    # Test with missing model
    try:
        prediction = predictor.predict_prop(minimal_player, "nonexistent_prop")
        print(f"✅ Handled missing model gracefully")
    except Exception as e:
        print(f"⚠️ Missing model error: {e}")
    
    # Test with loaded model
    if predictor.load_model("points"):
        prediction = predictor.predict_prop(minimal_player, "points")
        print(f"✅ Handled minimal data: {prediction.predicted_value:.1f} points")

async def main():
    """Main test function"""
    
    print("🚀 HYPER MEDUSA NEURAL VAULT - Player Props Prediction Testing")
    print("=" * 70)
    
    # Run all tests
    await test_model_info()
    await test_single_player_predictions()
    await test_multiple_players_single_prop()
    await test_edge_cases()
    
    print("\n" + "=" * 70)
    print("🎉 Player Props Prediction Testing Complete!")
    print("📈 Models are ready for production use")

if __name__ == "__main__":
    asyncio.run(main())
