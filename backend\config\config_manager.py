import os
import json
import logging
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from dataclasses import dataclass, field
from enum import Enum
import configparser
from datetime import datetime


#!/usr/bin/env python3
"""
Configuration Management System - HYPER MEDUSA NEURAL VAULT
==========================================================

Centralized configuration management with environment-specific settings,
validation, and dynamic reloading capabilities.
"""

try:
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    yaml = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Environment(Enum):
    """Environment types"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"

@dataclass
class DatabaseConfig:
    """Database configuration"""
    host: str = "localhost"
    port: int = 5432
    name: str = "hyper_medusa_vault"
    user: str = "postgres"
    password: str = ""
    pool_size: int = 20
    max_overflow: int = 30
    pool_timeout: int = 30
    pool_recycle: int = 3600
    echo: bool = False
    
    @property
    def url(self) -> str:
        """Generate database URL"""
        if self.password:
            return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"
        return f"postgresql://{self.user}@{self.host}:{self.port}/{self.name}"

@dataclass
class RedisConfig:
    """Redis configuration"""
    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: Optional[str] = None
    max_connections: int = 50
    socket_timeout: int = 30
    socket_connect_timeout: int = 30
    
    @property
    def url(self) -> str:
        """Generate Redis URL"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        return f"redis://{self.host}:{self.port}/{self.db}"

@dataclass
class APIConfig:
    """API configuration"""
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 4
    timeout: int = 30
    max_requests: int = 1000
    max_requests_jitter: int = 50
    preload_app: bool = True
    reload: bool = False
    debug: bool = False
    cors_origins: List[str] = field(default_factory=lambda: ["*"])
    rate_limit: str = "100/minute"

@dataclass
class MLConfig:
    """Machine Learning configuration"""
    model_path: str = "models"
    batch_size: int = 32
    learning_rate: float = 0.001
    epochs: int = 100
    validation_split: float = 0.2
    early_stopping_patience: int = 10
    device: str = "auto"  # auto, cpu, cuda
    mixed_precision: bool = True
    gradient_clipping: float = 1.0
    checkpoint_interval: int = 10

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_bytes: int = 10485760  # 10MB
    backup_count: int = 5
    json_format: bool = False
    structured_logging: bool = True

@dataclass
class SecurityConfig:
    """Security configuration"""
    secret_key: str = ""
    jwt_algorithm: str = "HS256"
    jwt_expiration: int = 3600  # 1 hour
    password_min_length: int = 8
    max_login_attempts: int = 5
    lockout_duration: int = 900  # 15 minutes
    encryption_key: Optional[str] = None
    cors_enabled: bool = True
    csrf_protection: bool = True

@dataclass
class MonitoringConfig:
    """Monitoring and metrics configuration"""
    prometheus_enabled: bool = True
    prometheus_port: int = 9090
    grafana_enabled: bool = True
    grafana_port: int = 3000
    health_check_interval: int = 30
    metrics_retention_days: int = 30
    alerting_enabled: bool = True
    slack_webhook: Optional[str] = None
    email_alerts: bool = False

class ConfigManager:
    """Centralized configuration management system"""
    
    def __init__(self, environment: Optional[Environment] = None):
        self.environment = environment or self._detect_environment()
        self.config_dir = Path(__file__).parent
        self.base_config_file = self.config_dir / "base.yaml"
        self.env_config_file = self.config_dir / f"{self.environment.value}.yaml"
        self.secrets_file = self.config_dir / "secrets.yaml"
        
        # Configuration sections
        self.database: DatabaseConfig = DatabaseConfig()
        self.redis: RedisConfig = RedisConfig()
        self.api: APIConfig = APIConfig()
        self.ml: MLConfig = MLConfig()
        self.logging: LoggingConfig = LoggingConfig()
        self.security: SecurityConfig = SecurityConfig()
        self.monitoring: MonitoringConfig = MonitoringConfig()
        
        # Load configurations
        self._load_configurations()
        self._validate_configurations()
        
        logger.info(f"Configuration loaded for environment: {self.environment.value}")
    
    def _detect_environment(self) -> Environment:
        """Detect current environment"""
        env_name = os.getenv("ENVIRONMENT", "development").lower()
        try:
            return Environment(env_name)
        except ValueError:
            logger.warning(f"Unknown environment '{env_name}', defaulting to development")
            return Environment.DEVELOPMENT
    
    def _load_configurations(self):
        """Load configurations from files and environment variables"""
        # Load base configuration
        if self.base_config_file.exists():
            self._load_yaml_config(self.base_config_file)
        
        # Load environment-specific configuration
        if self.env_config_file.exists():
            self._load_yaml_config(self.env_config_file)
        
        # Load secrets (if exists)
        if self.secrets_file.exists():
            self._load_yaml_config(self.secrets_file)
        
        # Override with environment variables
        self._load_environment_variables()
    
    def _load_yaml_config(self, config_file: Path):
        """Load configuration from YAML file"""
        if not YAML_AVAILABLE:
            logger.warning(f"YAML not available, skipping {config_file}")
            return

        try:
            with open(config_file, 'r') as f:
                config_data = yaml.safe_load(f) or {}

            # Update configuration sections
            if 'database' in config_data:
                self._update_dataclass(self.database, config_data['database'])
            if 'redis' in config_data:
                self._update_dataclass(self.redis, config_data['redis'])
            if 'api' in config_data:
                self._update_dataclass(self.api, config_data['api'])
            if 'ml' in config_data:
                self._update_dataclass(self.ml, config_data['ml'])
            if 'logging' in config_data:
                self._update_dataclass(self.logging, config_data['logging'])
            if 'security' in config_data:
                self._update_dataclass(self.security, config_data['security'])
            if 'monitoring' in config_data:
                self._update_dataclass(self.monitoring, config_data['monitoring'])

        except Exception as e:
            logger.error(f"Error loading config file {config_file}: {e}")
    
    def _update_dataclass(self, obj: Any, data: Dict[str, Any]):
        """Update dataclass instance with dictionary data"""
        for key, value in data.items():
            if hasattr(obj, key):
                setattr(obj, key, value)
    
    def _load_environment_variables(self):
        """Load configuration from environment variables"""
        # Database
        if os.getenv("DB_HOST"):
            self.database.host = os.getenv("DB_HOST")
        if os.getenv("DB_PORT"):
            self.database.port = int(os.getenv("DB_PORT"))
        if os.getenv("DB_NAME"):
            self.database.name = os.getenv("DB_NAME")
        if os.getenv("DB_USER"):
            self.database.user = os.getenv("DB_USER")
        if os.getenv("DB_PASSWORD"):
            self.database.password = os.getenv("DB_PASSWORD")
        
        # Redis
        if os.getenv("REDIS_HOST"):
            self.redis.host = os.getenv("REDIS_HOST")
        if os.getenv("REDIS_PORT"):
            self.redis.port = int(os.getenv("REDIS_PORT"))
        if os.getenv("REDIS_PASSWORD"):
            self.redis.password = os.getenv("REDIS_PASSWORD")
        
        # API
        if os.getenv("API_HOST"):
            self.api.host = os.getenv("API_HOST")
        if os.getenv("API_PORT"):
            self.api.port = int(os.getenv("API_PORT"))
        if os.getenv("API_WORKERS"):
            self.api.workers = int(os.getenv("API_WORKERS"))
        
        # Security
        if os.getenv("SECRET_KEY"):
            self.security.secret_key = os.getenv("SECRET_KEY")
        if os.getenv("JWT_SECRET"):
            self.security.secret_key = os.getenv("JWT_SECRET")
    
    def _validate_configurations(self):
        """Validate configuration values"""
        errors = []
        
        # Validate database configuration
        if not self.database.host:
            errors.append("Database host is required")
        if not self.database.name:
            errors.append("Database name is required")
        
        # Validate security configuration
        if not self.security.secret_key and self.environment == Environment.PRODUCTION:
            errors.append("Secret key is required for production environment")
        
        # Validate API configuration
        if self.api.port < 1 or self.api.port > 65535:
            errors.append("API port must be between 1 and 65535")
        
        if errors:
            raise ValueError(f"Configuration validation failed: {'; '.join(errors)}")
    
    def get_config_dict(self) -> Dict[str, Any]:
        """Get complete configuration as dictionary"""
        return {
            'environment': self.environment.value,
            'database': self.database.__dict__,
            'redis': self.redis.__dict__,
            'api': self.api.__dict__,
            'ml': self.ml.__dict__,
            'logging': self.logging.__dict__,
            'security': {k: v for k, v in self.security.__dict__.items() if k != 'secret_key'},
            'monitoring': self.monitoring.__dict__
        }
    
    def reload(self):
        """Reload configuration from files"""
        logger.info("Reloading configuration...")
        self._load_configurations()
        self._validate_configurations()
        logger.info("Configuration reloaded successfully")
    
    def save_config_template(self, file_path: Optional[Path] = None):
        """Save configuration template"""
        if file_path is None:
            file_path = self.config_dir / "template.json"  # Use JSON if YAML not available

        template = {
            'database': self.database.__dict__,
            'redis': self.redis.__dict__,
            'api': self.api.__dict__,
            'ml': self.ml.__dict__,
            'logging': self.logging.__dict__,
            'security': {k: v for k, v in self.security.__dict__.items() if k != 'secret_key'},
            'monitoring': self.monitoring.__dict__
        }

        if YAML_AVAILABLE and str(file_path).endswith('.yaml'):
            with open(file_path, 'w') as f:
                yaml.dump(template, f, default_flow_style=False, indent=2)
        else:
            # Fallback to JSON
            with open(file_path, 'w') as f:
                json.dump(template, f, indent=2)

        logger.info(f"Configuration template saved to {file_path}")

# Global configuration instance
config = ConfigManager()

def get_config() -> ConfigManager:
    """Get global configuration instance"""
    return config

def reload_config():
    """Reload global configuration"""
    global config
    config.reload()

def export_env_file(config_manager: ConfigManager, file_path: Optional[Path] = None):
    """Export configuration as .env file"""
    if file_path is None:
        file_path = config_manager.config_dir / f".env.{config_manager.environment.value}"

    env_vars = [
        f"ENVIRONMENT={config_manager.environment.value}",
        f"DB_HOST={config_manager.database.host}",
        f"DB_PORT={config_manager.database.port}",
        f"DB_NAME={config_manager.database.name}",
        f"DB_USER={config_manager.database.user}",
        f"REDIS_HOST={config_manager.redis.host}",
        f"REDIS_PORT={config_manager.redis.port}",
        f"API_HOST={config_manager.api.host}",
        f"API_PORT={config_manager.api.port}",
        f"API_WORKERS={config_manager.api.workers}",
    ]

    with open(file_path, 'w') as f:
        f.write('\n'.join(env_vars))

    logger.info(f"Environment file exported to {file_path}")

if __name__ == "__main__":
    # Example usage and testing
    config_manager = ConfigManager()

    # Save configuration template
    config_manager.save_config_template()

    # Export environment file
    export_env_file(config_manager)
