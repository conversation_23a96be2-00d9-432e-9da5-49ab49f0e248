import os
import sys
import logging
from pathlib import Path
from config import config
from database.models import engine_config
from database.crud import redis_config
from config_manager import ConfigManager, Environment, export_env_file


#!/usr/bin/env python3
"""
Configuration Integration Script - HYPER MEDUSA NEURAL VAULT
===========================================================

Integrates the new configuration management system with existing backend components.
"""


# Add backend to path
backend_path = Path(__file__).parent.parent
sys.path.insert(0, str(backend_path))


logger = logging.getLogger(__name__)

def integrate_database_config():
    """Integrate configuration with database models"""
    try:
        # Update database configuration
        db_url = config.database.url
        
        # Create environment variable for SQLAlchemy
        os.environ['DATABASE_URL'] = db_url
        
        # Update engine configuration if it exists
        if hasattr(engine_config, 'update'):
            engine_config.update({
                'pool_size': config.database.pool_size,
                'max_overflow': config.database.max_overflow,
                'pool_timeout': config.database.pool_timeout,
                'pool_recycle': config.database.pool_recycle,
                'echo': config.database.echo
            })
        
        logger.info("Database configuration integrated successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to integrate database configuration: {e}")
        return False

def integrate_redis_config():
    """Integrate configuration with Redis"""
    try:
        # Update Redis configuration
        redis_url = config.redis.url
        
        # Create environment variable for Redis
        os.environ['REDIS_URL'] = redis_url
        
        # Update Redis configuration if it exists
        if hasattr(redis_config, 'update'):
            redis_config.update({
                'host': config.redis.host,
                'port': config.redis.port,
                'db': config.redis.db,
                'password': config.redis.password,
                'max_connections': config.redis.max_connections,
                'socket_timeout': config.redis.socket_timeout,
                'socket_connect_timeout': config.redis.socket_connect_timeout
            })
        
        logger.info("Redis configuration integrated successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to integrate Redis configuration: {e}")
        return False

def integrate_api_config():
    """Integrate configuration with API settings"""
    try:
        # Set API environment variables
        os.environ['API_HOST'] = config.api.host
        os.environ['API_PORT'] = str(config.api.port)
        os.environ['API_WORKERS'] = str(config.api.workers)
        os.environ['API_TIMEOUT'] = str(config.api.timeout)
        os.environ['API_DEBUG'] = str(config.api.debug).lower()
        
        logger.info("API configuration integrated successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to integrate API configuration: {e}")
        return False

def integrate_security_config():
    """Integrate configuration with security settings"""
    try:
        # Set security environment variables
        if config.security.secret_key:
            os.environ['SECRET_KEY'] = config.security.secret_key
        
        os.environ['JWT_ALGORITHM'] = config.security.jwt_algorithm
        os.environ['JWT_EXPIRATION'] = str(config.security.jwt_expiration)
        os.environ['PASSWORD_MIN_LENGTH'] = str(config.security.password_min_length)
        os.environ['MAX_LOGIN_ATTEMPTS'] = str(config.security.max_login_attempts)
        os.environ['LOCKOUT_DURATION'] = str(config.security.lockout_duration)
        
        logger.info("Security configuration integrated successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to integrate security configuration: {e}")
        return False

def integrate_logging_config():
    """Integrate configuration with logging"""
    try:
        # Configure logging based on configuration
        log_level = getattr(logging, config.logging.level.upper())
        
        # Create formatter
        formatter = logging.Formatter(config.logging.format)
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # Clear existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Add console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # Add file handler if specified
        if config.logging.file_path:
            file_handler = logging.FileHandler(config.logging.file_path)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        
        logger.info("Logging configuration integrated successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to integrate logging configuration: {e}")
        return False

def integrate_ml_config():
    """Integrate configuration with ML settings"""
    try:
        # Set ML environment variables
        os.environ['ML_MODEL_PATH'] = config.ml.model_path
        os.environ['ML_BATCH_SIZE'] = str(config.ml.batch_size)
        os.environ['ML_LEARNING_RATE'] = str(config.ml.learning_rate)
        os.environ['ML_EPOCHS'] = str(config.ml.epochs)
        os.environ['ML_DEVICE'] = config.ml.device
        os.environ['ML_MIXED_PRECISION'] = str(config.ml.mixed_precision).lower()
        
        logger.info("ML configuration integrated successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to integrate ML configuration: {e}")
        return False

def create_env_files():
    """Create environment files for different environments"""
    try:
        
        # Create .env files for each environment
        for env in Environment:
            env_config = ConfigManager(env)
            env_file_path = Path(__file__).parent / f".env.{env.value}"
            export_env_file(env_config, env_file_path)
        
        logger.info("Environment files created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to create environment files: {e}")
        return False

def validate_integration():
    """Validate that all configurations are properly integrated"""
    try:
        validation_results = {
            'database': 'DATABASE_URL' in os.environ,
            'redis': 'REDIS_URL' in os.environ,
            'api': 'API_HOST' in os.environ and 'API_PORT' in os.environ,
            'security': 'JWT_ALGORITHM' in os.environ,
            'ml': 'ML_MODEL_PATH' in os.environ
        }
        
        all_valid = all(validation_results.values())
        
        logger.info("Configuration validation results:")
        for component, valid in validation_results.items():
            status = "✓" if valid else "✗"
            logger.info(f"  {status} {component.capitalize()}: {'Valid' if valid else 'Invalid'}")
        
        return all_valid
        
    except Exception as e:
        logger.error(f"Failed to validate configuration integration: {e}")
        return False

def main():
    """Main integration function"""
    logger.info("Starting configuration integration...")
    
    # Integration steps
    integration_steps = [
        ("Database", integrate_database_config),
        ("Redis", integrate_redis_config),
        ("API", integrate_api_config),
        ("Security", integrate_security_config),
        ("Logging", integrate_logging_config),
        ("ML", integrate_ml_config),
        ("Environment Files", create_env_files)
    ]
    
    success_count = 0
    total_steps = len(integration_steps)
    
    for step_name, step_function in integration_steps:
        logger.info(f"Integrating {step_name} configuration...")
        if step_function():
            success_count += 1
            logger.info(f"✓ {step_name} integration completed")
        else:
            logger.error(f"✗ {step_name} integration failed")
    
    # Validate integration
    logger.info("Validating configuration integration...")
    validation_success = validate_integration()
    
    # Summary
    logger.info(f"\nConfiguration Integration Summary:")
    logger.info(f"  Completed: {success_count}/{total_steps} steps")
    logger.info(f"  Validation: {'Passed' if validation_success else 'Failed'}")
    logger.info(f"  Environment: {config.environment.value}")
    
    if success_count == total_steps and validation_success:
        logger.info("✓ Configuration integration completed successfully!")
        return True
    else:
        logger.error("✗ Configuration integration completed with errors")
        return False

if __name__ == "__main__":
    # Configure logging for this script
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    success = main()
    sys.exit(0 if success else 1)
