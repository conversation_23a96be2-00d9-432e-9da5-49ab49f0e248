import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
import os

#!/usr/bin/env python3
"""
Fix Missing Player Data - Task 1.2
Investigate and fix source files with missing player data
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MissingPlayerDataFixer:
    """Fix source files with missing player data"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def investigate_missing_player_data(self) -> Dict[str, Any]:
        """Investigate source files with missing player data"""
        logger.info("🔍 INVESTIGATING MISSING PLAYER DATA - TASK 1.2")
        logger.info("=" * 55)
        
        conn = sqlite3.connect(self.db_path)
        
        # Check source files with missing player data
        query = """
        SELECT 
            source_file,
            data_type,
            COUNT(*) as total_records,
            COUNT(CASE WHEN player_name IS NOT NULL AND player_name != '' AND player_name != 'None' THEN 1 END) as records_with_players,
            COUNT(DISTINCT player_name) as unique_players
        FROM unified_nba_wnba_data 
        WHERE league_name = 'WNBA'
        GROUP BY source_file, data_type
        ORDER BY total_records DESC
        """
        
        source_analysis = pd.read_sql_query(query, conn)
        
        # Identify problematic files
        problematic_files = source_analysis[
            (source_analysis['total_records'] > 1000) & 
            (source_analysis['unique_players'] == 0)
        ]
        
        logger.info(f"📊 ANALYSIS RESULTS:")
        logger.info(f"   Total source files: {len(source_analysis)}")
        logger.info(f"   Problematic files (>1000 records, 0 players): {len(problematic_files)}")
        
        if not problematic_files.empty:
            logger.warning("🚨 PROBLEMATIC FILES FOUND:")
            for _, row in problematic_files.head(10).iterrows():
                logger.warning(f"   • {row['source_file']}: {row['total_records']:,} records, 0 players")
        
        # Check what these files contain
        sample_data = self._analyze_problematic_files(conn, problematic_files)
        
        conn.close()
        
        return {
            'total_source_files': len(source_analysis),
            'problematic_files_count': len(problematic_files),
            'problematic_files': problematic_files.to_dict('records'),
            'sample_data': sample_data,
            'source_analysis': source_analysis.to_dict('records')
        }
    
    def _analyze_problematic_files(self, conn: sqlite3.Connection, problematic_files: pd.DataFrame) -> Dict[str, Any]:
        """Analyze what's in the problematic files"""
        
        if problematic_files.empty:
            return {}
        
        # Sample data from problematic files
        sample_file = problematic_files.iloc[0]['source_file']
        
        query = """
        SELECT *
        FROM unified_nba_wnba_data 
        WHERE source_file = ? AND league_name = 'WNBA'
        LIMIT 5
        """
        
        sample_data = pd.read_sql_query(query, conn, params=(sample_file,))
        
        logger.info(f"📋 SAMPLE DATA FROM {sample_file}:")
        for col in sample_data.columns:
            sample_values = sample_data[col].head(3).tolist()
            logger.info(f"   • {col}: {sample_values}")
        
        return {
            'sample_file': sample_file,
            'sample_columns': list(sample_data.columns),
            'sample_records': sample_data.to_dict('records')
        }
    
    def fix_league_game_finder_files(self) -> Dict[str, Any]:
        """Fix league game finder files that have team data but no player data"""
        logger.info("🔧 FIXING LEAGUE GAME FINDER FILES...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Identify league game finder files
        cursor.execute("""
        SELECT DISTINCT source_file, COUNT(*) as record_count
        FROM unified_nba_wnba_data 
        WHERE league_name = 'WNBA'
        AND source_file LIKE '%league_game_finder%'
        GROUP BY source_file
        ORDER BY record_count DESC
        """)
        
        league_files = cursor.fetchall()
        
        logger.info(f"📊 Found {len(league_files)} league game finder files")
        
        # These files contain team/game data, not individual player data
        # Mark them appropriately
        fixed_count = 0
        for file_name, record_count in league_files:
            # Update data category to reflect that these are team/game records
            cursor.execute("""
            UPDATE unified_nba_wnba_data 
            SET data_category = 'team_game_data',
                player_name = 'TEAM_RECORD'
            WHERE source_file = ? AND league_name = 'WNBA'
            AND (player_name IS NULL OR player_name = '' OR player_name = 'None')
            """, (file_name,))
            
            fixed_count += cursor.rowcount
            logger.info(f"   ✅ Fixed {file_name}: {cursor.rowcount:,} records marked as team data")
        
        conn.commit()
        conn.close()
        
        return {
            'league_files_processed': len(league_files),
            'records_fixed': fixed_count
        }
    
    def extract_player_data_from_raw(self) -> Dict[str, Any]:
        """Extract player data from raw_data JSON fields"""
        logger.info("🔧 EXTRACTING PLAYER DATA FROM RAW FIELDS...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Find records with raw_data but missing player_name
        cursor.execute("""
        SELECT COUNT(*) 
        FROM unified_nba_wnba_data 
        WHERE league_name = 'WNBA'
        AND (player_name IS NULL OR player_name = '' OR player_name = 'None')
        AND raw_data IS NOT NULL
        AND raw_data != ''
        """)
        
        records_to_fix = cursor.fetchone()[0]
        logger.info(f"📊 Found {records_to_fix:,} records with raw_data but missing player_name")
        
        if records_to_fix == 0:
            conn.close()
            return {'records_processed': 0, 'players_extracted': 0}
        
        # Sample some raw_data to see the structure
        cursor.execute("""
        SELECT raw_data
        FROM unified_nba_wnba_data 
        WHERE league_name = 'WNBA'
        AND (player_name IS NULL OR player_name = '' OR player_name = 'None')
        AND raw_data IS NOT NULL
        AND raw_data != ''
        LIMIT 5
        """)
        
        sample_raw = cursor.fetchall()
        logger.info("📋 SAMPLE RAW DATA STRUCTURES:")
        for i, (raw_data,) in enumerate(sample_raw):
            try:
                # Try to parse as dict/JSON
                if raw_data.startswith('{') and raw_data.endswith('}'):
                    logger.info(f"   Sample {i+1}: JSON-like structure")
                else:
                    logger.info(f"   Sample {i+1}: {raw_data[:100]}...")
            except:
                logger.info(f"   Sample {i+1}: Complex structure")
        
        # For now, mark these as needing manual review
        cursor.execute("""
        UPDATE unified_nba_wnba_data 
        SET player_name = 'NEEDS_EXTRACTION',
            data_category = 'raw_data_extraction_needed'
        WHERE league_name = 'WNBA'
        AND (player_name IS NULL OR player_name = '' OR player_name = 'None')
        AND raw_data IS NOT NULL
        AND raw_data != ''
        """)
        
        updated_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        return {
            'records_processed': updated_count,
            'status': 'marked_for_extraction'
        }
    
    def create_data_quality_report(self) -> Dict[str, Any]:
        """Create comprehensive data quality report"""
        logger.info("📊 CREATING DATA QUALITY REPORT...")
        
        conn = sqlite3.connect(self.db_path)
        
        # Overall statistics
        stats_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN player_name IS NOT NULL AND player_name != '' AND player_name != 'None' AND player_name != 'TEAM_RECORD' AND player_name != 'NEEDS_EXTRACTION' THEN 1 END) as valid_player_records,
            COUNT(CASE WHEN player_name = 'TEAM_RECORD' THEN 1 END) as team_records,
            COUNT(CASE WHEN player_name = 'NEEDS_EXTRACTION' THEN 1 END) as extraction_needed,
            COUNT(DISTINCT CASE WHEN player_name IS NOT NULL AND player_name != '' AND player_name != 'None' AND player_name != 'TEAM_RECORD' AND player_name != 'NEEDS_EXTRACTION' THEN player_name END) as unique_players
        FROM unified_nba_wnba_data 
        WHERE league_name = 'WNBA'
        """
        
        overall_stats = pd.read_sql_query(stats_query, conn).iloc[0].to_dict()
        
        # Data category breakdown
        category_query = """
        SELECT 
            data_category,
            COUNT(*) as record_count,
            COUNT(DISTINCT player_name) as unique_players
        FROM unified_nba_wnba_data 
        WHERE league_name = 'WNBA'
        GROUP BY data_category
        ORDER BY record_count DESC
        """
        
        category_breakdown = pd.read_sql_query(category_query, conn)
        
        conn.close()
        
        # Calculate percentages
        total_records = overall_stats['total_records']
        valid_percentage = (overall_stats['valid_player_records'] / total_records) * 100
        team_percentage = (overall_stats['team_records'] / total_records) * 100
        extraction_percentage = (overall_stats['extraction_needed'] / total_records) * 100
        
        logger.info(f"📊 DATA QUALITY REPORT:")
        logger.info(f"   Total records: {total_records:,}")
        logger.info(f"   Valid player records: {overall_stats['valid_player_records']:,} ({valid_percentage:.1f}%)")
        logger.info(f"   Team records: {overall_stats['team_records']:,} ({team_percentage:.1f}%)")
        logger.info(f"   Needs extraction: {overall_stats['extraction_needed']:,} ({extraction_percentage:.1f}%)")
        logger.info(f"   Unique players: {overall_stats['unique_players']:,}")
        
        return {
            'overall_stats': overall_stats,
            'percentages': {
                'valid_player': valid_percentage,
                'team_records': team_percentage,
                'extraction_needed': extraction_percentage
            },
            'category_breakdown': category_breakdown.to_dict('records')
        }
    
    def run_complete_fix(self) -> Dict[str, Any]:
        """Run complete missing player data fix"""
        logger.info("🚀 RUNNING COMPLETE MISSING PLAYER DATA FIX")
        logger.info("=" * 50)
        
        # Step 1: Investigate
        investigation = self.investigate_missing_player_data()
        
        # Step 2: Fix league game finder files
        league_fix = self.fix_league_game_finder_files()
        
        # Step 3: Extract from raw data
        extraction = self.extract_player_data_from_raw()
        
        # Step 4: Create quality report
        quality_report = self.create_data_quality_report()
        
        return {
            'investigation': investigation,
            'league_fix': league_fix,
            'extraction': extraction,
            'quality_report': quality_report
        }

def main():
    """Run missing player data fix"""
    
    fixer = MissingPlayerDataFixer()
    results = fixer.run_complete_fix()
    
    investigation = results['investigation']
    
    league_fix = results['league_fix']
    
    extraction = results['extraction']
    
    quality = results['quality_report']
    stats = quality['overall_stats']
    percentages = quality['percentages']
    
    
    
    if percentages['valid_player'] >= 70:
    elif percentages['valid_player'] >= 50:
    else:
    
    
    return results

if __name__ == "__main__":
    main()
