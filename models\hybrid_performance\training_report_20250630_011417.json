{"training_timestamp": "2025-06-30T01:14:17.330292", "database_path": "medusa_master.db", "sklearn_available": true, "pandas_available": true, "training_results": {}, "data_summary": {"total_features": 10, "total_targets": 0, "feature_names": ["league_name_encoded", "season_type_encoded", "player_name_encoded", "team_abbreviation_encoded", "stat_value", "rank_position", "league_name_encoded", "season_type_encoded", "player_name_encoded", "team_abbreviation_encoded"], "target_names": []}, "models_trained": [], "models_directory": "models/hybrid_trained", "status": "failed"}