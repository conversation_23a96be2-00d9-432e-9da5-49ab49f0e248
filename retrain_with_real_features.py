#!/usr/bin/env python3
"""
Retrain Models with Real Engineered Features

This script retrains the neural models using the real engineered basketball features
instead of synthetic data, fixing the fundamental training/prediction mismatch.
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import logging
from pathlib import Path
import sys
import os
from typing import Tuple, List, Dict, Any
import json

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealBasketballDataset(Dataset):
    """Dataset using real engineered basketball features"""
    
    def __init__(self, features: np.ndarray, targets: np.ndarray):
        self.features = torch.FloatTensor(features)
        self.targets = torch.LongTensor(targets)
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.targets[idx]

class RealBasketballNet(nn.Module):
    """Neural network for real basketball data with correct input dimensions"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 64, num_classes: int = 2):
        super().__init__()
        self.input_projection = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        self.hidden_layers = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.1)
        )
        
        self.output_layer = nn.Linear(hidden_dim // 2, num_classes)
        
    def forward(self, x):
        x = self.input_projection(x)
        x = self.hidden_layers(x)
        return self.output_layer(x)

def load_and_prepare_data() -> Tuple[np.ndarray, np.ndarray, List[str], StandardScaler]:
    """Load engineered data and prepare for training"""
    logger.info("📂 Loading engineered basketball data...")
    
    data_path = Path("data/engineered_wnba_training_data.csv")
    if not data_path.exists():
        raise FileNotFoundError(f"Engineered data not found: {data_path}")
    
    df = pd.read_csv(data_path)
    logger.info(f"📊 Loaded {len(df)} engineered records")
    
    # Separate features and targets
    feature_cols = ['assists', 'defense_stats', 'field_goal_percentage', 'points', 'scoring_tier']
    target_col = 'high_scorer'  # Binary classification target
    
    # Extract features and targets
    X = df[feature_cols].fillna(0).values.astype(np.float32)
    y = df[target_col].fillna(0).values.astype(np.int64)
    
    # Scale features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    logger.info(f"✅ Prepared data: {X_scaled.shape[0]} samples, {X_scaled.shape[1]} features")
    logger.info(f"📊 Target distribution: {np.bincount(y)}")
    logger.info(f"🔧 Feature columns: {feature_cols}")
    
    return X_scaled, y, feature_cols, scaler

def train_real_model(X: np.ndarray, y: np.ndarray, feature_names: List[str], scaler: StandardScaler) -> Dict[str, Any]:
    """Train model with real engineered features"""
    logger.info("🚀 Starting training with real engineered features...")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    logger.info(f"📊 Train: {X_train.shape[0]} samples, Test: {X_test.shape[0]} samples")
    
    # Create datasets
    train_dataset = RealBasketballDataset(X_train, y_train)
    test_dataset = RealBasketballDataset(X_test, y_test)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # Initialize model
    input_dim = X.shape[1]
    model = RealBasketballNet(input_dim=input_dim)
    
    # Training setup
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
    
    # Training loop
    num_epochs = 50
    best_accuracy = 0.0
    best_model_state = None
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for features, targets in train_loader:
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += targets.size(0)
            train_correct += (predicted == targets).sum().item()
        
        # Validation phase
        model.eval()
        test_loss = 0.0
        test_correct = 0
        test_total = 0
        
        with torch.no_grad():
            for features, targets in test_loader:
                outputs = model(features)
                loss = criterion(outputs, targets)
                
                test_loss += loss.item()
                _, predicted = torch.max(outputs.data, 1)
                test_total += targets.size(0)
                test_correct += (predicted == targets).sum().item()
        
        # Calculate metrics
        train_accuracy = 100 * train_correct / train_total
        test_accuracy = 100 * test_correct / test_total
        
        # Save best model
        if test_accuracy > best_accuracy:
            best_accuracy = test_accuracy
            best_model_state = model.state_dict().copy()
        
        if epoch % 10 == 0:
            logger.info(f"Epoch {epoch:2d}: Train Acc: {train_accuracy:.1f}%, Test Acc: {test_accuracy:.1f}%")
    
    logger.info(f"✅ Training completed! Best accuracy: {best_accuracy:.1f}%")
    
    # Load best model
    model.load_state_dict(best_model_state)
    
    return {
        'model': model,
        'scaler': scaler,
        'feature_names': feature_names,
        'input_dim': input_dim,
        'best_accuracy': best_accuracy,
        'model_state_dict': best_model_state
    }

def save_real_model(model_info: Dict[str, Any]) -> None:
    """Save the trained model with real features"""
    logger.info("💾 Saving trained model with real features...")
    
    # Create models directory
    models_dir = Path("models/real_wnba_neural_models")
    models_dir.mkdir(parents=True, exist_ok=True)
    
    # Save model checkpoint
    checkpoint = {
        'model_state_dict': model_info['model_state_dict'],
        'config': {
            'input_dim': model_info['input_dim'],
            'hidden_dim': 64,
            'num_classes': 2,
            'feature_names': model_info['feature_names']
        },
        'feature_scaler_params': {
            'mean_': model_info['scaler'].mean_.tolist(),
            'scale_': model_info['scaler'].scale_.tolist(),
            'var_': model_info['scaler'].var_.tolist(),
            'n_features_in_': int(model_info['scaler'].n_features_in_),
            'n_samples_seen_': int(model_info['scaler'].n_samples_seen_)
        },
        'training_info': {
            'best_accuracy': model_info['best_accuracy'],
            'feature_engineering': 'real_basketball_stats',
            'data_source': 'engineered_wnba_training_data.csv'
        }
    }
    
    model_path = models_dir / "real_features_model.pt"
    torch.save(checkpoint, model_path)
    
    logger.info(f"✅ Model saved to {model_path}")
    logger.info(f"📊 Model expects {model_info['input_dim']} real engineered features")
    logger.info(f"🎯 Best accuracy: {model_info['best_accuracy']:.1f}%")

def main():
    """Main function to retrain with real features"""
    logger.info("🚀 Starting Real Feature Retraining Pipeline")
    
    try:
        # Load and prepare real engineered data
        X, y, feature_names, scaler = load_and_prepare_data()
        
        # Train model with real features
        model_info = train_real_model(X, y, feature_names, scaler)
        
        # Save the trained model
        save_real_model(model_info)
        
        logger.info("✅ Real feature retraining completed successfully!")
        logger.info("🔄 Next steps:")
        logger.info("1. Update unified prediction service to use real feature engineering")
        logger.info("2. Test predictions with real engineered features")
        logger.info("3. Compare performance with synthetic feature models")
        
    except Exception as e:
        logger.error(f"❌ Retraining failed: {e}")
        raise

if __name__ == "__main__":
    main()
