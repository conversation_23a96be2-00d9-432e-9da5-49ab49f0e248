# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/tfprof_log.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorboard.compat.proto import attr_value_pb2 as tensorboard_dot_compat_dot_proto_dot_attr__value__pb2
from tensorboard.compat.proto import step_stats_pb2 as tensorboard_dot_compat_dot_proto_dot_step__stats__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)tensorboard/compat/proto/tfprof_log.proto\x12\x0btensorboard\x1a)tensorboard/compat/proto/attr_value.proto\x1a)tensorboard/compat/proto/step_stats.proto\"\xd9\x01\n\x07\x43odeDef\x12*\n\x06traces\x18\x01 \x03(\x0b\x32\x1a.tensorboard.CodeDef.Trace\x1a\xa1\x01\n\x05Trace\x12\x10\n\x04\x66ile\x18\x01 \x01(\tB\x02\x18\x01\x12\x0f\n\x07\x66ile_id\x18\x06 \x01(\x03\x12\x0e\n\x06lineno\x18\x02 \x01(\x05\x12\x14\n\x08\x66unction\x18\x03 \x01(\tB\x02\x18\x01\x12\x13\n\x0b\x66unction_id\x18\x07 \x01(\x03\x12\x10\n\x04line\x18\x04 \x01(\tB\x02\x18\x01\x12\x0f\n\x07line_id\x18\x08 \x01(\x03\x12\x17\n\x0f\x66unc_start_line\x18\x05 \x01(\x05\"d\n\nOpLogEntry\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tfloat_ops\x18\x02 \x01(\x03\x12\r\n\x05types\x18\x03 \x03(\t\x12&\n\x08\x63ode_def\x18\x04 \x01(\x0b\x32\x14.tensorboard.CodeDef\"\xac\x01\n\nOpLogProto\x12,\n\x0blog_entries\x18\x01 \x03(\x0b\x32\x17.tensorboard.OpLogEntry\x12=\n\x0cid_to_string\x18\x02 \x03(\x0b\x32\'.tensorboard.OpLogProto.IdToStringEntry\x1a\x31\n\x0fIdToStringEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xc2\x02\n\x0cProfileProto\x12\x33\n\x05nodes\x18\x01 \x03(\x0b\x32$.tensorboard.ProfileProto.NodesEntry\x12\x11\n\thas_trace\x18\x02 \x01(\x08\x12\x1f\n\x17miss_accelerator_stream\x18\x05 \x01(\x08\x12\r\n\x05steps\x18\x03 \x03(\x03\x12?\n\x0cid_to_string\x18\x04 \x03(\x0b\x32).tensorboard.ProfileProto.IdToStringEntry\x1a\x46\n\nNodesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\'\n\x05value\x18\x02 \x01(\x0b\x32\x18.tensorboard.ProfileNode:\x02\x38\x01\x1a\x31\n\x0fIdToStringEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x92\x08\n\x0bProfileNode\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\n\n\x02op\x18\t \x01(\t\x12\n\n\x02id\x18\r \x01(\x03\x12\x34\n\x06inputs\x18\x02 \x03(\x0b\x32$.tensorboard.ProfileNode.InputsEntry\x12?\n\x0cinput_shapes\x18\x10 \x03(\x0b\x32).tensorboard.ProfileNode.InputShapesEntry\x12\x36\n\x07outputs\x18\x03 \x03(\x0b\x32%.tensorboard.ProfileNode.OutputsEntry\x12\x41\n\routput_shapes\x18\x0f \x03(\x0b\x32*.tensorboard.ProfileNode.OutputShapesEntry\x12\x46\n\x10src_output_index\x18\x0e \x03(\x0b\x32,.tensorboard.ProfileNode.SrcOutputIndexEntry\x12\r\n\x05shape\x18\x04 \x03(\x03\x12\x10\n\x08op_types\x18\x05 \x03(\t\x12\x18\n\x10\x63\x61nonical_device\x18\x06 \x01(\t\x12\x13\n\x0bhost_device\x18\x07 \x01(\t\x12\x11\n\tfloat_ops\x18\x08 \x01(\x03\x12#\n\x05trace\x18\n \x01(\x0b\x32\x14.tensorboard.CodeDef\x12\x32\n\x05\x61ttrs\x18\x0b \x03(\x0b\x32#.tensorboard.ProfileNode.AttrsEntry\x12\x32\n\x05\x65xecs\x18\x0c \x03(\x0b\x32#.tensorboard.ProfileNode.ExecsEntry\x1a-\n\x0bInputsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\x1a\x46\n\x10InputShapesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12!\n\x05value\x18\x02 \x01(\x0b\x32\x12.tensorboard.Tuple:\x02\x38\x01\x1a.\n\x0cOutputsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\x1aG\n\x11OutputShapesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12!\n\x05value\x18\x02 \x01(\x0b\x32\x12.tensorboard.Tuple:\x02\x38\x01\x1a\x35\n\x13SrcOutputIndexEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\r\n\x05value\x18\x02 \x01(\x05:\x02\x38\x01\x1a\x44\n\nAttrsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12%\n\x05value\x18\x02 \x01(\x0b\x32\x16.tensorboard.AttrValue:\x02\x38\x01\x1a\x46\n\nExecsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x03\x12\'\n\x05value\x18\x02 \x01(\x0b\x32\x18.tensorboard.ExecProfile:\x02\x38\x01\"\xe7\x03\n\x0b\x45xecProfile\x12\x11\n\trun_count\x18\x01 \x01(\x03\x12\x18\n\x10\x61ll_start_micros\x18\x02 \x01(\x03\x12\x19\n\x11latest_end_micros\x18\x03 \x01(\x03\x12I\n\x11\x61\x63\x63\x65lerator_execs\x18\x04 \x03(\x0b\x32..tensorboard.ExecProfile.AcceleratorExecsEntry\x12\x39\n\tcpu_execs\x18\x05 \x03(\x0b\x32&.tensorboard.ExecProfile.CpuExecsEntry\x12-\n\x0cmemory_execs\x18\x07 \x03(\x0b\x32\x17.tensorboard.ExecMemory\x12\x32\n\x0b\x61llocations\x18\x0b \x03(\x0b\x32\x1d.tensorboard.AllocationRecord\x12\x0f\n\x07\x64\x65vices\x18\x06 \x03(\t\x1aN\n\x15\x41\x63\x63\x65leratorExecsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12$\n\x05value\x18\x02 \x01(\x0b\x32\x15.tensorboard.ExecTime:\x02\x38\x01\x1a\x46\n\rCpuExecsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12$\n\x05value\x18\x02 \x01(\x0b\x32\x15.tensorboard.ExecTime:\x02\x38\x01\"-\n\x08\x45xecTime\x12!\n\x05times\x18\x01 \x03(\x0b\x32\x12.tensorboard.Tuple\"\xa8\x03\n\nExecMemory\x12\x15\n\rmemory_micros\x18\x01 \x01(\x03\x12\x17\n\x0fhost_temp_bytes\x18\x02 \x01(\x03\x12\x1d\n\x15host_persistent_bytes\x18\x03 \x01(\x03\x12\x1e\n\x16\x61\x63\x63\x65lerator_temp_bytes\x18\x04 \x01(\x03\x12$\n\x1c\x61\x63\x63\x65lerator_persistent_bytes\x18\x05 \x01(\x03\x12\x17\n\x0frequested_bytes\x18\x06 \x01(\x03\x12\x12\n\npeak_bytes\x18\x07 \x01(\x03\x12\x16\n\x0eresidual_bytes\x18\x08 \x01(\x03\x12\x14\n\x0coutput_bytes\x18\t \x01(\x03\x12\x1e\n\x16\x61llocator_bytes_in_use\x18\n \x01(\x03\x12@\n\routput_memory\x18\x0b \x03(\x0b\x32).tensorboard.ExecMemory.OutputMemoryEntry\x1aH\n\x11OutputMemoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\"\n\x05value\x18\x02 \x01(\x0b\x32\x13.tensorboard.Memory:\x02\x38\x01\"\x1d\n\x05Tuple\x12\x14\n\x0cint64_values\x18\x01 \x03(\x03\"$\n\x06Memory\x12\r\n\x05\x62ytes\x18\x01 \x01(\x03\x12\x0b\n\x03ptr\x18\x02 \x01(\x04\x42RZPgithub.com/tensorflow/tensorflow/tensorflow/go/core/profiler/protos_all_go_protob\x06proto3')



_CODEDEF = DESCRIPTOR.message_types_by_name['CodeDef']
_CODEDEF_TRACE = _CODEDEF.nested_types_by_name['Trace']
_OPLOGENTRY = DESCRIPTOR.message_types_by_name['OpLogEntry']
_OPLOGPROTO = DESCRIPTOR.message_types_by_name['OpLogProto']
_OPLOGPROTO_IDTOSTRINGENTRY = _OPLOGPROTO.nested_types_by_name['IdToStringEntry']
_PROFILEPROTO = DESCRIPTOR.message_types_by_name['ProfileProto']
_PROFILEPROTO_NODESENTRY = _PROFILEPROTO.nested_types_by_name['NodesEntry']
_PROFILEPROTO_IDTOSTRINGENTRY = _PROFILEPROTO.nested_types_by_name['IdToStringEntry']
_PROFILENODE = DESCRIPTOR.message_types_by_name['ProfileNode']
_PROFILENODE_INPUTSENTRY = _PROFILENODE.nested_types_by_name['InputsEntry']
_PROFILENODE_INPUTSHAPESENTRY = _PROFILENODE.nested_types_by_name['InputShapesEntry']
_PROFILENODE_OUTPUTSENTRY = _PROFILENODE.nested_types_by_name['OutputsEntry']
_PROFILENODE_OUTPUTSHAPESENTRY = _PROFILENODE.nested_types_by_name['OutputShapesEntry']
_PROFILENODE_SRCOUTPUTINDEXENTRY = _PROFILENODE.nested_types_by_name['SrcOutputIndexEntry']
_PROFILENODE_ATTRSENTRY = _PROFILENODE.nested_types_by_name['AttrsEntry']
_PROFILENODE_EXECSENTRY = _PROFILENODE.nested_types_by_name['ExecsEntry']
_EXECPROFILE = DESCRIPTOR.message_types_by_name['ExecProfile']
_EXECPROFILE_ACCELERATOREXECSENTRY = _EXECPROFILE.nested_types_by_name['AcceleratorExecsEntry']
_EXECPROFILE_CPUEXECSENTRY = _EXECPROFILE.nested_types_by_name['CpuExecsEntry']
_EXECTIME = DESCRIPTOR.message_types_by_name['ExecTime']
_EXECMEMORY = DESCRIPTOR.message_types_by_name['ExecMemory']
_EXECMEMORY_OUTPUTMEMORYENTRY = _EXECMEMORY.nested_types_by_name['OutputMemoryEntry']
_TUPLE = DESCRIPTOR.message_types_by_name['Tuple']
_MEMORY = DESCRIPTOR.message_types_by_name['Memory']
CodeDef = _reflection.GeneratedProtocolMessageType('CodeDef', (_message.Message,), {

  'Trace' : _reflection.GeneratedProtocolMessageType('Trace', (_message.Message,), {
    'DESCRIPTOR' : _CODEDEF_TRACE,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.CodeDef.Trace)
    })
  ,
  'DESCRIPTOR' : _CODEDEF,
  '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.CodeDef)
  })
_sym_db.RegisterMessage(CodeDef)
_sym_db.RegisterMessage(CodeDef.Trace)

OpLogEntry = _reflection.GeneratedProtocolMessageType('OpLogEntry', (_message.Message,), {
  'DESCRIPTOR' : _OPLOGENTRY,
  '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.OpLogEntry)
  })
_sym_db.RegisterMessage(OpLogEntry)

OpLogProto = _reflection.GeneratedProtocolMessageType('OpLogProto', (_message.Message,), {

  'IdToStringEntry' : _reflection.GeneratedProtocolMessageType('IdToStringEntry', (_message.Message,), {
    'DESCRIPTOR' : _OPLOGPROTO_IDTOSTRINGENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.OpLogProto.IdToStringEntry)
    })
  ,
  'DESCRIPTOR' : _OPLOGPROTO,
  '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.OpLogProto)
  })
_sym_db.RegisterMessage(OpLogProto)
_sym_db.RegisterMessage(OpLogProto.IdToStringEntry)

ProfileProto = _reflection.GeneratedProtocolMessageType('ProfileProto', (_message.Message,), {

  'NodesEntry' : _reflection.GeneratedProtocolMessageType('NodesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILEPROTO_NODESENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ProfileProto.NodesEntry)
    })
  ,

  'IdToStringEntry' : _reflection.GeneratedProtocolMessageType('IdToStringEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILEPROTO_IDTOSTRINGENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ProfileProto.IdToStringEntry)
    })
  ,
  'DESCRIPTOR' : _PROFILEPROTO,
  '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.ProfileProto)
  })
_sym_db.RegisterMessage(ProfileProto)
_sym_db.RegisterMessage(ProfileProto.NodesEntry)
_sym_db.RegisterMessage(ProfileProto.IdToStringEntry)

ProfileNode = _reflection.GeneratedProtocolMessageType('ProfileNode', (_message.Message,), {

  'InputsEntry' : _reflection.GeneratedProtocolMessageType('InputsEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_INPUTSENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ProfileNode.InputsEntry)
    })
  ,

  'InputShapesEntry' : _reflection.GeneratedProtocolMessageType('InputShapesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_INPUTSHAPESENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ProfileNode.InputShapesEntry)
    })
  ,

  'OutputsEntry' : _reflection.GeneratedProtocolMessageType('OutputsEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_OUTPUTSENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ProfileNode.OutputsEntry)
    })
  ,

  'OutputShapesEntry' : _reflection.GeneratedProtocolMessageType('OutputShapesEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_OUTPUTSHAPESENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ProfileNode.OutputShapesEntry)
    })
  ,

  'SrcOutputIndexEntry' : _reflection.GeneratedProtocolMessageType('SrcOutputIndexEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_SRCOUTPUTINDEXENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ProfileNode.SrcOutputIndexEntry)
    })
  ,

  'AttrsEntry' : _reflection.GeneratedProtocolMessageType('AttrsEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_ATTRSENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ProfileNode.AttrsEntry)
    })
  ,

  'ExecsEntry' : _reflection.GeneratedProtocolMessageType('ExecsEntry', (_message.Message,), {
    'DESCRIPTOR' : _PROFILENODE_EXECSENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ProfileNode.ExecsEntry)
    })
  ,
  'DESCRIPTOR' : _PROFILENODE,
  '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.ProfileNode)
  })
_sym_db.RegisterMessage(ProfileNode)
_sym_db.RegisterMessage(ProfileNode.InputsEntry)
_sym_db.RegisterMessage(ProfileNode.InputShapesEntry)
_sym_db.RegisterMessage(ProfileNode.OutputsEntry)
_sym_db.RegisterMessage(ProfileNode.OutputShapesEntry)
_sym_db.RegisterMessage(ProfileNode.SrcOutputIndexEntry)
_sym_db.RegisterMessage(ProfileNode.AttrsEntry)
_sym_db.RegisterMessage(ProfileNode.ExecsEntry)

ExecProfile = _reflection.GeneratedProtocolMessageType('ExecProfile', (_message.Message,), {

  'AcceleratorExecsEntry' : _reflection.GeneratedProtocolMessageType('AcceleratorExecsEntry', (_message.Message,), {
    'DESCRIPTOR' : _EXECPROFILE_ACCELERATOREXECSENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ExecProfile.AcceleratorExecsEntry)
    })
  ,

  'CpuExecsEntry' : _reflection.GeneratedProtocolMessageType('CpuExecsEntry', (_message.Message,), {
    'DESCRIPTOR' : _EXECPROFILE_CPUEXECSENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ExecProfile.CpuExecsEntry)
    })
  ,
  'DESCRIPTOR' : _EXECPROFILE,
  '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.ExecProfile)
  })
_sym_db.RegisterMessage(ExecProfile)
_sym_db.RegisterMessage(ExecProfile.AcceleratorExecsEntry)
_sym_db.RegisterMessage(ExecProfile.CpuExecsEntry)

ExecTime = _reflection.GeneratedProtocolMessageType('ExecTime', (_message.Message,), {
  'DESCRIPTOR' : _EXECTIME,
  '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.ExecTime)
  })
_sym_db.RegisterMessage(ExecTime)

ExecMemory = _reflection.GeneratedProtocolMessageType('ExecMemory', (_message.Message,), {

  'OutputMemoryEntry' : _reflection.GeneratedProtocolMessageType('OutputMemoryEntry', (_message.Message,), {
    'DESCRIPTOR' : _EXECMEMORY_OUTPUTMEMORYENTRY,
    '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.ExecMemory.OutputMemoryEntry)
    })
  ,
  'DESCRIPTOR' : _EXECMEMORY,
  '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.ExecMemory)
  })
_sym_db.RegisterMessage(ExecMemory)
_sym_db.RegisterMessage(ExecMemory.OutputMemoryEntry)

Tuple = _reflection.GeneratedProtocolMessageType('Tuple', (_message.Message,), {
  'DESCRIPTOR' : _TUPLE,
  '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.Tuple)
  })
_sym_db.RegisterMessage(Tuple)

Memory = _reflection.GeneratedProtocolMessageType('Memory', (_message.Message,), {
  'DESCRIPTOR' : _MEMORY,
  '__module__' : 'tensorboard.compat.proto.tfprof_log_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.Memory)
  })
_sym_db.RegisterMessage(Memory)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'ZPgithub.com/tensorflow/tensorflow/tensorflow/go/core/profiler/protos_all_go_proto'
  _CODEDEF_TRACE.fields_by_name['file']._options = None
  _CODEDEF_TRACE.fields_by_name['file']._serialized_options = b'\030\001'
  _CODEDEF_TRACE.fields_by_name['function']._options = None
  _CODEDEF_TRACE.fields_by_name['function']._serialized_options = b'\030\001'
  _CODEDEF_TRACE.fields_by_name['line']._options = None
  _CODEDEF_TRACE.fields_by_name['line']._serialized_options = b'\030\001'
  _OPLOGPROTO_IDTOSTRINGENTRY._options = None
  _OPLOGPROTO_IDTOSTRINGENTRY._serialized_options = b'8\001'
  _PROFILEPROTO_NODESENTRY._options = None
  _PROFILEPROTO_NODESENTRY._serialized_options = b'8\001'
  _PROFILEPROTO_IDTOSTRINGENTRY._options = None
  _PROFILEPROTO_IDTOSTRINGENTRY._serialized_options = b'8\001'
  _PROFILENODE_INPUTSENTRY._options = None
  _PROFILENODE_INPUTSENTRY._serialized_options = b'8\001'
  _PROFILENODE_INPUTSHAPESENTRY._options = None
  _PROFILENODE_INPUTSHAPESENTRY._serialized_options = b'8\001'
  _PROFILENODE_OUTPUTSENTRY._options = None
  _PROFILENODE_OUTPUTSENTRY._serialized_options = b'8\001'
  _PROFILENODE_OUTPUTSHAPESENTRY._options = None
  _PROFILENODE_OUTPUTSHAPESENTRY._serialized_options = b'8\001'
  _PROFILENODE_SRCOUTPUTINDEXENTRY._options = None
  _PROFILENODE_SRCOUTPUTINDEXENTRY._serialized_options = b'8\001'
  _PROFILENODE_ATTRSENTRY._options = None
  _PROFILENODE_ATTRSENTRY._serialized_options = b'8\001'
  _PROFILENODE_EXECSENTRY._options = None
  _PROFILENODE_EXECSENTRY._serialized_options = b'8\001'
  _EXECPROFILE_ACCELERATOREXECSENTRY._options = None
  _EXECPROFILE_ACCELERATOREXECSENTRY._serialized_options = b'8\001'
  _EXECPROFILE_CPUEXECSENTRY._options = None
  _EXECPROFILE_CPUEXECSENTRY._serialized_options = b'8\001'
  _EXECMEMORY_OUTPUTMEMORYENTRY._options = None
  _EXECMEMORY_OUTPUTMEMORYENTRY._serialized_options = b'8\001'
  _CODEDEF._serialized_start=145
  _CODEDEF._serialized_end=362
  _CODEDEF_TRACE._serialized_start=201
  _CODEDEF_TRACE._serialized_end=362
  _OPLOGENTRY._serialized_start=364
  _OPLOGENTRY._serialized_end=464
  _OPLOGPROTO._serialized_start=467
  _OPLOGPROTO._serialized_end=639
  _OPLOGPROTO_IDTOSTRINGENTRY._serialized_start=590
  _OPLOGPROTO_IDTOSTRINGENTRY._serialized_end=639
  _PROFILEPROTO._serialized_start=642
  _PROFILEPROTO._serialized_end=964
  _PROFILEPROTO_NODESENTRY._serialized_start=843
  _PROFILEPROTO_NODESENTRY._serialized_end=913
  _PROFILEPROTO_IDTOSTRINGENTRY._serialized_start=590
  _PROFILEPROTO_IDTOSTRINGENTRY._serialized_end=639
  _PROFILENODE._serialized_start=967
  _PROFILENODE._serialized_end=2009
  _PROFILENODE_INPUTSENTRY._serialized_start=1574
  _PROFILENODE_INPUTSENTRY._serialized_end=1619
  _PROFILENODE_INPUTSHAPESENTRY._serialized_start=1621
  _PROFILENODE_INPUTSHAPESENTRY._serialized_end=1691
  _PROFILENODE_OUTPUTSENTRY._serialized_start=1693
  _PROFILENODE_OUTPUTSENTRY._serialized_end=1739
  _PROFILENODE_OUTPUTSHAPESENTRY._serialized_start=1741
  _PROFILENODE_OUTPUTSHAPESENTRY._serialized_end=1812
  _PROFILENODE_SRCOUTPUTINDEXENTRY._serialized_start=1814
  _PROFILENODE_SRCOUTPUTINDEXENTRY._serialized_end=1867
  _PROFILENODE_ATTRSENTRY._serialized_start=1869
  _PROFILENODE_ATTRSENTRY._serialized_end=1937
  _PROFILENODE_EXECSENTRY._serialized_start=1939
  _PROFILENODE_EXECSENTRY._serialized_end=2009
  _EXECPROFILE._serialized_start=2012
  _EXECPROFILE._serialized_end=2499
  _EXECPROFILE_ACCELERATOREXECSENTRY._serialized_start=2349
  _EXECPROFILE_ACCELERATOREXECSENTRY._serialized_end=2427
  _EXECPROFILE_CPUEXECSENTRY._serialized_start=2429
  _EXECPROFILE_CPUEXECSENTRY._serialized_end=2499
  _EXECTIME._serialized_start=2501
  _EXECTIME._serialized_end=2546
  _EXECMEMORY._serialized_start=2549
  _EXECMEMORY._serialized_end=2973
  _EXECMEMORY_OUTPUTMEMORYENTRY._serialized_start=2901
  _EXECMEMORY_OUTPUTMEMORYENTRY._serialized_end=2973
  _TUPLE._serialized_start=2975
  _TUPLE._serialized_end=3004
  _MEMORY._serialized_start=3006
  _MEMORY._serialized_end=3042
# @@protoc_insertion_point(module_scope)
