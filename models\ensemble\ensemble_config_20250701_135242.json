{"config": {"base_models": ["random_forest", "gradient_boosting"], "meta_model": "logistic_regression", "ensemble_strategy": "stacking", "cv_folds": 3, "test_size": 0.2, "validation_size": 0.15, "random_state": 42, "target_accuracy": 0.75, "target_auc": 0.8, "target_f1": 0.75, "basketball_features": true, "temporal_modeling": true, "quantum_enhancement": true, "use_gpu": true, "parallel_training": true, "model_versioning": true, "performance_monitoring": true, "auto_hyperparameter_tuning": true, "feature_selection": true, "model_interpretability": true, "real_time_adaptation": true}, "feature_names": ["feature_0", "feature_1", "feature_2", "feature_3", "feature_4", "feature_5", "feature_6", "feature_7", "feature_8", "feature_9"], "model_paths": {"random_forest": "models\\ensemble\\random_forest_20250701_135242.pkl", "gradient_boosting": "models\\ensemble\\gradient_boosting_20250701_135242.pkl", "meta_model": "models\\ensemble\\meta_model_20250701_135242.pkl"}, "training_timestamp": "20250701_135242"}