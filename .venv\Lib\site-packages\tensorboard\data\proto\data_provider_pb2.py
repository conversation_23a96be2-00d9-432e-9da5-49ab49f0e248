# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/data/proto/data_provider.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from tensorboard.compat.proto import summary_pb2 as tensorboard_dot_compat_dot_proto_dot_summary__pb2
try:
  tensorboard_dot_compat_dot_proto_dot_histogram__pb2 = tensorboard_dot_compat_dot_proto_dot_summary__pb2.tensorboard_dot_compat_dot_proto_dot_histogram__pb2
except AttributeError:
  tensorboard_dot_compat_dot_proto_dot_histogram__pb2 = tensorboard_dot_compat_dot_proto_dot_summary__pb2.tensorboard.compat.proto.histogram_pb2
from tensorboard.compat.proto import tensor_pb2 as tensorboard_dot_compat_dot_proto_dot_tensor__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*tensorboard/data/proto/data_provider.proto\x12\x10tensorboard.data\x1a\x1fgoogle/protobuf/timestamp.proto\x1a&tensorboard/compat/proto/summary.proto\x1a%tensorboard/compat/proto/tensor.proto\"-\n\x14GetExperimentRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\"\x84\x01\n\x15GetExperimentResponse\x12\x15\n\rdata_location\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x03 \x01(\t\x12\x31\n\rcreation_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"#\n\x0cPluginFilter\x12\x13\n\x0bplugin_name\x18\x01 \x01(\t\"d\n\x0cRunTagFilter\x12)\n\x04runs\x18\x01 \x01(\x0b\x32\x1b.tensorboard.data.RunFilter\x12)\n\x04tags\x18\x02 \x01(\x0b\x32\x1b.tensorboard.data.TagFilter\"\x1a\n\tRunFilter\x12\r\n\x05names\x18\x01 \x03(\t\"\x1a\n\tTagFilter\x12\r\n\x05names\x18\x01 \x03(\t\" \n\nDownsample\x12\x12\n\nnum_points\x18\x01 \x01(\x03\"+\n\x12ListPluginsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\"@\n\x13ListPluginsResponse\x12)\n\x07plugins\x18\x01 \x03(\x0b\x32\x18.tensorboard.data.Plugin\"\x16\n\x06Plugin\x12\x0c\n\x04name\x18\x01 \x01(\t\"(\n\x0fListRunsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\"7\n\x10ListRunsResponse\x12#\n\x04runs\x18\x01 \x03(\x0b\x32\x15.tensorboard.data.Run\"1\n\x03Run\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x12\n\nstart_time\x18\x03 \x01(\x01J\x04\x08\x01\x10\x02R\x02id\"\x9a\x01\n\x12ListScalarsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\"\x81\x02\n\x13ListScalarsResponse\x12<\n\x04runs\x18\x01 \x03(\x0b\x32..tensorboard.data.ListScalarsResponse.RunEntry\x1aZ\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12<\n\x04tags\x18\x02 \x03(\x0b\x32..tensorboard.data.ListScalarsResponse.TagEntry\x1aP\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x32\n\x08metadata\x18\x02 \x01(\x0b\x32 .tensorboard.data.ScalarMetadata\"q\n\x0eScalarMetadata\x12\x10\n\x08max_step\x18\x01 \x01(\x03\x12\x15\n\rmax_wall_time\x18\x02 \x01(\x01\x12\x36\n\x10summary_metadata\x18\x03 \x01(\x0b\x32\x1c.tensorboard.SummaryMetadata\"\xcc\x01\n\x12ReadScalarsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\x12\x30\n\ndownsample\x18\x04 \x01(\x0b\x32\x1c.tensorboard.data.Downsample\"\xf9\x01\n\x13ReadScalarsResponse\x12<\n\x04runs\x18\x01 \x03(\x0b\x32..tensorboard.data.ReadScalarsResponse.RunEntry\x1aZ\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12<\n\x04tags\x18\x02 \x03(\x0b\x32..tensorboard.data.ReadScalarsResponse.TagEntry\x1aH\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12*\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1c.tensorboard.data.ScalarData\"<\n\nScalarData\x12\x0c\n\x04step\x18\x01 \x03(\x03\x12\x11\n\twall_time\x18\x02 \x03(\x01\x12\r\n\x05value\x18\x03 \x03(\x02\"\x9a\x01\n\x12ListTensorsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\"\x81\x02\n\x13ListTensorsResponse\x12<\n\x04runs\x18\x01 \x03(\x0b\x32..tensorboard.data.ListTensorsResponse.RunEntry\x1aZ\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12<\n\x04tags\x18\x02 \x03(\x0b\x32..tensorboard.data.ListTensorsResponse.TagEntry\x1aP\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x32\n\x08metadata\x18\x02 \x01(\x0b\x32 .tensorboard.data.TensorMetadata\"q\n\x0eTensorMetadata\x12\x10\n\x08max_step\x18\x01 \x01(\x03\x12\x15\n\rmax_wall_time\x18\x02 \x01(\x01\x12\x36\n\x10summary_metadata\x18\x03 \x01(\x0b\x32\x1c.tensorboard.SummaryMetadata\"\xcc\x01\n\x12ReadTensorsRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\x12\x30\n\ndownsample\x18\x04 \x01(\x0b\x32\x1c.tensorboard.data.Downsample\"\xf9\x01\n\x13ReadTensorsResponse\x12<\n\x04runs\x18\x01 \x03(\x0b\x32..tensorboard.data.ReadTensorsResponse.RunEntry\x1aZ\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12<\n\x04tags\x18\x02 \x03(\x0b\x32..tensorboard.data.ReadTensorsResponse.TagEntry\x1aH\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12*\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\x1c.tensorboard.data.TensorData\"V\n\nTensorData\x12\x0c\n\x04step\x18\x01 \x03(\x03\x12\x11\n\twall_time\x18\x02 \x03(\x01\x12\'\n\x05value\x18\x03 \x03(\x0b\x32\x18.tensorboard.TensorProto\"\xa0\x01\n\x18ListBlobSequencesRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\"\x99\x02\n\x19ListBlobSequencesResponse\x12\x42\n\x04runs\x18\x01 \x03(\x0b\x32\x34.tensorboard.data.ListBlobSequencesResponse.RunEntry\x1a`\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12\x42\n\x04tags\x18\x02 \x03(\x0b\x32\x34.tensorboard.data.ListBlobSequencesResponse.TagEntry\x1aV\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x38\n\x08metadata\x18\x02 \x01(\x0b\x32&.tensorboard.data.BlobSequenceMetadata\"\x8b\x01\n\x14\x42lobSequenceMetadata\x12\x10\n\x08max_step\x18\x01 \x01(\x03\x12\x15\n\rmax_wall_time\x18\x02 \x01(\x01\x12\x12\n\nmax_length\x18\x03 \x01(\x03\x12\x36\n\x10summary_metadata\x18\x04 \x01(\x0b\x32\x1c.tensorboard.SummaryMetadata\"\xd2\x01\n\x18ReadBlobSequencesRequest\x12\x15\n\rexperiment_id\x18\x01 \x01(\t\x12\x35\n\rplugin_filter\x18\x02 \x01(\x0b\x32\x1e.tensorboard.data.PluginFilter\x12\x36\n\x0erun_tag_filter\x18\x03 \x01(\x0b\x32\x1e.tensorboard.data.RunTagFilter\x12\x30\n\ndownsample\x18\x04 \x01(\x0b\x32\x1c.tensorboard.data.Downsample\"\x91\x02\n\x19ReadBlobSequencesResponse\x12\x42\n\x04runs\x18\x01 \x03(\x0b\x32\x34.tensorboard.data.ReadBlobSequencesResponse.RunEntry\x1a`\n\x08RunEntry\x12\x10\n\x08run_name\x18\x01 \x01(\t\x12\x42\n\x04tags\x18\x02 \x03(\x0b\x32\x34.tensorboard.data.ReadBlobSequencesResponse.TagEntry\x1aN\n\x08TagEntry\x12\x10\n\x08tag_name\x18\x01 \x01(\t\x12\x30\n\x04\x64\x61ta\x18\x02 \x01(\x0b\x32\".tensorboard.data.BlobSequenceData\"l\n\x10\x42lobSequenceData\x12\x0c\n\x04step\x18\x01 \x03(\x03\x12\x11\n\twall_time\x18\x02 \x03(\x01\x12\x37\n\x06values\x18\x03 \x03(\x0b\x32\'.tensorboard.data.BlobReferenceSequence\"K\n\x15\x42lobReferenceSequence\x12\x32\n\tblob_refs\x18\x01 \x03(\x0b\x32\x1f.tensorboard.data.BlobReference\".\n\rBlobReference\x12\x10\n\x08\x62lob_key\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\"#\n\x0fReadBlobRequest\x12\x10\n\x08\x62lob_key\x18\x01 \x01(\t\" \n\x10ReadBlobResponse\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\x32\xdd\x07\n\x17TensorBoardDataProvider\x12`\n\rGetExperiment\x12&.tensorboard.data.GetExperimentRequest\x1a\'.tensorboard.data.GetExperimentResponse\x12\\\n\x0bListPlugins\x12$.tensorboard.data.ListPluginsRequest\x1a%.tensorboard.data.ListPluginsResponse\"\x00\x12S\n\x08ListRuns\x12!.tensorboard.data.ListRunsRequest\x1a\".tensorboard.data.ListRunsResponse\"\x00\x12\\\n\x0bListScalars\x12$.tensorboard.data.ListScalarsRequest\x1a%.tensorboard.data.ListScalarsResponse\"\x00\x12\\\n\x0bReadScalars\x12$.tensorboard.data.ReadScalarsRequest\x1a%.tensorboard.data.ReadScalarsResponse\"\x00\x12\\\n\x0bListTensors\x12$.tensorboard.data.ListTensorsRequest\x1a%.tensorboard.data.ListTensorsResponse\"\x00\x12\\\n\x0bReadTensors\x12$.tensorboard.data.ReadTensorsRequest\x1a%.tensorboard.data.ReadTensorsResponse\"\x00\x12n\n\x11ListBlobSequences\x12*.tensorboard.data.ListBlobSequencesRequest\x1a+.tensorboard.data.ListBlobSequencesResponse\"\x00\x12n\n\x11ReadBlobSequences\x12*.tensorboard.data.ReadBlobSequencesRequest\x1a+.tensorboard.data.ReadBlobSequencesResponse\"\x00\x12U\n\x08ReadBlob\x12!.tensorboard.data.ReadBlobRequest\x1a\".tensorboard.data.ReadBlobResponse\"\x00\x30\x01\x42NZLgithub.com/tensorflow/tensorboard/tensorboard/data/proto/data_provider_protob\x06proto3')



_GETEXPERIMENTREQUEST = DESCRIPTOR.message_types_by_name['GetExperimentRequest']
_GETEXPERIMENTRESPONSE = DESCRIPTOR.message_types_by_name['GetExperimentResponse']
_PLUGINFILTER = DESCRIPTOR.message_types_by_name['PluginFilter']
_RUNTAGFILTER = DESCRIPTOR.message_types_by_name['RunTagFilter']
_RUNFILTER = DESCRIPTOR.message_types_by_name['RunFilter']
_TAGFILTER = DESCRIPTOR.message_types_by_name['TagFilter']
_DOWNSAMPLE = DESCRIPTOR.message_types_by_name['Downsample']
_LISTPLUGINSREQUEST = DESCRIPTOR.message_types_by_name['ListPluginsRequest']
_LISTPLUGINSRESPONSE = DESCRIPTOR.message_types_by_name['ListPluginsResponse']
_PLUGIN = DESCRIPTOR.message_types_by_name['Plugin']
_LISTRUNSREQUEST = DESCRIPTOR.message_types_by_name['ListRunsRequest']
_LISTRUNSRESPONSE = DESCRIPTOR.message_types_by_name['ListRunsResponse']
_RUN = DESCRIPTOR.message_types_by_name['Run']
_LISTSCALARSREQUEST = DESCRIPTOR.message_types_by_name['ListScalarsRequest']
_LISTSCALARSRESPONSE = DESCRIPTOR.message_types_by_name['ListScalarsResponse']
_LISTSCALARSRESPONSE_RUNENTRY = _LISTSCALARSRESPONSE.nested_types_by_name['RunEntry']
_LISTSCALARSRESPONSE_TAGENTRY = _LISTSCALARSRESPONSE.nested_types_by_name['TagEntry']
_SCALARMETADATA = DESCRIPTOR.message_types_by_name['ScalarMetadata']
_READSCALARSREQUEST = DESCRIPTOR.message_types_by_name['ReadScalarsRequest']
_READSCALARSRESPONSE = DESCRIPTOR.message_types_by_name['ReadScalarsResponse']
_READSCALARSRESPONSE_RUNENTRY = _READSCALARSRESPONSE.nested_types_by_name['RunEntry']
_READSCALARSRESPONSE_TAGENTRY = _READSCALARSRESPONSE.nested_types_by_name['TagEntry']
_SCALARDATA = DESCRIPTOR.message_types_by_name['ScalarData']
_LISTTENSORSREQUEST = DESCRIPTOR.message_types_by_name['ListTensorsRequest']
_LISTTENSORSRESPONSE = DESCRIPTOR.message_types_by_name['ListTensorsResponse']
_LISTTENSORSRESPONSE_RUNENTRY = _LISTTENSORSRESPONSE.nested_types_by_name['RunEntry']
_LISTTENSORSRESPONSE_TAGENTRY = _LISTTENSORSRESPONSE.nested_types_by_name['TagEntry']
_TENSORMETADATA = DESCRIPTOR.message_types_by_name['TensorMetadata']
_READTENSORSREQUEST = DESCRIPTOR.message_types_by_name['ReadTensorsRequest']
_READTENSORSRESPONSE = DESCRIPTOR.message_types_by_name['ReadTensorsResponse']
_READTENSORSRESPONSE_RUNENTRY = _READTENSORSRESPONSE.nested_types_by_name['RunEntry']
_READTENSORSRESPONSE_TAGENTRY = _READTENSORSRESPONSE.nested_types_by_name['TagEntry']
_TENSORDATA = DESCRIPTOR.message_types_by_name['TensorData']
_LISTBLOBSEQUENCESREQUEST = DESCRIPTOR.message_types_by_name['ListBlobSequencesRequest']
_LISTBLOBSEQUENCESRESPONSE = DESCRIPTOR.message_types_by_name['ListBlobSequencesResponse']
_LISTBLOBSEQUENCESRESPONSE_RUNENTRY = _LISTBLOBSEQUENCESRESPONSE.nested_types_by_name['RunEntry']
_LISTBLOBSEQUENCESRESPONSE_TAGENTRY = _LISTBLOBSEQUENCESRESPONSE.nested_types_by_name['TagEntry']
_BLOBSEQUENCEMETADATA = DESCRIPTOR.message_types_by_name['BlobSequenceMetadata']
_READBLOBSEQUENCESREQUEST = DESCRIPTOR.message_types_by_name['ReadBlobSequencesRequest']
_READBLOBSEQUENCESRESPONSE = DESCRIPTOR.message_types_by_name['ReadBlobSequencesResponse']
_READBLOBSEQUENCESRESPONSE_RUNENTRY = _READBLOBSEQUENCESRESPONSE.nested_types_by_name['RunEntry']
_READBLOBSEQUENCESRESPONSE_TAGENTRY = _READBLOBSEQUENCESRESPONSE.nested_types_by_name['TagEntry']
_BLOBSEQUENCEDATA = DESCRIPTOR.message_types_by_name['BlobSequenceData']
_BLOBREFERENCESEQUENCE = DESCRIPTOR.message_types_by_name['BlobReferenceSequence']
_BLOBREFERENCE = DESCRIPTOR.message_types_by_name['BlobReference']
_READBLOBREQUEST = DESCRIPTOR.message_types_by_name['ReadBlobRequest']
_READBLOBRESPONSE = DESCRIPTOR.message_types_by_name['ReadBlobResponse']
GetExperimentRequest = _reflection.GeneratedProtocolMessageType('GetExperimentRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETEXPERIMENTREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.GetExperimentRequest)
  })
_sym_db.RegisterMessage(GetExperimentRequest)

GetExperimentResponse = _reflection.GeneratedProtocolMessageType('GetExperimentResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETEXPERIMENTRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.GetExperimentResponse)
  })
_sym_db.RegisterMessage(GetExperimentResponse)

PluginFilter = _reflection.GeneratedProtocolMessageType('PluginFilter', (_message.Message,), {
  'DESCRIPTOR' : _PLUGINFILTER,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.PluginFilter)
  })
_sym_db.RegisterMessage(PluginFilter)

RunTagFilter = _reflection.GeneratedProtocolMessageType('RunTagFilter', (_message.Message,), {
  'DESCRIPTOR' : _RUNTAGFILTER,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.RunTagFilter)
  })
_sym_db.RegisterMessage(RunTagFilter)

RunFilter = _reflection.GeneratedProtocolMessageType('RunFilter', (_message.Message,), {
  'DESCRIPTOR' : _RUNFILTER,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.RunFilter)
  })
_sym_db.RegisterMessage(RunFilter)

TagFilter = _reflection.GeneratedProtocolMessageType('TagFilter', (_message.Message,), {
  'DESCRIPTOR' : _TAGFILTER,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.TagFilter)
  })
_sym_db.RegisterMessage(TagFilter)

Downsample = _reflection.GeneratedProtocolMessageType('Downsample', (_message.Message,), {
  'DESCRIPTOR' : _DOWNSAMPLE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.Downsample)
  })
_sym_db.RegisterMessage(Downsample)

ListPluginsRequest = _reflection.GeneratedProtocolMessageType('ListPluginsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTPLUGINSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListPluginsRequest)
  })
_sym_db.RegisterMessage(ListPluginsRequest)

ListPluginsResponse = _reflection.GeneratedProtocolMessageType('ListPluginsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTPLUGINSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListPluginsResponse)
  })
_sym_db.RegisterMessage(ListPluginsResponse)

Plugin = _reflection.GeneratedProtocolMessageType('Plugin', (_message.Message,), {
  'DESCRIPTOR' : _PLUGIN,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.Plugin)
  })
_sym_db.RegisterMessage(Plugin)

ListRunsRequest = _reflection.GeneratedProtocolMessageType('ListRunsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTRUNSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListRunsRequest)
  })
_sym_db.RegisterMessage(ListRunsRequest)

ListRunsResponse = _reflection.GeneratedProtocolMessageType('ListRunsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTRUNSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListRunsResponse)
  })
_sym_db.RegisterMessage(ListRunsResponse)

Run = _reflection.GeneratedProtocolMessageType('Run', (_message.Message,), {
  'DESCRIPTOR' : _RUN,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.Run)
  })
_sym_db.RegisterMessage(Run)

ListScalarsRequest = _reflection.GeneratedProtocolMessageType('ListScalarsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTSCALARSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListScalarsRequest)
  })
_sym_db.RegisterMessage(ListScalarsRequest)

ListScalarsResponse = _reflection.GeneratedProtocolMessageType('ListScalarsResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTSCALARSRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListScalarsResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTSCALARSRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListScalarsResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _LISTSCALARSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListScalarsResponse)
  })
_sym_db.RegisterMessage(ListScalarsResponse)
_sym_db.RegisterMessage(ListScalarsResponse.RunEntry)
_sym_db.RegisterMessage(ListScalarsResponse.TagEntry)

ScalarMetadata = _reflection.GeneratedProtocolMessageType('ScalarMetadata', (_message.Message,), {
  'DESCRIPTOR' : _SCALARMETADATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ScalarMetadata)
  })
_sym_db.RegisterMessage(ScalarMetadata)

ReadScalarsRequest = _reflection.GeneratedProtocolMessageType('ReadScalarsRequest', (_message.Message,), {
  'DESCRIPTOR' : _READSCALARSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadScalarsRequest)
  })
_sym_db.RegisterMessage(ReadScalarsRequest)

ReadScalarsResponse = _reflection.GeneratedProtocolMessageType('ReadScalarsResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _READSCALARSRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadScalarsResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _READSCALARSRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadScalarsResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _READSCALARSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadScalarsResponse)
  })
_sym_db.RegisterMessage(ReadScalarsResponse)
_sym_db.RegisterMessage(ReadScalarsResponse.RunEntry)
_sym_db.RegisterMessage(ReadScalarsResponse.TagEntry)

ScalarData = _reflection.GeneratedProtocolMessageType('ScalarData', (_message.Message,), {
  'DESCRIPTOR' : _SCALARDATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ScalarData)
  })
_sym_db.RegisterMessage(ScalarData)

ListTensorsRequest = _reflection.GeneratedProtocolMessageType('ListTensorsRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTTENSORSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListTensorsRequest)
  })
_sym_db.RegisterMessage(ListTensorsRequest)

ListTensorsResponse = _reflection.GeneratedProtocolMessageType('ListTensorsResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTTENSORSRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListTensorsResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTTENSORSRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListTensorsResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _LISTTENSORSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListTensorsResponse)
  })
_sym_db.RegisterMessage(ListTensorsResponse)
_sym_db.RegisterMessage(ListTensorsResponse.RunEntry)
_sym_db.RegisterMessage(ListTensorsResponse.TagEntry)

TensorMetadata = _reflection.GeneratedProtocolMessageType('TensorMetadata', (_message.Message,), {
  'DESCRIPTOR' : _TENSORMETADATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.TensorMetadata)
  })
_sym_db.RegisterMessage(TensorMetadata)

ReadTensorsRequest = _reflection.GeneratedProtocolMessageType('ReadTensorsRequest', (_message.Message,), {
  'DESCRIPTOR' : _READTENSORSREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadTensorsRequest)
  })
_sym_db.RegisterMessage(ReadTensorsRequest)

ReadTensorsResponse = _reflection.GeneratedProtocolMessageType('ReadTensorsResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _READTENSORSRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadTensorsResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _READTENSORSRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadTensorsResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _READTENSORSRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadTensorsResponse)
  })
_sym_db.RegisterMessage(ReadTensorsResponse)
_sym_db.RegisterMessage(ReadTensorsResponse.RunEntry)
_sym_db.RegisterMessage(ReadTensorsResponse.TagEntry)

TensorData = _reflection.GeneratedProtocolMessageType('TensorData', (_message.Message,), {
  'DESCRIPTOR' : _TENSORDATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.TensorData)
  })
_sym_db.RegisterMessage(TensorData)

ListBlobSequencesRequest = _reflection.GeneratedProtocolMessageType('ListBlobSequencesRequest', (_message.Message,), {
  'DESCRIPTOR' : _LISTBLOBSEQUENCESREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListBlobSequencesRequest)
  })
_sym_db.RegisterMessage(ListBlobSequencesRequest)

ListBlobSequencesResponse = _reflection.GeneratedProtocolMessageType('ListBlobSequencesResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTBLOBSEQUENCESRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListBlobSequencesResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _LISTBLOBSEQUENCESRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ListBlobSequencesResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _LISTBLOBSEQUENCESRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ListBlobSequencesResponse)
  })
_sym_db.RegisterMessage(ListBlobSequencesResponse)
_sym_db.RegisterMessage(ListBlobSequencesResponse.RunEntry)
_sym_db.RegisterMessage(ListBlobSequencesResponse.TagEntry)

BlobSequenceMetadata = _reflection.GeneratedProtocolMessageType('BlobSequenceMetadata', (_message.Message,), {
  'DESCRIPTOR' : _BLOBSEQUENCEMETADATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.BlobSequenceMetadata)
  })
_sym_db.RegisterMessage(BlobSequenceMetadata)

ReadBlobSequencesRequest = _reflection.GeneratedProtocolMessageType('ReadBlobSequencesRequest', (_message.Message,), {
  'DESCRIPTOR' : _READBLOBSEQUENCESREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobSequencesRequest)
  })
_sym_db.RegisterMessage(ReadBlobSequencesRequest)

ReadBlobSequencesResponse = _reflection.GeneratedProtocolMessageType('ReadBlobSequencesResponse', (_message.Message,), {

  'RunEntry' : _reflection.GeneratedProtocolMessageType('RunEntry', (_message.Message,), {
    'DESCRIPTOR' : _READBLOBSEQUENCESRESPONSE_RUNENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobSequencesResponse.RunEntry)
    })
  ,

  'TagEntry' : _reflection.GeneratedProtocolMessageType('TagEntry', (_message.Message,), {
    'DESCRIPTOR' : _READBLOBSEQUENCESRESPONSE_TAGENTRY,
    '__module__' : 'tensorboard.data.proto.data_provider_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobSequencesResponse.TagEntry)
    })
  ,
  'DESCRIPTOR' : _READBLOBSEQUENCESRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobSequencesResponse)
  })
_sym_db.RegisterMessage(ReadBlobSequencesResponse)
_sym_db.RegisterMessage(ReadBlobSequencesResponse.RunEntry)
_sym_db.RegisterMessage(ReadBlobSequencesResponse.TagEntry)

BlobSequenceData = _reflection.GeneratedProtocolMessageType('BlobSequenceData', (_message.Message,), {
  'DESCRIPTOR' : _BLOBSEQUENCEDATA,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.BlobSequenceData)
  })
_sym_db.RegisterMessage(BlobSequenceData)

BlobReferenceSequence = _reflection.GeneratedProtocolMessageType('BlobReferenceSequence', (_message.Message,), {
  'DESCRIPTOR' : _BLOBREFERENCESEQUENCE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.BlobReferenceSequence)
  })
_sym_db.RegisterMessage(BlobReferenceSequence)

BlobReference = _reflection.GeneratedProtocolMessageType('BlobReference', (_message.Message,), {
  'DESCRIPTOR' : _BLOBREFERENCE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.BlobReference)
  })
_sym_db.RegisterMessage(BlobReference)

ReadBlobRequest = _reflection.GeneratedProtocolMessageType('ReadBlobRequest', (_message.Message,), {
  'DESCRIPTOR' : _READBLOBREQUEST,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobRequest)
  })
_sym_db.RegisterMessage(ReadBlobRequest)

ReadBlobResponse = _reflection.GeneratedProtocolMessageType('ReadBlobResponse', (_message.Message,), {
  'DESCRIPTOR' : _READBLOBRESPONSE,
  '__module__' : 'tensorboard.data.proto.data_provider_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.data.ReadBlobResponse)
  })
_sym_db.RegisterMessage(ReadBlobResponse)

_TENSORBOARDDATAPROVIDER = DESCRIPTOR.services_by_name['TensorBoardDataProvider']
if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'ZLgithub.com/tensorflow/tensorboard/tensorboard/data/proto/data_provider_proto'
  _GETEXPERIMENTREQUEST._serialized_start=176
  _GETEXPERIMENTREQUEST._serialized_end=221
  _GETEXPERIMENTRESPONSE._serialized_start=224
  _GETEXPERIMENTRESPONSE._serialized_end=356
  _PLUGINFILTER._serialized_start=358
  _PLUGINFILTER._serialized_end=393
  _RUNTAGFILTER._serialized_start=395
  _RUNTAGFILTER._serialized_end=495
  _RUNFILTER._serialized_start=497
  _RUNFILTER._serialized_end=523
  _TAGFILTER._serialized_start=525
  _TAGFILTER._serialized_end=551
  _DOWNSAMPLE._serialized_start=553
  _DOWNSAMPLE._serialized_end=585
  _LISTPLUGINSREQUEST._serialized_start=587
  _LISTPLUGINSREQUEST._serialized_end=630
  _LISTPLUGINSRESPONSE._serialized_start=632
  _LISTPLUGINSRESPONSE._serialized_end=696
  _PLUGIN._serialized_start=698
  _PLUGIN._serialized_end=720
  _LISTRUNSREQUEST._serialized_start=722
  _LISTRUNSREQUEST._serialized_end=762
  _LISTRUNSRESPONSE._serialized_start=764
  _LISTRUNSRESPONSE._serialized_end=819
  _RUN._serialized_start=821
  _RUN._serialized_end=870
  _LISTSCALARSREQUEST._serialized_start=873
  _LISTSCALARSREQUEST._serialized_end=1027
  _LISTSCALARSRESPONSE._serialized_start=1030
  _LISTSCALARSRESPONSE._serialized_end=1287
  _LISTSCALARSRESPONSE_RUNENTRY._serialized_start=1115
  _LISTSCALARSRESPONSE_RUNENTRY._serialized_end=1205
  _LISTSCALARSRESPONSE_TAGENTRY._serialized_start=1207
  _LISTSCALARSRESPONSE_TAGENTRY._serialized_end=1287
  _SCALARMETADATA._serialized_start=1289
  _SCALARMETADATA._serialized_end=1402
  _READSCALARSREQUEST._serialized_start=1405
  _READSCALARSREQUEST._serialized_end=1609
  _READSCALARSRESPONSE._serialized_start=1612
  _READSCALARSRESPONSE._serialized_end=1861
  _READSCALARSRESPONSE_RUNENTRY._serialized_start=1697
  _READSCALARSRESPONSE_RUNENTRY._serialized_end=1787
  _READSCALARSRESPONSE_TAGENTRY._serialized_start=1789
  _READSCALARSRESPONSE_TAGENTRY._serialized_end=1861
  _SCALARDATA._serialized_start=1863
  _SCALARDATA._serialized_end=1923
  _LISTTENSORSREQUEST._serialized_start=1926
  _LISTTENSORSREQUEST._serialized_end=2080
  _LISTTENSORSRESPONSE._serialized_start=2083
  _LISTTENSORSRESPONSE._serialized_end=2340
  _LISTTENSORSRESPONSE_RUNENTRY._serialized_start=2168
  _LISTTENSORSRESPONSE_RUNENTRY._serialized_end=2258
  _LISTTENSORSRESPONSE_TAGENTRY._serialized_start=2260
  _LISTTENSORSRESPONSE_TAGENTRY._serialized_end=2340
  _TENSORMETADATA._serialized_start=2342
  _TENSORMETADATA._serialized_end=2455
  _READTENSORSREQUEST._serialized_start=2458
  _READTENSORSREQUEST._serialized_end=2662
  _READTENSORSRESPONSE._serialized_start=2665
  _READTENSORSRESPONSE._serialized_end=2914
  _READTENSORSRESPONSE_RUNENTRY._serialized_start=2750
  _READTENSORSRESPONSE_RUNENTRY._serialized_end=2840
  _READTENSORSRESPONSE_TAGENTRY._serialized_start=2842
  _READTENSORSRESPONSE_TAGENTRY._serialized_end=2914
  _TENSORDATA._serialized_start=2916
  _TENSORDATA._serialized_end=3002
  _LISTBLOBSEQUENCESREQUEST._serialized_start=3005
  _LISTBLOBSEQUENCESREQUEST._serialized_end=3165
  _LISTBLOBSEQUENCESRESPONSE._serialized_start=3168
  _LISTBLOBSEQUENCESRESPONSE._serialized_end=3449
  _LISTBLOBSEQUENCESRESPONSE_RUNENTRY._serialized_start=3265
  _LISTBLOBSEQUENCESRESPONSE_RUNENTRY._serialized_end=3361
  _LISTBLOBSEQUENCESRESPONSE_TAGENTRY._serialized_start=3363
  _LISTBLOBSEQUENCESRESPONSE_TAGENTRY._serialized_end=3449
  _BLOBSEQUENCEMETADATA._serialized_start=3452
  _BLOBSEQUENCEMETADATA._serialized_end=3591
  _READBLOBSEQUENCESREQUEST._serialized_start=3594
  _READBLOBSEQUENCESREQUEST._serialized_end=3804
  _READBLOBSEQUENCESRESPONSE._serialized_start=3807
  _READBLOBSEQUENCESRESPONSE._serialized_end=4080
  _READBLOBSEQUENCESRESPONSE_RUNENTRY._serialized_start=3904
  _READBLOBSEQUENCESRESPONSE_RUNENTRY._serialized_end=4000
  _READBLOBSEQUENCESRESPONSE_TAGENTRY._serialized_start=4002
  _READBLOBSEQUENCESRESPONSE_TAGENTRY._serialized_end=4080
  _BLOBSEQUENCEDATA._serialized_start=4082
  _BLOBSEQUENCEDATA._serialized_end=4190
  _BLOBREFERENCESEQUENCE._serialized_start=4192
  _BLOBREFERENCESEQUENCE._serialized_end=4267
  _BLOBREFERENCE._serialized_start=4269
  _BLOBREFERENCE._serialized_end=4315
  _READBLOBREQUEST._serialized_start=4317
  _READBLOBREQUEST._serialized_end=4352
  _READBLOBRESPONSE._serialized_start=4354
  _READBLOBRESPONSE._serialized_end=4386
  _TENSORBOARDDATAPROVIDER._serialized_start=4389
  _TENSORBOARDDATAPROVIDER._serialized_end=5378
# @@protoc_insertion_point(module_scope)
