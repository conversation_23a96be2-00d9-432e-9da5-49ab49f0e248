import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any, Set
import requests
import time
import json
from datetime import datetime
import os
import sys
from collections import defaultdict
from smart_duplicate_prevention_system import SmartDuplicatePreventionSystem
        import traceback


#!/usr/bin/env python3
"""
Phase 1 - Critical Player Props Data Collection
Collect the most important missing NBA API endpoints for player props accuracy
"""


# Add the scripts directory to path to import our duplicate prevention system
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase1CriticalDataCollector:
    """Collect critical missing data for player props enhancement with smart duplicate prevention"""

    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }

        # Initialize smart duplicate prevention system
        logger.info("🔍 Initializing Smart Duplicate Prevention System...")
        self.duplicate_prevention = SmartDuplicatePreventionSystem(db_path)
        logger.info("✅ Duplicate prevention system ready")
        
        # Phase 1 Critical Endpoints
        self.phase1_endpoints = {
            'playerdashboardbyclutch': {
                'description': 'Player clutch performance stats',
                'priority': 'CRITICAL',
                'params': {
                    'PlayerID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'ClutchTime': 'Last 5 Minutes',
                    'AheadBehind': 'Ahead or Behind',
                    'PointDiff': 5
                }
            },
            'playerdashboardbyshootingsplits': {
                'description': 'Player shooting splits by location/situation',
                'priority': 'CRITICAL',
                'params': {
                    'PlayerID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None
                }
            },
            'playerdashptshots': {
                'description': 'Player shot tracking data',
                'priority': 'CRITICAL',
                'params': {
                    'PlayerID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None
                }
            },
            'shotchartdetail': {
                'description': 'Shot chart detail with location data',
                'priority': 'CRITICAL',
                'params': {
                    'PlayerID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'TeamID': 0,
                    'GameID': '',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'Position': '',
                    'RookieYear': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0,
                    'ContextFilter': '',
                    'ContextMeasure': 'FGA'
                }
            },
            'boxscoreadvancedv2': {
                'description': 'Advanced box score metrics',
                'priority': 'CRITICAL',
                'params': {
                    'GameID': None,
                    'StartPeriod': 1,
                    'EndPeriod': 10,
                    'StartRange': 0,
                    'EndRange': 28800,
                    'RangeType': 0
                }
            },
            'leaguedashplayerclutch': {
                'description': 'League-wide player clutch stats',
                'priority': 'CRITICAL',
                'params': {
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'ClutchTime': 'Last 5 Minutes',
                    'AheadBehind': 'Ahead or Behind',
                    'PointDiff': 5,
                    'PerMode': 'PerGame',
                    'MeasureType': 'Base',
                    'PlusMinus': 'N',
                    'PaceAdjust': 'N',
                    'Rank': 'N',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0
                }
            }
        }
        
        # Seasons to collect
        self.seasons = {
            'NBA': ['2019-20', '2020-21', '2021-22', '2022-23', '2023-24', '2024-25'],
            'WNBA': ['2019', '2020', '2021', '2022', '2023', '2024', '2025']
        }
        
        # Get collection priorities from duplicate prevention system
        self.collection_priorities = self.duplicate_prevention.generate_collection_priority_list()
        logger.info(f"📊 Collection priorities identified: {len(self.collection_priorities['priority_actions'])} actions")
    
    def get_active_players(self, league: str, season: str) -> List[Dict[str, Any]]:
        """Get active players for a season"""
        logger.info(f"📋 Getting {league} players for {season}...")
        
        league_id = '00' if league == 'NBA' else '10'
        
        try:
            url = f"{self.base_url}/commonallplayers"
            params = {
                'LeagueID': league_id,
                'Season': season,
                'IsOnlyCurrentSeason': '0'  # Get all players for season
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            players = []
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                headers = data['resultSets'][0]['headers']
                rows = data['resultSets'][0]['rowSet']
                
                for row in rows:
                    player_dict = dict(zip(headers, row))
                    players.append({
                        'player_id': str(player_dict.get('PERSON_ID')),
                        'player_name': player_dict.get('DISPLAY_FIRST_LAST'),
                        'team_id': player_dict.get('TEAM_ID'),
                        'league': league,
                        'season': season
                    })
            
            logger.info(f"✅ Found {len(players)} {league} players for {season}")
            return players
            
        except Exception as e:
            logger.error(f"❌ Error getting {league} players for {season}: {e}")
            return []
    
    def collect_player_endpoint_data(self, endpoint: str, player: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Collect data from a specific endpoint for a player with smart duplicate prevention"""
        endpoint_config = self.phase1_endpoints[endpoint]
        player_id = player['player_id']
        player_name = player['player_name']
        league = player['league']
        season = player['season']

        # Check if we already have this endpoint data using smart duplicate prevention
        if self.duplicate_prevention.check_endpoint_data_exists(league, player_id, season, endpoint):
            return []
        
        collected_data = []
        source_file = f"{endpoint}_{player_id}_{season}.json"

        try:
            logger.info(f"   📊 Collecting {endpoint} for {player_name} ({season})...")

            url = f"{self.base_url}/{endpoint}"
            params = endpoint_config['params'].copy()

            # Set player-specific parameters
            params['PlayerID'] = player_id
            params['Season'] = season
            params['LeagueID'] = '00' if league == 'NBA' else '10'
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                for result_set_idx, result_set in enumerate(data['resultSets']):
                    headers = result_set.get('headers', [])
                    rows = result_set.get('rowSet', [])
                    result_set_name = result_set.get('name', f'ResultSet_{result_set_idx}')
                    
                    for row_idx, row in enumerate(rows):
                        if len(row) == len(headers):
                            row_dict = dict(zip(headers, row))
                            
                            # Create comprehensive record
                            record = {
                                'player_id': player_id,
                                'player_name': player_name,
                                'season': season,
                                'league_name': league,
                                'league_id': '00' if league == 'NBA' else '10',
                                'team_id': player.get('team_id'),
                                'endpoint': endpoint,
                                'result_set_name': result_set_name,
                                'data_category': f'phase1_{endpoint}',
                                'data_type': f'{endpoint}_analytics',
                                'source_file': source_file,
                                'source_table': endpoint,
                                'raw_data': json.dumps(row_dict),
                                'created_at': datetime.now().isoformat()
                            }
                            
                            # Extract key stats for easy querying
                            self._extract_key_stats(record, row_dict, endpoint)
                            
                            collected_data.append(record)
                            
                            # No need to manually track - duplicate prevention system handles this
            
            time.sleep(0.6)  # Rate limiting
            
        except Exception as e:
            logger.warning(f"⚠️ Error collecting {endpoint} for {player_name}: {e}")
        
        logger.info(f"✅ Collected {len(collected_data)} {endpoint} records for {player_name}")
        return collected_data
    
    def _extract_key_stats(self, record: Dict[str, Any], row_dict: Dict[str, Any], endpoint: str) -> None:
        """Extract key statistics for easy querying"""
        # Common stat extractions based on endpoint
        if endpoint == 'playerdashboardbyclutch':
            record['stat_category'] = 'clutch_performance'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['games_played'] = row_dict.get('GP', 0)
            record['minutes'] = row_dict.get('MIN', 0)
            
        elif endpoint == 'playerdashboardbyshootingsplits':
            record['stat_category'] = 'shooting_splits'
            record['stat_value'] = row_dict.get('FG_PCT', 0)
            record['field_goal_attempts'] = row_dict.get('FGA', 0)
            record['three_point_percentage'] = row_dict.get('FG3_PCT', 0)
            
        elif endpoint == 'playerdashptshots':
            record['stat_category'] = 'shot_tracking'
            record['stat_value'] = row_dict.get('FG_PCT', 0)
            record['shot_distance'] = row_dict.get('SHOT_DIST', 0)
            record['shot_attempts'] = row_dict.get('FGA', 0)
            
        elif endpoint == 'shotchartdetail':
            record['stat_category'] = 'shot_location'
            record['stat_value'] = 1 if row_dict.get('SHOT_MADE_FLAG') == 1 else 0
            record['shot_distance'] = row_dict.get('SHOT_DISTANCE', 0)
            record['shot_zone'] = row_dict.get('SHOT_ZONE_BASIC', '')
            
        elif endpoint == 'boxscoreadvancedv2':
            record['stat_category'] = 'advanced_metrics'
            record['stat_value'] = row_dict.get('OFF_RATING', 0)
            record['defensive_rating'] = row_dict.get('DEF_RATING', 0)
            record['net_rating'] = row_dict.get('NET_RATING', 0)
            
        elif endpoint == 'leaguedashplayerclutch':
            record['stat_category'] = 'league_clutch'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['clutch_games'] = row_dict.get('GP', 0)
            record['clutch_minutes'] = row_dict.get('MIN', 0)
    
    def insert_phase1_data(self, data_batch: List[Dict[str, Any]]) -> int:
        """Insert Phase 1 data into database with smart duplicate prevention"""
        if not data_batch:
            return 0

        # Filter out duplicates using smart duplicate prevention
        new_records, duplicate_stats = self.duplicate_prevention.filter_new_data_only(data_batch)

        if duplicate_stats['duplicates'] > 0:
            logger.info(f"   🔍 Filtered out {duplicate_stats['duplicates']} duplicates, inserting {duplicate_stats['new_records']} new records")

        if not new_records:
            return 0

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        inserted_count = 0

        for record in new_records:
            try:
                cursor.execute("""
                    INSERT INTO unified_nba_wnba_data (
                        source_file, source_table, data_category, season, league_id, league_name,
                        season_type, player_id, player_name, team_id, stat_category, stat_value,
                        data_type, raw_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['source_file'], record['source_table'], record['data_category'],
                    record['season'], record['league_id'], record['league_name'],
                    'Regular Season', record['player_id'], record['player_name'],
                    record.get('team_id'), record.get('stat_category'), record.get('stat_value'),
                    record['data_type'], record['raw_data'], record['created_at']
                ))
                inserted_count += 1
                
            except Exception as e:
                logger.warning(f"⚠️ Error inserting record: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        return inserted_count
    
    def collect_phase1_data(self, league: str) -> Dict[str, Any]:
        """Collect all Phase 1 data for a league"""
        logger.info(f"🚀 PHASE 1 CRITICAL DATA COLLECTION - {league}")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        total_records = 0
        
        for season in self.seasons[league]:
            logger.info(f"\n📅 Processing {league} {season}...")
            
            # Get players for this season
            players = self.get_active_players(league, season)
            
            if not players:
                continue
            
            # Collect data from each endpoint
            for endpoint, config in self.phase1_endpoints.items():
                logger.info(f"\n🎯 Collecting {endpoint} ({config['description']})...")
                
                batch_data = []
                players_processed = 0
                
                for player in players[:50]:  # Start with top 50 players per season
                    endpoint_data = self.collect_player_endpoint_data(endpoint, player)
                    batch_data.extend(endpoint_data)
                    players_processed += 1
                    
                    # Insert in batches
                    if len(batch_data) >= 100:
                        inserted = self.insert_phase1_data(batch_data)
                        total_records += inserted
                        logger.info(f"   ✅ Inserted {inserted:,} records")
                        batch_data = []
                
                # Insert remaining data
                if batch_data:
                    inserted = self.insert_phase1_data(batch_data)
                    total_records += inserted
                    logger.info(f"   ✅ Final batch: {inserted:,} records")
                
                logger.info(f"✅ {endpoint} complete: {players_processed} players processed")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎉 PHASE 1 {league} COLLECTION COMPLETE!")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   📊 Total records: {total_records:,}")
        
        return {
            'success': True,
            'league': league,
            'duration': str(duration),
            'total_records': total_records
        }

def main():
    """Execute Phase 1 critical data collection"""

    try:
        collector = Phase1CriticalDataCollector()

        # Start with NBA
        nba_results = collector.collect_phase1_data('NBA')

        if nba_results['success']:

            # Then WNBA
            wnba_results = collector.collect_phase1_data('WNBA')

            if wnba_results['success']:

                total_records = nba_results['total_records'] + wnba_results['total_records']



        return nba_results

    except Exception as e:
        traceback.print_exc()
        return {'success': False, 'error': str(e)}

if __name__ == "__main__":
    main()
