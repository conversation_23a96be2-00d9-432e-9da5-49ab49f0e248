{"total_tests": 7, "passed_tests": 0, "failed_tests": 7, "success_rate": 0.0, "total_time_seconds": 0.01, "test_results": {"Component Initialization": {"status": "FAILED", "details": {"success": false, "error": "name 'EnhancedRealTimePipelineIntegration' is not defined", "message": "Component initialization failed"}, "timestamp": "2025-07-01T10:47:26.187843"}, "Pipeline Integration": {"status": "FAILED", "details": {"success": false, "error": "name 'create_enhanced_pipeline_integration' is not defined", "message": "Pipeline integration failed"}, "timestamp": "2025-07-01T10:47:26.188135"}, "Data Processing": {"status": "FAILED", "details": {"success": false, "error": "name 'create_enhanced_pipeline_integration' is not defined", "message": "Data processing failed"}, "timestamp": "2025-07-01T10:47:26.188201"}, "Error Handling": {"status": "FAILED", "details": {"success": false, "error": "name 'create_enhanced_pipeline_integration' is not defined", "message": "Error handling test failed"}, "timestamp": "2025-07-01T10:47:26.188247"}, "Performance": {"status": "FAILED", "details": {"success": false, "error": "name 'create_enhanced_pipeline_integration' is not defined", "message": "Performance test failed"}, "timestamp": "2025-07-01T10:47:26.188290"}, "Database Integration": {"status": "FAILED", "details": {"success": false, "error": "name 'EnhancedRealTimePipelineIntegration' is not defined", "message": "Database integration failed"}, "timestamp": "2025-07-01T10:47:26.198219"}, "Orchestrator": {"status": "FAILED", "details": {"success": false, "error": "name 'create_orchestrator' is not defined", "message": "Orchestrator test failed"}, "timestamp": "2025-07-01T10:47:26.198379"}}, "overall_status": "FAILED"}