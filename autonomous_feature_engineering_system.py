import os
import sys
import sqlite3
import asyncio
import logging
import json
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass
from enum import Enum
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA
from scipy import stats
from scipy.signal import find_peaks

#!/usr/bin/env python3
"""
Autonomous Feature Engineering System - HYPER MEDUSA NEURAL VAULT
================================================================

Comprehensive autonomous feature engineering system with:
- Basketball-specific feature generation and optimization
- Temporal pattern analysis and time-series features
- Advanced analytics feature creation
- Automated feature selection and validation
- Real-time feature quality monitoring
- Integration with existing MEDUSA systems
"""


# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AUTONOMOUS_FEATURE_ENGINEER")

class FeatureType(Enum):
    """Types of features that can be generated"""
    STATISTICAL = "statistical"
    TEMPORAL = "temporal"
    BASKETBALL_SPECIFIC = "basketball_specific"
    INTERACTION = "interaction"
    POLYNOMIAL = "polynomial"
    ROLLING = "rolling"
    MOMENTUM = "momentum"
    CLUTCH = "clutch"
    EFFICIENCY = "efficiency"
    CONTEXTUAL = "contextual"

class FeatureQuality(Enum):
    """Feature quality levels"""
    EXCELLENT = "excellent"  # High correlation, low noise
    GOOD = "good"           # Moderate correlation, acceptable noise
    FAIR = "fair"           # Low correlation, some noise
    POOR = "poor"           # Very low correlation, high noise

@dataclass
class FeatureMetrics:
    """Feature quality and performance metrics"""
    feature_name: str
    correlation_with_target: float
    mutual_information: float
    variance: float
    missing_percentage: float
    outlier_percentage: float
    quality_score: float
    feature_type: FeatureType
    basketball_relevance: float

@dataclass
class FeatureEngineeringResult:
    """Result of feature engineering process"""
    original_features: int
    generated_features: int
    selected_features: int
    quality_improvement: float
    processing_time_seconds: float
    feature_metrics: List[FeatureMetrics]

class AutonomousFeatureEngineeringSystem:
    """
    Autonomous feature engineering system with basketball intelligence
    """
    
    def __init__(self, database_path: str = "hyper_medusa_consolidated.db"):
        self.database_path = database_path
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Feature engineering configuration
        self.max_features_per_type = 50
        self.quality_threshold = 0.3
        self.correlation_threshold = 0.05
        self.variance_threshold = 0.01
        
        # Basketball-specific feature definitions
        self.basketball_features = self._define_basketball_features()
        self.temporal_windows = [3, 5, 10, 15, 20]  # Rolling windows
        self.momentum_windows = [5, 10, 15]  # Momentum calculation windows
        
        # Feature quality tracking
        self.feature_history: List[Dict[str, Any]] = []
        
    def _define_basketball_features(self) -> Dict[str, Dict[str, Any]]:
        """Define basketball-specific feature engineering rules"""
        return {
            "efficiency_metrics": {
                "true_shooting_percentage": {
                    "formula": "points / (2 * (field_goals_attempted + 0.44 * free_throws_attempted))",
                    "description": "True shooting percentage - comprehensive shooting efficiency"
                },
                "effective_field_goal_percentage": {
                    "formula": "(field_goals_made + 0.5 * three_pointers_made) / field_goals_attempted",
                    "description": "Effective field goal percentage accounting for 3-pointers"
                },
                "player_efficiency_rating": {
                    "formula": "(points + total_rebounds + assists + steals + blocks - missed_shots - turnovers) / minutes_played",
                    "description": "Comprehensive player efficiency rating"
                }
            },
            "advanced_metrics": {
                "usage_rate": {
                    "formula": "100 * ((field_goals_attempted + 0.44 * free_throws_attempted + turnovers) * team_minutes) / (minutes_played * (team_field_goals_attempted + 0.44 * team_free_throws_attempted + team_turnovers))",
                    "description": "Percentage of team plays used by player while on court"
                },
                "assist_to_turnover_ratio": {
                    "formula": "assists / turnovers",
                    "description": "Ball handling efficiency metric"
                },
                "rebound_rate": {
                    "formula": "100 * total_rebounds / (minutes_played * (team_rebounds + opponent_rebounds) / team_minutes)",
                    "description": "Percentage of available rebounds grabbed"
                }
            },
            "clutch_metrics": {
                "clutch_scoring": {
                    "formula": "points_in_clutch_time / clutch_minutes_played",
                    "description": "Scoring efficiency in clutch situations"
                },
                "clutch_efficiency": {
                    "formula": "(clutch_points + clutch_assists - clutch_turnovers) / clutch_possessions",
                    "description": "Overall efficiency in clutch situations"
                }
            },
            "momentum_metrics": {
                "scoring_momentum": {
                    "formula": "recent_points_per_game - season_points_per_game",
                    "description": "Current scoring momentum vs season average"
                },
                "team_momentum": {
                    "formula": "recent_wins / recent_games - season_win_percentage",
                    "description": "Team momentum indicator"
                }
            }
        }
    
    async def run_autonomous_feature_engineering(self, target_table: str = "player_game_stats", 
                                               target_column: str = "points") -> FeatureEngineeringResult:
        """Run comprehensive autonomous feature engineering"""
        self.logger.info("🚀 Starting Autonomous Feature Engineering...")
        
        start_time = time.time()
        
        # Phase 1: Data Loading and Preparation
        self.logger.info("📊 Phase 1: Data Loading and Preparation")
        data = await self._load_and_prepare_data(target_table)
        
        if data is None or data.empty:
            raise ValueError(f"No data loaded from table: {target_table}")
        
        original_features = len(data.columns) - 1  # Exclude target
        
        # Phase 2: Basketball-Specific Feature Generation
        self.logger.info("🏀 Phase 2: Basketball-Specific Feature Generation")
        basketball_features = await self._generate_basketball_features(data)
        
        # Phase 3: Temporal Pattern Feature Generation
        self.logger.info("⏰ Phase 3: Temporal Pattern Feature Generation")
        temporal_features = await self._generate_temporal_features(data)
        
        # Phase 4: Statistical Feature Generation
        self.logger.info("📈 Phase 4: Statistical Feature Generation")
        statistical_features = await self._generate_statistical_features(data)
        
        # Phase 5: Interaction Feature Generation
        self.logger.info("🔗 Phase 5: Interaction Feature Generation")
        interaction_features = await self._generate_interaction_features(data)
        
        # Phase 6: Feature Combination with Duplicate Handling
        self.logger.info("🔗 Phase 6: Feature Combination with Duplicate Handling")
        all_features = data.copy()

        # Add each feature set while handling duplicates
        feature_sets = {
            'basketball': basketball_features,
            'temporal': temporal_features,
            'statistical': statistical_features,
            'interaction': interaction_features
        }

        for feature_type, features in feature_sets.items():
            if not features.empty:
                # Ensure indices match
                features = features.reindex(all_features.index, fill_value=0)

                # Add features while handling duplicates
                for col in features.columns:
                    if col in all_features.columns:
                        # Create unique column name
                        counter = 1
                        new_col = f"{col}_{feature_type}_{counter}"
                        while new_col in all_features.columns:
                            counter += 1
                            new_col = f"{col}_{feature_type}_{counter}"
                        all_features[new_col] = features[col]
                    else:
                        all_features[col] = features[col]

        # Final duplicate check and removal
        if all_features.columns.duplicated().any():
            self.logger.warning("Removing remaining duplicate columns")
            all_features = all_features.loc[:, ~all_features.columns.duplicated()]

        self.logger.info(f"🔗 Combined features: {len(all_features.columns)} total columns")

        # Phase 7: Feature Quality Assessment
        self.logger.info("🔍 Phase 7: Feature Quality Assessment")
        feature_metrics = await self._assess_feature_quality(all_features, target_column)
        
        # Phase 7: Intelligent Feature Selection
        self.logger.info("🎯 Phase 7: Intelligent Feature Selection")
        selected_features = await self._intelligent_feature_selection(all_features, target_column, feature_metrics)
        
        # Phase 8: Feature Validation and Optimization
        self.logger.info("✅ Phase 8: Feature Validation and Optimization")
        optimized_features = await self._optimize_selected_features(selected_features, target_column)
        
        # Calculate results
        processing_time = time.time() - start_time
        generated_features = len(all_features.columns) - original_features
        selected_feature_count = len(optimized_features.columns) - 1  # Exclude target
        
        # Calculate quality improvement
        original_quality = await self._calculate_baseline_quality(data, target_column)
        optimized_quality = await self._calculate_baseline_quality(optimized_features, target_column)
        quality_improvement = optimized_quality - original_quality
        
        # Save results
        await self._save_feature_engineering_results(optimized_features, feature_metrics)
        
        result = FeatureEngineeringResult(
            original_features=original_features,
            generated_features=generated_features,
            selected_features=selected_feature_count,
            quality_improvement=quality_improvement,
            processing_time_seconds=round(processing_time, 2),
            feature_metrics=feature_metrics
        )
        
        self.logger.info(f"🎉 Feature Engineering Complete! Generated {generated_features} features, selected {selected_feature_count}")
        
        return result
    
    async def _load_and_prepare_data(self, table_name: str) -> Optional[pd.DataFrame]:
        """Load and prepare data for feature engineering"""
        try:
            conn = sqlite3.connect(self.database_path)
            
            # Load data with proper handling
            query = f"SELECT * FROM {table_name} LIMIT 10000"  # Limit for performance
            data = pd.read_sql_query(query, conn)
            conn.close()
            
            if data.empty:
                self.logger.warning(f"No data found in table: {table_name}")
                return None
            
            # Basic data cleaning
            # Convert date columns
            date_columns = [col for col in data.columns if 'date' in col.lower()]
            for col in date_columns:
                try:
                    data[col] = pd.to_datetime(data[col], errors='coerce')
                except:
                    pass
            
            # Handle missing values
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            data[numeric_columns] = data[numeric_columns].fillna(data[numeric_columns].median())
            
            # Remove columns with too many missing values
            missing_threshold = 0.5
            data = data.loc[:, data.isnull().mean() < missing_threshold]
            
            self.logger.info(f"📊 Loaded {len(data)} records with {len(data.columns)} columns")
            return data
            
        except Exception as e:
            self.logger.error(f"Data loading failed: {e}")
            return None
    
    async def _generate_basketball_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate basketball-specific features"""
        basketball_features = pd.DataFrame(index=data.index)
        
        try:
            # Efficiency metrics
            if all(col in data.columns for col in ['points', 'field_goals_attempted', 'free_throws_attempted']):
                basketball_features['true_shooting_percentage'] = (
                    data['points'] / (2 * (data['field_goals_attempted'] + 0.44 * data['free_throws_attempted']))
                ).fillna(0)
            
            if all(col in data.columns for col in ['field_goals_made', 'three_pointers_made', 'field_goals_attempted']):
                basketball_features['effective_field_goal_percentage'] = (
                    (data['field_goals_made'] + 0.5 * data['three_pointers_made']) / data['field_goals_attempted']
                ).fillna(0)
            
            # Player efficiency rating
            efficiency_cols = ['points', 'total_rebounds', 'assists', 'steals', 'blocks', 'turnovers', 'minutes_played']
            if all(col in data.columns for col in efficiency_cols):
                missed_shots = data['field_goals_attempted'] - data['field_goals_made'] if 'field_goals_made' in data.columns else 0
                basketball_features['player_efficiency_rating'] = (
                    (data['points'] + data['total_rebounds'] + data['assists'] + 
                     data['steals'] + data['blocks'] - missed_shots - data['turnovers']) / 
                    data['minutes_played'].replace(0, 1)
                ).fillna(0)
            
            # Advanced ratios
            if all(col in data.columns for col in ['assists', 'turnovers']):
                basketball_features['assist_turnover_ratio'] = (
                    data['assists'] / data['turnovers'].replace(0, 1)
                ).fillna(0)
            
            if all(col in data.columns for col in ['steals', 'personal_fouls']):
                basketball_features['steal_foul_ratio'] = (
                    data['steals'] / data['personal_fouls'].replace(0, 1)
                ).fillna(0)
            
            # Shooting efficiency
            if all(col in data.columns for col in ['three_pointers_made', 'three_pointers_attempted']):
                basketball_features['three_point_percentage'] = (
                    data['three_pointers_made'] / data['three_pointers_attempted'].replace(0, 1)
                ).fillna(0)
            
            if all(col in data.columns for col in ['free_throws_made', 'free_throws_attempted']):
                basketball_features['free_throw_percentage'] = (
                    data['free_throws_made'] / data['free_throws_attempted'].replace(0, 1)
                ).fillna(0)
            
            # Impact metrics
            if all(col in data.columns for col in ['points', 'assists']):
                basketball_features['scoring_impact'] = data['points'] + (data['assists'] * 2.5)
            
            if all(col in data.columns for col in ['total_rebounds', 'steals', 'blocks']):
                basketball_features['defensive_impact'] = (
                    data['total_rebounds'] + data['steals'] * 1.5 + data['blocks'] * 2
                )
            
            self.logger.info(f"🏀 Generated {len(basketball_features.columns)} basketball-specific features")
            return basketball_features
            
        except Exception as e:
            self.logger.error(f"Basketball feature generation failed: {e}")
            return pd.DataFrame(index=data.index)
    
    async def _generate_temporal_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate temporal pattern features"""
        temporal_features = pd.DataFrame(index=data.index)
        
        try:
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            
            # Rolling statistics
            for window in self.temporal_windows:
                if len(data) > window:
                    for col in numeric_columns[:10]:  # Limit to prevent explosion
                        temporal_features[f'{col}_rolling_mean_{window}'] = data[col].rolling(window).mean()
                        temporal_features[f'{col}_rolling_std_{window}'] = data[col].rolling(window).std()
                        temporal_features[f'{col}_rolling_max_{window}'] = data[col].rolling(window).max()
                        temporal_features[f'{col}_rolling_min_{window}'] = data[col].rolling(window).min()
            
            # Momentum features
            for window in self.momentum_windows:
                if len(data) > window:
                    for col in numeric_columns[:5]:  # Key performance metrics
                        # Recent vs historical performance
                        recent_mean = data[col].rolling(window).mean()
                        historical_mean = data[col].expanding().mean()
                        temporal_features[f'{col}_momentum_{window}'] = recent_mean - historical_mean
                        
                        # Trend analysis
                        temporal_features[f'{col}_trend_{window}'] = data[col].rolling(window).apply(
                            lambda x: stats.linregress(range(len(x)), x)[0] if len(x) == window else 0
                        )
            
            # Lag features
            for col in numeric_columns[:8]:
                for lag in [1, 2, 3, 5]:
                    temporal_features[f'{col}_lag_{lag}'] = data[col].shift(lag)
            
            # Fill NaN values
            temporal_features = temporal_features.fillna(0)
            
            self.logger.info(f"⏰ Generated {len(temporal_features.columns)} temporal features")
            return temporal_features
            
        except Exception as e:
            self.logger.error(f"Temporal feature generation failed: {e}")
            return pd.DataFrame(index=data.index)

    async def _generate_statistical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate statistical features"""
        statistical_features = pd.DataFrame(index=data.index)

        try:
            numeric_columns = data.select_dtypes(include=[np.number]).columns

            # Polynomial features
            for col in numeric_columns[:8]:  # Limit to prevent explosion
                statistical_features[f'{col}_squared'] = data[col] ** 2
                statistical_features[f'{col}_cubed'] = data[col] ** 3
                statistical_features[f'{col}_sqrt'] = np.sqrt(np.abs(data[col]))
                statistical_features[f'{col}_log'] = np.log1p(np.abs(data[col]))

            # Statistical transformations
            for col in numeric_columns[:10]:
                # Z-score normalization
                statistical_features[f'{col}_zscore'] = (data[col] - data[col].mean()) / data[col].std()

                # Percentile rank
                statistical_features[f'{col}_percentile'] = data[col].rank(pct=True)

                # Outlier indicators
                Q1 = data[col].quantile(0.25)
                Q3 = data[col].quantile(0.75)
                IQR = Q3 - Q1
                statistical_features[f'{col}_is_outlier'] = (
                    (data[col] < (Q1 - 1.5 * IQR)) | (data[col] > (Q3 + 1.5 * IQR))
                ).astype(int)

            # Binning features
            for col in numeric_columns[:5]:
                statistical_features[f'{col}_binned'] = pd.cut(data[col], bins=5, labels=False)

            # Fill NaN values
            statistical_features = statistical_features.fillna(0)

            self.logger.info(f"📈 Generated {len(statistical_features.columns)} statistical features")
            return statistical_features

        except Exception as e:
            self.logger.error(f"Statistical feature generation failed: {e}")
            return pd.DataFrame(index=data.index)

    async def _generate_interaction_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Generate interaction features"""
        interaction_features = pd.DataFrame(index=data.index)

        try:
            numeric_columns = data.select_dtypes(include=[np.number]).columns

            # Pairwise interactions
            important_cols = numeric_columns[:8]  # Limit to most important columns
            for i, col1 in enumerate(important_cols):
                for col2 in important_cols[i+1:]:
                    # Multiplicative interaction
                    interaction_features[f'{col1}_{col2}_mult'] = data[col1] * data[col2]

                    # Ratio interaction
                    interaction_features[f'{col1}_{col2}_ratio'] = (
                        data[col1] / data[col2].replace(0, 1)
                    )

                    # Difference interaction
                    interaction_features[f'{col1}_{col2}_diff'] = data[col1] - data[col2]

            # Basketball-specific interactions
            if all(col in data.columns for col in ['points', 'minutes_played']):
                interaction_features['points_per_minute'] = (
                    data['points'] / data['minutes_played'].replace(0, 1)
                )

            if all(col in data.columns for col in ['assists', 'turnovers']):
                interaction_features['assist_turnover_efficiency'] = (
                    (data['assists'] - data['turnovers']) / (data['assists'] + data['turnovers']).replace(0, 1)
                )

            if all(col in data.columns for col in ['field_goals_made', 'field_goals_attempted']):
                interaction_features['shooting_efficiency'] = (
                    data['field_goals_made'] / data['field_goals_attempted'].replace(0, 1)
                )

            # Fill NaN values
            interaction_features = interaction_features.fillna(0)

            self.logger.info(f"🔗 Generated {len(interaction_features.columns)} interaction features")
            return interaction_features

        except Exception as e:
            self.logger.error(f"Interaction feature generation failed: {e}")
            return pd.DataFrame(index=data.index)

    async def _assess_feature_quality(self, data: pd.DataFrame, target_column: str) -> List[FeatureMetrics]:
        """Assess quality of all features with robust handling of NaN and infinite values"""
        feature_metrics = []

        try:
            # Remove duplicate columns first
            data = data.loc[:, ~data.columns.duplicated()]

            if target_column not in data.columns:
                self.logger.error(f"Target column '{target_column}' not found in data")
                return feature_metrics

            # Clean target column
            target = data[target_column].replace([np.inf, -np.inf], np.nan)
            if not pd.api.types.is_numeric_dtype(target):
                self.logger.warning(f"Target column '{target_column}' is not numeric")
                return feature_metrics

            features = data.drop(columns=[target_column])

            for feature_name in features.columns:
                try:
                    feature_data = features[feature_name]

                    # Skip non-numeric features
                    if not pd.api.types.is_numeric_dtype(feature_data):
                        continue

                    # Clean feature data
                    feature_data = feature_data.replace([np.inf, -np.inf], np.nan)

                    # Skip features with all NaN values or constant values
                    if feature_data.isna().all() or feature_data.nunique() <= 1:
                        continue

                    # Calculate correlation with proper NaN handling
                    try:
                        correlation = abs(target.corr(feature_data))
                        if pd.isna(correlation):
                            correlation = 0.0
                    except Exception:
                        correlation = 0.0

                    # Calculate variance with NaN handling
                    try:
                        variance = feature_data.var()
                        if pd.isna(variance):
                            variance = 0.0
                    except Exception:
                        variance = 0.0

                    # Missing percentage
                    missing_pct = feature_data.isna().sum() / len(feature_data)

                    # Outlier detection with NaN handling
                    try:
                        Q1 = feature_data.quantile(0.25)
                        Q3 = feature_data.quantile(0.75)
                        IQR = Q3 - Q1
                        if pd.notna(Q1) and pd.notna(Q3) and IQR > 0:
                            outliers = ((feature_data < (Q1 - 1.5 * IQR)) | (feature_data > (Q3 + 1.5 * IQR))).sum()
                            outlier_pct = outliers / len(feature_data)
                        else:
                            outlier_pct = 0.0
                    except Exception:
                        outlier_pct = 0.0

                    # Mutual information with robust error handling
                    try:
                        # Get valid indices (no NaN in either feature or target)
                        valid_indices = ~(feature_data.isna() | target.isna())
                        if valid_indices.sum() < 10:  # Need at least 10 valid samples
                            mi_score = 0.0
                        else:
                            feature_clean = feature_data[valid_indices].values.reshape(-1, 1)
                            target_clean = target[valid_indices].values

                            # Check for constant features after cleaning
                            if np.std(feature_clean) == 0 or np.std(target_clean) == 0:
                                mi_score = 0.0
                            else:
                                mi_score = mutual_info_regression(feature_clean, target_clean)[0]
                                if np.isnan(mi_score) or np.isinf(mi_score):
                                    mi_score = 0.0
                    except Exception:
                        mi_score = 0.0

                    # Determine feature type
                    feature_type = self._determine_feature_type(feature_name)

                    # Basketball relevance score
                    basketball_relevance = self._calculate_basketball_relevance(feature_name)

                    # Overall quality score with improved weighting
                    quality_score = (
                        correlation * 0.5 +  # Increased correlation weight
                        mi_score * 0.3 +
                        (1 - missing_pct) * 0.15 +  # Penalty for missing values
                        (1 - outlier_pct) * 0.05    # Small penalty for outliers
                    )

                    # Apply basketball relevance bonus
                    quality_score *= (1 + basketball_relevance * 0.1)  # Up to 10% bonus

                    metrics = FeatureMetrics(
                        feature_name=feature_name,
                        correlation_with_target=round(correlation, 4),
                        mutual_information=round(mi_score, 4),
                        variance=round(variance, 4),
                        missing_percentage=round(missing_pct, 4),
                        outlier_percentage=round(outlier_pct, 4),
                        quality_score=round(quality_score, 4),
                        feature_type=feature_type,
                        basketball_relevance=round(basketball_relevance, 4)
                    )

                    feature_metrics.append(metrics)

                except Exception as e:
                    continue

            # Sort by quality score
            feature_metrics.sort(key=lambda x: x.quality_score, reverse=True)

            self.logger.info(f"🔍 Assessed quality of {len(feature_metrics)} features")
            return feature_metrics

        except Exception as e:
            self.logger.error(f"Feature quality assessment failed: {e}")
            return feature_metrics

    def _determine_feature_type(self, feature_name: str) -> FeatureType:
        """Determine the type of feature based on its name"""
        name_lower = feature_name.lower()

        if any(keyword in name_lower for keyword in ['rolling', 'lag', 'trend', 'momentum']):
            return FeatureType.TEMPORAL
        elif any(keyword in name_lower for keyword in ['efficiency', 'percentage', 'ratio', 'impact']):
            return FeatureType.BASKETBALL_SPECIFIC
        elif any(keyword in name_lower for keyword in ['mult', 'interaction', 'diff']):
            return FeatureType.INTERACTION
        elif any(keyword in name_lower for keyword in ['squared', 'cubed', 'sqrt', 'log']):
            return FeatureType.POLYNOMIAL
        elif any(keyword in name_lower for keyword in ['zscore', 'percentile', 'outlier', 'binned']):
            return FeatureType.STATISTICAL
        elif any(keyword in name_lower for keyword in ['clutch', 'momentum']):
            return FeatureType.CLUTCH
        else:
            return FeatureType.STATISTICAL

    def _calculate_basketball_relevance(self, feature_name: str) -> float:
        """Calculate basketball relevance score for a feature"""
        name_lower = feature_name.lower()

        # Basketball-specific keywords and their weights
        basketball_keywords = {
            'points': 1.0, 'assists': 0.9, 'rebounds': 0.9, 'steals': 0.8, 'blocks': 0.8,
            'efficiency': 0.9, 'shooting': 0.8, 'percentage': 0.7, 'ratio': 0.6,
            'clutch': 0.9, 'momentum': 0.7, 'impact': 0.8, 'performance': 0.7,
            'field_goals': 0.8, 'free_throws': 0.7, 'three_pointers': 0.8,
            'turnovers': 0.7, 'fouls': 0.6, 'minutes': 0.6
        }

        relevance_score = 0.0
        for keyword, weight in basketball_keywords.items():
            if keyword in name_lower:
                relevance_score = max(relevance_score, weight)

        return relevance_score

    async def _intelligent_feature_selection(self, data: pd.DataFrame, target_column: str,
                                            feature_metrics: List[FeatureMetrics]) -> pd.DataFrame:
        """Intelligent feature selection based on quality metrics"""
        try:
            if target_column not in data.columns:
                self.logger.error(f"Target column '{target_column}' not found")
                return data

            # Get high-quality features
            high_quality_features = [
                metric.feature_name for metric in feature_metrics
                if metric.quality_score >= self.quality_threshold and
                   metric.correlation_with_target >= self.correlation_threshold
            ]

            # Always include target column
            selected_columns = [target_column] + high_quality_features

            # Ensure we don't exceed maximum features
            if len(selected_columns) > 100:  # Reasonable limit
                # Sort by quality and take top features
                sorted_features = sorted(feature_metrics, key=lambda x: x.quality_score, reverse=True)
                selected_columns = [target_column] + [f.feature_name for f in sorted_features[:99]]

            selected_data = data[selected_columns]

            # Additional statistical feature selection
            if len(selected_data.columns) > 20:
                X = selected_data.drop(columns=[target_column])
                y = selected_data[target_column]

                # Use SelectKBest for final selection
                k = min(50, len(X.columns))  # Select top 50 or all if less
                selector = SelectKBest(score_func=f_regression, k=k)
                X_selected = selector.fit_transform(X, y)

                # Get selected feature names
                selected_feature_names = X.columns[selector.get_support()].tolist()
                selected_data = data[[target_column] + selected_feature_names]

            self.logger.info(f"🎯 Selected {len(selected_data.columns)-1} high-quality features")
            return selected_data

        except Exception as e:
            self.logger.error(f"Feature selection failed: {e}")
            return data

    async def _optimize_selected_features(self, data: pd.DataFrame, target_column: str) -> pd.DataFrame:
        """Optimize selected features for best performance"""
        try:
            # Remove highly correlated features
            numeric_data = data.select_dtypes(include=[np.number])
            correlation_matrix = numeric_data.corr().abs()

            # Find pairs of highly correlated features
            high_corr_pairs = []
            for i in range(len(correlation_matrix.columns)):
                for j in range(i+1, len(correlation_matrix.columns)):
                    if correlation_matrix.iloc[i, j] > 0.95:  # Very high correlation
                        high_corr_pairs.append((correlation_matrix.columns[i], correlation_matrix.columns[j]))

            # Remove one feature from each highly correlated pair
            features_to_remove = set()
            for feature1, feature2 in high_corr_pairs:
                if feature1 != target_column and feature2 != target_column:
                    # Keep the one with higher correlation to target
                    corr1 = abs(data[feature1].corr(data[target_column]))
                    corr2 = abs(data[feature2].corr(data[target_column]))
                    if corr1 < corr2:
                        features_to_remove.add(feature1)
                    else:
                        features_to_remove.add(feature2)

            # Remove highly correlated features
            optimized_data = data.drop(columns=list(features_to_remove))

            # Handle infinite and NaN values
            optimized_data = optimized_data.replace([np.inf, -np.inf], np.nan)
            optimized_data = optimized_data.fillna(optimized_data.median())

            self.logger.info(f"✅ Optimized features: removed {len(features_to_remove)} highly correlated features")
            return optimized_data

        except Exception as e:
            self.logger.error(f"Feature optimization failed: {e}")
            return data

    async def _calculate_baseline_quality(self, data: pd.DataFrame, target_column: str) -> float:
        """Calculate baseline quality score for features"""
        try:
            if target_column not in data.columns:
                return 0.0

            target = data[target_column]
            features = data.drop(columns=[target_column])
            numeric_features = features.select_dtypes(include=[np.number])

            if numeric_features.empty:
                return 0.0

            # Ensure target is numeric
            if not pd.api.types.is_numeric_dtype(target):
                self.logger.warning(f"Target column '{target_column}' is not numeric, using first numeric column")
                numeric_cols = data.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) == 0:
                    return 0.0
                target = data[numeric_cols[0]]
                numeric_features = data[numeric_cols[1:]] if len(numeric_cols) > 1 else data[numeric_cols[:1]]

            # Handle infinite values
            target = target.replace([np.inf, -np.inf], np.nan)
            numeric_features = numeric_features.replace([np.inf, -np.inf], np.nan)

            # Calculate average correlation with target
            correlations = []
            for col in numeric_features.columns:
                try:
                    # Skip if either series has all NaN values
                    if target.isna().all() or numeric_features[col].isna().all():
                        continue

                    # Calculate correlation with proper handling of NaN
                    corr = target.corr(numeric_features[col])
                    if pd.notna(corr) and not np.isinf(corr):
                        correlations.append(abs(corr))
                except Exception as corr_error:
                    continue

            if not correlations:
                return 0.0

            avg_correlation = np.mean(correlations)

            # Calculate data quality metrics
            total_cells = len(numeric_features) * len(numeric_features.columns)
            if total_cells == 0:
                missing_ratio = 0.0
            else:
                missing_ratio = numeric_features.isnull().sum().sum() / total_cells

            quality_score = avg_correlation * (1 - missing_ratio)

            return round(quality_score, 4)

        except Exception as e:
            self.logger.error(f"Baseline quality calculation failed: {e}")
            return 0.0

    async def _save_feature_engineering_results(self, optimized_features: pd.DataFrame,
                                              feature_metrics: List[FeatureMetrics]) -> None:
        """Save feature engineering results"""
        try:
            timestamp = int(time.time())

            # Save optimized features to database
            conn = sqlite3.connect(self.database_path)
            table_name = f"engineered_features_{timestamp}"
            optimized_features.to_sql(table_name, conn, if_exists='replace', index=False)
            conn.close()

            # Save feature metrics report
            metrics_report = {
                "timestamp": datetime.now().isoformat(),
                "total_features_generated": len(feature_metrics),
                "high_quality_features": len([m for m in feature_metrics if m.quality_score >= 0.5]),
                "basketball_relevant_features": len([m for m in feature_metrics if m.basketball_relevance >= 0.7]),
                "feature_metrics": [
                    {
                        "feature_name": m.feature_name,
                        "correlation_with_target": m.correlation_with_target,
                        "mutual_information": m.mutual_information,
                        "quality_score": m.quality_score,
                        "feature_type": m.feature_type.value,
                        "basketball_relevance": m.basketball_relevance
                    }
                    for m in feature_metrics[:100]  # Top 100 features
                ]
            }

            report_filename = f"feature_engineering_report_{timestamp}.json"
            with open(report_filename, 'w') as f:
                json.dump(metrics_report, f, indent=2)

            self.logger.info(f"💾 Results saved: {table_name} table and {report_filename}")

        except Exception as e:
            self.logger.error(f"Failed to save results: {e}")

async def main():
    """Main execution function"""

    # Initialize feature engineering system
    engineer = AutonomousFeatureEngineeringSystem()

    # Run autonomous feature engineering
    result = await engineer.run_autonomous_feature_engineering()

    # Display results summary

    # Feature type breakdown
    feature_types = {}
    for metric in result.feature_metrics:
        feature_type = metric.feature_type.value
        if feature_type not in feature_types:
            feature_types[feature_type] = 0
        feature_types[feature_type] += 1

    for feature_type, count in feature_types.items():

    # Top quality features
    top_features = sorted(result.feature_metrics, key=lambda x: x.quality_score, reverse=True)[:10]
    for i, feature in enumerate(top_features, 1):

    return result

if __name__ == "__main__":
    asyncio.run(main())
