from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Depends
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging
import jwt
import os
from datetime import datetime, timezone
                from backend.database.crud import get_user_by_username


#!/usr/bin/env python3
"""
🧠 HYPER MEDUSA NEURAL VAULT - Authentication Utilities
================================================================================
Simple authentication utilities for the HYPER MEDUSA NEURAL VAULT system
================================================================================
"""


# Initialize logger
logger = logging.getLogger(__name__)

# Security scheme
security = HTTPBearer()

def verify_jwt_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify JWT token and return payload
    """
    try:
        jwt_secret = os.getenv("JWT_SECRET", "REPLACE_WITH_SECURE_JWT_SECRET_IN_PRODUCTION")
        jwt_algorithm = os.getenv("JWT_ALGORITHM", "HS256")

        # Decode and verify the token
        payload = jwt.decode(token, jwt_secret, algorithms=[jwt_algorithm])
        return payload

    except jwt.ExpiredSignatureError:
        logger.warning("JWT token has expired")
        return None
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid JWT token: {e}")
        return None
    except Exception as e:
        logger.error(f"JWT verification error: {e}")
        return None

def create_jwt_token(user_data: Dict[str, Any]) -> str:
    """
    Create JWT token for user
    """
    try:
        jwt_secret = os.getenv("JWT_SECRET", "REPLACE_WITH_SECURE_JWT_SECRET_IN_PRODUCTION")
        jwt_algorithm = os.getenv("JWT_ALGORITHM", "HS256")

        # Create payload
        payload = {
            "sub": user_data.get("username"),
            "user_id": user_data.get("user_id"),
            "tier": user_data.get("tier", "free"),
            "iat": datetime.now(timezone.utc).timestamp(),
            "exp": datetime.now(timezone.utc).timestamp() + 3600  # 1 hour expiry
        }

        # Create token
        token = jwt.encode(payload, jwt_secret, algorithm=jwt_algorithm)
        return token

    except Exception as e:
        logger.error(f"JWT creation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not create authentication token"
        )

class User:
    """Simple user model for authentication"""
    def __init__(self, user_id: str, username: str, email: str = None):
        self.user_id = user_id
        self.username = username
        self.email = email or f"{username}@hypermedusa.vault"
        self.is_active = True
        self.is_admin = False

# Mock user database for development
MOCK_USERS = {
    "admin": User("admin", "admin"),
    "user": User("user", "user"),
    "test": User("test", "test")
}

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """
    Get the current authenticated user
    
    For development purposes, this is a simple mock implementation.
    In production, this would validate JWT tokens or other authentication methods.
    """
    try:
        # For development, accept any token and return a mock user
        token = credentials.credentials
        
        # Validate JWT token
        payload = verify_jwt_token(token)
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication token",
                headers={"WWW-Authenticate": "Bearer"},
            )

        username = payload.get("sub")
        if not username:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # In development mode, return mock user if user exists in mock data
        if os.getenv("ENVIRONMENT", "development") == "development" and username in MOCK_USERS:
            user = MOCK_USERS[username]
            if username == "admin":
                user.is_admin = True
        else:
            # Production implementation: fetch user from database
            try:
                user = await get_user_by_username(username)
                if not user or not user.is_active:
                    raise HTTPException(status_code=401, detail="User not found or inactive")
            except ImportError:
                # Fallback for production without database integration
                logger.warning(f"Database integration not available, creating temporary user for {username}")
                user = User(
                    id=hash(username) % 10000,
                    username=username,
                    email=f"{username}@basketball.ai",
                    is_active=True,
                    is_admin=username == "admin",
                    created_at=datetime.utcnow()
                )
            except Exception as e:
                logger.error(f"Database error during user lookup: {e}")
                raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User authentication requires database integration",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        logger.info(f"🔐 User authenticated: {user.username}")
        return user
        
    except Exception as e:
        logger.error(f"❌ Authentication failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Get the current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

async def get_current_admin_user(current_user: User = Depends(get_current_user)) -> User:
    """Get the current admin user"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user

def create_access_token(data: Dict[str, Any]) -> str:
    """
    Create an access token
    
    For development purposes, this returns a simple token.
    In production, this would create a proper JWT token.
    """
    username = data.get("sub", "user")
    return f"{username}_token_12345"

def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify an access token
    
    For development purposes, this does basic validation.
    In production, this would verify JWT tokens properly.
    """
    try:
        if not token or len(token) < 10:
            return None
        
        # Extract username from token (mock implementation)
        username = token.split("_")[0]
        
        if username in MOCK_USERS:
            return {
                "sub": username,
                "user_id": MOCK_USERS[username].user_id,
                "username": username
            }
        
        return None
        
    except Exception as e:
        logger.error(f"❌ Token verification failed: {e}")
        return None

# Optional dependency for routes that don't require authentication
async def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)) -> Optional[User]:
    """Get current user if authenticated, otherwise return None"""
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None

logger.info("🔐 HYPER MEDUSA NEURAL VAULT - Authentication utilities loaded")
