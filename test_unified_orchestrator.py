#!/usr/bin/env python3
"""
Test script for Unified Comprehensive Prediction Orchestrator
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(os.path.abspath('.'))

from src.orchestrators.unified_comprehensive_prediction_orchestrator import (
    UnifiedComprehensivePredictionOrchestrator,
    PredictionType,
    PredictionConfidenceLevel
)

async def test_unified_orchestrator():
    """Test the unified prediction orchestrator"""
    print("🚀 Testing Unified Comprehensive Prediction Orchestrator...")
    
    # Initialize orchestrator
    models_dir = Path("models")
    orchestrator = UnifiedComprehensivePredictionOrchestrator(models_dir)
    
    # Test NBA game prediction
    nba_game_data = {
        'league': 'NBA',
        'home_team': 'Los Angeles Lakers',
        'away_team': 'Boston Celtics',
        'game_date': datetime.now().isoformat(),
        'season': '2024-25'
    }
    
    print("\n📊 Testing NBA Game Prediction...")
    try:
        nba_result = await orchestrator.predict_game(nba_game_data)
        
        print(f"✅ NBA Prediction Results:")
        print(f"   Winner: {nba_result.predicted_winner}")
        print(f"   Probability: {nba_result.win_probability:.3f}")
        print(f"   Confidence: {nba_result.confidence_score:.3f}")
        print(f"   Confidence Level: {nba_result.confidence_level.value}")
        print(f"   Home Score: {nba_result.home_score:.1f}")
        print(f"   Away Score: {nba_result.away_score:.1f}")
        print(f"   Total Score: {nba_result.total_score:.1f}")
        print(f"   Point Spread: {nba_result.point_spread:.1f}")
        print(f"   Models Used: {len(nba_result.models_used)}")
        print(f"   Processing Time: {nba_result.processing_time_ms:.1f}ms")
        print(f"   Betting Opportunities: {len(nba_result.betting_opportunities)}")
        print(f"   Kelly Criterion Bets: {len(nba_result.kelly_criterion_bets)}")
        print(f"   Cognitive Spiral Iterations: {nba_result.cognitive_spiral_iterations}")
        
    except Exception as e:
        print(f"❌ NBA prediction failed: {e}")
    
    # Test WNBA game prediction
    wnba_game_data = {
        'league': 'WNBA',
        'home_team': 'Las Vegas Aces',
        'away_team': 'New York Liberty',
        'game_date': datetime.now().isoformat(),
        'season': '2024'
    }
    
    print("\n📊 Testing WNBA Game Prediction...")
    try:
        wnba_result = await orchestrator.predict_game(wnba_game_data)
        
        print(f"✅ WNBA Prediction Results:")
        print(f"   Winner: {wnba_result.predicted_winner}")
        print(f"   Probability: {wnba_result.win_probability:.3f}")
        print(f"   Confidence: {wnba_result.confidence_score:.3f}")
        print(f"   Confidence Level: {wnba_result.confidence_level.value}")
        print(f"   Home Score: {wnba_result.home_score:.1f}")
        print(f"   Away Score: {wnba_result.away_score:.1f}")
        print(f"   Total Score: {wnba_result.total_score:.1f}")
        print(f"   Point Spread: {wnba_result.point_spread:.1f}")
        print(f"   Models Used: {len(wnba_result.models_used)}")
        print(f"   Processing Time: {wnba_result.processing_time_ms:.1f}ms")
        print(f"   Betting Opportunities: {len(wnba_result.betting_opportunities)}")
        print(f"   Kelly Criterion Bets: {len(wnba_result.kelly_criterion_bets)}")
        print(f"   Cognitive Spiral Iterations: {wnba_result.cognitive_spiral_iterations}")
        
    except Exception as e:
        print(f"❌ WNBA prediction failed: {e}")
    
    # Test NBA/WNBA parity
    print("\n🏀 Testing NBA/WNBA Parity...")
    try:
        # Compare prediction structures
        if 'nba_result' in locals() and 'wnba_result' in locals():
            print(f"✅ NBA/WNBA Parity Check:")
            print(f"   NBA League: {nba_result.league}")
            print(f"   WNBA League: {wnba_result.league}")
            print(f"   NBA Total Score: {nba_result.total_score:.1f}")
            print(f"   WNBA Total Score: {wnba_result.total_score:.1f}")
            print(f"   Both have same prediction structure: {type(nba_result) == type(wnba_result)}")
            print(f"   Both have betting opportunities: {hasattr(nba_result, 'betting_opportunities') and hasattr(wnba_result, 'betting_opportunities')}")
            print(f"   Both have quantum uncertainty: {hasattr(nba_result, 'quantum_uncertainty') and hasattr(wnba_result, 'quantum_uncertainty')}")
            
            # Verify league-specific scoring
            nba_expected_range = (200, 240)  # NBA typical range
            wnba_expected_range = (150, 180)  # WNBA typical range
            
            nba_in_range = nba_expected_range[0] <= nba_result.total_score <= nba_expected_range[1]
            wnba_in_range = wnba_expected_range[0] <= wnba_result.total_score <= wnba_expected_range[1]
            
            print(f"   NBA score in expected range ({nba_expected_range[0]}-{nba_expected_range[1]}): {nba_in_range}")
            print(f"   WNBA score in expected range ({wnba_expected_range[0]}-{wnba_expected_range[1]}): {wnba_in_range}")
            
    except Exception as e:
        print(f"❌ Parity check failed: {e}")
    
    print("\n🎯 Unified Orchestrator Test Complete!")

if __name__ == "__main__":
    asyncio.run(test_unified_orchestrator())
