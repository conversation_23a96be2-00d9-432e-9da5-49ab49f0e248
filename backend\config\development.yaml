# Development Environment Configuration - HYPER MEDUSA NEURAL VAULT
# ==================================================================
# Development-specific settings with enhanced debugging and local services

database:
  host: "localhost"
  port: 5432
  name: "hyper_medusa_vault_dev"
  user: "postgres"
  password: "dev_password"
  pool_size: 10
  max_overflow: 20
  echo: true  # Enable SQL logging for development

redis:
  host: "localhost"
  port: 6379
  db: 1  # Use different DB for development
  password: null
  max_connections: 25

api:
  host: "127.0.0.1"
  port: 8000
  workers: 1  # Single worker for easier debugging
  timeout: 60  # Longer timeout for debugging
  reload: true  # Enable auto-reload
  debug: true
  cors_origins:
    - "http://localhost:3000"
    - "http://127.0.0.1:3000"
    - "http://localhost:8080"
  rate_limit: "1000/minute"  # More lenient rate limiting

ml:
  model_path: "models/dev"
  batch_size: 16  # Smaller batch size for development
  learning_rate: 0.01  # Higher learning rate for faster experimentation
  epochs: 50  # Fewer epochs for faster training
  validation_split: 0.3
  early_stopping_patience: 5
  device: "cpu"  # Force CPU for consistent development
  mixed_precision: false  # Disable for debugging
  gradient_clipping: 0.5
  checkpoint_interval: 5  # More frequent checkpoints

logging:
  level: "DEBUG"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
  file_path: "logs/development.log"
  max_bytes: 5242880  # 5MB
  backup_count: 3
  json_format: false
  structured_logging: true

security:
  secret_key: "dev-secret-key-not-for-production"
  jwt_algorithm: "HS256"
  jwt_expiration: 7200  # 2 hours for development
  password_min_length: 4  # Relaxed for development
  max_login_attempts: 10  # More lenient
  lockout_duration: 300  # 5 minutes
  cors_enabled: true
  csrf_protection: false  # Disabled for easier API testing

monitoring:
  prometheus_enabled: true
  prometheus_port: 9090
  grafana_enabled: true
  grafana_port: 3000
  health_check_interval: 60  # Less frequent checks
  metrics_retention_days: 7  # Shorter retention
  alerting_enabled: false  # Disable alerts in development
  slack_webhook: null
  email_alerts: false
