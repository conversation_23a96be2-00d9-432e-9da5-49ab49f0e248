#!/usr/bin/env python3
"""
Test script for the fixed player props model with proper scaling and linear output
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import numpy as np
import matplotlib.pyplot as plt
from src.neural_cortex.player_props_neural_pipeline import PlayerPropsConfig, PlayerPropsTrainingPipeline

def test_fixed_model():
    """Test the fixed model with proper scaling and linear output"""
    print("🔧 Testing fixed player props model...")
    
    # Create config with linear output (no ReLU clipping)
    config = PlayerPropsConfig(
        prop_type='points',
        league='WNBA',
        num_epochs=5,  # Quick test
        batch_size=64,
        learning_rate=0.001,
        output_activation='linear',  # FIXED: Use linear instead of relu
        dropout_rate=0.2,  # Reduced dropout for better learning
        hidden_dim=256,  # Increased capacity
        num_layers=3
    )
    
    print(f"📊 Config: {config.output_activation} output, {config.hidden_dim} hidden, {config.dropout_rate} dropout")
    
    # Create pipeline
    pipeline = PlayerPropsTrainingPipeline(config)
    
    print("🚀 Running training with fixes...")
    
    # Train model
    import asyncio
    results = asyncio.run(pipeline.train())

    # Print detailed results
    print("\n✅ Fixed Model Results:")
    test_metrics = results['test_metrics']
    print(f"   MAE: {test_metrics['mae']:.3f}")
    print(f"   R²: {test_metrics['r2']:.3f}")
    print(f"   Accuracy (±1 point): {test_metrics['accuracy_1pt']:.1f}%")
    print(f"   Accuracy (±2 points): {test_metrics['accuracy_2pt']:.1f}%")
    print(f"   Accuracy (±25%): {test_metrics['accuracy_25pct']:.1f}%")
    
    # Check if R² is positive (model learning)
    if test_metrics['r2'] > 0:
        print("🎉 SUCCESS: Positive R² indicates model is learning!")
    else:
        print("⚠️ WARNING: Negative R² indicates model still struggling")
    
    # Check prediction bias
    if hasattr(pipeline, 'target_scaler') and pipeline.target_scaler is not None:
        print(f"📊 Target scaling applied: mean={pipeline.target_scaler.mean_[0]:.2f}, std={pipeline.target_scaler.scale_[0]:.2f}")
    
    return results

def plot_predictions_vs_actual(pipeline, test_loader):
    """Plot predictions vs actual values for visual inspection"""
    pipeline.model.eval()
    predictions = []
    actuals = []
    
    import torch
    with torch.no_grad():
        for data, targets in test_loader:
            data = data.to(pipeline.device)
            outputs = pipeline.model(data).squeeze()
            
            # Unscale if needed
            if hasattr(pipeline, 'target_scaler') and pipeline.target_scaler is not None:
                outputs_unscaled = pipeline.target_scaler.inverse_transform(outputs.cpu().numpy().reshape(-1, 1)).flatten()
                targets_unscaled = pipeline.target_scaler.inverse_transform(targets.numpy().reshape(-1, 1)).flatten()
                predictions.extend(outputs_unscaled)
                actuals.extend(targets_unscaled)
            else:
                predictions.extend(outputs.cpu().numpy())
                actuals.extend(targets.numpy())
    
    # Create scatter plot
    plt.figure(figsize=(10, 8))
    plt.scatter(actuals, predictions, alpha=0.6, s=20)
    plt.plot([min(actuals), max(actuals)], [min(actuals), max(actuals)], 'r--', lw=2, label='Perfect Prediction')
    plt.xlabel('Actual Points')
    plt.ylabel('Predicted Points')
    plt.title('Player Props Predictions vs Actual (WNBA Points)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Add statistics
    mae = np.mean(np.abs(np.array(predictions) - np.array(actuals)))
    r2 = np.corrcoef(predictions, actuals)[0, 1]**2
    plt.text(0.05, 0.95, f'MAE: {mae:.2f}\nR²: {r2:.3f}', 
             transform=plt.gca().transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('player_props_predictions.png', dpi=150, bbox_inches='tight')
    print("📊 Prediction plot saved as 'player_props_predictions.png'")

if __name__ == "__main__":
    try:
        results = test_fixed_model()
        print("\n🎯 Key Improvements Made:")
        print("   1. ✅ Linear output activation (no ReLU clipping)")
        print("   2. ✅ Proper feature and target scaling")
        print("   3. ✅ Unscaling for accurate metrics")
        print("   4. ✅ Reduced dropout for better learning")
        print("   5. ✅ Increased model capacity")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
