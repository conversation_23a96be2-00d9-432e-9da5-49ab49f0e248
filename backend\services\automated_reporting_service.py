import asyncio
import logging
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any, Union
from enum import Enum
from dataclasses import dataclass, asdict
import json
from pathlib import Path
from backend.middleware.feature_flags import UserTier
from src.analytics.intelligent_performance_analytics import IntelligentPerformanceAnalytics
import csv
from backend.services.notification_service import (

    notification_service, NotificationRequest, NotificationType, NotificationPriority
)

# Configure logging
logger = logging.getLogger("HYPER_MEDUSA_REPORTING")

class ReportType(Enum):
    """Report types"""
    PERFORMANCE_SUMMARY = "performance_summary"
    SYSTEM_HEALTH = "system_health"
    PREDICTION_ACCURACY = "prediction_accuracy"
    USER_ANALYTICS = "user_analytics"
    FINANCIAL_METRICS = "financial_metrics"
    TIER_USAGE = "tier_usage"
    ANOMALY_DETECTION = "anomaly_detection"
    COMPREHENSIVE = "comprehensive"

class ReportFrequency(Enum):
    """Report frequency options"""
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"

class ReportFormat(Enum):
    """Report output formats"""
    JSON = "json"
    HTML = "html"
    PDF = "pdf"
    CSV = "csv"
    EMAIL = "email"

@dataclass
class ReportSchedule:
    """Report schedule configuration"""
    schedule_id: str
    report_type: ReportType
    frequency: ReportFrequency
    format: ReportFormat
    recipients: List[str]
    tier_filter: Optional[UserTier] = None
    enabled: bool = True
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    parameters: Dict[str, Any] = None

@dataclass
class GeneratedReport:
    """Generated report data"""
    report_id: str
    report_type: ReportType
    generated_at: datetime
    timeframe: str
    format: ReportFormat
    data: Dict[str, Any]
    file_path: Optional[str] = None
    size_bytes: Optional[int] = None
    recipients: List[str] = None

class AutomatedReportingService:
    """Comprehensive automated reporting service"""
    
    def __init__(self):
        self.analytics_system = IntelligentPerformanceAnalytics()
        self.schedules: Dict[str, ReportSchedule] = {}
        self.report_history: List[GeneratedReport] = []
        self.is_running = False
        self.reports_directory = Path("reports")
        self.reports_directory.mkdir(exist_ok=True)
        
        # Initialize default schedules
        self._initialize_default_schedules()
        
    def _initialize_default_schedules(self):
        """Initialize default report schedules"""
        default_schedules = [
            ReportSchedule(
                schedule_id="daily_performance",
                report_type=ReportType.PERFORMANCE_SUMMARY,
                frequency=ReportFrequency.DAILY,
                format=ReportFormat.EMAIL,
                recipients=["<EMAIL>"],
                parameters={"timeframe": "24h", "include_trends": True}
            ),
            ReportSchedule(
                schedule_id="weekly_comprehensive",
                report_type=ReportType.COMPREHENSIVE,
                frequency=ReportFrequency.WEEKLY,
                format=ReportFormat.HTML,
                recipients=["<EMAIL>"],
                parameters={"timeframe": "7d", "detailed": True}
            ),
            ReportSchedule(
                schedule_id="hourly_system_health",
                report_type=ReportType.SYSTEM_HEALTH,
                frequency=ReportFrequency.HOURLY,
                format=ReportFormat.JSON,
                recipients=["<EMAIL>"],
                parameters={"timeframe": "1h", "alerts_only": False}
            ),
            ReportSchedule(
                schedule_id="daily_prediction_accuracy",
                report_type=ReportType.PREDICTION_ACCURACY,
                frequency=ReportFrequency.DAILY,
                format=ReportFormat.EMAIL,
                recipients=["<EMAIL>"],
                parameters={"timeframe": "24h", "include_breakdown": True}
            ),
            ReportSchedule(
                schedule_id="weekly_tier_usage",
                report_type=ReportType.TIER_USAGE,
                frequency=ReportFrequency.WEEKLY,
                format=ReportFormat.CSV,
                recipients=["<EMAIL>"],
                parameters={"timeframe": "7d", "include_conversion": True}
            )
        ]
        
        for schedule in default_schedules:
            self.schedules[schedule.schedule_id] = schedule
            schedule.next_run = self._calculate_next_run(schedule)
    
    def _calculate_next_run(self, schedule: ReportSchedule) -> datetime:
        """Calculate next run time for a schedule"""
        now = datetime.now(timezone.utc)
        
        if schedule.frequency == ReportFrequency.HOURLY:
            return now + timedelta(hours=1)
        elif schedule.frequency == ReportFrequency.DAILY:
            # Run at 6 AM UTC daily
            next_run = now.replace(hour=6, minute=0, second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(days=1)
            return next_run
        elif schedule.frequency == ReportFrequency.WEEKLY:
            # Run on Mondays at 8 AM UTC
            days_ahead = 0 - now.weekday()  # Monday is 0
            if days_ahead <= 0:
                days_ahead += 7
            next_run = now + timedelta(days=days_ahead)
            return next_run.replace(hour=8, minute=0, second=0, microsecond=0)
        elif schedule.frequency == ReportFrequency.MONTHLY:
            # Run on 1st of month at 9 AM UTC
            if now.day == 1 and now.hour < 9:
                next_run = now.replace(hour=9, minute=0, second=0, microsecond=0)
            else:
                next_month = now.replace(day=1) + timedelta(days=32)
                next_run = next_month.replace(day=1, hour=9, minute=0, second=0, microsecond=0)
            return next_run
        else:
            return now + timedelta(hours=24)  # Default to daily
    
    async def generate_report(self, report_type: ReportType, parameters: Dict[str, Any] = None) -> GeneratedReport:
        """Generate a specific report"""
        try:
            report_id = f"{report_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            timeframe = parameters.get("timeframe", "24h") if parameters else "24h"
            
            logger.info(f"📊 Generating report: {report_type.value} ({timeframe})")
            
            # Generate report data based on type
            if report_type == ReportType.PERFORMANCE_SUMMARY:
                data = await self._generate_performance_summary(parameters)
            elif report_type == ReportType.SYSTEM_HEALTH:
                data = await self._generate_system_health_report(parameters)
            elif report_type == ReportType.PREDICTION_ACCURACY:
                data = await self._generate_prediction_accuracy_report(parameters)
            elif report_type == ReportType.USER_ANALYTICS:
                data = await self._generate_user_analytics_report(parameters)
            elif report_type == ReportType.TIER_USAGE:
                data = await self._generate_tier_usage_report(parameters)
            elif report_type == ReportType.ANOMALY_DETECTION:
                data = await self._generate_anomaly_report(parameters)
            elif report_type == ReportType.COMPREHENSIVE:
                data = await self._generate_comprehensive_report(parameters)
            else:
                raise ValueError(f"Unknown report type: {report_type}")
            
            # Create report object
            report = GeneratedReport(
                report_id=report_id,
                report_type=report_type,
                generated_at=datetime.now(timezone.utc),
                timeframe=timeframe,
                format=ReportFormat.JSON,  # Default format
                data=data
            )
            
            # Store in history
            self.report_history.append(report)
            
            # Keep only last 100 reports in memory
            if len(self.report_history) > 100:
                self.report_history = self.report_history[-100:]
            
            logger.info(f"✅ Report generated successfully: {report_id}")
            return report
            
        except Exception as e:
            logger.error(f"❌ Failed to generate report {report_type.value}: {e}")
            raise
    
    async def _generate_performance_summary(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate performance summary report"""
        timeframe = parameters.get("timeframe", "24h") if parameters else "24h"
        
        # Get performance report from analytics system
        performance_report = await self.analytics_system.generate_performance_report(timeframe)
        
        # Get dashboard data
        dashboard_data = await self.analytics_system.get_real_time_dashboard_data()
        
        return {
            "summary": {
                "overall_score": performance_report.overall_score,
                "system_health": performance_report.system_health,
                "timeframe": timeframe,
                "generated_at": datetime.now(timezone.utc).isoformat()
            },
            "key_metrics": {
                "total_metrics": len(performance_report.metrics),
                "critical_insights": len([i for i in performance_report.insights if i.severity == "CRITICAL"]),
                "recommendations": len(performance_report.recommendations)
            },
            "performance_data": {
                "metrics": [asdict(m) for m in performance_report.metrics[:10]],  # Top 10 metrics
                "insights": [asdict(i) for i in performance_report.insights[:5]],  # Top 5 insights
                "trends": performance_report.trends,
                "recommendations": performance_report.recommendations[:5]  # Top 5 recommendations
            },
            "dashboard_kpis": dashboard_data.get("kpis", {}),
            "system_status": dashboard_data.get("system_status", {})
        }
    
    async def _generate_system_health_report(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate system health report"""
        # Get current system metrics
        metrics = await self.analytics_system.collect_system_metrics()
        dashboard_data = await self.analytics_system.get_real_time_dashboard_data()
        
        # Analyze system health
        health_score = dashboard_data.get("system_status", {}).get("health_score", 0.0)
        active_alerts = dashboard_data.get("system_status", {}).get("active_alerts", 0)
        
        return {
            "health_overview": {
                "overall_health_score": health_score,
                "status": "HEALTHY" if health_score > 0.8 else "DEGRADED" if health_score > 0.5 else "CRITICAL",
                "active_alerts": active_alerts,
                "last_analysis": dashboard_data.get("system_status", {}).get("last_analysis"),
                "analysis_count": dashboard_data.get("system_status", {}).get("analysis_count", 0)
            },
            "system_metrics": {
                "total_metrics_collected": len(metrics),
                "metrics_summary": [
                    {
                        "name": m.name,
                        "value": m.value,
                        "status": m.status,
                        "threshold": m.threshold
                    } for m in metrics[:15]  # Top 15 metrics
                ]
            },
            "performance_indicators": dashboard_data.get("kpis", {}),
            "recommendations": [
                "Monitor system health score trends",
                "Address any critical alerts immediately",
                "Review performance metrics regularly",
                "Ensure adequate system resources"
            ]
        }
    
    async def _generate_prediction_accuracy_report(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate prediction accuracy report"""
        # This would integrate with prediction systems
        # For now, return mock data structure
        return {
            "accuracy_overview": {
                "overall_accuracy": 0.847,
                "nba_accuracy": 0.852,
                "wnba_accuracy": 0.841,
                "player_props_accuracy": 0.863,
                "game_predictions_accuracy": 0.831
            },
            "model_performance": {
                "neural_networks": 0.856,
                "ensemble_models": 0.849,
                "quantum_enhanced": 0.871,
                "traditional_ml": 0.834
            },
            "recent_predictions": {
                "total_predictions": 1247,
                "successful_predictions": 1056,
                "failed_predictions": 191,
                "success_rate": 0.847
            },
            "trends": {
                "accuracy_trend": "improving",
                "confidence_trend": "stable",
                "volume_trend": "increasing"
            }
        }
    
    async def _generate_comprehensive_report(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate comprehensive report combining all report types"""
        performance_data = await self._generate_performance_summary(parameters)
        health_data = await self._generate_system_health_report(parameters)
        accuracy_data = await self._generate_prediction_accuracy_report(parameters)
        
        return {
            "executive_summary": {
                "overall_system_score": performance_data["summary"]["overall_score"],
                "health_status": health_data["health_overview"]["status"],
                "prediction_accuracy": accuracy_data["accuracy_overview"]["overall_accuracy"],
                "generated_at": datetime.now(timezone.utc).isoformat()
            },
            "performance_analytics": performance_data,
            "system_health": health_data,
            "prediction_performance": accuracy_data,
            "key_insights": [
                "System performance is within acceptable parameters",
                "Prediction accuracy continues to improve",
                "No critical system issues detected",
                "User engagement metrics are positive"
            ],
            "action_items": [
                "Continue monitoring system health metrics",
                "Optimize prediction model performance",
                "Review user tier usage patterns",
                "Plan capacity scaling for growth"
            ]
        }
    
    async def _generate_user_analytics_report(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate user analytics report"""
        # Mock user analytics data
        return {
            "user_metrics": {
                "total_users": 15847,
                "active_users_24h": 3421,
                "new_registrations": 127,
                "tier_distribution": {
                    "free": 12456,
                    "pro": 2891,
                    "enterprise": 500
                }
            },
            "engagement": {
                "avg_session_duration": "24.3 minutes",
                "api_calls_24h": 89234,
                "feature_usage": {
                    "predictions": 15678,
                    "analytics": 8934,
                    "reports": 2341
                }
            }
        }
    
    async def _generate_tier_usage_report(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate tier usage report"""
        # Mock tier usage data
        return {
            "tier_analytics": {
                "conversion_rates": {
                    "free_to_pro": 0.087,
                    "pro_to_enterprise": 0.156
                },
                "usage_by_tier": {
                    "free": {"api_calls": 45123, "features_used": 3},
                    "pro": {"api_calls": 34567, "features_used": 8},
                    "enterprise": {"api_calls": 9544, "features_used": 15}
                },
                "revenue_metrics": {
                    "mrr": 47850.00,
                    "arr": 574200.00,
                    "churn_rate": 0.034
                }
            }
        }
    
    async def _generate_anomaly_report(self, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate anomaly detection report"""
        # Get anomalies from analytics system
        metrics = await self.analytics_system.collect_system_metrics()
        anomalies = await self.analytics_system.detect_anomalies(metrics)
        
        return {
            "anomaly_summary": {
                "total_anomalies": len(anomalies),
                "critical_anomalies": len([a for a in anomalies if a.severity == "CRITICAL"]),
                "resolved_anomalies": 0,  # Would track resolved anomalies
                "detection_accuracy": 0.923
            },
            "detected_anomalies": [asdict(a) for a in anomalies[:10]],  # Top 10 anomalies
            "patterns": {
                "most_common_type": "performance_degradation",
                "peak_detection_time": "14:30 UTC",
                "affected_systems": ["prediction_engine", "analytics_system"]
            }
        }
    
    async def start_scheduler(self):
        """Start the automated reporting scheduler"""
        if self.is_running:
            logger.warning("📊 Reporting scheduler is already running")
            return
        
        self.is_running = True
        logger.info("🚀 Starting automated reporting scheduler")
        
        while self.is_running:
            try:
                await self._process_scheduled_reports()
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"❌ Scheduler error: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying
    
    async def _process_scheduled_reports(self):
        """Process scheduled reports that are due"""
        now = datetime.now(timezone.utc)
        
        for schedule in self.schedules.values():
            if not schedule.enabled:
                continue
                
            if schedule.next_run and now >= schedule.next_run:
                try:
                    logger.info(f"📊 Processing scheduled report: {schedule.schedule_id}")
                    
                    # Generate report
                    report = await self.generate_report(schedule.report_type, schedule.parameters)
                    
                    # Deliver report
                    await self._deliver_report(report, schedule)
                    
                    # Update schedule
                    schedule.last_run = now
                    schedule.next_run = self._calculate_next_run(schedule)
                    
                    logger.info(f"✅ Scheduled report completed: {schedule.schedule_id}")
                    
                except Exception as e:
                    logger.error(f"❌ Failed to process scheduled report {schedule.schedule_id}: {e}")
    
    async def _deliver_report(self, report: GeneratedReport, schedule: ReportSchedule):
        """Deliver report to recipients"""
        try:
            if schedule.format == ReportFormat.EMAIL:
                await self._send_email_report(report, schedule.recipients)
            elif schedule.format == ReportFormat.JSON:
                await self._save_json_report(report)
            elif schedule.format == ReportFormat.HTML:
                await self._save_html_report(report)
            elif schedule.format == ReportFormat.CSV:
                await self._save_csv_report(report)
            
            logger.info(f"📧 Report delivered: {report.report_id} to {len(schedule.recipients)} recipients")
            
        except Exception as e:
            logger.error(f"❌ Failed to deliver report {report.report_id}: {e}")
    
    async def _send_email_report(self, report: GeneratedReport, recipients: List[str]):
        """Send report via email"""
        for recipient in recipients:
            notification_request = NotificationRequest(
                user_id=recipient,
                template_id="analytics_report",
                notification_type=NotificationType.EMAIL,
                variables={
                    "report_type": report.report_type.value,
                    "report_id": report.report_id,
                    "generated_at": report.generated_at.strftime("%Y-%m-%d %H:%M UTC"),
                    "summary": json.dumps(report.data, indent=2)[:1000] + "..."  # Truncated summary
                },
                priority=NotificationPriority.NORMAL
            )
            
            await notification_service.send_notification(notification_request)
    
    async def _save_json_report(self, report: GeneratedReport):
        """Save report as JSON file"""
        file_path = self.reports_directory / f"{report.report_id}.json"
        
        with open(file_path, 'w') as f:
            json.dump({
                "report_metadata": {
                    "report_id": report.report_id,
                    "report_type": report.report_type.value,
                    "generated_at": report.generated_at.isoformat(),
                    "timeframe": report.timeframe
                },
                "data": report.data
            }, f, indent=2)
        
        report.file_path = str(file_path)
        report.size_bytes = file_path.stat().st_size
    
    async def _save_html_report(self, report: GeneratedReport):
        """Save report as HTML file"""
        # Basic HTML template - could be enhanced with proper templating
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>HYPER MEDUSA Report - {report.report_type.value}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background: #1a1a1a; color: white; padding: 20px; }}
                .content {{ padding: 20px; }}
                .metric {{ margin: 10px 0; padding: 10px; background: #f5f5f5; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🧠 HYPER MEDUSA NEURAL VAULT</h1>
                <h2>{report.report_type.value.replace('_', ' ').title()} Report</h2>
                <p>Generated: {report.generated_at.strftime('%Y-%m-%d %H:%M UTC')}</p>
            </div>
            <div class="content">
                <pre>{json.dumps(report.data, indent=2)}</pre>
            </div>
        </body>
        </html>
        """
        
        file_path = self.reports_directory / f"{report.report_id}.html"
        
        with open(file_path, 'w') as f:
            f.write(html_content)
        
        report.file_path = str(file_path)
        report.size_bytes = file_path.stat().st_size
    
    async def _save_csv_report(self, report: GeneratedReport):
        """Save report as CSV file"""
        # Basic CSV export - could be enhanced based on data structure
        
        file_path = self.reports_directory / f"{report.report_id}.csv"
        
        with open(file_path, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(["Report ID", "Type", "Generated At", "Data"])
            writer.writerow([
                report.report_id,
                report.report_type.value,
                report.generated_at.isoformat(),
                json.dumps(report.data)
            ])
        
        report.file_path = str(file_path)
        report.size_bytes = file_path.stat().st_size
    
    def stop_scheduler(self):
        """Stop the automated reporting scheduler"""
        self.is_running = False
        logger.info("🛑 Automated reporting scheduler stopped")
    
    def add_schedule(self, schedule: ReportSchedule):
        """Add a new report schedule"""
        schedule.next_run = self._calculate_next_run(schedule)
        self.schedules[schedule.schedule_id] = schedule
        logger.info(f"📅 Added report schedule: {schedule.schedule_id}")
    
    def remove_schedule(self, schedule_id: str):
        """Remove a report schedule"""
        if schedule_id in self.schedules:
            del self.schedules[schedule_id]
            logger.info(f"🗑️ Removed report schedule: {schedule_id}")
    
    def get_schedules(self) -> List[ReportSchedule]:
        """Get all report schedules"""
        return list(self.schedules.values())
    
    def get_report_history(self, limit: int = 50) -> List[GeneratedReport]:
        """Get recent report history"""
        return self.report_history[-limit:]

# Global reporting service instance
reporting_service = AutomatedReportingService()
