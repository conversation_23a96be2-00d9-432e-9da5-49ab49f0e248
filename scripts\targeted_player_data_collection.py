import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
import requests
import time
import json
from datetime import datetime
import os


#!/usr/bin/env python3
"""
Targeted Player Data Collection
Focus specifically on individual player statistics for player props ML training
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TargetedPlayerDataCollector:
    """Collect individual player data specifically for player props"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }
        
    def analyze_current_player_data_gaps(self) -> Dict[str, Any]:
        """Analyze what player data we're missing"""
        logger.info("🔍 ANALYZING CURRENT PLAYER DATA GAPS")
        logger.info("=" * 45)
        
        conn = sqlite3.connect(self.db_path)
        
        # Current player data breakdown
        player_data_query = """
        SELECT 
            league_name,
            data_category,
            COUNT(*) as record_count,
            COUNT(DISTINCT player_name) as unique_players
        FROM unified_nba_wnba_data
        WHERE player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None' 
        AND player_name != 'TEAM_RECORD' 
        AND player_name != 'NEEDS_EXTRACTION'
        GROUP BY league_name, data_category
        ORDER BY league_name, record_count DESC
        """
        
        current_data = pd.read_sql_query(player_data_query, conn)
        
        # Check for specific player prop categories
        prop_categories_query = """
        SELECT 
            league_name,
            stat_category,
            COUNT(*) as record_count,
            COUNT(DISTINCT player_name) as unique_players,
            AVG(CAST(stat_value AS FLOAT)) as avg_value
        FROM unified_nba_wnba_data
        WHERE player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None' 
        AND player_name != 'TEAM_RECORD' 
        AND player_name != 'NEEDS_EXTRACTION'
        AND stat_category IN ('points', 'rebounds', 'assists', 'field_goal_percentage', 'steals', 'blocks')
        GROUP BY league_name, stat_category
        ORDER BY league_name, record_count DESC
        """
        
        prop_data = pd.read_sql_query(prop_categories_query, conn)
        
        # Get list of players we have data for
        players_query = """
        SELECT 
            league_name,
            player_name,
            COUNT(*) as total_records,
            COUNT(DISTINCT stat_category) as stat_categories,
            MIN(season) as first_season,
            MAX(season) as last_season
        FROM unified_nba_wnba_data
        WHERE player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None' 
        AND player_name != 'TEAM_RECORD' 
        AND player_name != 'NEEDS_EXTRACTION'
        GROUP BY league_name, player_name
        HAVING COUNT(*) >= 10
        ORDER BY league_name, total_records DESC
        LIMIT 50
        """
        
        top_players = pd.read_sql_query(players_query, conn)
        
        conn.close()
        
        # Log analysis
        logger.info("📊 CURRENT PLAYER DATA BY CATEGORY:")
        for _, row in current_data.head(10).iterrows():
            logger.info(f"   {row['league_name']} | {row['data_category']}: {row['record_count']:,} records, {row['unique_players']} players")
        
        logger.info("\n📊 PLAYER PROP CATEGORIES AVAILABLE:")
        for _, row in prop_data.iterrows():
            logger.info(f"   {row['league_name']} | {row['stat_category']}: {row['record_count']:,} records, {row['unique_players']} players (avg: {row['avg_value']:.1f})")
        
        logger.info("\n👑 TOP PLAYERS BY DATA VOLUME:")
        for _, row in top_players.head(10).iterrows():
            logger.info(f"   {row['league_name']} | {row['player_name']}: {row['total_records']:,} records, {row['stat_categories']} categories ({row['first_season']}-{row['last_season']})")
        
        return {
            'current_data': current_data.to_dict('records'),
            'prop_data': prop_data.to_dict('records'),
            'top_players': top_players.to_dict('records')
        }
    
    def get_player_list_for_collection(self, league: str = 'NBA') -> List[Dict[str, Any]]:
        """Get comprehensive list of players to collect data for"""
        logger.info(f"📋 Getting {league} player list for targeted collection...")
        
        league_id = '00' if league == 'NBA' else '10'
        
        try:
            # Get all players for current season
            url = f"{self.base_url}/commonallplayers"
            params = {
                'LeagueID': league_id,
                'Season': '2023-24',
                'IsOnlyCurrentSeason': '0'  # Get all players, not just current
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            players = []
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                headers = data['resultSets'][0]['headers']
                rows = data['resultSets'][0]['rowSet']
                
                for row in rows:
                    player_dict = dict(zip(headers, row))
                    players.append({
                        'player_id': player_dict.get('PERSON_ID'),
                        'player_name': player_dict.get('DISPLAY_FIRST_LAST'),
                        'team_id': player_dict.get('TEAM_ID'),
                        'team_abbreviation': player_dict.get('TEAM_ABBREVIATION'),
                        'is_active': player_dict.get('ROSTERSTATUS') == 1,
                        'from_year': player_dict.get('FROM_YEAR'),
                        'to_year': player_dict.get('TO_YEAR')
                    })
            
            logger.info(f"✅ Found {len(players)} {league} players for collection")
            return players
            
        except Exception as e:
            logger.error(f"❌ Error getting {league} player list: {e}")
            return []
    
    def collect_player_game_logs(self, player_id: str, player_name: str, league: str, seasons: List[str] = None) -> List[Dict[str, Any]]:
        """Collect individual player game logs - the core data we need"""
        if seasons is None:
            seasons = ['2023-24', '2022-23', '2021-22']  # Last 3 seasons
        
        all_game_logs = []
        
        for season in seasons:
            try:
                logger.info(f"   📊 Collecting {player_name} game logs for {season}...")
                
                url = f"{self.base_url}/playergamelog"
                params = {
                    'PlayerID': player_id,
                    'Season': season,
                    'SeasonType': 'Regular Season',
                    'LeagueID': '00' if league == 'NBA' else '10'
                }
                
                response = requests.get(url, headers=self.headers, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                
                if 'resultSets' in data and len(data['resultSets']) > 0:
                    headers = data['resultSets'][0]['headers']
                    rows = data['resultSets'][0]['rowSet']
                    
                    for row in rows:
                        game_dict = dict(zip(headers, row))
                        
                        # Extract key player prop stats
                        game_log = {
                            'player_id': player_id,
                            'player_name': player_name,
                            'season': season,
                            'league_name': league,
                            'league_id': '00' if league == 'NBA' else '10',
                            'game_id': game_dict.get('Game_ID'),
                            'game_date': game_dict.get('GAME_DATE'),
                            'matchup': game_dict.get('MATCHUP'),
                            'team_abbreviation': game_dict.get('TEAM_ABBREVIATION'),
                            'points': game_dict.get('PTS'),
                            'rebounds': game_dict.get('REB'),
                            'assists': game_dict.get('AST'),
                            'steals': game_dict.get('STL'),
                            'blocks': game_dict.get('BLK'),
                            'turnovers': game_dict.get('TOV'),
                            'field_goals_made': game_dict.get('FGM'),
                            'field_goals_attempted': game_dict.get('FGA'),
                            'three_pointers_made': game_dict.get('FG3M'),
                            'three_pointers_attempted': game_dict.get('FG3A'),
                            'free_throws_made': game_dict.get('FTM'),
                            'free_throws_attempted': game_dict.get('FTA'),
                            'minutes': game_dict.get('MIN'),
                            'plus_minus': game_dict.get('PLUS_MINUS'),
                            'data_type': 'individual_player_game_log',
                            'data_category': 'player_game_performance',
                            'source_file': f'player_gamelog_{player_id}_{season}.json',
                            'raw_data': json.dumps(game_dict),
                            'created_at': datetime.now().isoformat()
                        }
                        
                        all_game_logs.append(game_log)
                
                time.sleep(0.6)  # Rate limiting
                
            except Exception as e:
                logger.warning(f"⚠️ Error collecting {player_name} {season} game logs: {e}")
                continue
        
        logger.info(f"✅ Collected {len(all_game_logs)} game logs for {player_name}")
        return all_game_logs
    
    def collect_player_season_stats(self, player_id: str, player_name: str, league: str) -> List[Dict[str, Any]]:
        """Collect player season averages and totals"""
        season_stats = []
        
        try:
            url = f"{self.base_url}/playercareerstats"
            params = {
                'PlayerID': player_id,
                'LeagueID': '00' if league == 'NBA' else '10'
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                headers = data['resultSets'][0]['headers']
                rows = data['resultSets'][0]['rowSet']
                
                for row in rows:
                    season_dict = dict(zip(headers, row))
                    
                    season_stat = {
                        'player_id': player_id,
                        'player_name': player_name,
                        'season': season_dict.get('SEASON_ID'),
                        'league_name': league,
                        'league_id': '00' if league == 'NBA' else '10',
                        'team_abbreviation': season_dict.get('TEAM_ABBREVIATION'),
                        'games_played': season_dict.get('GP'),
                        'games_started': season_dict.get('GS'),
                        'minutes_per_game': season_dict.get('MIN'),
                        'points_per_game': season_dict.get('PTS'),
                        'rebounds_per_game': season_dict.get('REB'),
                        'assists_per_game': season_dict.get('AST'),
                        'steals_per_game': season_dict.get('STL'),
                        'blocks_per_game': season_dict.get('BLK'),
                        'field_goal_percentage': season_dict.get('FG_PCT'),
                        'three_point_percentage': season_dict.get('FG3_PCT'),
                        'free_throw_percentage': season_dict.get('FT_PCT'),
                        'data_type': 'player_season_averages',
                        'data_category': 'player_season_stats',
                        'source_file': f'player_career_{player_id}.json',
                        'raw_data': json.dumps(season_dict),
                        'created_at': datetime.now().isoformat()
                    }
                    
                    season_stats.append(season_stat)
        
        except Exception as e:
            logger.warning(f"⚠️ Error collecting {player_name} season stats: {e}")
        
        return season_stats
    
    def insert_player_data(self, player_data: List[Dict[str, Any]]) -> int:
        """Insert player data into database"""
        if not player_data:
            return 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        
        for record in player_data:
            try:
                # Create individual records for each stat
                stats_to_insert = []
                
                if record.get('data_type') == 'individual_player_game_log':
                    # Create separate records for each stat category
                    base_record = {
                        'source_file': record['source_file'],
                        'source_table': 'player_game_logs',
                        'data_category': record['data_category'],
                        'season': record['season'],
                        'league_id': record['league_id'],
                        'league_name': record['league_name'],
                        'season_type': 'Regular Season',
                        'player_id': record['player_id'],
                        'player_name': record['player_name'],
                        'team_abbreviation': record['team_abbreviation'],
                        'game_id': record['game_id'],
                        'game_date': record['game_date'],
                        'data_type': record['data_type'],
                        'raw_data': record['raw_data'],
                        'created_at': record['created_at']
                    }
                    
                    # Individual stat records
                    stat_mappings = {
                        'points': record.get('points'),
                        'rebounds': record.get('rebounds'),
                        'assists': record.get('assists'),
                        'steals': record.get('steals'),
                        'blocks': record.get('blocks'),
                        'three_pointers_made': record.get('three_pointers_made'),
                        'field_goal_percentage': (record.get('field_goals_made', 0) / record.get('field_goals_attempted', 1)) if record.get('field_goals_attempted', 0) > 0 else 0
                    }
                    
                    for stat_category, stat_value in stat_mappings.items():
                        if stat_value is not None:
                            stat_record = base_record.copy()
                            stat_record['stat_category'] = stat_category
                            stat_record['stat_value'] = stat_value
                            stats_to_insert.append(stat_record)
                
                elif record.get('data_type') == 'player_season_averages':
                    # Season averages
                    base_record = {
                        'source_file': record['source_file'],
                        'source_table': 'player_season_stats',
                        'data_category': record['data_category'],
                        'season': record['season'],
                        'league_id': record['league_id'],
                        'league_name': record['league_name'],
                        'season_type': 'Regular Season',
                        'player_id': record['player_id'],
                        'player_name': record['player_name'],
                        'team_abbreviation': record['team_abbreviation'],
                        'data_type': record['data_type'],
                        'raw_data': record['raw_data'],
                        'created_at': record['created_at']
                    }
                    
                    stat_mappings = {
                        'points_per_game': record.get('points_per_game'),
                        'rebounds_per_game': record.get('rebounds_per_game'),
                        'assists_per_game': record.get('assists_per_game'),
                        'steals_per_game': record.get('steals_per_game'),
                        'blocks_per_game': record.get('blocks_per_game'),
                        'field_goal_percentage': record.get('field_goal_percentage'),
                        'three_point_percentage': record.get('three_point_percentage'),
                        'games_played': record.get('games_played')
                    }
                    
                    for stat_category, stat_value in stat_mappings.items():
                        if stat_value is not None:
                            stat_record = base_record.copy()
                            stat_record['stat_category'] = stat_category
                            stat_record['stat_value'] = stat_value
                            stats_to_insert.append(stat_record)
                
                # Insert all stat records
                for stat_record in stats_to_insert:
                    cursor.execute("""
                        INSERT INTO unified_nba_wnba_data (
                            source_file, source_table, data_category, season, league_id, league_name,
                            season_type, player_id, player_name, team_abbreviation, game_id, game_date,
                            stat_category, stat_value, data_type, raw_data, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        stat_record['source_file'], stat_record['source_table'], stat_record['data_category'],
                        stat_record['season'], stat_record['league_id'], stat_record['league_name'],
                        stat_record['season_type'], stat_record['player_id'], stat_record['player_name'],
                        stat_record['team_abbreviation'], stat_record.get('game_id'), stat_record.get('game_date'),
                        stat_record['stat_category'], stat_record['stat_value'], stat_record['data_type'],
                        stat_record['raw_data'], stat_record['created_at']
                    ))
                    inserted_count += 1
                    
            except Exception as e:
                logger.warning(f"⚠️ Error inserting record: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        return inserted_count
    
    def run_targeted_collection(self, league: str = 'WNBA', max_players: int = None) -> Dict[str, Any]:
        """Run targeted player data collection"""
        logger.info(f"🚀 STARTING TARGETED {league} PLAYER DATA COLLECTION")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        # Step 1: Analyze current gaps
        gaps_analysis = self.analyze_current_player_data_gaps()
        
        # Step 2: Get player list
        players = self.get_player_list_for_collection(league)
        
        if not players:
            return {'success': False, 'error': f'No {league} players found'}
        
        # Limit players for initial collection
        if len(players) > max_players:
            players = players[:max_players]
            logger.info(f"📊 Processing first {max_players} players for targeted collection")
        
        # Step 3: Collect individual player data
        total_records_collected = 0
        players_processed = 0
        
        for i, player in enumerate(players):
            player_id = player['player_id']
            player_name = player['player_name']
            
            if not player_id or not player_name:
                continue
            
            logger.info(f"📊 Processing {player_name} ({i+1}/{len(players)})...")
            
            # Collect game logs (most important for player props)
            game_logs = self.collect_player_game_logs(player_id, player_name, league)
            
            # Collect season stats
            season_stats = self.collect_player_season_stats(player_id, player_name, league)
            
            # Insert data
            all_player_data = game_logs + season_stats
            inserted_count = self.insert_player_data(all_player_data)
            
            total_records_collected += inserted_count
            players_processed += 1
            
            logger.info(f"   ✅ {player_name}: {inserted_count:,} records inserted")
            
            # Progress update
            if (i + 1) % 10 == 0:
                logger.info(f"   📈 Progress: {i+1}/{len(players)} players, {total_records_collected:,} records collected")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎯 TARGETED {league} COLLECTION COMPLETE!")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   👥 Players processed: {players_processed}")
        logger.info(f"   📊 Total records collected: {total_records_collected:,}")
        
        return {
            'success': True,
            'league': league,
            'duration': str(duration),
            'players_processed': players_processed,
            'total_records': total_records_collected,
            'gaps_analysis': gaps_analysis
        }

def main():
    """Run targeted player data collection"""
    
    collector = TargetedPlayerDataCollector()
    
    # Start with WNBA since it has lower player data percentage
    results = collector.run_targeted_collection(league='WNBA', max_players=25)
    
    if results['success']:
        
        
    else:
    
    return results

if __name__ == "__main__":
    main()
