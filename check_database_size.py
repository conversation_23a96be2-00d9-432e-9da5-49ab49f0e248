import sqlite3
import os

#!/usr/bin/env python3
"""
Database Size and Structure Analysis
"""


def analyze_database():
    db_path = 'hyper_medusa_consolidated.db'
    
    if not os.path.exists(db_path):
        return
    
    # Get database size
    db_size_mb = os.path.getsize(db_path) / (1024*1024)
    
    # Connect and analyze tables
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    
    
    total_records = 0
    for table in sorted(tables):
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            total_records += count
        except Exception as e:
    
    
    # Check for indexes
    for table in sorted(tables):
        cursor.execute(f"PRAGMA index_list({table})")
        indexes = cursor.fetchall()
        if indexes:
            for idx in indexes:
    
    conn.close()

if __name__ == "__main__":
    analyze_database()
