#!/usr/bin/env python3
"""
Test script for UnifiedModelForge implementation
"""

import asyncio
import logging
from src.models.unified_model_forge import UnifiedModelForge, ModelArchetype, ModelForgeConfig

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_unified_model_forge():
    """Test the UnifiedModelForge implementation"""
    logger.info("🔥 Testing UnifiedModelForge implementation")
    
    try:
        # Create forge instance
        config = ModelForgeConfig(
            archetype=ModelArchetype.BASKETBALL_SPECIALIST,
            enable_quantum_features=True,
            basketball_awareness=True
        )
        
        forge = UnifiedModelForge(config)
        logger.info("✅ UnifiedModelForge created successfully")
        
        # Test model creation
        logger.info("🏀 Testing NBA model creation...")
        nba_result = await forge.train_and_save_nba_model()
        logger.info(f"✅ NBA model created: {nba_result.model_id}")
        logger.info(f"📊 NBA model accuracy: {nba_result.performance_metrics.get('accuracy', 0):.3f}")
        
        # Test WNBA model creation
        logger.info("🏀 Testing WNBA model creation...")
        wnba_result = await forge.train_and_save_wnba_model()
        logger.info(f"✅ WNBA model created: {wnba_result.model_id}")
        logger.info(f"📊 WNBA model accuracy: {wnba_result.performance_metrics.get('accuracy', 0):.3f}")
        
        # Test model listing
        models = forge.list_models()
        logger.info(f"📋 Total models created: {len(models)}")
        
        # Test performance summary
        summary = forge.get_performance_summary()
        logger.info(f"📈 Performance summary: {summary}")
        
        logger.info("🎉 UnifiedModelForge test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"🚨 UnifiedModelForge test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_unified_model_forge())
    if success:
        print("✅ UnifiedModelForge implementation test PASSED")
    else:
        print("❌ UnifiedModelForge implementation test FAILED")
