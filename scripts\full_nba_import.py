import os
import sys
from comprehensive_nba_import import ComprehensiveNBAImporter
import logging
        import sqlite3

#!/usr/bin/env python3
"""
Full NBA Import - Complete all 834 files
Import all remaining NBA files to complete the dataset
"""

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_full_nba_import():
    """Run the complete NBA import for all files"""
    
    importer = ComprehensiveNBAImporter()
    
    # Run full import (no file limit)
    results = importer.run_comprehensive_import(max_files=None)
    
    if results['success']:
        
        if results['files_failed'] > 0:
            for file_path, error in results['failed_files'][:10]:  # Show first 10 failures
        
        # Check final database size
        conn = sqlite3.connect("medusa_master.db")
        cursor = conn.cursor()
        
        # NBA records
        cursor.execute("SELECT COUNT(*) FROM unified_nba_wnba_data WHERE league_name = 'NBA'")
        nba_count = cursor.fetchone()[0]
        
        # WNBA records  
        cursor.execute("SELECT COUNT(*) FROM unified_nba_wnba_data WHERE league_name = 'WNBA'")
        wnba_count = cursor.fetchone()[0]
        
        # Total records
        cursor.execute("SELECT COUNT(*) FROM unified_nba_wnba_data")
        total_count = cursor.fetchone()[0]
        
        conn.close()
        
        
        # Assessment
        if nba_count > 100000:
        elif nba_count > 50000:
        else:
        
        
    else:
    
    return results

if __name__ == "__main__":
    run_full_nba_import()
