#!/usr/bin/env python3
"""
Test the improved target generation for player props
"""

import sys
import asyncio
sys.path.append('.')

from src.neural_cortex.player_props_neural_pipeline import PlayerPropsTrainingPipeline, PlayerPropsConfig

async def test_target_quality():
    """Test the quality of generated targets"""

    print("🔧 Testing improved target generation...")

    # Test with points model
    config = PlayerPropsConfig(
        prop_type='points',
        league='WNBA',
        num_epochs=3,  # Quick test
        batch_size=64,
        learning_rate=0.001
    )

    pipeline = PlayerPropsTrainingPipeline(config)

    # Quick training test to see improved metrics
    print("🚀 Running quick training test with improved targets...")
    results = await pipeline.train()

    print(f"\n✅ Improved Results:")
    print(f"   MAE: {results['test_metrics']['mae']:.3f}")
    print(f"   R²: {results['test_metrics']['r2']:.3f}")
    print(f"   Accuracy (±1 point): {results['test_metrics']['accuracy_1pt']:.1f}%")
    print(f"   Accuracy (±2 points): {results['test_metrics']['accuracy_2pt']:.1f}%")
    print(f"   Accuracy (±25%): {results['test_metrics']['accuracy_25pct']:.1f}%")

    print(f"\n📊 Target Statistics:")
    print(f"   Mean prediction: {results['test_metrics']['mean_prediction']:.2f}")
    print(f"   Mean target: {results['test_metrics']['mean_target']:.2f}")
    print(f"   Median absolute error: {results['test_metrics']['median_abs_error']:.2f}")

    # Compare with old strict accuracy
    print(f"\n📈 Accuracy Comparison:")
    print(f"   Old metric (±10%): {results['test_metrics']['accuracy_10pct']:.1f}% - Too strict!")
    print(f"   Better metric (±25%): {results['test_metrics']['accuracy_25pct']:.1f}% - Realistic")
    print(f"   Practical metric (±2 pts): {results['test_metrics']['accuracy_2pt']:.1f}% - Most useful")

if __name__ == "__main__":
    asyncio.run(test_target_quality())
