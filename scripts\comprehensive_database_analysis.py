import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
from collections import defaultdict, Counter
import os
import glob

#!/usr/bin/env python3
"""
Comprehensive Database Analysis
Analyze our complete database to understand what data we have and what 10-year coverage we need
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveDatabaseAnalyzer:
    """Analyze our complete database and identify 10-year data gaps"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def get_complete_database_summary(self) -> Dict[str, Any]:
        """Get comprehensive summary of ALL data in our database"""
        logger.info("📊 COMPREHENSIVE DATABASE ANALYSIS")
        logger.info("=" * 50)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Total records
            total_records = conn.execute("SELECT COUNT(*) FROM unified_nba_wnba_data").fetchone()[0]
            logger.info(f"📊 TOTAL RECORDS: {total_records:,}")
            
            # Records by league
            league_breakdown = conn.execute("""
                SELECT 
                    COALESCE(league_name, 'Unknown') as league,
                    COUNT(*) as count 
                FROM unified_nba_wnba_data 
                GROUP BY league_name 
                ORDER BY count DESC
            """).fetchall()
            
            logger.info(f"\n🏀 LEAGUE BREAKDOWN:")
            for league, count in league_breakdown:
                logger.info(f"   {league}: {count:,} records")
            
            # Records by season
            season_breakdown = conn.execute("""
                SELECT 
                    COALESCE(season, 'Unknown') as season,
                    COUNT(*) as count 
                FROM unified_nba_wnba_data 
                GROUP BY season 
                ORDER BY season DESC
            """).fetchall()
            
            logger.info(f"\n📅 SEASON BREAKDOWN:")
            for season, count in season_breakdown:
                logger.info(f"   {season}: {count:,} records")
            
            # Records by data category
            category_breakdown = conn.execute("""
                SELECT 
                    COALESCE(data_category, 'Unknown') as category,
                    COUNT(*) as count 
                FROM unified_nba_wnba_data 
                GROUP BY data_category 
                ORDER BY count DESC
                LIMIT 20
            """).fetchall()
            
            logger.info(f"\n📈 DATA CATEGORY BREAKDOWN (Top 20):")
            for category, count in category_breakdown:
                logger.info(f"   {category}: {count:,} records")
            
            # Records by source table (endpoints)
            endpoint_breakdown = conn.execute("""
                SELECT 
                    COALESCE(source_table, 'Unknown') as endpoint,
                    COUNT(*) as count 
                FROM unified_nba_wnba_data 
                GROUP BY source_table 
                ORDER BY count DESC
                LIMIT 20
            """).fetchall()
            
            logger.info(f"\n🔗 ENDPOINT BREAKDOWN (Top 20):")
            for endpoint, count in endpoint_breakdown:
                logger.info(f"   {endpoint}: {count:,} records")
            
            # Records by data type
            data_type_breakdown = conn.execute("""
                SELECT 
                    COALESCE(data_type, 'Unknown') as data_type,
                    COUNT(*) as count 
                FROM unified_nba_wnba_data 
                GROUP BY data_type 
                ORDER BY count DESC
                LIMIT 15
            """).fetchall()
            
            logger.info(f"\n📋 DATA TYPE BREAKDOWN (Top 15):")
            for data_type, count in data_type_breakdown:
                logger.info(f"   {data_type}: {count:,} records")
            
            conn.close()
            
            return {
                'total_records': total_records,
                'league_breakdown': league_breakdown,
                'season_breakdown': season_breakdown,
                'category_breakdown': category_breakdown,
                'endpoint_breakdown': endpoint_breakdown,
                'data_type_breakdown': data_type_breakdown
            }
            
        except Exception as e:
            logger.error(f"❌ Error analyzing database: {e}")
            return {'error': str(e)}
    
    def analyze_10_year_coverage(self) -> Dict[str, Any]:
        """Analyze what 10-year data coverage we have and what's missing"""
        logger.info("\n🎯 10-YEAR DATA COVERAGE ANALYSIS")
        logger.info("=" * 40)
        
        # Define 10-year range
        nba_seasons = [
            '2024-25', '2023-24', '2022-23', '2021-22', '2020-21',
            '2019-20', '2018-19', '2017-18', '2016-17', '2015-16'
        ]
        
        wnba_seasons = [
            '2025', '2024', '2023', '2022', '2021',
            '2020', '2019', '2018', '2017', '2016'
        ]
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Check NBA season coverage by endpoint
            logger.info(f"\n📊 NBA 10-YEAR COVERAGE BY ENDPOINT:")
            nba_coverage = {}
            
            for endpoint_row in conn.execute("""
                SELECT DISTINCT source_table 
                FROM unified_nba_wnba_data 
                WHERE league_name = 'NBA' AND source_table IS NOT NULL
                ORDER BY source_table
            """).fetchall():
                endpoint = endpoint_row[0]
                
                seasons_with_data = conn.execute("""
                    SELECT DISTINCT season 
                    FROM unified_nba_wnba_data 
                    WHERE league_name = 'NBA' AND source_table = ? AND season IS NOT NULL
                    ORDER BY season DESC
                """, (endpoint,)).fetchall()
                
                seasons_list = [s[0] for s in seasons_with_data if s[0] in nba_seasons]
                coverage_pct = (len(seasons_list) / len(nba_seasons)) * 100
                
                nba_coverage[endpoint] = {
                    'seasons': seasons_list,
                    'coverage': len(seasons_list),
                    'coverage_pct': coverage_pct,
                    'missing': [s for s in nba_seasons if s not in seasons_list]
                }
                
                logger.info(f"   {endpoint}: {len(seasons_list)}/10 seasons ({coverage_pct:.1f}%)")
            
            # Check WNBA season coverage by endpoint
            logger.info(f"\n📊 WNBA 10-YEAR COVERAGE BY ENDPOINT:")
            wnba_coverage = {}
            
            for endpoint_row in conn.execute("""
                SELECT DISTINCT source_table 
                FROM unified_nba_wnba_data 
                WHERE league_name = 'WNBA' AND source_table IS NOT NULL
                ORDER BY source_table
            """).fetchall():
                endpoint = endpoint_row[0]
                
                seasons_with_data = conn.execute("""
                    SELECT DISTINCT season 
                    FROM unified_nba_wnba_data 
                    WHERE league_name = 'WNBA' AND source_table = ? AND season IS NOT NULL
                    ORDER BY season DESC
                """, (endpoint,)).fetchall()
                
                seasons_list = [s[0] for s in seasons_with_data if s[0] in wnba_seasons]
                coverage_pct = (len(seasons_list) / len(wnba_seasons)) * 100
                
                wnba_coverage[endpoint] = {
                    'seasons': seasons_list,
                    'coverage': len(seasons_list),
                    'coverage_pct': coverage_pct,
                    'missing': [s for s in wnba_seasons if s not in seasons_list]
                }
                
                logger.info(f"   {endpoint}: {len(seasons_list)}/10 seasons ({coverage_pct:.1f}%)")
            
            conn.close()
            
            return {
                'nba_seasons': nba_seasons,
                'wnba_seasons': wnba_seasons,
                'nba_coverage': nba_coverage,
                'wnba_coverage': wnba_coverage
            }
            
        except Exception as e:
            logger.error(f"❌ Error analyzing 10-year coverage: {e}")
            return {'error': str(e)}
    
    def identify_successful_endpoints(self) -> Dict[str, Any]:
        """Identify which endpoints have been successful and should get 10-year coverage"""
        logger.info("\n🎯 SUCCESSFUL ENDPOINTS ANALYSIS")
        logger.info("=" * 35)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get endpoints with significant data
            successful_endpoints = conn.execute("""
                SELECT 
                    source_table,
                    league_name,
                    COUNT(*) as record_count,
                    COUNT(DISTINCT season) as season_count,
                    MIN(season) as earliest_season,
                    MAX(season) as latest_season
                FROM unified_nba_wnba_data 
                WHERE source_table IS NOT NULL 
                GROUP BY source_table, league_name
                HAVING COUNT(*) >= 10  -- Only endpoints with significant data
                ORDER BY record_count DESC
            """).fetchall()
            
            logger.info(f"\n✅ SUCCESSFUL ENDPOINTS (10+ records):")
            
            high_value_endpoints = []
            medium_value_endpoints = []
            
            for endpoint, league, count, seasons, earliest, latest in successful_endpoints:
                logger.info(f"   {endpoint} ({league}): {count:,} records, {seasons} seasons ({earliest} to {latest})")
                
                if count >= 100:  # High-value endpoints
                    high_value_endpoints.append({
                        'endpoint': endpoint,
                        'league': league,
                        'records': count,
                        'seasons': seasons,
                        'priority': 'HIGH'
                    })
                elif count >= 10:  # Medium-value endpoints
                    medium_value_endpoints.append({
                        'endpoint': endpoint,
                        'league': league,
                        'records': count,
                        'seasons': seasons,
                        'priority': 'MEDIUM'
                    })
            
            logger.info(f"\n🎯 PRIORITIZATION:")
            logger.info(f"   HIGH PRIORITY (100+ records): {len(high_value_endpoints)} endpoints")
            logger.info(f"   MEDIUM PRIORITY (10-99 records): {len(medium_value_endpoints)} endpoints")
            
            conn.close()
            
            return {
                'high_value_endpoints': high_value_endpoints,
                'medium_value_endpoints': medium_value_endpoints,
                'all_successful_endpoints': successful_endpoints
            }
            
        except Exception as e:
            logger.error(f"❌ Error identifying successful endpoints: {e}")
            return {'error': str(e)}
    
    def find_empty_files(self) -> List[str]:
        """Find and identify empty files for removal"""
        logger.info("\n🗑️ EMPTY FILES ANALYSIS")
        logger.info("=" * 25)
        
        empty_files = []
        
        # Check for empty Python files
        for py_file in glob.glob("**/*.py", recursive=True):
            try:
                if os.path.getsize(py_file) == 0:
                    empty_files.append(py_file)
                    logger.info(f"   📄 Empty Python file: {py_file}")
            except OSError:
                continue
        
        # Check for empty JSON files
        for json_file in glob.glob("**/*.json", recursive=True):
            try:
                if os.path.getsize(json_file) == 0:
                    empty_files.append(json_file)
                    logger.info(f"   📄 Empty JSON file: {json_file}")
            except OSError:
                continue
        
        # Check for empty CSV files
        for csv_file in glob.glob("**/*.csv", recursive=True):
            try:
                if os.path.getsize(csv_file) == 0:
                    empty_files.append(csv_file)
                    logger.info(f"   📄 Empty CSV file: {csv_file}")
            except OSError:
                continue
        
        # Check for empty text files
        for txt_file in glob.glob("**/*.txt", recursive=True):
            try:
                if os.path.getsize(txt_file) == 0:
                    empty_files.append(txt_file)
                    logger.info(f"   📄 Empty text file: {txt_file}")
            except OSError:
                continue
        
        if empty_files:
            logger.info(f"\n🗑️ FOUND {len(empty_files)} EMPTY FILES")
        else:
            logger.info(f"\n✅ NO EMPTY FILES FOUND")
        
        return empty_files

def main():
    """Execute comprehensive database analysis"""
    
    analyzer = ComprehensiveDatabaseAnalyzer()
    
    # Get complete database summary
    db_summary = analyzer.get_complete_database_summary()
    
    # Analyze 10-year coverage
    coverage_analysis = analyzer.analyze_10_year_coverage()
    
    # Identify successful endpoints
    successful_endpoints = analyzer.identify_successful_endpoints()
    
    # Find empty files
    empty_files = analyzer.find_empty_files()
    
    
    if 'total_records' in db_summary:
    
    if 'high_value_endpoints' in successful_endpoints:
    
    
    
    return {
        'database_summary': db_summary,
        'coverage_analysis': coverage_analysis,
        'successful_endpoints': successful_endpoints,
        'empty_files': empty_files
    }

if __name__ == "__main__":
    main()
