#!/usr/bin/env python3
"""
Test Class Name Fixes

This test validates that the class name mismatch issues are resolved
for FateForge_Expert and OlympianCouncil_Expert.
"""

import logging
import sys

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

def test_fateforge_expert_import():
    """Test that FateForge_Expert can be imported successfully."""
    logger.info("🚀 Testing FateForge_Expert Import")
    
    try:
        # Test the import that was previously failing
        from src.cognitive_spires.FateForge_Expert import FateForge_Expert
        logger.info("✅ Successfully imported FateForge_Expert")
        
        # Test creating an instance
        forge = FateForge_Expert()
        logger.info("✅ Successfully created FateForge_Expert instance")
        
        # Test basic functionality
        status = forge.get_forge_status()
        logger.info(f"✅ get_forge_status() returned: {status}")
        
        # Test model forging
        model = forge.forge_model("test_model")
        logger.info(f"✅ forge_model() returned: {model}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ FateForge_Expert import test failed: {e}")
        return False

def test_olympian_council_expert_import():
    """Test that OlympianCouncil_Expert can be imported successfully."""
    logger.info("🚀 Testing OlympianCouncil_Expert Import")
    
    try:
        # Test the import that was previously failing
        from src.cognitive_spires.OlympianCouncil_Expert import OlympianCouncil_Expert
        logger.info("✅ Successfully imported OlympianCouncil_Expert")
        
        # Test creating an instance
        council = OlympianCouncil_Expert()
        logger.info("✅ Successfully created OlympianCouncil_Expert instance")
        
        # Test basic functionality
        status = council.get_council_status()
        logger.info(f"✅ get_council_status() returned: {status}")
        
        # Test council decision
        test_input = {'test_data': 'sample'}
        decision = council.coordinate_council_decision(test_input)
        logger.info(f"✅ coordinate_council_decision() returned keys: {list(decision.keys())}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ OlympianCouncil_Expert import test failed: {e}")
        return False

def test_cognitive_spires_init():
    """Test that cognitive spires __init__.py can import both classes."""
    logger.info("🚀 Testing Cognitive Spires __init__.py Import")
    
    try:
        # Test importing from the cognitive spires package
        import src.cognitive_spires
        logger.info("✅ Successfully imported src.cognitive_spires")
        
        # Check if the classes are available
        if hasattr(src.cognitive_spires, 'FateForge'):
            logger.info("✅ FateForge available in cognitive spires")
        else:
            logger.warning("⚠️ FateForge not available in cognitive spires")
        
        if hasattr(src.cognitive_spires, 'OlympianCouncil'):
            logger.info("✅ OlympianCouncil available in cognitive spires")
        else:
            logger.warning("⚠️ OlympianCouncil not available in cognitive spires")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Cognitive spires __init__.py test failed: {e}")
        return False

def main():
    """Run all class name fix tests."""
    logger.info("🧪 Starting Class Name Fix Tests")
    
    tests = [
        ("FateForge_Expert Import", test_fateforge_expert_import),
        ("OlympianCouncil_Expert Import", test_olympian_council_expert_import),
        ("Cognitive Spires __init__.py", test_cognitive_spires_init),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        except Exception as e:
            logger.error(f"❌ FAILED: {test_name} - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All class name fix tests PASSED!")
        logger.info("🔧 Infrastructure Issue #9 (Fix Class Name Mismatches) is RESOLVED!")
        return True
    else:
        logger.error(f"💥 {total - passed} tests FAILED!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
