{"database": {"host": "localhost", "port": 5432, "name": "hyper_medusa_vault_dev", "user": "postgres", "password": "dev_password", "pool_size": 10, "max_overflow": 20, "pool_timeout": 30, "pool_recycle": 3600, "echo": true}, "redis": {"host": "localhost", "port": 6379, "db": 1, "password": "Redis_Secure_2025_Tz9qN4pM7kS2vX6tE9wA3sD5fH8jK1oL", "max_connections": 25, "socket_timeout": 30, "socket_connect_timeout": 30}, "api": {"host": "127.0.0.1", "port": 8000, "workers": 1, "timeout": 60, "max_requests": 1000, "max_requests_jitter": 50, "preload_app": true, "reload": true, "debug": true, "cors_origins": ["http://localhost:3000", "http://127.0.0.1:3000", "http://localhost:8080"], "rate_limit": "1000/minute"}, "ml": {"model_path": "models/dev", "batch_size": 16, "learning_rate": 0.01, "epochs": 50, "validation_split": 0.3, "early_stopping_patience": 5, "device": "cpu", "mixed_precision": false, "gradient_clipping": 0.5, "checkpoint_interval": 5}, "logging": {"level": "DEBUG", "format": "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s", "file_path": "logs/development.log", "max_bytes": 5242880, "backup_count": 3, "json_format": false, "structured_logging": true}, "security": {"jwt_algorithm": "HS256", "jwt_expiration": 7200, "password_min_length": 4, "max_login_attempts": 10, "lockout_duration": 300, "encryption_key": null, "cors_enabled": true, "csrf_protection": false}, "monitoring": {"prometheus_enabled": true, "prometheus_port": 9090, "grafana_enabled": true, "grafana_port": 3000, "health_check_interval": 60, "metrics_retention_days": 7, "alerting_enabled": false, "slack_webhook": null, "email_alerts": false}}