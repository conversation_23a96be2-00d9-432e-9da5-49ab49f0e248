#!/usr/bin/env python3
"""
Test script to verify ensemble training data type fixes
"""

import numpy as np
import asyncio
import logging
from ensemble_model_training_infrastructure import (
    EnsembleModelTrainingInfrastructure,
    EnsembleTrainingConfig,
    detect_target_type,
    preprocess_target
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_classification_data():
    """Test with classification data"""
    logger.info("🧪 Testing Classification Data")
    
    # Generate classification data
    np.random.seed(42)
    X = np.random.randn(1000, 10)
    y = np.random.randint(0, 2, 1000)  # Binary classification
    
    # Test target type detection
    target_type = detect_target_type(y)
    logger.info(f"Detected target type: {target_type}")
    
    # Test target preprocessing
    y_processed, preprocessor = preprocess_target(y, target_type)
    logger.info(f"Preprocessed target shape: {y_processed.shape}")
    logger.info(f"Preprocessor: {type(preprocessor).__name__ if preprocessor else 'None'}")
    
    # Test ensemble training
    config = EnsembleTrainingConfig(
        base_models=['random_forest', 'gradient_boosting'],
        meta_model='logistic_regression',
        cv_folds=3,
        random_state=42
    )
    
    ensemble = EnsembleModelTrainingInfrastructure(config)
    
    try:
        result = await ensemble.train_ensemble(X, y)
        logger.info(f"✅ Classification test successful!")
        logger.info(f"Ensemble accuracy: {result.ensemble_performance.accuracy:.3f}")
        return True
    except Exception as e:
        logger.error(f"❌ Classification test failed: {e}")
        return False

async def test_regression_data():
    """Test with regression data"""
    logger.info("🧪 Testing Regression Data")
    
    # Generate regression data
    np.random.seed(42)
    X = np.random.randn(1000, 10)
    y = np.random.randn(1000) * 10 + 5  # Continuous values
    
    # Test target type detection
    target_type = detect_target_type(y)
    logger.info(f"Detected target type: {target_type}")
    
    # Test target preprocessing
    y_processed, preprocessor = preprocess_target(y, target_type)
    logger.info(f"Preprocessed target shape: {y_processed.shape}")
    logger.info(f"Preprocessor: {type(preprocessor).__name__ if preprocessor else 'None'}")
    
    # Test ensemble training
    config = EnsembleTrainingConfig(
        base_models=['random_forest', 'gradient_boosting'],
        meta_model='linear_regression',
        cv_folds=3,
        random_state=42
    )
    
    ensemble = EnsembleModelTrainingInfrastructure(config)
    
    try:
        result = await ensemble.train_ensemble(X, y)
        logger.info(f"✅ Regression test successful!")
        logger.info(f"Ensemble R²: {result.ensemble_performance.accuracy:.3f}")
        return True
    except Exception as e:
        logger.error(f"❌ Regression test failed: {e}")
        return False

async def test_mixed_data():
    """Test with mixed/edge case data"""
    logger.info("🧪 Testing Mixed/Edge Case Data")
    
    # Generate data with few unique values (could be classification or regression)
    np.random.seed(42)
    X = np.random.randn(1000, 10)
    y = np.random.choice([1.0, 2.0, 3.0, 4.0, 5.0], 1000)  # Few unique continuous values
    
    # Test target type detection
    target_type = detect_target_type(y)
    logger.info(f"Detected target type: {target_type}")
    
    # Test target preprocessing
    y_processed, preprocessor = preprocess_target(y, target_type)
    logger.info(f"Preprocessed target shape: {y_processed.shape}")
    logger.info(f"Preprocessor: {type(preprocessor).__name__ if preprocessor else 'None'}")
    
    # Test ensemble training
    config = EnsembleTrainingConfig(
        base_models=['random_forest'],
        meta_model='logistic_regression',
        cv_folds=3,
        random_state=42
    )
    
    ensemble = EnsembleModelTrainingInfrastructure(config)
    
    try:
        result = await ensemble.train_ensemble(X, y)
        logger.info(f"✅ Mixed data test successful!")
        logger.info(f"Ensemble performance: {result.ensemble_performance.accuracy:.3f}")
        return True
    except Exception as e:
        logger.error(f"❌ Mixed data test failed: {e}")
        return False

async def main():
    """Run all tests"""
    logger.info("🚀 Starting Ensemble Training Data Type Fix Tests")
    
    tests = [
        test_classification_data,
        test_regression_data,
        test_mixed_data
    ]
    
    results = []
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            logger.error(f"Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    logger.info(f"\n📊 Test Summary: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Ensemble training data type fixes are working correctly.")
    else:
        logger.error("❌ Some tests failed. Please check the logs above for details.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main())
