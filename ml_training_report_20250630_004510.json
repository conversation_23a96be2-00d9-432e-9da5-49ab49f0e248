{"training_session": {"timestamp": "2025-06-30T00:45:10.976950", "database": "medusa_master.db", "database_size_mb": 53.12}, "training_results": {"hybrid_training": {"status": "success", "training_results": {}, "report": {"training_timestamp": "2025-06-30T00:44:24.895450", "database_path": "medusa_master.db", "sklearn_available": true, "pandas_available": true, "training_results": {}, "data_summary": {"total_features": 10, "total_targets": 0, "feature_names": ["league_name_encoded", "season_type_encoded", "player_name_encoded", "team_abbreviation_encoded", "stat_value", "rank_position", "league_name_encoded", "season_type_encoded", "player_name_encoded", "team_abbreviation_encoded"], "target_names": []}, "models_trained": [], "models_directory": "models/hybrid_trained", "status": "failed"}, "data_summary": {"tables_loaded": ["advanced_stats", "league_leaders", "game_data", "assist_tracker"], "features_engineered": 10, "models_trained": []}}, "standard_training": {"nba": {"status": "failed", "reason": "no_data"}, "wnba": {"status": "failed", "reason": "no_data"}}, "neural_training": {"status": "failed", "error": "DataLoader worker (pid(s) 54936, 57496, 35356, 58136) exited unexpectedly"}}, "summary": {"hybrid_success": true, "standard_success": true, "neural_success": false, "models_trained": 0}}