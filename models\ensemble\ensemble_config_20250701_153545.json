{"config": {"base_models": ["xgboost", "random_forest", "gradient_boosting", "neural_network"], "meta_model": "logistic_regression", "ensemble_strategy": "stacking", "cv_folds": 5, "test_size": 0.2, "validation_size": 0.15, "random_state": 42, "target_accuracy": 0.75, "target_auc": 0.8, "target_f1": 0.75, "basketball_features": true, "temporal_modeling": true, "quantum_enhancement": true, "use_gpu": true, "parallel_training": true, "model_versioning": true, "performance_monitoring": true, "auto_hyperparameter_tuning": true, "feature_selection": true, "model_interpretability": true, "real_time_adaptation": true}, "feature_names": ["team_points", "opponent_points", "team_rebounds", "opponent_rebounds", "team_assists", "opponent_assists", "team_steals", "opponent_steals", "advanced_metric_8", "advanced_metric_9", "advanced_metric_10", "advanced_metric_11", "advanced_metric_12", "advanced_metric_13", "advanced_metric_14", "advanced_metric_15", "advanced_metric_16", "advanced_metric_17", "advanced_metric_18", "advanced_metric_19", "advanced_metric_20", "advanced_metric_21", "advanced_metric_22", "advanced_metric_23", "advanced_metric_24"], "model_paths": {"xgboost": "models\\ensemble\\xgboost_20250701_153545.pkl", "random_forest": "models\\ensemble\\random_forest_20250701_153545.pkl", "gradient_boosting": "models\\ensemble\\gradient_boosting_20250701_153545.pkl", "neural_network": "models\\ensemble\\neural_network_20250701_153545.pkl", "meta_model": "models\\ensemble\\meta_model_20250701_153545.pkl"}, "training_timestamp": "20250701_153545"}