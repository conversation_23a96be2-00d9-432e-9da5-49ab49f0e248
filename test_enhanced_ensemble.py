#!/usr/bin/env python3
"""
Test Enhanced Ensemble Model Training Infrastructure
"""

import sys
sys.path.append('.')
import asyncio
import numpy as np
from ensemble_model_training_infrastructure import EnsembleTrainingConfig, EnsembleModelTrainingInfrastructure

async def test_enhanced_ensemble():
    print('🏛️ MEDUSA VAULT: Testing Enhanced Ensemble Model Training Infrastructure')
    
    # Generate comprehensive test data
    np.random.seed(42)
    n_samples = 2000
    n_features = 25
    
    # Create basketball-like features
    X = np.random.randn(n_samples, n_features)
    # Add some basketball-specific patterns
    X[:, 0] = np.random.uniform(80, 130, n_samples)  # team_points
    X[:, 1] = np.random.uniform(75, 125, n_samples)  # opponent_points
    X[:, 2] = np.random.uniform(35, 55, n_samples)   # team_rebounds
    X[:, 3] = np.random.uniform(30, 50, n_samples)   # opponent_rebounds
    
    # Create realistic target (win probability based on point differential)
    point_diff = X[:, 0] - X[:, 1]
    win_prob = 1 / (1 + np.exp(-point_diff / 10))
    y = (np.random.random(n_samples) < win_prob).astype(int)
    
    feature_names = [
        'team_points', 'opponent_points', 'team_rebounds', 'opponent_rebounds',
        'team_assists', 'opponent_assists', 'team_steals', 'opponent_steals'
    ] + [f'advanced_metric_{i}' for i in range(8, n_features)]
    
    print(f'📊 Test Dataset: {X.shape[0]} samples, {X.shape[1]} features')
    print(f'🎯 Target Balance: {np.mean(y):.3f} win rate')
    
    # Test enhanced configuration
    config = EnsembleTrainingConfig(
        base_models=['xgboost', 'random_forest', 'gradient_boosting', 'neural_network'],
        meta_model='logistic_regression',
        ensemble_strategy='stacking',
        cv_folds=5,
        target_accuracy=0.75,
        target_auc=0.80,
        basketball_features=True,
        quantum_enhancement=True,
        parallel_training=True,
        performance_monitoring=True
    )
    
    print(f'⚙️ Configuration: {len(config.base_models)} base models, {config.ensemble_strategy} strategy')
    
    # Initialize and train ensemble
    infrastructure = EnsembleModelTrainingInfrastructure(config)
    
    try:
        result = await infrastructure.train_ensemble(X, y, feature_names)
        
        if result.success:
            print('\n🎉 ENSEMBLE TRAINING SUCCESS!')
            print('📈 Performance Metrics:')
            print(f'   🎯 Accuracy: {result.ensemble_performance.accuracy:.3f}')
            print(f'   📊 AUC-ROC: {result.ensemble_performance.auc_roc:.3f}')
            print(f'   🏀 F1-Score: {result.ensemble_performance.f1_score:.3f}')
            print(f'   ⚡ Precision: {result.ensemble_performance.precision:.3f}')
            print(f'   🔄 Recall: {result.ensemble_performance.recall:.3f}')
            
            print('\n🔧 Base Model Performance (Enhanced Access):')
            # Test both list and dict access
            for model_perf in result.base_model_performances:
                print(f'   {model_perf.model_name}: {model_perf.accuracy:.3f} accuracy')
            
            # Test dictionary access
            print('\n📊 Base Model Performance (Dict Access):')
            for model_name, perf in result.base_model_performances_dict.items():
                print(f'   {model_name}: {perf.accuracy:.3f} accuracy, {perf.auc_roc:.3f} AUC')
            
            # Test enhanced methods
            best_model = result.get_best_base_model()
            if best_model:
                print(f'\n🏆 Best Base Model: {best_model.model_name} ({best_model.accuracy:.3f} accuracy)')
            
            # Test performance summary
            summary = result.get_performance_summary()
            print('\n📈 Performance Summary:')
            print(f'   Models Trained: {summary.get("num_models", 0)}')
            print(f'   Best Accuracy: {summary.get("best_accuracy", 0):.3f}')
            print(f'   Average Accuracy: {summary.get("avg_accuracy", 0):.3f}')
            ensemble_improvement = summary.get("ensemble_improvement", {})
            print(f'   Ensemble Improvement: {ensemble_improvement.get("accuracy", 0):.3f}')
            
            print('\n📊 Training Summary:')
            print(f'   Training Time: {result.training_summary.get("total_training_time", 0):.2f}s')
            print(f'   Cross-Validation Folds: {config.cv_folds}')
            print(f'   Models Saved: {len(result.model_paths)}')
            
            # Check if targets are met
            targets_met = []
            if result.ensemble_performance.accuracy >= config.target_accuracy:
                targets_met.append('✅ Accuracy Target Met')
            else:
                targets_met.append(f'❌ Accuracy Target: {result.ensemble_performance.accuracy:.3f} < {config.target_accuracy}')
                
            if result.ensemble_performance.auc_roc >= config.target_auc:
                targets_met.append('✅ AUC Target Met')
            else:
                targets_met.append(f'❌ AUC Target: {result.ensemble_performance.auc_roc:.3f} < {config.target_auc}')
                
            if result.ensemble_performance.f1_score >= config.target_f1:
                targets_met.append('✅ F1 Target Met')
            else:
                targets_met.append(f'❌ F1 Target: {result.ensemble_performance.f1_score:.3f} < {config.target_f1}')
            
            print('\n🎯 Target Achievement:')
            for target in targets_met:
                print(f'   {target}')
                
            return True
        else:
            print('❌ ENSEMBLE TRAINING FAILED')
            print(f'Error: {result.error_message}')
            return False
            
    except Exception as e:
        print(f'❌ Exception during ensemble training: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Run test
    success = asyncio.run(test_enhanced_ensemble())
    print(f'\n🏆 Enhanced Test Result: {"SUCCESS" if success else "FAILED"}')
