#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Dynamic Basketball Statistics Calculator
===================================================================

Real-time basketball statistics calculation engine with advanced metrics
for both NBA and WNBA leagues. Provides comprehensive statistical analysis
for player performance, team dynamics, and game situations.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import sqlite3
import json

logger = logging.getLogger("DynamicBasketballStats")

class League(Enum):
    NBA = "NBA"
    WNBA = "WNBA"

@dataclass
class PlayerStats:
    """Comprehensive player statistics"""
    player_id: str
    name: str
    league: League
    season: str
    
    # Basic stats
    games_played: int = 0
    minutes_per_game: float = 0.0
    points_per_game: float = 0.0
    rebounds_per_game: float = 0.0
    assists_per_game: float = 0.0
    steals_per_game: float = 0.0
    blocks_per_game: float = 0.0
    turnovers_per_game: float = 0.0
    
    # Advanced stats
    field_goal_percentage: float = 0.0
    three_point_percentage: float = 0.0
    free_throw_percentage: float = 0.0
    true_shooting_percentage: float = 0.0
    effective_field_goal_percentage: float = 0.0
    
    # Advanced metrics
    player_efficiency_rating: float = 0.0
    usage_rate: float = 0.0
    win_shares: float = 0.0
    box_plus_minus: float = 0.0
    value_over_replacement: float = 0.0

@dataclass
class TeamStats:
    """Comprehensive team statistics"""
    team_id: str
    name: str
    league: League
    season: str
    
    # Basic team stats
    games_played: int = 0
    wins: int = 0
    losses: int = 0
    win_percentage: float = 0.0
    
    # Offensive stats
    points_per_game: float = 0.0
    field_goals_made: float = 0.0
    field_goals_attempted: float = 0.0
    three_pointers_made: float = 0.0
    three_pointers_attempted: float = 0.0
    free_throws_made: float = 0.0
    free_throws_attempted: float = 0.0
    
    # Defensive stats
    opponent_points_per_game: float = 0.0
    defensive_rating: float = 0.0
    
    # Advanced metrics
    offensive_rating: float = 0.0
    pace: float = 0.0
    net_rating: float = 0.0

class DynamicBasketballStatisticsCalculator:
    """
    Advanced basketball statistics calculator with real-time capabilities
    """
    
    def __init__(self, db_path: str = "data/basketball_data.db"):
        self.db_path = db_path
        self.logger = logging.getLogger("BasketballStatsCalculator")
        
        # League-specific constants
        self.league_constants = {
            League.NBA: {
                "avg_pace": 100.0,
                "avg_total": 220.0,
                "season_games": 82,
                "playoff_games": 16
            },
            League.WNBA: {
                "avg_pace": 92.0,
                "avg_total": 165.0,
                "season_games": 40,
                "playoff_games": 12
            }
        }
    
    def calculate_player_efficiency_rating(self, stats: Dict[str, float]) -> float:
        """
        Calculate Player Efficiency Rating (PER)
        """
        try:
            # Basic PER calculation
            per = (stats.get('points', 0) + 
                   stats.get('rebounds', 0) + 
                   stats.get('assists', 0) + 
                   stats.get('steals', 0) + 
                   stats.get('blocks', 0) - 
                   stats.get('turnovers', 0) - 
                   (stats.get('field_goals_attempted', 0) - stats.get('field_goals_made', 0)) - 
                   (stats.get('free_throws_attempted', 0) - stats.get('free_throws_made', 0))) / stats.get('minutes', 1)
            
            return max(0.0, per * 15.0)  # Scale to standard PER
        except Exception as e:
            self.logger.warning(f"PER calculation error: {e}")
            return 15.0  # League average
    
    def calculate_true_shooting_percentage(self, stats: Dict[str, float]) -> float:
        """
        Calculate True Shooting Percentage
        """
        try:
            points = stats.get('points', 0)
            fga = stats.get('field_goals_attempted', 0)
            fta = stats.get('free_throws_attempted', 0)
            
            if fga + 0.44 * fta == 0:
                return 0.0
                
            ts_pct = points / (2 * (fga + 0.44 * fta))
            return min(1.0, max(0.0, ts_pct))
        except Exception as e:
            self.logger.warning(f"TS% calculation error: {e}")
            return 0.0
    
    def calculate_usage_rate(self, player_stats: Dict[str, float], team_stats: Dict[str, float]) -> float:
        """
        Calculate Usage Rate
        """
        try:
            player_fga = player_stats.get('field_goals_attempted', 0)
            player_fta = player_stats.get('free_throws_attempted', 0)
            player_to = player_stats.get('turnovers', 0)
            player_min = player_stats.get('minutes', 0)
            
            team_fga = team_stats.get('field_goals_attempted', 0)
            team_fta = team_stats.get('free_throws_attempted', 0)
            team_to = team_stats.get('turnovers', 0)
            team_min = team_stats.get('minutes', 240)  # 48 minutes * 5 players
            
            if player_min == 0 or team_min == 0:
                return 0.0
            
            usage = 100 * ((player_fga + 0.44 * player_fta + player_to) * team_min) / (player_min * (team_fga + 0.44 * team_fta + team_to))
            return min(100.0, max(0.0, usage))
        except Exception as e:
            self.logger.warning(f"Usage rate calculation error: {e}")
            return 20.0  # Average usage rate
    
    def get_player_stats(self, player_id: str, season: str = "2023-24") -> Optional[PlayerStats]:
        """
        Get comprehensive player statistics
        """
        try:
            # This would connect to real database in production
            # For now, return calculated stats based on available data
            
            # Mock data structure - replace with real database query
            mock_stats = {
                'games_played': 70,
                'minutes': 32.5,
                'points': 25.2,
                'rebounds': 8.1,
                'assists': 6.3,
                'steals': 1.2,
                'blocks': 0.8,
                'turnovers': 3.1,
                'field_goals_made': 9.2,
                'field_goals_attempted': 19.8,
                'three_pointers_made': 2.1,
                'three_pointers_attempted': 6.2,
                'free_throws_made': 4.7,
                'free_throws_attempted': 5.8
            }
            
            # Calculate advanced metrics
            fg_pct = mock_stats['field_goals_made'] / max(1, mock_stats['field_goals_attempted'])
            three_pct = mock_stats['three_pointers_made'] / max(1, mock_stats['three_pointers_attempted'])
            ft_pct = mock_stats['free_throws_made'] / max(1, mock_stats['free_throws_attempted'])
            
            per = self.calculate_player_efficiency_rating(mock_stats)
            ts_pct = self.calculate_true_shooting_percentage(mock_stats)
            
            return PlayerStats(
                player_id=player_id,
                name=f"Player_{player_id}",
                league=League.NBA,
                season=season,
                games_played=mock_stats['games_played'],
                minutes_per_game=mock_stats['minutes'],
                points_per_game=mock_stats['points'],
                rebounds_per_game=mock_stats['rebounds'],
                assists_per_game=mock_stats['assists'],
                steals_per_game=mock_stats['steals'],
                blocks_per_game=mock_stats['blocks'],
                turnovers_per_game=mock_stats['turnovers'],
                field_goal_percentage=fg_pct,
                three_point_percentage=three_pct,
                free_throw_percentage=ft_pct,
                true_shooting_percentage=ts_pct,
                player_efficiency_rating=per
            )
            
        except Exception as e:
            self.logger.error(f"Error getting player stats for {player_id}: {e}")
            return None
    
    def get_team_stats(self, team_id: str, season: str = "2023-24") -> Optional[TeamStats]:
        """
        Get comprehensive team statistics
        """
        try:
            # Mock team data - replace with real database query
            mock_team_stats = {
                'games_played': 82,
                'wins': 45,
                'losses': 37,
                'points_per_game': 112.5,
                'opponent_points_per_game': 110.2,
                'field_goals_made': 41.2,
                'field_goals_attempted': 88.7,
                'three_pointers_made': 12.8,
                'three_pointers_attempted': 36.4,
                'free_throws_made': 17.3,
                'free_throws_attempted': 22.1
            }
            
            win_pct = mock_team_stats['wins'] / max(1, mock_team_stats['games_played'])
            net_rating = mock_team_stats['points_per_game'] - mock_team_stats['opponent_points_per_game']
            
            return TeamStats(
                team_id=team_id,
                name=f"Team_{team_id}",
                league=League.NBA,
                season=season,
                games_played=mock_team_stats['games_played'],
                wins=mock_team_stats['wins'],
                losses=mock_team_stats['losses'],
                win_percentage=win_pct,
                points_per_game=mock_team_stats['points_per_game'],
                opponent_points_per_game=mock_team_stats['opponent_points_per_game'],
                net_rating=net_rating
            )
            
        except Exception as e:
            self.logger.error(f"Error getting team stats for {team_id}: {e}")
            return None

def get_basketball_statistics_calculator(db_path: str = "data/basketball_data.db") -> DynamicBasketballStatisticsCalculator:
    """
    Factory function to get basketball statistics calculator instance
    """
    return DynamicBasketballStatisticsCalculator(db_path)

def get_dynamic_league_config(league: str) -> Dict[str, Any]:
    """
    Get dynamic league configuration for NBA or WNBA
    """
    try:
        calculator = get_basketball_statistics_calculator()

        # Basic league configurations
        if league.upper() == "NBA":
            return {
                'league': 'NBA',
                'season_length': 82,
                'playoff_teams': 16,
                'conference_teams': 15,
                'divisions': 6,
                'teams_per_division': 5,
                'roster_size': 15,
                'active_roster': 13,
                'salary_cap': *********,  # 2024-25 season
                'luxury_tax': *********,
                'confidence_score': 0.95,
                'data_quality': 'high',
                'last_updated': datetime.now().isoformat()
            }
        elif league.upper() == "WNBA":
            return {
                'league': 'WNBA',
                'season_length': 40,
                'playoff_teams': 8,
                'conference_teams': 6,
                'divisions': 0,  # No divisions in WNBA
                'teams_per_division': 0,
                'roster_size': 12,
                'active_roster': 11,
                'salary_cap': 1463000,  # 2024 season
                'luxury_tax': 0,  # No luxury tax in WNBA
                'confidence_score': 0.92,
                'data_quality': 'high',
                'last_updated': datetime.now().isoformat()
            }
        else:
            raise ValueError(f"Unsupported league: {league}")

    except Exception as e:
        logger.error(f"Error getting dynamic league config for {league}: {e}")
        # Fallback configuration
        return {
            'league': league,
            'confidence_score': 0.5,
            'data_quality': 'fallback',
            'error': str(e),
            'last_updated': datetime.now().isoformat()
        }
