#!/usr/bin/env python3
"""
Debug script to check the actual model configuration
"""

import sys
from pathlib import Path
import torch

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Check the actual model configuration"""
    try:
        # Load the game model checkpoint
        model_path = "models/wnba_neural_models/best_model.pt"
        
        if not Path(model_path).exists():
            logger.error(f"❌ Model not found: {model_path}")
            return
            
        logger.info(f"📊 Loading model from: {model_path}")
        checkpoint = torch.load(model_path, map_location='cpu')
        
        logger.info(f"📊 Checkpoint keys: {list(checkpoint.keys())}")
        
        if 'config' in checkpoint:
            config = checkpoint['config']
            logger.info(f"📊 Model config: {config}")
            
            if 'input_dim' in config:
                logger.info(f"🎯 Model expects input_dim: {config['input_dim']}")
            else:
                logger.warning("⚠️ No input_dim found in config")
        else:
            logger.warning("⚠️ No config found in checkpoint")
            
        # Check scaler state dict
        if 'scaler_state_dict' in checkpoint:
            scaler_state = checkpoint['scaler_state_dict']
            logger.info(f"📊 Scaler state keys: {list(scaler_state.keys()) if scaler_state else 'None'}")
            if scaler_state:
                logger.info(f"📊 Scaler n_features_in_: {scaler_state.get('n_features_in_', 'N/A')}")
        else:
            logger.info("📊 No scaler_state_dict found")

        # Check model state dict for layer sizes
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']

            # Look for the first layer to determine input size
            for key, tensor in state_dict.items():
                if 'weight' in key and len(tensor.shape) == 2:
                    logger.info(f"📊 Layer {key}: {tensor.shape}")
                    if 'layers.0' in key or 'input' in key.lower():
                        logger.info(f"🎯 First layer input size: {tensor.shape[1]}")
                        break
                        
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
