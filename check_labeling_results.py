import sqlite3

#!/usr/bin/env python3
"""
Check Automated Labeling Results
===============================

Quick script to check the results of automated data labeling.
"""


def check_labeling_results():
    """Check the automated labeling results"""
    conn = sqlite3.connect('hyper_medusa_consolidated.db')
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
    tables = cursor.fetchall()
    for table in tables:
    
    # Check new labeling tables
    labeling_tables = ['player_performance_labels', 'game_context_labels', 'clutch_situation_labels']
    
    for table_name in labeling_tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            
            # Show sample data
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
            sample_data = cursor.fetchall()
            if sample_data:
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = [col[1] for col in cursor.fetchall()]
        except Exception as e:
    
    # Check total database size
    cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
    table_count = cursor.fetchone()[0]
    
    # Calculate total records across all main tables
    main_tables = ['teams', 'players', 'games', 'player_game_stats', 'shot_analytics', 
                   'clutch_analytics', 'shooting_analytics', 'advanced_analytics', 'defense_analytics']
    total_records = 0
    for table in main_tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            total_records += count
        except:
            pass
    
    
    conn.close()

if __name__ == "__main__":
    check_labeling_results()
