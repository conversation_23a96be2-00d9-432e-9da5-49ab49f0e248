#!/usr/bin/env python3
"""
WNBA Neural Model Testing Against Past Games
Test the trained WNBA model against historical game data to validate performance
"""

import sys
import os
import torch
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Any
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

# Add project root to path
sys.path.append('.')

from src.neural_cortex.neural_training_pipeline import NeuralTrainingPipeline, TrainingConfig, EnhancedNeuralBasketballCore
from src.data.basketball_data_loader import BasketballDataLoader

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WNBAModelTester:
    """Test trained WNBA neural model against past games"""
    
    def __init__(self, model_path: str = "models/wnba_neural_models"):
        self.model_path = Path(model_path)
        self.model = None
        self.config = None
        self.scaler = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.data_loader = None
        
        logger.info(f"🏀 WNBA Model Tester initialized")
        logger.info(f"📁 Model path: {self.model_path}")
        logger.info(f"💻 Device: {self.device}")
    
    def load_trained_model(self) -> bool:
        """Load the trained WNBA model"""
        try:
            # Look for the best model checkpoint
            best_model_path = self.model_path / "best_model.pt"
            
            if not best_model_path.exists():
                logger.error(f"❌ Best model not found at: {best_model_path}")
                return False
            
            logger.info(f"🔥 Loading trained WNBA model from: {best_model_path}")
            
            # Load checkpoint
            checkpoint = torch.load(best_model_path, map_location=self.device)
            
            # Reconstruct config
            config_dict = checkpoint['config']
            self.config = TrainingConfig(**config_dict)
            
            # Build model with same architecture
            self.model = EnhancedNeuralBasketballCore(
                input_dim=self.config.input_dim,
                hidden_dim=self.config.hidden_dim,
                num_layers=self.config.num_layers,
                dropout_rate=self.config.dropout_rate
            ).to(self.device)
            
            # Load model weights
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()
            
            logger.info("✅ WNBA model loaded successfully!")
            logger.info(f"📊 Model architecture: {self.config.input_dim} → {self.config.hidden_dim} → 2")
            logger.info(f"📊 Best validation loss: {checkpoint.get('best_val_loss', 'N/A')}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            return False
    
    def load_test_data(self) -> bool:
        """Load WNBA test data for evaluation"""
        try:
            logger.info("📊 Loading WNBA test data...")
            
            # Initialize data loader
            self.data_loader = BasketballDataLoader()
            
            # Load WNBA data
            data = self.data_loader.load_training_data(league='WNBA')
            
            if data is None or len(data) == 0:
                logger.error("❌ No WNBA test data available")
                return False
            
            logger.info(f"✅ Loaded {len(data)} WNBA records for testing")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to load test data: {e}")
            return False
    
    def prepare_test_features(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features and labels for testing - must match training format"""
        try:
            logger.info("🔧 Preparing test features...")
            logger.info(f"📊 Input data shape: {data.shape}")
            logger.info(f"📊 Available columns: {list(data.columns)}")

            # The model expects 34 features as used in training
            # We need to use the same preprocessing as the neural training pipeline

            # Get all numerical columns (excluding target columns)
            exclude_cols = ['win_prediction', 'elite_performer', 'top_tier', 'game_id', 'player_id', 'team_id']
            numerical_cols = []

            for col in data.columns:
                if col not in exclude_cols and data[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                    numerical_cols.append(col)

            logger.info(f"📊 Selected numerical columns ({len(numerical_cols)}): {numerical_cols}")

            if len(numerical_cols) == 0:
                logger.error("❌ No numerical features found!")
                return None, None

            # Extract features
            features = data[numerical_cols].values

            # Handle missing values
            features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)

            # If we have fewer features than expected (34), pad with zeros
            if features.shape[1] < self.config.input_dim:
                logger.warning(f"⚠️ Padding features from {features.shape[1]} to {self.config.input_dim}")
                padding = np.zeros((features.shape[0], self.config.input_dim - features.shape[1]))
                features = np.concatenate([features, padding], axis=1)
            elif features.shape[1] > self.config.input_dim:
                logger.warning(f"⚠️ Truncating features from {features.shape[1]} to {self.config.input_dim}")
                features = features[:, :self.config.input_dim]

            # Create binary target (win/loss prediction)
            if 'win_prediction' in data.columns:
                labels = data['win_prediction'].values
                logger.info("✅ Using 'win_prediction' as target")
            elif 'elite_performer' in data.columns:
                labels = data['elite_performer'].values
                logger.info("✅ Using 'elite_performer' as target")
            else:
                # Create synthetic labels based on stat_value or first numerical column
                if 'stat_value' in data.columns:
                    labels = (data['stat_value'] > data['stat_value'].median()).astype(int)
                    logger.warning("⚠️ Using synthetic labels based on stat_value")
                else:
                    # Use first numerical column
                    first_col = numerical_cols[0]
                    labels = (data[first_col] > data[first_col].median()).astype(int)
                    logger.warning(f"⚠️ Using synthetic labels based on {first_col}")

            # Ensure labels are binary (0 or 1)
            labels = labels.astype(int)

            logger.info(f"✅ Prepared features: {features.shape}")
            logger.info(f"✅ Prepared labels: {labels.shape}")
            logger.info(f"📊 Label distribution: {np.bincount(labels)}")

            return features, labels

        except Exception as e:
            logger.error(f"❌ Failed to prepare features: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return None, None
    
    def predict_games(self, features: np.ndarray) -> np.ndarray:
        """Make predictions on test games"""
        try:
            logger.info("🎯 Making predictions on test games...")
            
            # Convert to tensor
            features_tensor = torch.FloatTensor(features).to(self.device)
            
            predictions = []
            probabilities = []
            
            self.model.eval()
            with torch.no_grad():
                # Process in batches to handle memory
                batch_size = 1000
                for i in range(0, len(features), batch_size):
                    batch = features_tensor[i:i+batch_size]
                    
                    # Get model outputs
                    outputs = self.model(batch)
                    
                    # Apply softmax to get probabilities
                    probs = torch.softmax(outputs, dim=1)
                    
                    # Get predictions (argmax)
                    batch_preds = torch.argmax(outputs, dim=1)
                    
                    predictions.extend(batch_preds.cpu().numpy())
                    probabilities.extend(probs.cpu().numpy())
            
            predictions = np.array(predictions)
            probabilities = np.array(probabilities)
            
            logger.info(f"✅ Generated {len(predictions)} predictions")
            logger.info(f"📊 Prediction distribution: {np.bincount(predictions)}")
            
            return predictions, probabilities
            
        except Exception as e:
            logger.error(f"❌ Failed to make predictions: {e}")
            return None, None
    
    def evaluate_performance(self, true_labels: np.ndarray, predictions: np.ndarray, 
                           probabilities: np.ndarray) -> Dict[str, float]:
        """Evaluate model performance against past games"""
        try:
            logger.info("📊 Evaluating model performance...")
            
            # Calculate metrics
            accuracy = accuracy_score(true_labels, predictions)
            precision = precision_score(true_labels, predictions, average='weighted', zero_division=0)
            recall = recall_score(true_labels, predictions, average='weighted', zero_division=0)
            f1 = f1_score(true_labels, predictions, average='weighted', zero_division=0)
            
            # Calculate confidence metrics
            max_probs = np.max(probabilities, axis=1)
            avg_confidence = np.mean(max_probs)
            
            # Calculate class-specific metrics
            cm = confusion_matrix(true_labels, predictions)
            
            metrics = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'avg_confidence': avg_confidence,
                'confusion_matrix': cm.tolist()
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"❌ Failed to evaluate performance: {e}")
            return {}
    
    def print_results(self, metrics: Dict[str, float]):
        """Print detailed test results"""
        print("\n" + "="*60)
        print("🏀 WNBA MODEL TEST RESULTS")
        print("="*60)
        
        print(f"📊 Accuracy:     {metrics.get('accuracy', 0):.3f}")
        print(f"📊 Precision:    {metrics.get('precision', 0):.3f}")
        print(f"📊 Recall:       {metrics.get('recall', 0):.3f}")
        print(f"📊 F1-Score:     {metrics.get('f1_score', 0):.3f}")
        print(f"📊 Avg Confidence: {metrics.get('avg_confidence', 0):.3f}")
        
        if 'confusion_matrix' in metrics:
            cm = np.array(metrics['confusion_matrix'])
            print(f"\n📊 Confusion Matrix:")
            print(f"    Predicted:  [0]  [1]")
            print(f"Actual [0]:     {cm[0][0]:3d}  {cm[0][1]:3d}")
            print(f"Actual [1]:     {cm[1][0]:3d}  {cm[1][1]:3d}")
        
        print("="*60)
    
    def prepare_test_data_with_pipeline(self) -> Tuple[np.ndarray, np.ndarray]:
        """Use the same data preparation pipeline as training"""
        try:
            logger.info("🔧 Using training pipeline for test data preparation...")

            # Create a temporary training config to match the model
            temp_config = TrainingConfig(
                league='WNBA',
                input_dim=self.config.input_dim,
                batch_size=1000  # Large batch for testing
            )

            # Use the same dataset class as training
            from src.neural_cortex.neural_training_pipeline import BasketballDataset

            # Create dataset with same preprocessing
            dataset = BasketballDataset(
                data_path="data/clean_wnba_training_data.csv",
                config=temp_config,
                split="test"
            )

            # Get all data
            features = dataset.data
            labels = dataset.labels

            logger.info(f"✅ Pipeline prepared features: {features.shape}")
            logger.info(f"✅ Pipeline prepared labels: {labels.shape}")
            logger.info(f"📊 Label distribution: {np.bincount(labels)}")

            return features, labels

        except Exception as e:
            logger.error(f"❌ Failed to prepare data with pipeline: {e}")
            import traceback
            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            return None, None

    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive test of WNBA model against past games"""
        logger.info("🚀 Starting comprehensive WNBA model test...")

        # Load trained model
        if not self.load_trained_model():
            return {"error": "Failed to load trained model"}

        # Try using the same pipeline as training first
        logger.info("🔧 Attempting to use training pipeline for data preparation...")
        features, labels = self.prepare_test_data_with_pipeline()

        if features is None or labels is None:
            logger.warning("⚠️ Pipeline method failed, falling back to manual preparation...")

            # Load test data manually
            if not self.load_test_data():
                return {"error": "Failed to load test data"}

            # Get test data
            test_data = self.data_loader.load_training_data(league='WNBA')

            # Prepare features and labels manually
            features, labels = self.prepare_test_features(test_data)
            if features is None or labels is None:
                return {"error": "Failed to prepare test features"}

        # Make predictions
        predictions, probabilities = self.predict_games(features)
        if predictions is None or probabilities is None:
            return {"error": "Failed to make predictions"}

        # Evaluate performance
        metrics = self.evaluate_performance(labels, predictions, probabilities)

        # Print results
        self.print_results(metrics)

        return {
            "status": "success",
            "metrics": metrics,
            "test_samples": len(features),
            "model_path": str(self.model_path)
        }

async def main():
    """Main testing function"""
    print("🏀 WNBA Neural Model Testing Against Past Games")
    print("="*60)
    
    # Initialize tester
    tester = WNBAModelTester()
    
    # Run comprehensive test
    results = await tester.run_comprehensive_test()
    
    if results.get("status") == "success":
        print(f"\n✅ Testing completed successfully!")
        print(f"📊 Tested on {results.get('test_samples', 0)} samples")
    else:
        print(f"\n❌ Testing failed: {results.get('error', 'Unknown error')}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
