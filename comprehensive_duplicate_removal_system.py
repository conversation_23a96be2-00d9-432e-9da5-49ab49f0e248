import asyncio
import sqlite3
import pandas as pd
import numpy as np
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import hashlib
            import shutil

#!/usr/bin/env python3
"""
Comprehensive Duplicate Detection and Removal System
===================================================

Advanced duplicate detection system that ensures the HYPER MEDUSA NEURAL VAULT
database contains no duplicate records across all tables.

Features:
- Multi-level duplicate detection (exact, fuzzy, semantic)
- Basketball-specific duplicate identification rules
- Cross-table relationship validation
- Safe duplicate removal with backup
- Comprehensive reporting and analytics
"""


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DuplicateType(Enum):
    """Types of duplicates detected"""
    EXACT_DUPLICATE = "EXACT_DUPLICATE"
    FUZZY_DUPLICATE = "FUZZY_DUPLICATE"
    SEMANTIC_DUPLICATE = "SEMANTIC_DUPLICATE"
    CROSS_TABLE_DUPLICATE = "CROSS_TABLE_DUPLICATE"

@dataclass
class DuplicateReport:
    """Report of detected duplicates"""
    table_name: str
    duplicate_type: DuplicateType
    duplicate_count: int
    total_records: int
    duplicate_percentage: float
    sample_duplicates: List[Dict]
    removal_strategy: str
    timestamp: datetime

class ComprehensiveDuplicateRemovalSystem:
    """
    Comprehensive Duplicate Detection and Removal System
    
    Ensures the HYPER MEDUSA NEURAL VAULT database contains no duplicate
    records using advanced detection algorithms and basketball-specific rules.
    """
    
    def __init__(self, db_path: str = "hyper_medusa_consolidated.db"):
        self.db_path = db_path
        self.duplicate_reports = []
        
        # Basketball-specific duplicate detection rules
        self.duplicate_rules = {
            'teams': {
                'primary_keys': ['team_id'],
                'unique_combinations': [['team_name', 'abbreviation']],
                'fuzzy_match_fields': ['team_name'],
                'similarity_threshold': 0.85
            },
            'players': {
                'primary_keys': ['player_id'],
                'unique_combinations': [['player_name', 'team_id'], ['player_name', 'height', 'weight']],
                'fuzzy_match_fields': ['player_name'],
                'similarity_threshold': 0.90
            },
            'games': {
                'primary_keys': ['game_id'],
                'unique_combinations': [['home_team_id', 'away_team_id', 'game_date']],
                'fuzzy_match_fields': [],
                'similarity_threshold': 0.95
            },
            'player_game_stats': {
                'primary_keys': ['player_id', 'game_id'],
                'unique_combinations': [['player_id', 'game_id']],
                'fuzzy_match_fields': [],
                'similarity_threshold': 0.95
            },
            'shot_analytics': {
                'primary_keys': ['player_id', 'season', 'league'],
                'unique_combinations': [['player_id', 'season', 'league']],
                'fuzzy_match_fields': [],
                'similarity_threshold': 0.95
            },
            'clutch_analytics': {
                'primary_keys': ['player_id', 'season', 'league'],
                'unique_combinations': [['player_id', 'season', 'league']],
                'fuzzy_match_fields': [],
                'similarity_threshold': 0.95
            },
            'advanced_analytics': {
                'primary_keys': ['player_id', 'season', 'league'],
                'unique_combinations': [['player_id', 'season', 'league']],
                'fuzzy_match_fields': [],
                'similarity_threshold': 0.95
            },
            'defense_analytics': {
                'primary_keys': ['player_id', 'season', 'league'],
                'unique_combinations': [['player_id', 'season', 'league']],
                'fuzzy_match_fields': [],
                'similarity_threshold': 0.95
            },
            'shooting_analytics': {
                'primary_keys': ['player_id', 'season', 'league'],
                'unique_combinations': [['player_id', 'season', 'league']],
                'fuzzy_match_fields': [],
                'similarity_threshold': 0.95
            }
        }
        
        logger.info("🔍 Comprehensive Duplicate Removal System initialized")
    
    async def run_comprehensive_duplicate_detection(self) -> Dict[str, DuplicateReport]:
        """Run comprehensive duplicate detection across all tables"""
        logger.info("🚀 Starting comprehensive duplicate detection")
        
        duplicate_reports = {}
        total_duplicates_found = 0
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get all table names (excluding system tables)
            tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name != 'sqlite_sequence'").fetchall()
            
            for table_tuple in tables:
                table_name = table_tuple[0]
                
                if table_name in self.duplicate_rules:
                    logger.info(f"🔍 Detecting duplicates in table: {table_name}")
                    
                    # Load table data
                    df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
                    
                    # Detect duplicates
                    report = await self._detect_table_duplicates(table_name, df)
                    
                    if report and report.duplicate_count > 0:
                        duplicate_reports[table_name] = report
                        total_duplicates_found += report.duplicate_count
                        
                        logger.info(f"⚠️  Found {report.duplicate_count} duplicates in {table_name} ({report.duplicate_percentage:.1f}%)")
                    else:
                        logger.info(f"✅ No duplicates found in {table_name}")
            
            conn.close()
            
            # Generate comprehensive report
            await self._generate_duplicate_summary(duplicate_reports, total_duplicates_found)
            
            logger.info(f"✅ Duplicate detection completed - {total_duplicates_found} total duplicates found")
            return duplicate_reports
            
        except Exception as e:
            logger.error(f"❌ Duplicate detection failed: {e}")
            raise
    
    async def _detect_table_duplicates(self, table_name: str, df: pd.DataFrame) -> Optional[DuplicateReport]:
        """Detect duplicates in a specific table"""
        if df.empty:
            return None
        
        rules = self.duplicate_rules[table_name]
        total_records = len(df)
        
        # 1. Detect exact duplicates (entire row)
        exact_duplicates = df.duplicated().sum()
        
        # 2. Detect primary key duplicates
        pk_duplicates = 0
        if rules['primary_keys']:
            pk_fields = [field for field in rules['primary_keys'] if field in df.columns]
            if pk_fields:
                pk_duplicates = df.duplicated(subset=pk_fields).sum()
        
        # 3. Detect unique combination duplicates
        combo_duplicates = 0
        for combo in rules['unique_combinations']:
            combo_fields = [field for field in combo if field in df.columns]
            if len(combo_fields) == len(combo):  # All fields exist
                combo_dups = df.duplicated(subset=combo_fields).sum()
                combo_duplicates = max(combo_duplicates, combo_dups)
        
        # 4. Detect fuzzy duplicates (for text fields)
        fuzzy_duplicates = 0
        if rules['fuzzy_match_fields']:
            fuzzy_duplicates = await self._detect_fuzzy_duplicates(df, rules['fuzzy_match_fields'], rules['similarity_threshold'])
        
        # Total unique duplicates (avoid double counting)
        total_duplicates = max(exact_duplicates, pk_duplicates, combo_duplicates, fuzzy_duplicates)
        
        if total_duplicates > 0:
            # Get sample duplicates for reporting
            sample_duplicates = self._get_sample_duplicates(df, rules, max_samples=5)
            
            # Determine removal strategy
            removal_strategy = self._determine_removal_strategy(exact_duplicates, pk_duplicates, combo_duplicates, fuzzy_duplicates)
            
            return DuplicateReport(
                table_name=table_name,
                duplicate_type=DuplicateType.EXACT_DUPLICATE if exact_duplicates > 0 else DuplicateType.SEMANTIC_DUPLICATE,
                duplicate_count=total_duplicates,
                total_records=total_records,
                duplicate_percentage=(total_duplicates / total_records) * 100,
                sample_duplicates=sample_duplicates,
                removal_strategy=removal_strategy,
                timestamp=datetime.now()
            )
        
        return None
    
    async def _detect_fuzzy_duplicates(self, df: pd.DataFrame, fuzzy_fields: List[str], threshold: float) -> int:
        """Detect fuzzy duplicates using string similarity"""
        fuzzy_count = 0
        
        for field in fuzzy_fields:
            if field in df.columns:
                # Simple fuzzy matching using string length and character overlap
                values = df[field].dropna().astype(str).unique()
                
                for i, val1 in enumerate(values):
                    for val2 in values[i+1:]:
                        similarity = self._calculate_string_similarity(val1, val2)
                        if similarity >= threshold:
                            fuzzy_count += 1
        
        return fuzzy_count
    
    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """Calculate string similarity using Jaccard similarity"""
        if not str1 or not str2:
            return 0.0
        
        # Convert to lowercase and create character sets
        set1 = set(str1.lower())
        set2 = set(str2.lower())
        
        # Calculate Jaccard similarity
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def _get_sample_duplicates(self, df: pd.DataFrame, rules: Dict, max_samples: int = 5) -> List[Dict]:
        """Get sample duplicate records for reporting"""
        samples = []
        
        # Get exact duplicates
        if df.duplicated().any():
            duplicate_rows = df[df.duplicated(keep=False)]
            for _, row in duplicate_rows.head(max_samples).iterrows():
                samples.append({
                    'type': 'exact_duplicate',
                    'data': row.to_dict()
                })
        
        return samples
    
    def _determine_removal_strategy(self, exact: int, pk: int, combo: int, fuzzy: int) -> str:
        """Determine the best strategy for removing duplicates"""
        if exact > 0:
            return "Remove exact duplicate rows"
        elif pk > 0:
            return "Remove records with duplicate primary keys (keep first occurrence)"
        elif combo > 0:
            return "Remove records with duplicate unique combinations (keep most complete record)"
        elif fuzzy > 0:
            return "Manual review required for fuzzy duplicates"
        else:
            return "No removal needed"

    async def remove_all_duplicates(self) -> Dict[str, int]:
        """Remove all detected duplicates from the database"""
        logger.info("🗑️  Starting comprehensive duplicate removal")

        removal_results = {}
        total_removed = 0

        try:
            # First detect all duplicates
            duplicate_reports = await self.run_comprehensive_duplicate_detection()

            if not duplicate_reports:
                logger.info("✅ No duplicates found - database is clean!")
                return removal_results

            # Create backup before removal
            backup_path = f"database_backup_before_duplicate_removal_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            await self._create_database_backup(backup_path)

            conn = sqlite3.connect(self.db_path)

            # Remove duplicates table by table
            for table_name, report in duplicate_reports.items():
                logger.info(f"🗑️  Removing {report.duplicate_count} duplicates from {table_name}")

                # Load table data
                df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)

                # Remove duplicates based on strategy
                cleaned_df = await self._remove_table_duplicates(df, self.duplicate_rules[table_name])

                # Calculate removed count
                removed_count = len(df) - len(cleaned_df)
                removal_results[table_name] = removed_count
                total_removed += removed_count

                # Update database
                cleaned_df.to_sql(table_name, conn, if_exists='replace', index=False)

                logger.info(f"✅ Removed {removed_count} duplicates from {table_name}")

            conn.close()

            # Generate removal summary
            await self._generate_removal_summary(removal_results, total_removed)

            logger.info(f"✅ Duplicate removal completed - {total_removed} total duplicates removed")
            return removal_results

        except Exception as e:
            logger.error(f"❌ Duplicate removal failed: {e}")
            raise

    async def _remove_table_duplicates(self, df: pd.DataFrame, rules: Dict) -> pd.DataFrame:
        """Remove duplicates from a specific table"""
        if df.empty:
            return df

        # Start with the original dataframe
        cleaned_df = df.copy()

        # 1. Remove exact duplicates (keep first occurrence)
        cleaned_df = cleaned_df.drop_duplicates()

        # 2. Remove primary key duplicates
        if rules['primary_keys']:
            pk_fields = [field for field in rules['primary_keys'] if field in cleaned_df.columns]
            if pk_fields:
                cleaned_df = cleaned_df.drop_duplicates(subset=pk_fields, keep='first')

        # 3. Remove unique combination duplicates
        for combo in rules['unique_combinations']:
            combo_fields = [field for field in combo if field in cleaned_df.columns]
            if len(combo_fields) == len(combo):  # All fields exist
                # Keep the record with the most complete data (fewest nulls)
                cleaned_df = self._remove_combo_duplicates_smart(cleaned_df, combo_fields)

        return cleaned_df

    def _remove_combo_duplicates_smart(self, df: pd.DataFrame, combo_fields: List[str]) -> pd.DataFrame:
        """Remove combination duplicates keeping the most complete record"""
        if df.empty:
            return df

        # Group by combination fields
        grouped = df.groupby(combo_fields)

        # For each group, keep the record with fewest nulls
        def select_best_record(group):
            if len(group) == 1:
                return group

            # Calculate completeness score for each record
            group = group.copy()
            group['completeness_score'] = group.isnull().sum(axis=1)

            # Keep the record with the lowest null count (highest completeness)
            best_record = group.loc[group['completeness_score'].idxmin()]

            # Remove the completeness score column
            return best_record.drop('completeness_score').to_frame().T

        # Apply the selection and concatenate results
        result_groups = []
        for name, group in grouped:
            best_record = select_best_record(group)
            result_groups.append(best_record)

        if result_groups:
            return pd.concat(result_groups, ignore_index=True)
        else:
            return df

    async def _create_database_backup(self, backup_path: str) -> None:
        """Create a backup of the database before duplicate removal"""
        try:
            shutil.copy2(self.db_path, backup_path)
            logger.info(f"💾 Database backup created: {backup_path}")
        except Exception as e:
            logger.warning(f"⚠️  Failed to create backup: {e}")

    async def _generate_duplicate_summary(self, duplicate_reports: Dict[str, DuplicateReport], total_duplicates: int) -> None:
        """Generate comprehensive duplicate detection summary"""
        logger.info("📋 DUPLICATE DETECTION SUMMARY")
        logger.info("=" * 40)

        if not duplicate_reports:
            logger.info("✅ NO DUPLICATES FOUND - DATABASE IS CLEAN!")
            return

        logger.info(f"⚠️  Total Duplicates Found: {total_duplicates:,}")
        logger.info(f"📊 Tables with Duplicates: {len(duplicate_reports)}")
        logger.info("")

        # Table-by-table summary
        for table_name, report in duplicate_reports.items():
            logger.info(f"📈 {table_name.upper()}:")
            logger.info(f"   Duplicates: {report.duplicate_count:,} ({report.duplicate_percentage:.1f}%)")
            logger.info(f"   Total Records: {report.total_records:,}")
            logger.info(f"   Type: {report.duplicate_type.value}")
            logger.info(f"   Strategy: {report.removal_strategy}")
            logger.info("")

    async def _generate_removal_summary(self, removal_results: Dict[str, int], total_removed: int) -> None:
        """Generate comprehensive duplicate removal summary"""
        logger.info("📋 DUPLICATE REMOVAL SUMMARY")
        logger.info("=" * 40)

        logger.info(f"🗑️  Total Duplicates Removed: {total_removed:,}")
        logger.info(f"📊 Tables Cleaned: {len(removal_results)}")
        logger.info("")

        # Table-by-table summary
        for table_name, removed_count in removal_results.items():
            logger.info(f"📈 {table_name.upper()}: {removed_count:,} duplicates removed")

        logger.info("")
        logger.info("✅ Database is now duplicate-free!")

async def main():
    """Main execution function"""
    duplicate_system = ComprehensiveDuplicateRemovalSystem()

    try:
        # Run duplicate detection
        duplicate_reports = await duplicate_system.run_comprehensive_duplicate_detection()

        # If duplicates found, remove them
        if duplicate_reports:
            removal_results = await duplicate_system.remove_all_duplicates()

            # Save results to JSON
            results_summary = {
                'timestamp': datetime.now().isoformat(),
                'duplicates_detected': len(duplicate_reports),
                'total_duplicates_removed': sum(removal_results.values()),
                'tables_cleaned': list(removal_results.keys()),
                'removal_details': removal_results
            }

            with open('duplicate_removal_results.json', 'w') as f:
                json.dump(results_summary, f, indent=2)

            logger.info("💾 Duplicate removal results saved to duplicate_removal_results.json")
        else:
            logger.info("✅ No duplicates detected - database is already clean!")

    except Exception as e:
        logger.error(f"❌ Duplicate removal process failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
