#!/usr/bin/env python3
"""
Fix Feature Engineering Pipeline

This script addresses the critical issue where models were trained on synthetic data
instead of real basketball statistics due to insufficient feature engineering.

PROBLEM IDENTIFIED:
- Real data has only 1 numeric feature (stat_value)
- Training pipeline adds 9 synthetic features to reach minimum 10
- Model expects 34 features, so falls back to entirely synthetic data
- Prediction service generates 34 different synthetic features
- Result: Complete mismatch between training and prediction

SOLUTION:
- Create proper feature engineering from the real stat_value data
- Generate meaningful basketball features from stat_category and stat_value
- Retrain models with real feature-engineered data
- Update prediction service to use same feature engineering
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple
import sys
import os

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# Import not needed for this script
# from src.data_loaders.basketball_data_loader import BasketballDataLoader

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealFeatureEngineer:
    """Engineer meaningful basketball features from real stat_value data"""
    
    def __init__(self):
        self.stat_categories = [
            'points', 'rebounds', 'assists', 'steals', 'blocks', 'turnovers',
            'field_goals_made', 'field_goals_attempted', 'three_pointers_made',
            'three_pointers_attempted', 'free_throws_made', 'free_throws_attempted',
            'minutes', 'plus_minus', 'offensive_rebounds', 'defensive_rebounds'
        ]
        
    def engineer_features_from_real_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create meaningful basketball features from stat_value and stat_category"""
        logger.info(f"🔧 Engineering features from {len(df)} real basketball records")
        
        # Create pivot table to get stats per player per game/season
        pivot_df = df.pivot_table(
            index=['player_name', 'season', 'team_abbreviation'],
            columns='stat_category',
            values='stat_value',
            aggfunc='mean',  # Average stats per player per season
            fill_value=0
        ).reset_index()
        
        logger.info(f"📊 Pivot table created: {len(pivot_df)} player-season records")
        logger.info(f"📊 Available stat categories: {list(pivot_df.columns[3:])}")
        
        # Create engineered features
        engineered_df = pivot_df.copy()
        
        # Basic efficiency metrics
        if 'field_goals_made' in pivot_df.columns and 'field_goals_attempted' in pivot_df.columns:
            engineered_df['field_goal_percentage'] = np.where(
                pivot_df['field_goals_attempted'] > 0,
                pivot_df['field_goals_made'] / pivot_df['field_goals_attempted'],
                0
            )
        
        if 'three_pointers_made' in pivot_df.columns and 'three_pointers_attempted' in pivot_df.columns:
            engineered_df['three_point_percentage'] = np.where(
                pivot_df['three_pointers_attempted'] > 0,
                pivot_df['three_pointers_made'] / pivot_df['three_pointers_attempted'],
                0
            )
        
        if 'free_throws_made' in pivot_df.columns and 'free_throws_attempted' in pivot_df.columns:
            engineered_df['free_throw_percentage'] = np.where(
                pivot_df['free_throws_attempted'] > 0,
                pivot_df['free_throws_made'] / pivot_df['free_throws_attempted'],
                0
            )
        
        # Advanced metrics
        if 'points' in pivot_df.columns and 'minutes' in pivot_df.columns:
            engineered_df['points_per_minute'] = np.where(
                pivot_df['minutes'] > 0,
                pivot_df['points'] / pivot_df['minutes'],
                0
            )
        
        if 'assists' in pivot_df.columns and 'turnovers' in pivot_df.columns:
            engineered_df['assist_to_turnover_ratio'] = np.where(
                pivot_df['turnovers'] > 0,
                pivot_df['assists'] / pivot_df['turnovers'],
                pivot_df['assists']  # If no turnovers, just use assists
            )
        
        # Rebounding metrics
        if 'offensive_rebounds' in pivot_df.columns and 'defensive_rebounds' in pivot_df.columns:
            engineered_df['total_rebounds'] = pivot_df['offensive_rebounds'] + pivot_df['defensive_rebounds']
            engineered_df['offensive_rebound_percentage'] = np.where(
                engineered_df['total_rebounds'] > 0,
                pivot_df['offensive_rebounds'] / engineered_df['total_rebounds'],
                0
            )
        
        # Performance tiers
        if 'points' in pivot_df.columns:
            engineered_df['scoring_tier'] = pd.cut(
                pivot_df['points'],
                bins=[0, 5, 10, 15, 20, 100],
                labels=[1, 2, 3, 4, 5]
            ).astype(float)
        
        # Create target variables
        if 'points' in pivot_df.columns:
            points_median = pivot_df['points'].median()
            engineered_df['high_scorer'] = (pivot_df['points'] > points_median).astype(int)
        
        if 'assists' in pivot_df.columns:
            assists_median = pivot_df['assists'].median()
            engineered_df['high_assists'] = (pivot_df['assists'] > assists_median).astype(int)
        
        # Select only numeric features for training
        numeric_cols = engineered_df.select_dtypes(include=[np.number]).columns.tolist()
        
        # Remove identifier columns
        exclude_cols = ['player_name', 'season', 'team_abbreviation']
        numeric_cols = [col for col in numeric_cols if col not in exclude_cols]
        
        logger.info(f"✅ Created {len(numeric_cols)} engineered features: {numeric_cols[:10]}...")
        
        return engineered_df[['player_name', 'season', 'team_abbreviation'] + numeric_cols]

def main():
    """Main function to fix the feature engineering pipeline"""
    logger.info("🚀 Starting Feature Engineering Pipeline Fix")
    
    # Load real training data
    data_path = Path("data/clean_wnba_training_data.csv")
    if not data_path.exists():
        logger.error(f"❌ Training data not found: {data_path}")
        return
    
    logger.info(f"📂 Loading real training data from {data_path}")
    df = pd.read_csv(data_path)
    logger.info(f"📊 Loaded {len(df)} records with columns: {list(df.columns)}")
    
    # Initialize feature engineer
    engineer = RealFeatureEngineer()
    
    # Engineer features from real data
    engineered_df = engineer.engineer_features_from_real_data(df)
    
    # Save engineered features
    output_path = Path("data/engineered_wnba_training_data.csv")
    engineered_df.to_csv(output_path, index=False)
    logger.info(f"💾 Saved engineered features to {output_path}")
    
    # Display summary
    numeric_cols = engineered_df.select_dtypes(include=[np.number]).columns.tolist()
    logger.info(f"📊 Final dataset: {len(engineered_df)} records, {len(numeric_cols)} features")
    logger.info(f"📊 Sample features: {numeric_cols[:10]}")
    
    # Show sample data
    print("\n📈 Sample Engineered Data:")
    print(engineered_df.head())
    
    print("\n📊 Feature Statistics:")
    print(engineered_df[numeric_cols].describe())
    
    logger.info("✅ Feature engineering pipeline fix completed!")
    logger.info("🔄 Next steps:")
    logger.info("1. Update training pipeline to use engineered features")
    logger.info("2. Retrain models with real feature-engineered data")
    logger.info("3. Update prediction service to use same feature engineering")

if __name__ == "__main__":
    main()
