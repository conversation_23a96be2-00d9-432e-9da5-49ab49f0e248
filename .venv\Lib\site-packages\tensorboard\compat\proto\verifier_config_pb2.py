# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/verifier_config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n.tensorboard/compat/proto/verifier_config.proto\x12\x0btensorboard\"\x9c\x01\n\x0eVerifierConfig\x12\"\n\x1averification_timeout_in_ms\x18\x01 \x01(\x03\x12>\n\x12structure_verifier\x18\x02 \x01(\x0e\x32\".tensorboard.VerifierConfig.Toggle\"&\n\x06Toggle\x12\x0b\n\x07\x44\x45\x46\x41ULT\x10\x00\x12\x06\n\x02ON\x10\x01\x12\x07\n\x03OFF\x10\x02\x42\x8c\x01\n\x18org.tensorflow.frameworkB\x14VerifierConfigProtosP\x01ZUgithub.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto\xf8\x01\x01\x62\x06proto3')



_VERIFIERCONFIG = DESCRIPTOR.message_types_by_name['VerifierConfig']
_VERIFIERCONFIG_TOGGLE = _VERIFIERCONFIG.enum_types_by_name['Toggle']
VerifierConfig = _reflection.GeneratedProtocolMessageType('VerifierConfig', (_message.Message,), {
  'DESCRIPTOR' : _VERIFIERCONFIG,
  '__module__' : 'tensorboard.compat.proto.verifier_config_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.VerifierConfig)
  })
_sym_db.RegisterMessage(VerifierConfig)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkB\024VerifierConfigProtosP\001ZUgithub.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto\370\001\001'
  _VERIFIERCONFIG._serialized_start=64
  _VERIFIERCONFIG._serialized_end=220
  _VERIFIERCONFIG_TOGGLE._serialized_start=182
  _VERIFIERCONFIG_TOGGLE._serialized_end=220
# @@protoc_insertion_point(module_scope)
