{"optimization_timestamp": "2025-07-01T11:06:50.938908", "database_path": "hyper_medusa_consolidated.db", "optimization_phases": {"baseline_assessment": {"success": true, "database_size_mb": 41.828125, "total_records": 330800, "total_tables": 13, "table_statistics": {"sqlite_sequence": 0, "teams": 4021, "players": 24153, "games": 135816, "player_game_stats": 26213, "shot_analytics": 10, "clutch_analytics": 1458, "shooting_analytics": 20, "advanced_analytics": 1733, "defense_analytics": 1733, "player_performance_labels": 26213, "game_context_labels": 108531, "clutch_situation_labels": 899}, "query_performance": [{"query": "SELECT * FROM player_game_stats WHERE hero_id = ? AND season = ?", "execution_time_ms": 5.77, "result_count": 0}, {"query": "SELECT AVG(points), AVG(total_rebounds), AVG(assists) FROM player_game_stats WHERE hero_id = ?", "execution_time_ms": 4.52, "result_count": 1}, {"query": "SELECT * FROM player_game_stats WHERE points > ? AND date >= ?", "execution_time_ms": 4.19, "result_count": 0}, {"query": "SELECT * FROM games WHERE date BETWEEN ? AND ? ORDER BY date", "execution_time_ms": 67.94, "result_count": 34}, {"query": "SELECT * FROM games WHERE home_team_id = ? OR away_team_id = ?", "execution_time_ms": 74.95, "result_count": 0}], "average_query_time_ms": 31.47, "slow_queries_count": 0, "existing_indexes": {"sqlite_sequence": 0, "teams": 0, "players": 0, "games": 0, "player_game_stats": 0, "shot_analytics": 0, "clutch_analytics": 0, "shooting_analytics": 0, "advanced_analytics": 0, "defense_analytics": 0, "player_performance_labels": 0, "game_context_labels": 0, "clutch_situation_labels": 0}, "performance_level": "good", "baseline_metrics": {"total_records": 330800, "database_size_mb": 41.828125, "average_query_time_ms": 31.474, "slow_queries_count": 0, "overall_performance_score": 57.052}}, "indexing_optimization": {"success": true, "indexing_results": {"indexes_created": [{"index_name": "idx_player_performance", "table_name": "player_game_stats", "columns": ["hero_id", "points", "total_rebounds", "assists"], "creation_time_seconds": 0.042, "basketball_specific": true, "estimated_improvement": 0.75}, {"index_name": "idx_player_season", "table_name": "player_game_stats", "columns": ["hero_id", "season", "date"], "creation_time_seconds": 0.027, "basketball_specific": true, "estimated_improvement": 0.8}, {"index_name": "idx_player_efficiency", "table_name": "player_game_stats", "columns": ["hero_id", "field_goal_percentage", "three_point_percentage"], "creation_time_seconds": 0.045, "basketball_specific": true, "estimated_improvement": 0.65}, {"index_name": "idx_game_date_season", "table_name": "games", "columns": ["date", "season"], "creation_time_seconds": 0.244, "basketball_specific": true, "estimated_improvement": 0.85}, {"index_name": "idx_game_teams", "table_name": "games", "columns": ["home_team_id", "away_team_id"], "creation_time_seconds": 0.173, "basketball_specific": true, "estimated_improvement": 0.7}, {"index_name": "idx_clutch_situations", "table_name": "clutch_situation_labels", "columns": ["player_id", "clutch_tier", "clutch_efficiency"], "creation_time_seconds": 0.005, "basketball_specific": true, "estimated_improvement": 0.6}], "indexes_failed": [{"index_name": "idx_clutch_performance", "table_name": "clutch_analytics", "error": "no such column: clutch_efficiency"}, {"index_name": "idx_shot_zones", "table_name": "shot_analytics", "error": "no such column: zone_type"}, {"index_name": "idx_advanced_metrics", "table_name": "advanced_analytics", "error": "no such column: efficiency_rating"}, {"index_name": "idx_performance_tier", "table_name": "player_performance_labels", "error": "no such column: hero_id"}, {"index_name": "idx_game_context", "table_name": "game_context_labels", "error": "no such column: titan_clash_id"}], "performance_improvements": [], "total_indexes_created": 6}, "message": "Successfully created 6 basketball-specific indexes"}, "query_optimization": {"success": true, "query_optimizations": [{"query_pattern": "SELECT * FROM player_game_stats WHERE hero_id = ? AND season = ?", "execution_time_ms": 1.14, "result_count": 0, "query_plan": ["(3, 0, 61, 'SEARCH player_game_stats USING INDEX idx_player_season (hero_id=? AND season=?)')"], "performance_level": "excellent"}, {"query_pattern": "SELECT AVG(points), AVG(total_rebounds), AVG(assists) FROM player_game_stats WHERE hero_id = ?", "execution_time_ms": 0.12, "result_count": 1, "query_plan": ["(3, 0, 53, 'SEARCH player_game_stats USING COVERING INDEX idx_player_performance (hero_id=?)')"], "performance_level": "excellent"}, {"query_pattern": "SELECT * FROM player_game_stats WHERE points > ? AND date >= ?", "execution_time_ms": 8.54, "result_count": 0, "query_plan": ["(2, 0, 216, 'SCAN player_game_stats')"], "performance_level": "excellent"}, {"query_pattern": "SELECT * FROM games WHERE date BETWEEN ? AND ? ORDER BY date", "execution_time_ms": 0.5, "result_count": 34, "query_plan": ["(4, 0, 163, 'SEARCH games USING INDEX idx_game_date_season (date>? AND date<?)')"], "performance_level": "excellent"}, {"query_pattern": "SELECT * FROM games WHERE home_team_id = ? OR away_team_id = ?", "execution_time_ms": 57.17, "result_count": 0, "query_plan": ["(2, 0, 216, 'SCAN games')"], "performance_level": "fair"}, {"query_pattern": "SELECT * FROM clutch_analytics WHERE clutch_efficiency > ? ORDER BY clutch_volume DESC", "error": "no such column: clutch_efficiency", "execution_time_ms": 0}, {"query_pattern": "SELECT * FROM advanced_analytics WHERE efficiency_rating > ? AND usage_rate > ?", "error": "no such column: efficiency_rating", "execution_time_ms": 0}, {"query_pattern": "SELECT p.full_name, AVG(pgs.points), COUNT(*) \n               FROM players p JOIN player_game_stats ...", "execution_time_ms": 12.16, "result_count": 0, "query_plan": ["(8, 0, 216, 'SCAN p')", "(10, 0, 61, 'SEARCH pgs USING INDEX idx_player_season (hero_id=? AND season=?)')", "(18, 0, 0, 'USE TEMP B-TREE FOR GROUP BY')"], "performance_level": "good"}, {"query_pattern": "SELECT * FROM player_performance_labels WHERE performance_tier = ?", "execution_time_ms": 5.12, "result_count": 0, "query_plan": ["(2, 0, 216, 'SCAN player_performance_labels')"], "performance_level": "excellent"}, {"query_pattern": "SELECT * FROM game_context_labels WHERE game_competitiveness = 'high'", "execution_time_ms": 28.24, "result_count": 0, "query_plan": ["(2, 0, 216, 'SCAN game_context_labels')"], "performance_level": "good"}], "total_queries_tested": 10, "successful_queries": 8, "average_query_time_ms": 14.12, "performance_level": "good", "message": "Analyzed 8 query patterns successfully"}, "maintenance": {"success": true, "maintenance_results": {"operations_performed": [{"operation": "VACUUM", "duration_seconds": 1.01, "space_reclaimed_mb": 3.09}, {"operation": "ANALYZE", "duration_seconds": 0.08, "description": "Updated table statistics for query optimizer"}, {"operation": "INTEGRITY_CHECK", "duration_seconds": 0.41, "result": "ok"}, {"operation": "CONFIGURATION_UPDATE", "description": "Updated SQLite configuration for optimal performance", "settings": {"cache_size": 10000, "temp_store": "MEMORY", "journal_mode": "WAL", "synchronous": "NORMAL"}}], "space_reclaimed_mb": 3.09, "integrity_check": "ok"}, "message": "Database maintenance completed. Reclaimed 3.09 MB of space."}, "validation": {"success": true, "database_size_mb": 38.74609375, "total_records": 330815, "total_tables": 13, "table_statistics": {"teams": 4021, "players": 24153, "games": 135816, "player_game_stats": 26213, "shot_analytics": 10, "clutch_analytics": 1458, "shooting_analytics": 20, "advanced_analytics": 1733, "defense_analytics": 1733, "player_performance_labels": 26213, "game_context_labels": 108531, "clutch_situation_labels": 899, "sqlite_stat1": 15}, "query_performance": [{"query": "SELECT * FROM player_game_stats WHERE hero_id = ? AND season = ?", "execution_time_ms": 0.28, "result_count": 0}, {"query": "SELECT AVG(points), AVG(total_rebounds), AVG(assists) FROM player_game_stats WHERE hero_id = ?", "execution_time_ms": 0.19, "result_count": 1}, {"query": "SELECT * FROM player_game_stats WHERE points > ? AND date >= ?", "execution_time_ms": 8.98, "result_count": 0}, {"query": "SELECT * FROM games WHERE date BETWEEN ? AND ? ORDER BY date", "execution_time_ms": 0.57, "result_count": 34}, {"query": "SELECT * FROM games WHERE home_team_id = ? OR away_team_id = ?", "execution_time_ms": 44.08, "result_count": 0}], "average_query_time_ms": 10.82, "slow_queries_count": 0, "existing_indexes": {"teams": 0, "players": 0, "games": 2, "player_game_stats": 3, "shot_analytics": 0, "clutch_analytics": 0, "shooting_analytics": 0, "advanced_analytics": 0, "defense_analytics": 0, "player_performance_labels": 0, "game_context_labels": 0, "clutch_situation_labels": 1, "sqlite_stat1": 0}, "performance_level": "good", "baseline_metrics": {"total_records": 330815, "database_size_mb": 38.74609375, "average_query_time_ms": 10.82, "slow_queries_count": 0, "overall_performance_score": 98.36}, "improvements": {"query_time_improvement": {"before_ms": 10.82, "after_ms": 10.82, "improvement_percentage": 0.0}, "slow_queries_improvement": {"before_count": 0, "after_count": 0, "improvement_count": 0}, "performance_score_improvement": {"before_score": 98.36, "after_score": 98.36, "improvement_points": 0.0}}, "optimization_successful": false}}, "total_optimization_time_seconds": 2.46, "overall_improvement": {"query_time_improvement_percentage": 65.62, "slow_queries_reduced": 0, "baseline_performance_level": "good", "optimized_performance_level": "good", "optimization_successful": true, "summary": "Query time improved by 65.6%, reduced 0 slow queries"}}