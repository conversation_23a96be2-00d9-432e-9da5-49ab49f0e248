import json
import logging
import numpy as np
import random
from datetime import datetime

#!/usr/bin/env python3
"""
PROPS-TO-GAME INTEGRATION DEMONSTRATION
======================================

Simple demonstration of the props-to-game integration strategy.
Shows how leveraging 75% player props accuracy can improve game predictions.
"""


logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("PROPS_DEMO")

class PropsIntegrationDemo:
    """Demonstrates props-to-game integration strategy"""
    
    def __init__(self):
        # Known performance metrics
        self.props_accuracy = 0.75      # Our excellent props accuracy
        self.base_game_accuracy = 0.429 # Current game accuracy (from WNBA validation)
        self.target_improvement = 0.08  # Expected +8% improvement
        self.baseline_target = 0.65     # 65% baseline target
        
        # WNBA teams
        self.teams = [
            "Las Vegas Aces", "New York Liberty", "Connecticut Sun", "Seattle Storm",
            "Minnesota Lynx", "Indiana Fever", "Chicago Sky", "Atlanta Dream"
        ]
        
        # Team strength ratings
        self.team_strengths = {
            "Las Vegas Aces": 0.82, "New York Liberty": 0.78, "Connecticut Sun": 0.75,
            "Seattle Storm": 0.72, "Minnesota Lynx": 0.68, "Indiana Fever": 0.65,
            "Chicago Sky": 0.62, "Atlanta Dream": 0.60
        }
    
    def simulate_player_props_prediction(self, team: str) -> dict:
        """Simulate player props prediction with 75% accuracy"""
        # Simulate 3 key players per team
        team_props = []
        
        for i in range(3):
            player_name = f"{team} Player {i+1}"
            
            # Simulate actual performance
            if i == 0:  # Star player
                actual_points = random.uniform(18, 26)
            elif i == 1:  # Second option  
                actual_points = random.uniform(12, 20)
            else:  # Role player
                actual_points = random.uniform(6, 14)
            
            # Predict with 75% accuracy
            if random.random() < self.props_accuracy:
                # Good prediction (within 2 points)
                predicted_points = actual_points + random.uniform(-2, 2)
                confidence = random.uniform(0.72, 0.85)
            else:
                # Poor prediction
                predicted_points = actual_points + random.uniform(-5, 5)
                confidence = random.uniform(0.55, 0.70)
            
            team_props.append({
                'player': player_name,
                'predicted_points': max(0, predicted_points),
                'actual_points': max(0, actual_points),
                'confidence': confidence
            })
        
        # Calculate team totals
        predicted_total = sum(p['predicted_points'] for p in team_props)
        actual_total = sum(p['actual_points'] for p in team_props)
        avg_confidence = np.mean([p['confidence'] for p in team_props])
        
        return {
            'team': team,
            'players': team_props,
            'predicted_total_points': predicted_total,
            'actual_total_points': actual_total,
            'confidence': avg_confidence
        }
    
    def base_game_prediction(self, home_team: str, away_team: str) -> dict:
        """Simulate base game prediction with current 42.9% accuracy"""
        home_strength = self.team_strengths.get(home_team, 0.60)
        away_strength = self.team_strengths.get(away_team, 0.60)
        
        # Add noise to reflect poor current performance
        noise = 0.3
        home_strength_noisy = home_strength + random.uniform(-noise, noise)
        away_strength_noisy = away_strength + random.uniform(-noise, noise)
        
        # Calculate win probability with home advantage
        total_strength = home_strength_noisy + away_strength_noisy
        home_win_prob = home_strength_noisy / total_strength if total_strength > 0 else 0.5
        home_win_prob += 0.08  # Home advantage
        home_win_prob = max(0.1, min(0.9, home_win_prob))
        
        return {
            'method': 'base_game_model',
            'home_win_probability': home_win_prob,
            'confidence': random.uniform(0.55, 0.70)
        }
    
    def props_to_game_prediction(self, home_props: dict, away_props: dict) -> dict:
        """Convert player props to game prediction"""
        home_total = home_props['predicted_total_points']
        away_total = away_props['predicted_total_points']
        
        # Calculate win probability based on predicted scoring
        score_diff = home_total - away_total
        home_advantage = 3.0  # 3-point home advantage
        adjusted_diff = score_diff + home_advantage
        
        # Convert to probability (sigmoid-like)
        home_win_prob = 1 / (1 + np.exp(-adjusted_diff / 8.0))
        home_win_prob = max(0.15, min(0.85, home_win_prob))
        
        # Average confidence from both teams
        confidence = (home_props['confidence'] + away_props['confidence']) / 2
        
        return {
            'method': 'props_aggregation',
            'home_win_probability': home_win_prob,
            'confidence': confidence,
            'home_predicted_score': home_total,
            'away_predicted_score': away_total
        }
    
    def integrate_predictions(self, props_pred: dict, base_pred: dict) -> dict:
        """Integrate props and base predictions"""
        # Weight props higher due to 75% accuracy
        props_weight = 0.6
        base_weight = 0.4
        
        # Confidence adjustment
        props_conf = props_pred['confidence']
        base_conf = base_pred['confidence']
        
        if props_conf > base_conf:
            props_weight += 0.1
            base_weight -= 0.1
        
        # Integrate probabilities
        integrated_prob = (
            props_weight * props_pred['home_win_probability'] +
            base_weight * base_pred['home_win_probability']
        )
        
        integrated_conf = props_weight * props_conf + base_weight * base_conf
        
        return {
            'method': 'props_integrated',
            'home_win_probability': integrated_prob,
            'confidence': integrated_conf,
            'weights': {'props': props_weight, 'base': base_weight}
        }
    
    def test_single_game(self, home_team: str, away_team: str) -> dict:
        """Test prediction methods on a single game"""
        logger.info(f"Testing: {home_team} vs {away_team}")
        
        # Determine actual winner based on team strengths
        home_strength = self.team_strengths.get(home_team, 0.60)
        away_strength = self.team_strengths.get(away_team, 0.60)
        true_home_win_prob = (home_strength + 0.08) / (home_strength + away_strength + 0.08)
        actual_home_win = random.random() < true_home_win_prob
        
        # Get player props
        home_props = self.simulate_player_props_prediction(home_team)
        away_props = self.simulate_player_props_prediction(away_team)
        
        # Get base prediction
        base_pred = self.base_game_prediction(home_team, away_team)
        
        # Get props-based prediction
        props_pred = self.props_to_game_prediction(home_props, away_props)
        
        # Get integrated prediction
        integrated_pred = self.integrate_predictions(props_pred, base_pred)
        
        # Evaluate predictions
        def evaluate_prediction(pred, actual_win):
            predicted_win = pred['home_win_probability'] > 0.5
            return predicted_win == actual_win
        
        base_correct = evaluate_prediction(base_pred, actual_home_win)
        props_correct = evaluate_prediction(props_pred, actual_home_win)
        integrated_correct = evaluate_prediction(integrated_pred, actual_home_win)
        
        return {
            'home_team': home_team,
            'away_team': away_team,
            'actual_home_win': actual_home_win,
            'base_prediction': {**base_pred, 'correct': base_correct},
            'props_prediction': {**props_pred, 'correct': props_correct},
            'integrated_prediction': {**integrated_pred, 'correct': integrated_correct},
            'home_props': home_props,
            'away_props': away_props
        }
    
    def run_comprehensive_test(self, num_games: int = 20) -> dict:
        """Run comprehensive test of integration strategy"""
        logger.info("STARTING PROPS-TO-GAME INTEGRATION TEST")
        logger.info("=" * 50)
        
        results = []
        base_correct = 0
        props_correct = 0
        integrated_correct = 0
        
        for i in range(num_games):
            # Random matchup
            home_team = random.choice(self.teams)
            away_team = random.choice([t for t in self.teams if t != home_team])
            
            game_result = self.test_single_game(home_team, away_team)
            results.append(game_result)
            
            if game_result['base_prediction']['correct']:
                base_correct += 1
            if game_result['props_prediction']['correct']:
                props_correct += 1
            if game_result['integrated_prediction']['correct']:
                integrated_correct += 1
        
        # Calculate accuracies
        base_accuracy = base_correct / num_games
        props_accuracy = props_correct / num_games
        integrated_accuracy = integrated_correct / num_games
        
        # Calculate improvements
        props_improvement = props_accuracy - base_accuracy
        integrated_improvement = integrated_accuracy - base_accuracy
        
        # Check targets
        target_achieved = integrated_improvement >= self.target_improvement
        baseline_achieved = integrated_accuracy >= self.baseline_target
        
        summary = {
            'test_timestamp': datetime.now().isoformat(),
            'games_tested': num_games,
            'accuracies': {
                'base_game': base_accuracy,
                'props_only': props_accuracy,
                'integrated': integrated_accuracy
            },
            'improvements': {
                'props_vs_base': props_improvement,
                'integrated_vs_base': integrated_improvement
            },
            'targets': {
                'target_improvement': self.target_improvement,
                'target_achieved': target_achieved,
                'baseline_target': self.baseline_target,
                'baseline_achieved': baseline_achieved
            },
            'detailed_results': results
        }
        
        # Log results
        self.log_results(summary)
        
        return summary
    
    def log_results(self, summary: dict) -> None:
        """Log comprehensive test results"""
        acc = summary['accuracies']
        imp = summary['improvements']
        targets = summary['targets']
        
        logger.info("\n" + "=" * 50)
        logger.info("PROPS-TO-GAME INTEGRATION TEST RESULTS")
        logger.info("=" * 50)
        
        logger.info(f"Games Tested: {summary['games_tested']}")
        logger.info(f"Base Game Accuracy: {acc['base_game']*100:.1f}%")
        logger.info(f"Props-Only Accuracy: {acc['props_only']*100:.1f}%")
        logger.info(f"Integrated Accuracy: {acc['integrated']*100:.1f}%")
        
        logger.info(f"\nImprovements:")
        logger.info(f"Props vs Base: +{imp['props_vs_base']*100:.1f}%")
        logger.info(f"Integrated vs Base: +{imp['integrated_vs_base']*100:.1f}%")
        
        if targets['target_achieved']:
            logger.info("TARGET IMPROVEMENT ACHIEVED!")
        else:
            deficit = (targets['target_improvement'] - imp['integrated_vs_base']) * 100
            logger.info(f"Target missed by {deficit:.1f}%")
        
        if targets['baseline_achieved']:
            logger.info("BASELINE ACCURACY ACHIEVED!")
        else:
            deficit = (targets['baseline_target'] - acc['integrated']) * 100
            logger.info(f"Baseline missed by {deficit:.1f}%")
        
        if imp['integrated_vs_base'] > 0:
            logger.info("STRATEGY IS EFFECTIVE!")
        else:
            logger.info("Strategy needs refinement")
        
        logger.info("=" * 50)

def main():
    """Main demonstration"""
    
    demo = PropsIntegrationDemo()
    results = demo.run_comprehensive_test(num_games=20)
    
    # Save results (convert numpy bools to Python bools for JSON)
    def convert_for_json(obj):
        if isinstance(obj, np.bool_):
            return bool(obj)
        elif isinstance(obj, dict):
            return {k: convert_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_for_json(item) for item in obj]
        return obj

    json_results = convert_for_json(results)
    results_file = f"props_integration_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(json_results, f, indent=2)
    
    
    return results

if __name__ == "__main__":
    main()
