# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/plugins/projector/projector_config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n4tensorboard/plugins/projector/projector_config.proto\x12\x15tensorboard.projector\">\n\x0eSpriteMetadata\x12\x12\n\nimage_path\x18\x01 \x01(\t\x12\x18\n\x10single_image_dim\x18\x02 \x03(\r\"\xb5\x01\n\rEmbeddingInfo\x12\x13\n\x0btensor_name\x18\x01 \x01(\t\x12\x15\n\rmetadata_path\x18\x02 \x01(\t\x12\x16\n\x0e\x62ookmarks_path\x18\x03 \x01(\t\x12\x14\n\x0ctensor_shape\x18\x04 \x03(\r\x12\x35\n\x06sprite\x18\x05 \x01(\x0b\x32%.tensorboard.projector.SpriteMetadata\x12\x13\n\x0btensor_path\x18\x06 \x01(\t\"\x88\x01\n\x0fProjectorConfig\x12\x1d\n\x15model_checkpoint_path\x18\x01 \x01(\t\x12\x38\n\nembeddings\x18\x02 \x03(\x0b\x32$.tensorboard.projector.EmbeddingInfo\x12\x1c\n\x14model_checkpoint_dir\x18\x03 \x01(\tb\x06proto3')



_SPRITEMETADATA = DESCRIPTOR.message_types_by_name['SpriteMetadata']
_EMBEDDINGINFO = DESCRIPTOR.message_types_by_name['EmbeddingInfo']
_PROJECTORCONFIG = DESCRIPTOR.message_types_by_name['ProjectorConfig']
SpriteMetadata = _reflection.GeneratedProtocolMessageType('SpriteMetadata', (_message.Message,), {
  'DESCRIPTOR' : _SPRITEMETADATA,
  '__module__' : 'tensorboard.plugins.projector.projector_config_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.projector.SpriteMetadata)
  })
_sym_db.RegisterMessage(SpriteMetadata)

EmbeddingInfo = _reflection.GeneratedProtocolMessageType('EmbeddingInfo', (_message.Message,), {
  'DESCRIPTOR' : _EMBEDDINGINFO,
  '__module__' : 'tensorboard.plugins.projector.projector_config_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.projector.EmbeddingInfo)
  })
_sym_db.RegisterMessage(EmbeddingInfo)

ProjectorConfig = _reflection.GeneratedProtocolMessageType('ProjectorConfig', (_message.Message,), {
  'DESCRIPTOR' : _PROJECTORCONFIG,
  '__module__' : 'tensorboard.plugins.projector.projector_config_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.projector.ProjectorConfig)
  })
_sym_db.RegisterMessage(ProjectorConfig)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _SPRITEMETADATA._serialized_start=79
  _SPRITEMETADATA._serialized_end=141
  _EMBEDDINGINFO._serialized_start=144
  _EMBEDDINGINFO._serialized_end=325
  _PROJECTORCONFIG._serialized_start=328
  _PROJECTORCONFIG._serialized_end=464
# @@protoc_insertion_point(module_scope)
