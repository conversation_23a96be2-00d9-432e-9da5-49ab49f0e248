#!/usr/bin/env python3
"""
Comprehensive training script for all WNBA player props using the proven architecture
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import time
from datetime import datetime
from src.neural_cortex.player_props_neural_pipeline import PlayerPropsConfig, PlayerPropsTrainingPipeline

# All player props to train
PLAYER_PROPS = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']

def create_optimized_config(prop_type: str) -> PlayerPropsConfig:
    """Create optimized config based on proven architecture"""
    return PlayerPropsConfig(
        prop_type=prop_type,
        league='WNBA',
        num_epochs=15,  # Full training
        batch_size=64,
        learning_rate=0.001,
        output_activation='linear',  # PROVEN: Linear for regression
        dropout_rate=0.2,  # PROVEN: Reduced dropout
        hidden_dim=256,  # PROVEN: Increased capacity
        num_layers=3,
        use_batch_norm=True,
        early_stopping_patience=5,
        # Feature engineering
        include_player_features=True,
        include_matchup_features=True,
        include_situational_features=True,
        include_recent_form=True
    )

async def train_single_prop(prop_type: str) -> dict:
    """Train a single player prop model"""
    print(f"\n🏀 ===== TRAINING {prop_type.upper()} MODEL =====")
    start_time = time.time()
    
    # Create optimized config
    config = create_optimized_config(prop_type)
    
    # Create pipeline
    pipeline = PlayerPropsTrainingPipeline(config)
    
    print(f"📊 Config: {config.output_activation} output, {config.hidden_dim} hidden, {config.dropout_rate} dropout")
    print(f"🎯 Target: WNBA {prop_type} prediction")
    print(f"📈 Epochs: {config.num_epochs}, Batch: {config.batch_size}, LR: {config.learning_rate}")
    
    # Train model
    try:
        results = await pipeline.train()
        
        # Extract metrics
        test_metrics = results['test_metrics']
        training_time = time.time() - start_time
        
        print(f"\n✅ {prop_type.upper()} TRAINING COMPLETED!")
        print(f"⏱️  Training Time: {training_time:.1f}s")
        print(f"📊 Test MAE: {test_metrics['mae']:.3f}")
        print(f"📊 Test R²: {test_metrics['r2']:.3f}")
        print(f"🎯 Accuracy (±1 unit): {test_metrics['accuracy_1pt']:.1f}%")
        print(f"🎯 Accuracy (±2 units): {test_metrics['accuracy_2pt']:.1f}%")
        print(f"🎯 Accuracy (±25%): {test_metrics['accuracy_25pct']:.1f}%")
        
        # Quality assessment
        if test_metrics['r2'] > 0.6:
            print("🏆 EXCELLENT: R² > 0.6 - Production ready!")
        elif test_metrics['r2'] > 0.4:
            print("✅ GOOD: R² > 0.4 - Solid performance")
        elif test_metrics['r2'] > 0.2:
            print("⚠️  FAIR: R² > 0.2 - Acceptable but could improve")
        else:
            print("❌ POOR: R² < 0.2 - Needs investigation")
        
        return {
            'prop_type': prop_type,
            'success': True,
            'training_time': training_time,
            'metrics': test_metrics,
            'config': config
        }
        
    except Exception as e:
        print(f"❌ ERROR training {prop_type}: {e}")
        import traceback
        traceback.print_exc()
        return {
            'prop_type': prop_type,
            'success': False,
            'error': str(e),
            'training_time': time.time() - start_time
        }

async def train_all_props():
    """Train all player props models"""
    print("🚀 ===== WNBA PLAYER PROPS COMPREHENSIVE TRAINING =====")
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Props to train: {', '.join(PLAYER_PROPS)}")
    print(f"📊 Using proven architecture: Linear output + Proper scaling + Optimized hyperparameters")
    
    overall_start = time.time()
    results = []
    
    # Train each prop sequentially to avoid memory issues
    for prop_type in PLAYER_PROPS:
        result = await train_single_prop(prop_type)
        results.append(result)
        
        # Brief pause between models
        await asyncio.sleep(2)
    
    # Summary report
    total_time = time.time() - overall_start
    successful_models = [r for r in results if r['success']]
    failed_models = [r for r in results if not r['success']]
    
    print(f"\n🏆 ===== TRAINING SUMMARY =====")
    print(f"⏱️  Total Time: {total_time:.1f}s ({total_time/60:.1f} minutes)")
    print(f"✅ Successful: {len(successful_models)}/{len(PLAYER_PROPS)}")
    print(f"❌ Failed: {len(failed_models)}")
    
    if successful_models:
        print(f"\n📊 ===== MODEL PERFORMANCE =====")
        for result in successful_models:
            metrics = result['metrics']
            prop = result['prop_type']
            print(f"{prop:8s}: R²={metrics['r2']:.3f}, MAE={metrics['mae']:.3f}, "
                  f"±1={metrics['accuracy_1pt']:.1f}%, ±2={metrics['accuracy_2pt']:.1f}%")
        
        # Best performing model
        best_model = max(successful_models, key=lambda x: x['metrics']['r2'])
        print(f"\n🏆 Best Model: {best_model['prop_type']} (R² = {best_model['metrics']['r2']:.3f})")
        
        # Average performance
        avg_r2 = sum(r['metrics']['r2'] for r in successful_models) / len(successful_models)
        avg_mae = sum(r['metrics']['mae'] for r in successful_models) / len(successful_models)
        avg_acc_2pt = sum(r['metrics']['accuracy_2pt'] for r in successful_models) / len(successful_models)
        
        print(f"📈 Average Performance: R²={avg_r2:.3f}, MAE={avg_mae:.3f}, ±2pts={avg_acc_2pt:.1f}%")
    
    if failed_models:
        print(f"\n❌ ===== FAILED MODELS =====")
        for result in failed_models:
            print(f"{result['prop_type']}: {result['error']}")
    
    print(f"\n🎯 All models saved to: models/player_props/wnba_*")
    print(f"📅 Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return results

if __name__ == "__main__":
    try:
        results = asyncio.run(train_all_props())
        
        # Final status
        successful_count = sum(1 for r in results if r['success'])
        if successful_count == len(PLAYER_PROPS):
            print(f"\n🎉 SUCCESS: All {len(PLAYER_PROPS)} player props models trained successfully!")
            print("🚀 WNBA Player Props Neural System is now PRODUCTION READY!")
        else:
            print(f"\n⚠️  PARTIAL SUCCESS: {successful_count}/{len(PLAYER_PROPS)} models trained")
            
    except KeyboardInterrupt:
        print("\n⏹️  Training interrupted by user")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        import traceback
        traceback.print_exc()
