import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
import os

#!/usr/bin/env python3
"""
Check NBA Data Quality
Investigate NBA dataset for similar issues as WNBA data
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NBADataQualityChecker:
    """Check NBA data quality and compare with WNBA"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def comprehensive_nba_analysis(self) -> Dict[str, Any]:
        """Comprehensive analysis of NBA data quality"""
        logger.info("🔍 COMPREHENSIVE NBA DATA ANALYSIS")
        logger.info("=" * 50)
        
        conn = sqlite3.connect(self.db_path)
        
        # Basic NBA statistics
        basic_stats = self._get_basic_nba_stats(conn)
        
        # Source file analysis
        source_analysis = self._analyze_nba_source_files(conn)
        
        # Player data quality
        player_quality = self._analyze_nba_player_data(conn)
        
        # Duplicate analysis
        duplicate_analysis = self._check_nba_duplicates(conn)
        
        # Compare with WNBA
        comparison = self._compare_nba_vs_wnba(conn)
        
        conn.close()
        
        return {
            'basic_stats': basic_stats,
            'source_analysis': source_analysis,
            'player_quality': player_quality,
            'duplicate_analysis': duplicate_analysis,
            'nba_vs_wnba_comparison': comparison
        }
    
    def _get_basic_nba_stats(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Get basic NBA dataset statistics"""
        logger.info("📊 Getting basic NBA statistics...")
        
        # Total records
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM unified_nba_wnba_data WHERE league_name = 'NBA'")
        total_records = cursor.fetchone()[0]
        
        # Records with player names
        cursor.execute("""
        SELECT COUNT(*) FROM unified_nba_wnba_data 
        WHERE league_name = 'NBA' 
        AND player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None'
        """)
        records_with_players = cursor.fetchone()[0]
        
        # Unique players
        cursor.execute("""
        SELECT COUNT(DISTINCT player_name) FROM unified_nba_wnba_data 
        WHERE league_name = 'NBA' 
        AND player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None'
        """)
        unique_players = cursor.fetchone()[0]
        
        # Unique seasons
        cursor.execute("SELECT COUNT(DISTINCT season) FROM unified_nba_wnba_data WHERE league_name = 'NBA'")
        unique_seasons = cursor.fetchone()[0]
        
        # Data categories
        cursor.execute("SELECT COUNT(DISTINCT data_category) FROM unified_nba_wnba_data WHERE league_name = 'NBA'")
        data_categories = cursor.fetchone()[0]
        
        logger.info(f"📊 NBA BASIC STATISTICS:")
        logger.info(f"   Total records: {total_records:,}")
        logger.info(f"   Records with players: {records_with_players:,}")
        logger.info(f"   Unique players: {unique_players:,}")
        logger.info(f"   Unique seasons: {unique_seasons}")
        logger.info(f"   Data categories: {data_categories}")
        
        return {
            'total_records': total_records,
            'records_with_players': records_with_players,
            'unique_players': unique_players,
            'unique_seasons': unique_seasons,
            'data_categories': data_categories,
            'player_percentage': (records_with_players / total_records * 100) if total_records > 0 else 0
        }
    
    def _analyze_nba_source_files(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Analyze NBA source files"""
        logger.info("📁 Analyzing NBA source files...")
        
        query = """
        SELECT 
            source_file,
            data_type,
            COUNT(*) as total_records,
            COUNT(CASE WHEN player_name IS NOT NULL AND player_name != '' AND player_name != 'None' THEN 1 END) as records_with_players,
            COUNT(DISTINCT player_name) as unique_players
        FROM unified_nba_wnba_data 
        WHERE league_name = 'NBA'
        GROUP BY source_file, data_type
        ORDER BY total_records DESC
        """
        
        source_df = pd.read_sql_query(query, conn)
        
        # Identify problematic files
        problematic_files = source_df[
            (source_df['total_records'] > 1000) & 
            (source_df['unique_players'] == 0)
        ]
        
        logger.info(f"📊 NBA SOURCE FILE ANALYSIS:")
        logger.info(f"   Total source files: {len(source_df)}")
        logger.info(f"   Problematic files (>1000 records, 0 players): {len(problematic_files)}")
        
        if not problematic_files.empty:
            logger.warning("🚨 PROBLEMATIC NBA FILES:")
            for _, row in problematic_files.head(10).iterrows():
                logger.warning(f"   • {row['source_file']}: {row['total_records']:,} records, 0 players")
        
        # Top files by record count
        logger.info("📊 TOP NBA FILES BY RECORD COUNT:")
        for _, row in source_df.head(10).iterrows():
            logger.info(f"   • {row['source_file']}: {row['total_records']:,} records, {row['unique_players']} players")
        
        return {
            'total_source_files': len(source_df),
            'problematic_files_count': len(problematic_files),
            'problematic_files': problematic_files.to_dict('records'),
            'top_files': source_df.head(20).to_dict('records')
        }
    
    def _analyze_nba_player_data(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Analyze NBA player data quality"""
        logger.info("👥 Analyzing NBA player data quality...")
        
        # Player record distribution
        query = """
        SELECT 
            player_name,
            COUNT(*) as record_count,
            COUNT(DISTINCT season) as seasons_played,
            MIN(season) as first_season,
            MAX(season) as last_season
        FROM unified_nba_wnba_data 
        WHERE league_name = 'NBA'
        AND player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None'
        GROUP BY player_name
        ORDER BY record_count DESC
        LIMIT 20
        """
        
        top_players = pd.read_sql_query(query, conn)
        
        # Season distribution
        season_query = """
        SELECT 
            season,
            COUNT(*) as total_records,
            COUNT(DISTINCT player_name) as unique_players
        FROM unified_nba_wnba_data 
        WHERE league_name = 'NBA'
        AND player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None'
        GROUP BY season
        ORDER BY season
        """
        
        season_dist = pd.read_sql_query(season_query, conn)
        
        logger.info("👑 TOP NBA PLAYERS BY RECORD COUNT:")
        for _, row in top_players.head(10).iterrows():
            logger.info(f"   • {row['player_name']}: {row['record_count']:,} records ({row['seasons_played']} seasons)")
        
        logger.info("📅 NBA SEASON DISTRIBUTION:")
        for _, row in season_dist.iterrows():
            logger.info(f"   • {row['season']}: {row['total_records']:,} records, {row['unique_players']:,} players")
        
        return {
            'top_players': top_players.to_dict('records'),
            'season_distribution': season_dist.to_dict('records')
        }
    
    def _check_nba_duplicates(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Check for duplicates in NBA data"""
        logger.info("🔍 Checking NBA duplicates...")
        
        # Exact duplicates
        duplicate_query = """
        SELECT COUNT(*) FROM (
            SELECT player_name, season, stat_category, stat_value, COUNT(*) as cnt
            FROM unified_nba_wnba_data 
            WHERE league_name = 'NBA'
            GROUP BY player_name, season, stat_category, stat_value
            HAVING COUNT(*) > 1
        )
        """
        
        cursor = conn.cursor()
        cursor.execute(duplicate_query)
        duplicate_groups = cursor.fetchone()[0]
        
        # Calculate duplicate percentage
        cursor.execute("SELECT COUNT(*) FROM unified_nba_wnba_data WHERE league_name = 'NBA'")
        total_nba = cursor.fetchone()[0]
        
        logger.info(f"🔍 NBA DUPLICATE ANALYSIS:")
        logger.info(f"   Duplicate groups: {duplicate_groups:,}")
        logger.info(f"   Total records: {total_nba:,}")
        
        return {
            'duplicate_groups': duplicate_groups,
            'total_records': total_nba,
            'estimated_duplicate_percentage': (duplicate_groups / total_nba * 100) if total_nba > 0 else 0
        }
    
    def _compare_nba_vs_wnba(self, conn: sqlite3.Connection) -> Dict[str, Any]:
        """Compare NBA vs WNBA data quality"""
        logger.info("⚖️ Comparing NBA vs WNBA data...")
        
        # NBA stats
        cursor = conn.cursor()
        cursor.execute("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN player_name IS NOT NULL AND player_name != '' AND player_name != 'None' THEN 1 END) as valid_player_records,
            COUNT(DISTINCT player_name) as unique_players
        FROM unified_nba_wnba_data 
        WHERE league_name = 'NBA'
        """)
        nba_stats = cursor.fetchone()
        
        # WNBA stats
        cursor.execute("""
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN player_name IS NOT NULL AND player_name != '' AND player_name != 'None' AND player_name != 'TEAM_RECORD' AND player_name != 'NEEDS_EXTRACTION' THEN 1 END) as valid_player_records,
            COUNT(DISTINCT CASE WHEN player_name IS NOT NULL AND player_name != '' AND player_name != 'None' AND player_name != 'TEAM_RECORD' AND player_name != 'NEEDS_EXTRACTION' THEN player_name END) as unique_players
        FROM unified_nba_wnba_data 
        WHERE league_name = 'WNBA'
        """)
        wnba_stats = cursor.fetchone()
        
        comparison = {
            'nba': {
                'total_records': nba_stats[0],
                'valid_player_records': nba_stats[1],
                'unique_players': nba_stats[2],
                'player_percentage': (nba_stats[1] / nba_stats[0] * 100) if nba_stats[0] > 0 else 0
            },
            'wnba': {
                'total_records': wnba_stats[0],
                'valid_player_records': wnba_stats[1],
                'unique_players': wnba_stats[2],
                'player_percentage': (wnba_stats[1] / wnba_stats[0] * 100) if wnba_stats[0] > 0 else 0
            }
        }
        
        logger.info("⚖️ NBA vs WNBA COMPARISON:")
        logger.info(f"   NBA: {comparison['nba']['total_records']:,} total, {comparison['nba']['valid_player_records']:,} player records ({comparison['nba']['player_percentage']:.1f}%)")
        logger.info(f"   WNBA: {comparison['wnba']['total_records']:,} total, {comparison['wnba']['valid_player_records']:,} player records ({comparison['wnba']['player_percentage']:.1f}%)")
        logger.info(f"   NBA Players: {comparison['nba']['unique_players']:,}")
        logger.info(f"   WNBA Players: {comparison['wnba']['unique_players']:,}")
        
        return comparison
    
    def check_nba_data_collection_gaps(self) -> Dict[str, Any]:
        """Check for gaps in NBA data collection"""
        logger.info("🔍 Checking NBA data collection gaps...")
        
        # Check if we have NBA files in the file system
        nba_files_found = []
        data_dirs = ['data', 'data/nba_10year_historical', 'data/smart_10year_historical']
        
        for data_dir in data_dirs:
            if os.path.exists(data_dir):
                for root, dirs, files in os.walk(data_dir):
                    for file in files:
                        if ('nba' in file.lower() or 'NBA' in file) and file.endswith('.csv'):
                            file_path = os.path.join(root, file)
                            file_size = os.path.getsize(file_path)
                            nba_files_found.append({
                                'file': file,
                                'path': file_path,
                                'size': file_size
                            })
        
        logger.info(f"📁 NBA FILES IN FILESYSTEM:")
        if nba_files_found:
            logger.info(f"   Found {len(nba_files_found)} NBA files")
            for file_info in nba_files_found[:10]:  # Show first 10
                logger.info(f"   • {file_info['file']}: {file_info['size']:,} bytes")
        else:
            logger.warning("   ⚠️ No NBA files found in filesystem!")
        
        return {
            'nba_files_found': len(nba_files_found),
            'nba_files': nba_files_found
        }

def main():
    """Run comprehensive NBA data quality check"""
    
    checker = NBADataQualityChecker()
    
    # Comprehensive analysis
    analysis = checker.comprehensive_nba_analysis()
    
    basic = analysis['basic_stats']
    
    source = analysis['source_analysis']
    
    duplicates = analysis['duplicate_analysis']
    
    comparison = analysis['nba_vs_wnba_comparison']
    nba = comparison['nba']
    wnba = comparison['wnba']
    
    
    # Check for collection gaps
    gaps = checker.check_nba_data_collection_gaps()
    
    # Assessment
    
    if basic['total_records'] < 50000:
    
    if basic['player_percentage'] < 20:
    
    if duplicates['estimated_duplicate_percentage'] > 50:
    
    if gaps['nba_files_found'] == 0:
    
    # Next steps
    if basic['total_records'] < wnba['total_records'] / 2:
    
    if source['problematic_files_count'] > 0:
    
    if duplicates['estimated_duplicate_percentage'] > 30:
    
    
    return analysis

if __name__ == "__main__":
    main()
