#!/usr/bin/env python3
"""
Fixed Feature Engineering Test
Tests the corrected autonomous feature engineering system
"""

import asyncio
import sqlite3
import pandas as pd
import numpy as np
import logging
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class FixedFeatureEngineeringTest:
    """Test the fixed feature engineering system"""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.db_path = "hyper_medusa_consolidated.db"
        
    async def test_fixed_feature_engineering(self):
        """Test the fixed feature engineering system"""
        try:
            self.logger.info("🧪 Starting Fixed Feature Engineering Test...")
            
            # Test 1: Load sample data
            self.logger.info("📊 Test 1: Loading sample data...")
            data = await self._load_sample_data()
            
            if data is None or data.empty:
                self.logger.error("❌ Failed to load sample data")
                return False
                
            self.logger.info(f"✅ Loaded {len(data)} records with {len(data.columns)} columns")
            
            # Test 2: Check for duplicate columns
            self.logger.info("🔍 Test 2: Checking for duplicate columns...")
            duplicate_cols = data.columns.duplicated()
            if duplicate_cols.any():
                self.logger.warning(f"⚠️ Found {duplicate_cols.sum()} duplicate columns")
                data = data.loc[:, ~duplicate_cols]
                self.logger.info(f"✅ Removed duplicates, now {len(data.columns)} columns")
            else:
                self.logger.info("✅ No duplicate columns found")
            
            # Test 3: Handle infinite and NaN values
            self.logger.info("🧹 Test 3: Cleaning infinite and NaN values...")
            original_shape = data.shape
            
            # Replace infinite values with NaN
            data = data.replace([np.inf, -np.inf], np.nan)
            
            # Check for NaN values
            nan_counts = data.isnull().sum()
            total_nans = nan_counts.sum()
            
            self.logger.info(f"📊 Found {total_nans} NaN values across all columns")
            
            # Test 4: Feature quality assessment
            self.logger.info("🔍 Test 4: Testing feature quality assessment...")
            target_column = 'points'
            
            if target_column not in data.columns:
                self.logger.warning(f"Target column '{target_column}' not found, using first numeric column")
                numeric_cols = data.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    target_column = numeric_cols[0]
                else:
                    self.logger.error("❌ No numeric columns found")
                    return False
            
            # Calculate basic feature quality metrics
            quality_results = await self._calculate_feature_quality(data, target_column)
            
            # Test 5: Generate simple basketball features
            self.logger.info("🏀 Test 5: Generating basketball-specific features...")
            basketball_features = await self._generate_simple_basketball_features(data)
            
            # Test 6: Combine features safely
            self.logger.info("🔗 Test 6: Combining features safely...")
            combined_data = await self._safe_feature_combination(data, basketball_features)
            
            # Test 7: Final validation
            self.logger.info("✅ Test 7: Final validation...")
            final_results = {
                "original_features": len(data.columns),
                "basketball_features": len(basketball_features.columns) if not basketball_features.empty else 0,
                "combined_features": len(combined_data.columns),
                "duplicate_columns": combined_data.columns.duplicated().sum(),
                "nan_values": combined_data.isnull().sum().sum(),
                "infinite_values": np.isinf(combined_data.select_dtypes(include=[np.number])).sum().sum(),
                "quality_metrics": quality_results
            }
            
            self.logger.info("🎉 Fixed Feature Engineering Test Results:")
            for key, value in final_results.items():
                if key != "quality_metrics":
                    self.logger.info(f"   {key}: {value}")
            
            # Save results
            timestamp = int(datetime.now().timestamp())
            results_file = f"fixed_feature_engineering_results_{timestamp}.json"
            
            # Convert numpy types to Python types for JSON serialization
            json_results = self._convert_for_json(final_results)
            
            with open(results_file, 'w') as f:
                json.dump(json_results, f, indent=2)
            
            self.logger.info(f"💾 Results saved to: {results_file}")
            
            # Success criteria
            success = (
                final_results["duplicate_columns"] == 0 and
                final_results["infinite_values"] == 0 and
                final_results["combined_features"] > final_results["original_features"]
            )
            
            if success:
                self.logger.info("🎉 ✅ ALL TESTS PASSED - Feature engineering system is fixed!")
            else:
                self.logger.warning("⚠️ Some issues remain - check results for details")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Test failed: {e}")
            return False
    
    async def _load_sample_data(self):
        """Load sample data from database"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Load a sample of player game stats
            query = """
            SELECT * FROM player_game_stats 
            WHERE points IS NOT NULL 
            AND minutes_played > 0 
            LIMIT 1000
            """
            
            data = pd.read_sql_query(query, conn)
            conn.close()
            
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to load sample data: {e}")
            return None
    
    async def _calculate_feature_quality(self, data: pd.DataFrame, target_column: str):
        """Calculate basic feature quality metrics"""
        try:
            results = {}
            
            target = data[target_column]
            features = data.drop(columns=[target_column])
            numeric_features = features.select_dtypes(include=[np.number])
            
            for col in numeric_features.columns[:10]:  # Test first 10 features
                try:
                    feature_data = numeric_features[col]
                    
                    # Clean data
                    feature_clean = feature_data.replace([np.inf, -np.inf], np.nan)
                    target_clean = target.replace([np.inf, -np.inf], np.nan)
                    
                    # Calculate correlation
                    correlation = abs(target_clean.corr(feature_clean))
                    if pd.isna(correlation):
                        correlation = 0.0
                    
                    # Calculate missing percentage
                    missing_pct = feature_clean.isnull().sum() / len(feature_clean)
                    
                    results[col] = {
                        "correlation": round(correlation, 4),
                        "missing_percentage": round(missing_pct, 4),
                        "unique_values": feature_clean.nunique()
                    }
                    
                except Exception as e:
                    self.logger.debug(f"Failed to assess {col}: {e}")
                    continue
            
            return results
            
        except Exception as e:
            self.logger.error(f"Feature quality calculation failed: {e}")
            return {}
    
    async def _generate_simple_basketball_features(self, data: pd.DataFrame):
        """Generate simple basketball features"""
        try:
            basketball_features = pd.DataFrame(index=data.index)
            
            # Simple efficiency metrics
            if all(col in data.columns for col in ['points', 'minutes_played']):
                basketball_features['points_per_minute'] = (
                    data['points'] / data['minutes_played'].replace(0, 1)
                )
            
            if all(col in data.columns for col in ['field_goals_made', 'field_goals_attempted']):
                basketball_features['field_goal_percentage'] = (
                    data['field_goals_made'] / data['field_goals_attempted'].replace(0, 1)
                )
            
            if all(col in data.columns for col in ['assists', 'turnovers']):
                basketball_features['assist_turnover_ratio'] = (
                    data['assists'] / data['turnovers'].replace(0, 1)
                )
            
            # Fill NaN values
            basketball_features = basketball_features.fillna(0)
            
            return basketball_features
            
        except Exception as e:
            self.logger.error(f"Basketball feature generation failed: {e}")
            return pd.DataFrame(index=data.index)
    
    async def _safe_feature_combination(self, original_data: pd.DataFrame, new_features: pd.DataFrame):
        """Safely combine features with duplicate handling"""
        try:
            combined_data = original_data.copy()
            
            if not new_features.empty:
                # Ensure indices match
                new_features = new_features.reindex(combined_data.index, fill_value=0)
                
                # Add features while handling duplicates
                for col in new_features.columns:
                    if col in combined_data.columns:
                        # Create unique column name
                        counter = 1
                        new_col = f"{col}_engineered_{counter}"
                        while new_col in combined_data.columns:
                            counter += 1
                            new_col = f"{col}_engineered_{counter}"
                        combined_data[new_col] = new_features[col]
                        self.logger.debug(f"Renamed duplicate column '{col}' to '{new_col}'")
                    else:
                        combined_data[col] = new_features[col]
            
            # Final duplicate check
            if combined_data.columns.duplicated().any():
                combined_data = combined_data.loc[:, ~combined_data.columns.duplicated()]
            
            return combined_data
            
        except Exception as e:
            self.logger.error(f"Feature combination failed: {e}")
            return original_data
    
    def _convert_for_json(self, obj):
        """Convert numpy types to Python types for JSON serialization"""
        if isinstance(obj, dict):
            return {key: self._convert_for_json(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_for_json(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj

async def main():
    """Run the fixed feature engineering test"""
    test = FixedFeatureEngineeringTest()
    success = await test.test_fixed_feature_engineering()
    
    if success:
        print("\n🎉 ✅ FEATURE ENGINEERING SYSTEM SUCCESSFULLY FIXED!")
    else:
        print("\n⚠️ ❌ Issues remain - check logs for details")

if __name__ == "__main__":
    asyncio.run(main())
