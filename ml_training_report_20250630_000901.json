{"training_session": {"timestamp": "2025-06-30T00:09:01.469852", "database": "medusa_master.db", "database_size_mb": 50.93}, "training_results": {"hybrid_training": {"status": "failed", "reason": "no_data"}, "standard_training": {"status": "failed", "error": "No module named 'xgboost'"}, "neural_training": {"status": "failed", "error": "expected an indented block after function definition on line 149 (neural_training_pipeline.py, line 150)"}}, "summary": {"hybrid_success": false, "standard_success": false, "neural_success": false, "models_trained": 0}}