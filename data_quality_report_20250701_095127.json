{
  "timestamp": "2025-07-01T09:51:26.546643",
  "database_path": "hyper_medusa_consolidated.db",
  "tables": {
    "teams": {
      "total_records": 4021,
      "sample_records": 1000,
      "columns": 22,
      "null_percentage": 24.5,
      "duplicate_count": 0,
      "duplicate_percentage": 0.0,
      "completeness_score": 75.5,
      "uniqueness_score": 100.0,
      "quality_score": 87.75,
      "column_names": [
        "id",
        "name",
        "abbreviation",
        "city",
        "conference",
        "division",
        "league",
        "season",
        "wins",
        "losses",
        "win_percentage",
        "games_played",
        "points_per_game",
        "points_allowed_per_game",
        "net_rating",
        "offensive_rating",
        "defensive_rating",
        "pace",
        "true_shooting_percentage",
        "effective_field_goal_percentage",
        "created_at",
        "updated_at"
      ]
    },
    "players": {
      "total_records": 24153,
      "sample_records": 1000,
      "columns": 29,
      "null_percentage": 44.83,
      "duplicate_count": 0,
      "duplicate_percentage": 0.0,
      "completeness_score": 55.17,
      "uniqueness_score": 100.0,
      "quality_score": 77.59,
      "column_names": [
        "id",
        "full_name",
        "first_name",
        "last_name",
        "position",
        "height",
        "weight",
        "birth_date",
        "years_experience",
        "college",
        "country",
        "mythic_roster_id",
        "league",
        "season",
        "is_active",
        "jersey_number",
        "points_per_game",
        "rebounds_per_game",
        "assists_per_game",
        "steals_per_game",
        "blocks_per_game",
        "field_goal_percentage",
        "three_point_percentage",
        "free_throw_percentage",
        "player_efficiency_rating",
        "usage_rate",
        "true_shooting_percentage",
        "created_at",
        "updated_at"
      ]
    },
    "games": {
      "total_records": 135816,
      "sample_records": 1000,
      "columns": 21,
      "null_percentage": 67.2,
      "duplicate_count": 0,
      "duplicate_percentage": 0.0,
      "completeness_score": 32.8,
      "uniqueness_score": 100.0,
      "quality_score": 66.4,
      "column_names": [
        "id",
        "date",
        "season",
        "status",
        "home_team_id",
        "away_team_id",
        "league",
        "home_score",
        "away_score",
        "attendance",
        "arena",
        "broadcast_network",
        "spread_home",
        "spread_away",
        "total_points",
        "moneyline_home",
        "moneyline_away",
        "over_under_result",
        "spread_result_home",
        "created_at",
        "updated_at"
      ]
    },
    "player_game_stats": {
      "total_records": 26213,
      "sample_records": 1000,
      "columns": 28,
      "null_percentage": 14.29,
      "duplicate_count": 0,
      "duplicate_percentage": 0.0,
      "completeness_score": 85.71,
      "uniqueness_score": 100.0,
      "quality_score": 92.86,
      "column_names": [
        "id",
        "hero_id",
        "titan_clash_id",
        "date",
        "season",
        "league",
        "minutes_played",
        "points",
        "field_goals_made",
        "field_goals_attempted",
        "field_goal_percentage",
        "three_pointers_made",
        "three_pointers_attempted",
        "three_point_percentage",
        "free_throws_made",
        "free_throws_attempted",
        "free_throw_percentage",
        "offensive_rebounds",
        "defensive_rebounds",
        "total_rebounds",
        "assists",
        "steals",
        "blocks",
        "turnovers",
        "personal_fouls",
        "plus_minus",
        "created_at",
        "updated_at"
      ]
    }
  },
  "overall_stats": {
    "total_tables": 4,
    "total_records": 