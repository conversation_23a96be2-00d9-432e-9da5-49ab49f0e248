#!/usr/bin/env python3
"""Test script for feature engineering pipeline"""

from backend.utils.feature_engineering import ExpertFeatureEngineer
import pandas as pd

def test_feature_engineering():
    print('Testing fixed feature engineering pipeline...')

    # Create sample NBA player data
    sample_data = {
        'PLAYER_ID': 1627773,
        'PLAYER_NAME': 'Test Player',
        'TEAM_ID': 1610612742,
        'PTS': 25.5,
        'REB': 8.2,
        'AST': 6.1,
        'STL': 1.5,
        'BLK': 0.8,
        'TOV': 3.2,
        'FGM': 9.5,
        'FGA': 18.2,
        'FG_PCT': 0.522,
        'FG3M': 2.1,
        'FG3A': 5.8,
        'FG3_PCT': 0.362,
        'FTM': 4.4,
        'FTA': 5.1,
        'FT_PCT': 0.863,
        'MIN': 35.2,
        'GP': 72,
        'W': 45,
        'L': 27,
        'W_PCT': 0.625,
        'AGE': 26
    }

    print(f'Sample data: {sample_data["PLAYER_NAME"]} - {sample_data["PTS"]} PPG')

    # Initialize feature engineer
    engineer = ExpertFeatureEngineer()

    # Test feature generation
    features = engineer.generate_matchup_features(sample_data, 'NBA')
    print(f'\nGenerated features shape: {features.shape}')
    print(f'Feature values (first 10): {features[0][:10]}')
    print(f'All zeros? {(features == 0).all()}')
    print(f'Non-zero count: {(features != 0).sum()}')
    print(f'Max value: {features.max():.3f}')
    print(f'Min value: {features.min():.3f}')

    # Test WNBA data
    wnba_data = sample_data.copy()
    wnba_data['PLAYER_NAME'] = 'WNBA Test Player'
    wnba_features = engineer.generate_matchup_features(wnba_data, 'WNBA')
    print(f'\nWNBA features shape: {wnba_features.shape}')
    print(f'WNBA non-zero count: {(wnba_features != 0).sum()}')

    print('\n✅ Feature engineering test completed successfully!')

if __name__ == "__main__":
    test_feature_engineering()
