[system]
environment = "production"
debug = false
log_level = "INFO"

[database]
provider = "postgresql"
host = "${DB_HOST:localhost}"
port = "${DB_PORT:5432}"
name = "${DB_NAME:hyper_medusa}"
user = "${DB_USER:medusa}"
password = "${DB_PASSWORD:}"
pool_size = 10
timeout = 30

[redis]
host = "${REDIS_HOST:localhost}"
port = "${REDIS_PORT:6379}"
password = "${REDIS_PASSWORD:}"
enabled = true

[api]
host = "${API_HOST:0.0.0.0}"
port = "${API_PORT:8000}"
prefix = "/api/v1"
cors_enabled = true

[basketball]
nba_enabled = true
wnba_enabled = true
data_sources = [
    "nba_api",
    "wnba_api",
]
prediction_confidence_threshold = 0.65

[models]
nba_model_path = "${MODELS_DIR:./models}/nba/ensemble_v1.pkl"
wnba_model_path = "${MODELS_DIR:./models}/wnba/ensemble_v1.pkl"
retraining_enabled = true

[security]
secret_key = "${SECRET_KEY:}"
jwt_algorithm = "HS256"
jwt_expiration = 3600

[monitoring]
prometheus_enabled = true
grafana_enabled = true
health_check_interval = 30
