import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any, Set
import requests
import time
import json
from datetime import datetime
import os

#!/usr/bin/env python3
"""
Complete Player Data Collection - All Players, Skip Existing
Collect ALL players and skip data already collected to avoid duplicates
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CompletePlayerDataCollector:
    """Collect ALL player data and skip existing data"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }
        
        # Track what we already have
        self.existing_players = set()
        self.existing_game_logs = set()
        self.load_existing_data()
        
    def load_existing_data(self) -> None:
        """Load existing player data to skip duplicates"""
        logger.info("📊 Loading existing player data to skip duplicates...")
        
        conn = sqlite3.connect(self.db_path)
        
        # Get existing players
        existing_players_query = """
        SELECT DISTINCT league_name, player_id, player_name
        FROM unified_nba_wnba_data
        WHERE player_id IS NOT NULL 
        AND player_name IS NOT NULL
        AND data_type IN ('individual_player_game_log', 'player_season_averages')
        """
        
        existing_players_df = pd.read_sql_query(existing_players_query, conn)
        
        for _, row in existing_players_df.iterrows():
            self.existing_players.add((row['league_name'], row['player_id']))
        
        # Get existing game logs to avoid re-collecting
        existing_gamelogs_query = """
        SELECT DISTINCT league_name, player_id, season, game_id
        FROM unified_nba_wnba_data
        WHERE data_type = 'individual_player_game_log'
        AND game_id IS NOT NULL
        """
        
        existing_gamelogs_df = pd.read_sql_query(existing_gamelogs_query, conn)
        
        for _, row in existing_gamelogs_df.iterrows():
            self.existing_game_logs.add((row['league_name'], row['player_id'], row['season'], row['game_id']))
        
        conn.close()
        
        logger.info(f"✅ Found {len(self.existing_players)} players with existing data")
        logger.info(f"✅ Found {len(self.existing_game_logs)} existing game logs to skip")
    
    def get_all_players(self, league: str = 'NBA') -> List[Dict[str, Any]]:
        """Get ALL players for the league"""
        logger.info(f"📋 Getting ALL {league} players...")
        
        league_id = '00' if league == 'NBA' else '10'
        all_players = []
        
        # Get players from multiple seasons to ensure completeness
        seasons = ['2023-24', '2022-23', '2021-22', '2020-21', '2019-20']
        
        for season in seasons:
            try:
                logger.info(f"   📅 Getting {league} players for {season}...")
                
                url = f"{self.base_url}/commonallplayers"
                params = {
                    'LeagueID': league_id,
                    'Season': season,
                    'IsOnlyCurrentSeason': '0'
                }
                
                response = requests.get(url, headers=self.headers, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                
                if 'resultSets' in data and len(data['resultSets']) > 0:
                    headers = data['resultSets'][0]['headers']
                    rows = data['resultSets'][0]['rowSet']
                    
                    for row in rows:
                        player_dict = dict(zip(headers, row))
                        player_id = str(player_dict.get('PERSON_ID'))
                        
                        # Skip if we already have this player
                        if (league, player_id) in self.existing_players:
                            continue
                        
                        player_info = {
                            'player_id': player_id,
                            'player_name': player_dict.get('DISPLAY_FIRST_LAST'),
                            'team_id': player_dict.get('TEAM_ID'),
                            'team_abbreviation': player_dict.get('TEAM_ABBREVIATION'),
                            'is_active': player_dict.get('ROSTERSTATUS') == 1,
                            'from_year': player_dict.get('FROM_YEAR'),
                            'to_year': player_dict.get('TO_YEAR'),
                            'league': league
                        }
                        
                        # Add if not already in list
                        if not any(p['player_id'] == player_id for p in all_players):
                            all_players.append(player_info)
                
                time.sleep(0.6)  # Rate limiting
                
            except Exception as e:
                logger.warning(f"⚠️ Error getting {league} players for {season}: {e}")
                continue
        
        logger.info(f"✅ Found {len(all_players)} NEW {league} players to collect")
        return all_players
    
    def collect_player_game_logs_smart(self, player_id: str, player_name: str, league: str) -> List[Dict[str, Any]]:
        """Collect game logs but skip existing ones"""
        seasons = ['2023-24', '2022-23', '2021-22', '2020-21', '2019-20']
        all_game_logs = []
        
        for season in seasons:
            try:
                # Check if we already have this player's season data
                existing_season_logs = [log for log in self.existing_game_logs 
                                      if log[0] == league and log[1] == player_id and log[2] == season]
                
                if len(existing_season_logs) > 10:  # If we have substantial data, skip
                    logger.info(f"   ⏭️ Skipping {player_name} {season} (already have {len(existing_season_logs)} games)")
                    continue
                
                logger.info(f"   📊 Collecting {player_name} game logs for {season}...")
                
                url = f"{self.base_url}/playergamelog"
                params = {
                    'PlayerID': player_id,
                    'Season': season,
                    'SeasonType': 'Regular Season',
                    'LeagueID': '00' if league == 'NBA' else '10'
                }
                
                response = requests.get(url, headers=self.headers, params=params, timeout=30)
                response.raise_for_status()
                
                data = response.json()
                
                if 'resultSets' in data and len(data['resultSets']) > 0:
                    headers = data['resultSets'][0]['headers']
                    rows = data['resultSets'][0]['rowSet']
                    
                    for row in rows:
                        game_dict = dict(zip(headers, row))
                        game_id = game_dict.get('Game_ID')
                        
                        # Skip if we already have this specific game
                        if (league, player_id, season, game_id) in self.existing_game_logs:
                            continue
                        
                        # Create comprehensive game log record
                        game_log = {
                            'player_id': player_id,
                            'player_name': player_name,
                            'season': season,
                            'league_name': league,
                            'league_id': '00' if league == 'NBA' else '10',
                            'game_id': game_id,
                            'game_date': game_dict.get('GAME_DATE'),
                            'matchup': game_dict.get('MATCHUP'),
                            'team_abbreviation': game_dict.get('TEAM_ABBREVIATION'),
                            'points': game_dict.get('PTS'),
                            'rebounds': game_dict.get('REB'),
                            'assists': game_dict.get('AST'),
                            'steals': game_dict.get('STL'),
                            'blocks': game_dict.get('BLK'),
                            'turnovers': game_dict.get('TOV'),
                            'field_goals_made': game_dict.get('FGM'),
                            'field_goals_attempted': game_dict.get('FGA'),
                            'three_pointers_made': game_dict.get('FG3M'),
                            'three_pointers_attempted': game_dict.get('FG3A'),
                            'free_throws_made': game_dict.get('FTM'),
                            'free_throws_attempted': game_dict.get('FTA'),
                            'minutes': game_dict.get('MIN'),
                            'plus_minus': game_dict.get('PLUS_MINUS'),
                            'data_type': 'individual_player_game_log',
                            'data_category': 'player_game_performance',
                            'source_file': f'complete_player_gamelog_{player_id}_{season}.json',
                            'raw_data': json.dumps(game_dict),
                            'created_at': datetime.now().isoformat()
                        }
                        
                        all_game_logs.append(game_log)
                        
                        # Add to existing set to avoid re-collecting in this session
                        self.existing_game_logs.add((league, player_id, season, game_id))
                
                time.sleep(0.6)  # Rate limiting
                
            except Exception as e:
                logger.warning(f"⚠️ Error collecting {player_name} {season} game logs: {e}")
                continue
        
        logger.info(f"✅ Collected {len(all_game_logs)} NEW game logs for {player_name}")
        return all_game_logs
    
    def insert_player_data_batch(self, player_data: List[Dict[str, Any]]) -> int:
        """Insert player data in batches for efficiency"""
        if not player_data:
            return 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        
        # Prepare batch insert data
        records_to_insert = []
        
        for record in player_data:
            if record.get('data_type') == 'individual_player_game_log':
                # Create separate records for each stat category
                base_record = {
                    'source_file': record['source_file'],
                    'source_table': 'complete_player_game_logs',
                    'data_category': record['data_category'],
                    'season': record['season'],
                    'league_id': record['league_id'],
                    'league_name': record['league_name'],
                    'season_type': 'Regular Season',
                    'player_id': record['player_id'],
                    'player_name': record['player_name'],
                    'team_abbreviation': record['team_abbreviation'],
                    'game_id': record['game_id'],
                    'game_date': record['game_date'],
                    'data_type': record['data_type'],
                    'raw_data': record['raw_data'],
                    'created_at': record['created_at']
                }
                
                # Individual stat records for player props
                stat_mappings = {
                    'points': record.get('points'),
                    'rebounds': record.get('rebounds'),
                    'assists': record.get('assists'),
                    'steals': record.get('steals'),
                    'blocks': record.get('blocks'),
                    'turnovers': record.get('turnovers'),
                    'three_pointers_made': record.get('three_pointers_made'),
                    'field_goals_made': record.get('field_goals_made'),
                    'free_throws_made': record.get('free_throws_made'),
                    'minutes': record.get('minutes')
                }
                
                for stat_category, stat_value in stat_mappings.items():
                    if stat_value is not None:
                        stat_record = base_record.copy()
                        stat_record['stat_category'] = stat_category
                        stat_record['stat_value'] = stat_value
                        records_to_insert.append(stat_record)
        
        # Batch insert
        try:
            for record in records_to_insert:
                cursor.execute("""
                    INSERT INTO unified_nba_wnba_data (
                        source_file, source_table, data_category, season, league_id, league_name,
                        season_type, player_id, player_name, team_abbreviation, game_id, game_date,
                        stat_category, stat_value, data_type, raw_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['source_file'], record['source_table'], record['data_category'],
                    record['season'], record['league_id'], record['league_name'],
                    record['season_type'], record['player_id'], record['player_name'],
                    record['team_abbreviation'], record['game_id'], record['game_date'],
                    record['stat_category'], record['stat_value'], record['data_type'],
                    record['raw_data'], record['created_at']
                ))
                inserted_count += 1
            
            conn.commit()
            
        except Exception as e:
            logger.error(f"❌ Error in batch insert: {e}")
            conn.rollback()
        
        conn.close()
        return inserted_count
    
    def run_complete_collection(self, league: str = 'WNBA') -> Dict[str, Any]:
        """Run complete player data collection for ALL players"""
        logger.info(f"🚀 COMPLETE {league} PLAYER DATA COLLECTION - ALL PLAYERS")
        logger.info("=" * 70)
        
        start_time = datetime.now()
        
        # Step 1: Get ALL players (skipping existing)
        all_players = self.get_all_players(league)
        
        if not all_players:
            logger.info(f"✅ No new {league} players to collect - all data already exists!")
            return {'success': True, 'message': 'All data already collected', 'total_records': 0}
        
        logger.info(f"🎯 Processing {len(all_players)} NEW {league} players...")
        
        # Step 2: Collect data for all players
        total_records_collected = 0
        players_processed = 0
        batch_size = 10  # Process in batches
        
        for i in range(0, len(all_players), batch_size):
            batch = all_players[i:i + batch_size]
            batch_data = []
            
            logger.info(f"📦 Processing batch {i//batch_size + 1}/{(len(all_players) + batch_size - 1)//batch_size}")
            
            for player in batch:
                player_id = player['player_id']
                player_name = player['player_name']
                
                if not player_id or not player_name:
                    continue
                
                logger.info(f"   📊 {player_name} ({players_processed + 1}/{len(all_players)})...")
                
                # Collect game logs (skip existing)
                game_logs = self.collect_player_game_logs_smart(player_id, player_name, league)
                batch_data.extend(game_logs)
                
                players_processed += 1
                
                # Add to existing players set
                self.existing_players.add((league, player_id))
            
            # Insert batch
            if batch_data:
                inserted_count = self.insert_player_data_batch(batch_data)
                total_records_collected += inserted_count
                logger.info(f"   ✅ Batch inserted: {inserted_count:,} records")
            
            # Progress update
            logger.info(f"📈 Progress: {players_processed}/{len(all_players)} players, {total_records_collected:,} total records")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎉 COMPLETE {league} COLLECTION FINISHED!")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   👥 Players processed: {players_processed}")
        logger.info(f"   📊 Total NEW records: {total_records_collected:,}")
        
        return {
            'success': True,
            'league': league,
            'duration': str(duration),
            'players_processed': players_processed,
            'total_records': total_records_collected
        }

def main():
    """Run complete player data collection"""
    
    collector = CompletePlayerDataCollector()
    
    # Collect WNBA first
    wnba_results = collector.run_complete_collection(league='WNBA')
    
    if wnba_results['success']:
        
        # Now collect NBA
        nba_results = collector.run_complete_collection(league='NBA')
        
        if nba_results['success']:
            
            total_new_records = wnba_results['total_records'] + nba_results['total_records']
            total_players = wnba_results['players_processed'] + nba_results['players_processed']
            
            
    
    return wnba_results

if __name__ == "__main__":
    main()
