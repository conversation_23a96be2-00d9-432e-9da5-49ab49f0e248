# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/cost_graph.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorboard.compat.proto import tensor_shape_pb2 as tensorboard_dot_compat_dot_proto_dot_tensor__shape__pb2
from tensorboard.compat.proto import types_pb2 as tensorboard_dot_compat_dot_proto_dot_types__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)tensorboard/compat/proto/cost_graph.proto\x12\x0btensorboard\x1a+tensorboard/compat/proto/tensor_shape.proto\x1a$tensorboard/compat/proto/types.proto\"\xd0\x06\n\x0c\x43ostGraphDef\x12,\n\x04node\x18\x01 \x03(\x0b\x32\x1e.tensorboard.CostGraphDef.Node\x12\x36\n\x04\x63ost\x18\x02 \x03(\x0b\x32(.tensorboard.CostGraphDef.AggregatedCost\x1a\xa6\x05\n\x04Node\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06\x64\x65vice\x18\x02 \x01(\t\x12\n\n\x02id\x18\x03 \x01(\x05\x12<\n\ninput_info\x18\x04 \x03(\x0b\x32(.tensorboard.CostGraphDef.Node.InputInfo\x12>\n\x0boutput_info\x18\x05 \x03(\x0b\x32).tensorboard.CostGraphDef.Node.OutputInfo\x12\x1d\n\x15temporary_memory_size\x18\x06 \x01(\x03\x12\x1e\n\x16persistent_memory_size\x18\x0c \x01(\x03\x12!\n\x15host_temp_memory_size\x18\n \x01(\x03\x42\x02\x18\x01\x12#\n\x17\x64\x65vice_temp_memory_size\x18\x0b \x01(\x03\x42\x02\x18\x01\x12)\n\x1d\x64\x65vice_persistent_memory_size\x18\x10 \x01(\x03\x42\x02\x18\x01\x12\x14\n\x0c\x63ompute_cost\x18\t \x01(\x03\x12\x14\n\x0c\x63ompute_time\x18\x0e \x01(\x03\x12\x13\n\x0bmemory_time\x18\x0f \x01(\x03\x12\x10\n\x08is_final\x18\x07 \x01(\x08\x12\x15\n\rcontrol_input\x18\x08 \x03(\x05\x12\x12\n\ninaccurate\x18\x11 \x01(\x08\x1a;\n\tInputInfo\x12\x16\n\x0epreceding_node\x18\x01 \x01(\x05\x12\x16\n\x0epreceding_port\x18\x02 \x01(\x05\x1a\x88\x01\n\nOutputInfo\x12\x0c\n\x04size\x18\x01 \x01(\x03\x12\x18\n\x10\x61lias_input_port\x18\x02 \x01(\x03\x12,\n\x05shape\x18\x03 \x01(\x0b\x32\x1d.tensorboard.TensorShapeProto\x12$\n\x05\x64type\x18\x04 \x01(\x0e\x32\x15.tensorboard.DataType\x1a\x31\n\x0e\x41ggregatedCost\x12\x0c\n\x04\x63ost\x18\x01 \x01(\x02\x12\x11\n\tdimension\x18\x02 \x01(\tB\x83\x01\n\x18org.tensorflow.frameworkB\x0f\x43ostGraphProtosP\x01ZQgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto\xf8\x01\x01\x62\x06proto3')



_COSTGRAPHDEF = DESCRIPTOR.message_types_by_name['CostGraphDef']
_COSTGRAPHDEF_NODE = _COSTGRAPHDEF.nested_types_by_name['Node']
_COSTGRAPHDEF_NODE_INPUTINFO = _COSTGRAPHDEF_NODE.nested_types_by_name['InputInfo']
_COSTGRAPHDEF_NODE_OUTPUTINFO = _COSTGRAPHDEF_NODE.nested_types_by_name['OutputInfo']
_COSTGRAPHDEF_AGGREGATEDCOST = _COSTGRAPHDEF.nested_types_by_name['AggregatedCost']
CostGraphDef = _reflection.GeneratedProtocolMessageType('CostGraphDef', (_message.Message,), {

  'Node' : _reflection.GeneratedProtocolMessageType('Node', (_message.Message,), {

    'InputInfo' : _reflection.GeneratedProtocolMessageType('InputInfo', (_message.Message,), {
      'DESCRIPTOR' : _COSTGRAPHDEF_NODE_INPUTINFO,
      '__module__' : 'tensorboard.compat.proto.cost_graph_pb2'
      # @@protoc_insertion_point(class_scope:tensorboard.CostGraphDef.Node.InputInfo)
      })
    ,

    'OutputInfo' : _reflection.GeneratedProtocolMessageType('OutputInfo', (_message.Message,), {
      'DESCRIPTOR' : _COSTGRAPHDEF_NODE_OUTPUTINFO,
      '__module__' : 'tensorboard.compat.proto.cost_graph_pb2'
      # @@protoc_insertion_point(class_scope:tensorboard.CostGraphDef.Node.OutputInfo)
      })
    ,
    'DESCRIPTOR' : _COSTGRAPHDEF_NODE,
    '__module__' : 'tensorboard.compat.proto.cost_graph_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.CostGraphDef.Node)
    })
  ,

  'AggregatedCost' : _reflection.GeneratedProtocolMessageType('AggregatedCost', (_message.Message,), {
    'DESCRIPTOR' : _COSTGRAPHDEF_AGGREGATEDCOST,
    '__module__' : 'tensorboard.compat.proto.cost_graph_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.CostGraphDef.AggregatedCost)
    })
  ,
  'DESCRIPTOR' : _COSTGRAPHDEF,
  '__module__' : 'tensorboard.compat.proto.cost_graph_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.CostGraphDef)
  })
_sym_db.RegisterMessage(CostGraphDef)
_sym_db.RegisterMessage(CostGraphDef.Node)
_sym_db.RegisterMessage(CostGraphDef.Node.InputInfo)
_sym_db.RegisterMessage(CostGraphDef.Node.OutputInfo)
_sym_db.RegisterMessage(CostGraphDef.AggregatedCost)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkB\017CostGraphProtosP\001ZQgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/cost_graph_go_proto\370\001\001'
  _COSTGRAPHDEF_NODE.fields_by_name['host_temp_memory_size']._options = None
  _COSTGRAPHDEF_NODE.fields_by_name['host_temp_memory_size']._serialized_options = b'\030\001'
  _COSTGRAPHDEF_NODE.fields_by_name['device_temp_memory_size']._options = None
  _COSTGRAPHDEF_NODE.fields_by_name['device_temp_memory_size']._serialized_options = b'\030\001'
  _COSTGRAPHDEF_NODE.fields_by_name['device_persistent_memory_size']._options = None
  _COSTGRAPHDEF_NODE.fields_by_name['device_persistent_memory_size']._serialized_options = b'\030\001'
  _COSTGRAPHDEF._serialized_start=142
  _COSTGRAPHDEF._serialized_end=990
  _COSTGRAPHDEF_NODE._serialized_start=261
  _COSTGRAPHDEF_NODE._serialized_end=939
  _COSTGRAPHDEF_NODE_INPUTINFO._serialized_start=741
  _COSTGRAPHDEF_NODE_INPUTINFO._serialized_end=800
  _COSTGRAPHDEF_NODE_OUTPUTINFO._serialized_start=803
  _COSTGRAPHDEF_NODE_OUTPUTINFO._serialized_end=939
  _COSTGRAPHDEF_AGGREGATEDCOST._serialized_start=941
  _COSTGRAPHDEF_AGGREGATEDCOST._serialized_end=990
# @@protoc_insertion_point(module_scope)
