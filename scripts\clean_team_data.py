import sqlite3
import logging
from typing import Set, Dict, Any

#!/usr/bin/env python3
"""
Clean Team Data
Remove invalid team abbreviations and keep only official NBA/WNBA teams
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TeamDataCleaner:
    """Clean team data to keep only official NBA/WNBA teams"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
        # Official NBA teams (30 teams)
        self.official_nba_teams = {
            'ATL', 'BOS', 'BKN', 'CHA', 'CHI', 'CLE', 'DAL', 'DEN', 'DET', 'GSW',
            'HOU', 'IND', 'LAC', 'LAL', 'MEM', 'MIA', 'MIL', 'MIN', 'NOP', 'NYK',
            'OKC', 'ORL', 'PHI', 'PHX', 'POR', 'SAC', 'SAS', 'TOR', 'UTA', 'WAS'
        }
        
        # Official WNBA teams (13 teams as of 2025)
        self.official_wnba_teams = {
            'ATL', 'CHI', 'CONN', 'DAL', 'IND', 'LAS', 'MIN', 'NY', 'PHX', 'SEA', 'WAS', 'GSV', 'TOR'
        }
        
        # Team name mappings for common variations
        self.team_name_mappings = {
            # NBA full names to abbreviations
            'Philadelphia 76ers': 'PHI',
            'Washington Wizards': 'WAS',
            'Charlotte Hornets': 'CHA',
            'Minnesota Timberwolves': 'MIN',
            'San Antonio Spurs': 'SAS',
            'Toronto Raptors': 'TOR',
            'Dallas Mavericks': 'DAL',
            'LA Clippers': 'LAC',
            'Oklahoma City Thunder': 'OKC',
            'Phoenix Suns': 'PHX',
            'Sacramento Kings': 'SAC',
            'Chicago Bulls': 'CHI',
            'Detroit Pistons': 'DET',
            'Orlando Magic': 'ORL',
            'Utah Jazz': 'UTA',
            'Atlanta Hawks': 'ATL',
            'Indiana Pacers': 'IND',
            'Portland Trail Blazers': 'POR',
            'Brooklyn Nets': 'BKN',
            'Golden State Warriors': 'GSW',
            'Houston Rockets': 'HOU',
            'Milwaukee Bucks': 'MIL',
            'New Orleans Pelicans': 'NOP',
            'New York Knicks': 'NYK',
            'Denver Nuggets': 'DEN',
            'Los Angeles Lakers': 'LAL',
            'Memphis Grizzlies': 'MEM',
            'Miami Heat': 'MIA',
            'Boston Celtics': 'BOS',
            'Cleveland Cavaliers': 'CLE',
            
            # WNBA full names to abbreviations
            'Golden State Valkyries': 'GSV',
            'Phoenix Mercury': 'PHX',
            'Minnesota Lynx': 'MIN',
            'Connecticut Sun': 'CONN',
            'Dallas Wings': 'DAL',
            'Indiana Fever': 'IND',
            'Las Vegas Aces': 'LAS',
            'Los Angeles Sparks': 'LAS',  # Note: LAS is for Las Vegas, LA Sparks might need different handling
            'New York Liberty': 'NY',
            'Atlanta Dream': 'ATL',
            'Seattle Storm': 'SEA',
            'Washington Mystics': 'WAS',
            'Chicago Sky': 'CHI',
            
            # Common variations
            'PHO': 'PHX',  # Phoenix variation
            'CON': 'CONN', # Connecticut variation
            'NYL': 'NY',   # New York Liberty variation
            'LVA': 'LAS',  # Las Vegas variation
        }
        
    def analyze_current_state(self) -> Dict[str, Any]:
        """Analyze current team data state"""
        logger.info("📊 ANALYZING CURRENT TEAM DATA STATE")
        logger.info("=" * 45)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Count total records
            total_records = conn.execute("SELECT COUNT(*) FROM unified_nba_wnba_data").fetchone()[0]
            
            # Count records with valid teams
            valid_nba_records = conn.execute("""
                SELECT COUNT(*) FROM unified_nba_wnba_data 
                WHERE league_name = 'NBA' AND team_abbreviation IN ({})
            """.format(','.join(['?' for _ in self.official_nba_teams])), 
            list(self.official_nba_teams)).fetchone()[0]
            
            valid_wnba_records = conn.execute("""
                SELECT COUNT(*) FROM unified_nba_wnba_data 
                WHERE league_name = 'WNBA' AND team_abbreviation IN ({})
            """.format(','.join(['?' for _ in self.official_wnba_teams])), 
            list(self.official_wnba_teams)).fetchone()[0]
            
            # Count records with invalid teams
            invalid_records = conn.execute("""
                SELECT COUNT(*) FROM unified_nba_wnba_data 
                WHERE (league_name = 'NBA' AND team_abbreviation NOT IN ({}))
                OR (league_name = 'WNBA' AND team_abbreviation NOT IN ({}))
            """.format(
                ','.join(['?' for _ in self.official_nba_teams]),
                ','.join(['?' for _ in self.official_wnba_teams])
            ), list(self.official_nba_teams) + list(self.official_wnba_teams)).fetchone()[0]
            
            conn.close()
            
            logger.info(f"📊 CURRENT STATE:")
            logger.info(f"   Total records: {total_records:,}")
            logger.info(f"   Valid NBA records: {valid_nba_records:,}")
            logger.info(f"   Valid WNBA records: {valid_wnba_records:,}")
            logger.info(f"   Invalid team records: {invalid_records:,}")
            logger.info(f"   Invalid percentage: {(invalid_records/total_records)*100:.1f}%")
            
            return {
                'total_records': total_records,
                'valid_nba_records': valid_nba_records,
                'valid_wnba_records': valid_wnba_records,
                'invalid_records': invalid_records,
                'invalid_percentage': (invalid_records/total_records)*100
            }
            
        except Exception as e:
            logger.error(f"❌ Error analyzing current state: {e}")
            return {'error': str(e)}
    
    def fix_team_name_mappings(self) -> int:
        """Fix team names using mappings"""
        logger.info("\n🔧 FIXING TEAM NAME MAPPINGS")
        logger.info("=" * 35)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            total_fixed = 0
            
            for full_name, abbreviation in self.team_name_mappings.items():
                # Determine league for the abbreviation
                if abbreviation in self.official_nba_teams:
                    league = 'NBA'
                elif abbreviation in self.official_wnba_teams:
                    league = 'WNBA'
                else:
                    continue
                
                # Fix the team name
                fixes = cursor.execute("""
                    UPDATE unified_nba_wnba_data 
                    SET team_abbreviation = ?
                    WHERE team_abbreviation = ? AND league_name = ?
                """, (abbreviation, full_name, league)).rowcount
                
                if fixes > 0:
                    logger.info(f"✅ Fixed {fixes:,} records: '{full_name}' → '{abbreviation}' ({league})")
                    total_fixed += fixes
            
            conn.commit()
            conn.close()
            
            logger.info(f"\n📊 MAPPING FIXES: {total_fixed:,} records updated")
            return total_fixed
            
        except Exception as e:
            logger.error(f"❌ Error fixing team name mappings: {e}")
            return 0
    
    def remove_invalid_teams(self) -> int:
        """Remove records with invalid team abbreviations"""
        logger.info("\n🗑️ REMOVING INVALID TEAM RECORDS")
        logger.info("=" * 40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Remove records with invalid NBA teams
            nba_removed = cursor.execute("""
                DELETE FROM unified_nba_wnba_data 
                WHERE league_name = 'NBA' 
                AND team_abbreviation NOT IN ({})
                AND team_abbreviation IS NOT NULL
                AND team_abbreviation != ''
            """.format(','.join(['?' for _ in self.official_nba_teams])), 
            list(self.official_nba_teams)).rowcount
            
            logger.info(f"🗑️ Removed {nba_removed:,} invalid NBA team records")
            
            # Remove records with invalid WNBA teams
            wnba_removed = cursor.execute("""
                DELETE FROM unified_nba_wnba_data 
                WHERE league_name = 'WNBA' 
                AND team_abbreviation NOT IN ({})
                AND team_abbreviation IS NOT NULL
                AND team_abbreviation != ''
            """.format(','.join(['?' for _ in self.official_wnba_teams])), 
            list(self.official_wnba_teams)).rowcount
            
            logger.info(f"🗑️ Removed {wnba_removed:,} invalid WNBA team records")
            
            total_removed = nba_removed + wnba_removed
            
            conn.commit()
            conn.close()
            
            logger.info(f"\n📊 TOTAL REMOVED: {total_removed:,} invalid records")
            return total_removed
            
        except Exception as e:
            logger.error(f"❌ Error removing invalid teams: {e}")
            return 0
    
    def verify_cleanup(self) -> Dict[str, Any]:
        """Verify the cleanup results"""
        logger.info("\n✅ VERIFYING CLEANUP RESULTS")
        logger.info("=" * 35)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get final team counts
            final_teams = conn.execute("""
                SELECT 
                    league_name,
                    team_abbreviation,
                    COUNT(*) as count
                FROM unified_nba_wnba_data 
                WHERE team_abbreviation IS NOT NULL AND team_abbreviation != ''
                GROUP BY league_name, team_abbreviation
                ORDER BY league_name, count DESC
            """).fetchall()
            
            nba_teams = set()
            wnba_teams = set()
            
            logger.info(f"📊 FINAL TEAM BREAKDOWN:")
            
            for league, team, count in final_teams:
                if league == 'NBA':
                    nba_teams.add(team)
                elif league == 'WNBA':
                    wnba_teams.add(team)
                logger.info(f"   {team} ({league}): {count:,} records")
            
            # Verify against official lists
            missing_nba = self.official_nba_teams - nba_teams
            missing_wnba = self.official_wnba_teams - wnba_teams
            invalid_nba = nba_teams - self.official_nba_teams
            invalid_wnba = wnba_teams - self.official_wnba_teams
            
            logger.info(f"\n🎯 VERIFICATION RESULTS:")
            logger.info(f"   NBA teams found: {len(nba_teams)}/30")
            logger.info(f"   WNBA teams found: {len(wnba_teams)}/13")
            logger.info(f"   Missing NBA teams: {len(missing_nba)} {list(missing_nba) if missing_nba else ''}")
            logger.info(f"   Missing WNBA teams: {len(missing_wnba)} {list(missing_wnba) if missing_wnba else ''}")
            logger.info(f"   Invalid NBA teams: {len(invalid_nba)} {list(invalid_nba) if invalid_nba else ''}")
            logger.info(f"   Invalid WNBA teams: {len(invalid_wnba)} {list(invalid_wnba) if invalid_wnba else ''}")
            
            total_teams = len(nba_teams) + len(wnba_teams)
            success = len(invalid_nba) == 0 and len(invalid_wnba) == 0
            
            if success:
                logger.info(f"✅ CLEANUP SUCCESSFUL! {total_teams} valid teams remaining")
            else:
                logger.warning(f"⚠️ Some invalid teams still remain")
            
            conn.close()
            
            return {
                'nba_teams_count': len(nba_teams),
                'wnba_teams_count': len(wnba_teams),
                'total_teams': total_teams,
                'missing_nba': list(missing_nba),
                'missing_wnba': list(missing_wnba),
                'invalid_nba': list(invalid_nba),
                'invalid_wnba': list(invalid_wnba),
                'success': success
            }
            
        except Exception as e:
            logger.error(f"❌ Error verifying cleanup: {e}")
            return {'error': str(e)}

def main():
    """Execute team data cleanup"""
    
    cleaner = TeamDataCleaner()
    
    # Step 1: Analyze current state
    current_state = cleaner.analyze_current_state()
    
    if 'error' in current_state:
        return
    
    # Step 2: Fix team name mappings
    mapping_fixes = cleaner.fix_team_name_mappings()
    
    # Step 3: Remove invalid teams
    removed_count = cleaner.remove_invalid_teams()
    
    # Step 4: Verify cleanup
    verification = cleaner.verify_cleanup()
    
    
    if verification.get('success'):
    else:
        if verification.get('invalid_nba'):
        if verification.get('invalid_wnba'):
    
    return {
        'current_state': current_state,
        'mapping_fixes': mapping_fixes,
        'removed_count': removed_count,
        'verification': verification
    }

if __name__ == "__main__":
    main()
