import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any, Tuple
import json
import re

#!/usr/bin/env python3
"""
Fix League Data Routing - CRITICAL
Correctly separate NBA and WNBA data and fix routing issues
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LeagueDataRoutingFixer:
    """Fix critical league data routing issues"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def analyze_routing_issues(self) -> Dict[str, Any]:
        """Analyze the extent of routing issues"""
        logger.info("🔍 ANALYZING LEAGUE DATA ROUTING ISSUES")
        logger.info("=" * 55)
        
        conn = sqlite3.connect(self.db_path)
        
        # Check league_id vs league_name consistency
        consistency_query = """
        SELECT 
            league_id,
            league_name,
            COUNT(*) as record_count,
            COUNT(CASE WHEN player_name IS NOT NULL AND player_name != '' AND player_name != 'None' AND player_name != 'TEAM_RECORD' AND player_name != 'NEEDS_EXTRACTION' THEN 1 END) as player_records
        FROM unified_nba_wnba_data
        GROUP BY league_id, league_name
        ORDER BY record_count DESC
        """
        
        consistency_df = pd.read_sql_query(consistency_query, conn)
        
        # Check for mixed data in files
        mixed_files_query = """
        SELECT 
            source_file,
            league_name,
            COUNT(*) as record_count,
            COUNT(DISTINCT league_name) as league_count
        FROM unified_nba_wnba_data
        GROUP BY source_file, league_name
        HAVING COUNT(DISTINCT league_name) > 1
        ORDER BY record_count DESC
        """
        
        mixed_files_df = pd.read_sql_query(mixed_files_query, conn)
        
        # Check game_id patterns
        game_id_query = """
        SELECT 
            league_name,
            SUBSTR(game_id, 1, 1) as game_id_prefix,
            COUNT(*) as count
        FROM unified_nba_wnba_data
        WHERE game_id IS NOT NULL
        GROUP BY league_name, SUBSTR(game_id, 1, 1)
        ORDER BY league_name, count DESC
        """
        
        game_id_df = pd.read_sql_query(game_id_query, conn)
        
        # Check team_id patterns
        team_id_query = """
        SELECT 
            league_name,
            team_id,
            COUNT(*) as count
        FROM unified_nba_wnba_data
        WHERE team_id IS NOT NULL
        GROUP BY league_name, team_id
        ORDER BY league_name, count DESC
        LIMIT 20
        """
        
        team_id_df = pd.read_sql_query(team_id_query, conn)
        
        conn.close()
        
        # Log findings
        logger.info("📊 LEAGUE ID vs LEAGUE NAME CONSISTENCY:")
        for _, row in consistency_df.iterrows():
            player_pct = (row['player_records'] / row['record_count']) * 100 if row['record_count'] > 0 else 0
            logger.info(f"   {row['league_id']} | {row['league_name']}: {row['record_count']:,} records ({player_pct:.1f}% players)")
        
        logger.info(f"\n🔍 MIXED FILES DETECTED: {len(mixed_files_df)}")
        if not mixed_files_df.empty:
            for _, row in mixed_files_df.head(5).iterrows():
                logger.warning(f"   ⚠️ {row['source_file']}: {row['league_count']} different leagues")
        
        return {
            'consistency_issues': consistency_df.to_dict('records'),
            'mixed_files': mixed_files_df.to_dict('records'),
            'game_id_patterns': game_id_df.to_dict('records'),
            'team_id_patterns': team_id_df.to_dict('records')
        }
    
    def fix_league_id_routing(self) -> Dict[str, Any]:
        """Fix league_id routing based on actual data patterns"""
        logger.info("🔧 FIXING LEAGUE ID ROUTING")
        logger.info("=" * 35)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        fixes_applied = []
        
        # Fix 1: Correct league_id based on game_id patterns
        # NBA game IDs start with '002', WNBA with '102'
        logger.info("🔄 Fixing league_id based on game_id patterns...")
        
        # Fix NBA records with wrong league_id
        cursor.execute("""
        UPDATE unified_nba_wnba_data 
        SET league_id = '00', league_name = 'NBA'
        WHERE (game_id LIKE '002%' OR game_id LIKE '0022%')
        AND (league_id != '00' OR league_name != 'NBA')
        """)
        nba_game_fixes = cursor.rowcount
        
        # Fix WNBA records with wrong league_id  
        cursor.execute("""
        UPDATE unified_nba_wnba_data 
        SET league_id = '10', league_name = 'WNBA'
        WHERE (game_id LIKE '102%' OR game_id LIKE '1022%')
        AND (league_id != '10' OR league_name != 'WNBA')
        """)
        wnba_game_fixes = cursor.rowcount
        
        fixes_applied.append(f"Game ID fixes: NBA={nba_game_fixes}, WNBA={wnba_game_fixes}")
        
        # Fix 2: Correct league_id based on team_id patterns
        logger.info("🔄 Fixing league_id based on team_id patterns...")
        
        # NBA team IDs are typically 1610612xxx
        cursor.execute("""
        UPDATE unified_nba_wnba_data 
        SET league_id = '00', league_name = 'NBA'
        WHERE team_id LIKE '1610612%'
        AND (league_id != '00' OR league_name != 'NBA')
        """)
        nba_team_fixes = cursor.rowcount
        
        # WNBA team IDs are typically 1611661xxx
        cursor.execute("""
        UPDATE unified_nba_wnba_data 
        SET league_id = '10', league_name = 'WNBA'
        WHERE team_id LIKE '1611661%'
        AND (league_id != '10' OR league_name != 'WNBA')
        """)
        wnba_team_fixes = cursor.rowcount
        
        fixes_applied.append(f"Team ID fixes: NBA={nba_team_fixes}, WNBA={wnba_team_fixes}")
        
        # Fix 3: Correct league_id based on source file patterns
        logger.info("🔄 Fixing league_id based on source file patterns...")
        
        # Files with 'nba' should be NBA
        cursor.execute("""
        UPDATE unified_nba_wnba_data 
        SET league_id = '00', league_name = 'NBA'
        WHERE (LOWER(source_file) LIKE '%nba%' OR LOWER(source_file) LIKE '%_nba_%')
        AND source_file NOT LIKE '%wnba%'
        AND (league_id != '00' OR league_name != 'NBA')
        """)
        nba_file_fixes = cursor.rowcount
        
        # Files with 'wnba' should be WNBA
        cursor.execute("""
        UPDATE unified_nba_wnba_data 
        SET league_id = '10', league_name = 'WNBA'
        WHERE (LOWER(source_file) LIKE '%wnba%' OR LOWER(source_file) LIKE '%_wnba_%')
        AND (league_id != '10' OR league_name != 'WNBA')
        """)
        wnba_file_fixes = cursor.rowcount
        
        fixes_applied.append(f"File pattern fixes: NBA={nba_file_fixes}, WNBA={wnba_file_fixes}")
        
        # Fix 4: Handle player_id patterns
        logger.info("🔄 Fixing league_id based on player_id patterns...")
        
        # Check for known NBA vs WNBA player ID ranges
        cursor.execute("""
        UPDATE unified_nba_wnba_data 
        SET league_id = '00', league_name = 'NBA'
        WHERE player_id IS NOT NULL 
        AND CAST(player_id AS INTEGER) BETWEEN 1 AND 999999
        AND source_file LIKE '%nba%'
        AND (league_id != '00' OR league_name != 'NBA')
        """)
        nba_player_fixes = cursor.rowcount
        
        fixes_applied.append(f"Player ID fixes: NBA={nba_player_fixes}")
        
        conn.commit()
        conn.close()
        
        logger.info("✅ League ID routing fixes applied:")
        for fix in fixes_applied:
            logger.info(f"   • {fix}")
        
        return {
            'fixes_applied': fixes_applied,
            'total_fixes': sum([nba_game_fixes, wnba_game_fixes, nba_team_fixes, wnba_team_fixes, nba_file_fixes, wnba_file_fixes, nba_player_fixes])
        }
    
    def separate_mixed_files(self) -> Dict[str, Any]:
        """Separate records from files that contain both NBA and WNBA data"""
        logger.info("🔧 SEPARATING MIXED NBA/WNBA FILES")
        logger.info("=" * 40)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Find files with mixed league data
        mixed_files_query = """
        SELECT source_file, COUNT(DISTINCT league_name) as league_count
        FROM unified_nba_wnba_data
        GROUP BY source_file
        HAVING COUNT(DISTINCT league_name) > 1
        """
        
        mixed_files = pd.read_sql_query(mixed_files_query, conn)
        
        separations_made = 0
        
        for _, row in mixed_files.iterrows():
            source_file = row['source_file']
            logger.info(f"🔄 Separating mixed file: {source_file}")
            
            # Analyze the file content to determine correct league
            cursor.execute("""
            SELECT 
                league_name,
                COUNT(*) as count,
                COUNT(CASE WHEN game_id LIKE '002%' OR game_id LIKE '0022%' THEN 1 END) as nba_game_ids,
                COUNT(CASE WHEN game_id LIKE '102%' OR game_id LIKE '1022%' THEN 1 END) as wnba_game_ids,
                COUNT(CASE WHEN team_id LIKE '1610612%' THEN 1 END) as nba_team_ids,
                COUNT(CASE WHEN team_id LIKE '1611661%' THEN 1 END) as wnba_team_ids
            FROM unified_nba_wnba_data
            WHERE source_file = ?
            GROUP BY league_name
            """, (source_file,))
            
            file_analysis = cursor.fetchall()
            
            # Determine the correct league based on patterns
            for league_name, count, nba_games, wnba_games, nba_teams, wnba_teams in file_analysis:
                if nba_games > 0 or nba_teams > 0:
                    # This should be NBA
                    cursor.execute("""
                    UPDATE unified_nba_wnba_data 
                    SET league_id = '00', league_name = 'NBA'
                    WHERE source_file = ? AND league_name = ?
                    """, (source_file, league_name))
                    separations_made += cursor.rowcount
                    
                elif wnba_games > 0 or wnba_teams > 0:
                    # This should be WNBA
                    cursor.execute("""
                    UPDATE unified_nba_wnba_data 
                    SET league_id = '10', league_name = 'WNBA'
                    WHERE source_file = ? AND league_name = ?
                    """, (source_file, league_name))
                    separations_made += cursor.rowcount
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ Separated {separations_made} records from {len(mixed_files)} mixed files")
        
        return {
            'mixed_files_processed': len(mixed_files),
            'records_separated': separations_made
        }
    
    def validate_routing_fixes(self) -> Dict[str, Any]:
        """Validate that routing fixes were successful"""
        logger.info("✅ VALIDATING ROUTING FIXES")
        logger.info("=" * 30)
        
        conn = sqlite3.connect(self.db_path)
        
        # Check final league distribution
        final_stats_query = """
        SELECT 
            league_id,
            league_name,
            COUNT(*) as total_records,
            COUNT(CASE WHEN player_name IS NOT NULL AND player_name != '' AND player_name != 'None' AND player_name != 'TEAM_RECORD' AND player_name != 'NEEDS_EXTRACTION' THEN 1 END) as player_records,
            COUNT(DISTINCT player_name) as unique_players
        FROM unified_nba_wnba_data
        GROUP BY league_id, league_name
        ORDER BY total_records DESC
        """
        
        final_stats = pd.read_sql_query(final_stats_query, conn)
        
        # Check for remaining inconsistencies
        inconsistency_query = """
        SELECT COUNT(*) as inconsistent_records
        FROM unified_nba_wnba_data
        WHERE (league_name = 'NBA' AND league_id != '00')
        OR (league_name = 'WNBA' AND league_id != '10')
        """
        
        inconsistencies = pd.read_sql_query(inconsistency_query, conn).iloc[0]['inconsistent_records']
        
        # Check mixed files remaining
        mixed_remaining_query = """
        SELECT COUNT(DISTINCT source_file) as mixed_files
        FROM (
            SELECT source_file, COUNT(DISTINCT league_name) as league_count
            FROM unified_nba_wnba_data
            GROUP BY source_file
            HAVING COUNT(DISTINCT league_name) > 1
        )
        """
        
        mixed_remaining = pd.read_sql_query(mixed_remaining_query, conn).iloc[0]['mixed_files']
        
        conn.close()
        
        # Log validation results
        logger.info("📊 FINAL LEAGUE DISTRIBUTION:")
        for _, row in final_stats.iterrows():
            player_pct = (row['player_records'] / row['total_records']) * 100 if row['total_records'] > 0 else 0
            logger.info(f"   {row['league_id']} | {row['league_name']}: {row['total_records']:,} records ({player_pct:.1f}% players, {row['unique_players']:,} unique players)")
        
        if inconsistencies == 0:
            logger.info("✅ No league_id/league_name inconsistencies remaining")
        else:
            logger.warning(f"⚠️ {inconsistencies} inconsistent records still exist")
        
        if mixed_remaining == 0:
            logger.info("✅ No mixed files remaining")
        else:
            logger.warning(f"⚠️ {mixed_remaining} mixed files still exist")
        
        return {
            'final_stats': final_stats.to_dict('records'),
            'inconsistencies_remaining': inconsistencies,
            'mixed_files_remaining': mixed_remaining,
            'routing_success': inconsistencies == 0 and mixed_remaining == 0
        }
    
    def run_complete_routing_fix(self) -> Dict[str, Any]:
        """Run complete league data routing fix"""
        logger.info("🚀 RUNNING COMPLETE LEAGUE DATA ROUTING FIX")
        logger.info("=" * 60)
        
        # Step 1: Analyze issues
        analysis = self.analyze_routing_issues()
        
        # Step 2: Fix league ID routing
        routing_fixes = self.fix_league_id_routing()
        
        # Step 3: Separate mixed files
        separation_results = self.separate_mixed_files()
        
        # Step 4: Validate fixes
        validation = self.validate_routing_fixes()
        
        return {
            'analysis': analysis,
            'routing_fixes': routing_fixes,
            'separation_results': separation_results,
            'validation': validation
        }

def main():
    """Run the complete league data routing fix"""
    
    fixer = LeagueDataRoutingFixer()
    results = fixer.run_complete_routing_fix()
    
    
    # Show before/after comparison
    validation = results['validation']
    final_stats = validation['final_stats']
    
    for stat in final_stats:
        league = stat['league_name']
        total = stat['total_records']
        players = stat['player_records']
        player_pct = (players / total * 100) if total > 0 else 0
        unique_players = stat['unique_players']
        
    
    
    if validation['routing_success']:
    else:
    
    # Assessment
    nba_stats = next((s for s in final_stats if s['league_name'] == 'NBA'), None)
    wnba_stats = next((s for s in final_stats if s['league_name'] == 'WNBA'), None)
    
    if nba_stats and wnba_stats:
        nba_player_pct = (nba_stats['player_records'] / nba_stats['total_records'] * 100) if nba_stats['total_records'] > 0 else 0
        wnba_player_pct = (wnba_stats['player_records'] / wnba_stats['total_records'] * 100) if wnba_stats['total_records'] > 0 else 0
        
        if nba_player_pct > 90 and wnba_player_pct > 90:
        elif nba_player_pct > 70 and wnba_player_pct > 70:
        else:
    
    
    return results

if __name__ == "__main__":
    main()
