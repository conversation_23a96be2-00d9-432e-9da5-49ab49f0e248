#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Dynamic Configuration Manager
========================================================

Production-ready configuration system that replaces hardcoded values with
environment-driven or model-based dynamic values.

Features:
- Environment variable integration
- Model-based dynamic thresholds
- Basketball intelligence-driven parameters
- Real-time configuration adaptation
- Production security hardening
"""

import os
import logging
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
from src.analytics.dynamic_basketball_statistics_calculator import get_basketball_statistics_calculator, get_dynamic_league_config

logger = logging.getLogger(__name__)

class ConfigurationMode(Enum):
    """Configuration modes"""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"

class LeagueType(Enum):
    """League types for configuration"""
    NBA = "NBA"
    WNBA = "WNBA"
    BOTH = "BOTH"

@dataclass
class ModelBasedThresholds:
    """Model-based dynamic thresholds"""
    confidence_threshold: float = 0.75
    prediction_accuracy_threshold: float = 0.65
    data_quality_threshold: float = 0.80
    basketball_intelligence_threshold: float = 0.85
    real_time_adaptation_rate: float = 0.1

@dataclass
class EnvironmentConfig:
    """Environment-driven configuration"""
    mode: ConfigurationMode = ConfigurationMode.PRODUCTION
    debug_enabled: bool = False
    performance_monitoring: bool = True
    real_data_enabled: bool = True
    basketball_intelligence_enabled: bool = True

class DynamicConfigManager:
    """
    Dynamic configuration manager that replaces hardcoded values
    with intelligent, environment-driven, and model-based configurations.
    """
    
    def __init__(self, mode: Optional[ConfigurationMode] = None):
        """Initialize dynamic configuration manager"""
        self.logger = logger
        self.mode = mode or self._detect_environment_mode()
        self.model_thresholds = self._initialize_model_based_thresholds()
        self.env_config = self._initialize_environment_config()
        self.basketball_config = self._initialize_basketball_config()

        self.logger.info(f"🏀 Dynamic Config Manager initialized in {self.mode.value} mode")
    
    def _detect_environment_mode(self) -> ConfigurationMode:
        """Detect environment mode from environment variables"""
        env_mode = os.getenv('MEDUSA_ENV', 'production').lower()
        
        mode_mapping = {
            'dev': ConfigurationMode.DEVELOPMENT,
            'development': ConfigurationMode.DEVELOPMENT,
            'staging': ConfigurationMode.STAGING,
            'stage': ConfigurationMode.STAGING,
            'prod': ConfigurationMode.PRODUCTION,
            'production': ConfigurationMode.PRODUCTION,
            'test': ConfigurationMode.TESTING,
            'testing': ConfigurationMode.TESTING
        }
        
        return mode_mapping.get(env_mode, ConfigurationMode.PRODUCTION)
    
    def _initialize_model_based_thresholds(self) -> ModelBasedThresholds:
        """Initialize model-based dynamic thresholds"""
        # Get thresholds from trained models or environment
        confidence_threshold = float(os.getenv('MODEL_CONFIDENCE_THRESHOLD', '0.75'))
        accuracy_threshold = float(os.getenv('MODEL_ACCURACY_THRESHOLD', '0.65'))
        
        # Adjust based on environment mode
        if self.mode == ConfigurationMode.DEVELOPMENT:
            confidence_threshold = max(0.60, confidence_threshold - 0.10)
            accuracy_threshold = max(0.55, accuracy_threshold - 0.05)
        elif self.mode == ConfigurationMode.PRODUCTION:
            confidence_threshold = min(0.85, confidence_threshold + 0.05)
            accuracy_threshold = min(0.75, accuracy_threshold + 0.05)
        
        return ModelBasedThresholds(
            confidence_threshold=confidence_threshold,
            prediction_accuracy_threshold=accuracy_threshold,
            data_quality_threshold=float(os.getenv('DATA_QUALITY_THRESHOLD', '0.80')),
            basketball_intelligence_threshold=float(os.getenv('BASKETBALL_IQ_THRESHOLD', '0.85')),
            real_time_adaptation_rate=float(os.getenv('ADAPTATION_RATE', '0.1'))
        )
    
    def _initialize_environment_config(self) -> EnvironmentConfig:
        """Initialize environment-driven configuration"""
        return EnvironmentConfig(
            mode=self.mode,
            debug_enabled=os.getenv('DEBUG', 'false').lower() == 'true',
            performance_monitoring=os.getenv('PERFORMANCE_MONITORING', 'true').lower() == 'true',
            real_data_enabled=os.getenv('REAL_DATA_ENABLED', 'true').lower() == 'true',
            basketball_intelligence_enabled=os.getenv('BASKETBALL_INTELLIGENCE', 'true').lower() == 'true'
        )
    
    def _initialize_basketball_config(self) -> Dict[str, Any]:
        """Initialize basketball intelligence configuration with dynamic statistics"""
        try:

            # Get dynamic configurations from real data
            nba_config = get_dynamic_league_config("NBA")
            wnba_config = get_dynamic_league_config("WNBA")

            self.logger.info(f"✅ Loaded dynamic NBA config (confidence: {nba_config['confidence_score']:.1%})")
            self.logger.info(f"✅ Loaded dynamic WNBA config (confidence: {wnba_config['confidence_score']:.1%})")

            return {
                'nba_config': nba_config,
                'wnba_config': wnba_config
            }

        except Exception as e:
            self.logger.warning(f"⚠️ Could not load dynamic basketball config: {e}. Using environment fallbacks.")

            # Fallback to environment variables
            return {
                'nba_config': {
                    'average_total_points': float(os.getenv('NBA_AVG_TOTAL', '220.0')),
                    'home_court_advantage': float(os.getenv('NBA_HOME_ADVANTAGE', '0.54')),
                    'pace_factor': float(os.getenv('NBA_PACE_FACTOR', '100.0')),
                    'overtime_probability': float(os.getenv('NBA_OT_PROB', '0.12'))
                },
                'wnba_config': {
                    'average_total_points': float(os.getenv('WNBA_AVG_TOTAL', '165.0')),
                    'home_court_advantage': float(os.getenv('WNBA_HOME_ADVANTAGE', '0.52')),
                    'pace_factor': float(os.getenv('WNBA_PACE_FACTOR', '92.0')),
                    'overtime_probability': float(os.getenv('WNBA_OT_PROB', '0.08'))
                }
            }
    
    def get_model_service_config(self) -> Dict[str, Any]:
        """Get dynamic model service configuration"""
        base_model_path = os.getenv('MODEL_BASE_PATH', './models')
        
        config = {
            'nba_model_path': os.getenv('NBA_MODEL_PATH', f'{base_model_path}/nba/ensemble_v1.pkl'),
            'wnba_model_path': os.getenv('WNBA_MODEL_PATH', f'{base_model_path}/wnba/ensemble_v1.pkl'),
            'retraining_schedule_cron': os.getenv('RETRAINING_SCHEDULE', self._get_dynamic_retraining_schedule()),
            'drift_detection_enabled': os.getenv('DRIFT_DETECTION', 'true').lower() == 'true',
            'shap_explainability_enabled': os.getenv('SHAP_ENABLED', 'true').lower() == 'true',
            'confidence_threshold': self.model_thresholds.confidence_threshold,
            'accuracy_threshold': self.model_thresholds.prediction_accuracy_threshold
        }
        
        return config
    
    def get_prediction_service_config(self) -> Dict[str, Any]:
        """Get dynamic prediction service configuration"""
        # Dynamic update intervals based on environment and load
        update_interval = self._calculate_dynamic_update_interval()
        max_predictions = self._calculate_max_predictions_per_request()
        
        return {
            'live_update_interval_seconds': update_interval,
            'confidence_threshold': self.model_thresholds.confidence_threshold,
            'max_predictions_per_request': max_predictions,
            'basketball_intelligence_enabled': self.env_config.basketball_intelligence_enabled,
            'real_time_adaptation': self.model_thresholds.real_time_adaptation_rate
        }
    
    def get_security_config(self) -> Dict[str, Any]:
        """Get dynamic security configuration"""
        return {
            'threat_detection': True,  # Always enabled in production
            'neural_monitoring': True,  # Always enabled
            'session_timeout_minutes': int(os.getenv('SESSION_TIMEOUT', self._get_dynamic_session_timeout())),
            'max_login_attempts': int(os.getenv('MAX_LOGIN_ATTEMPTS', self._get_dynamic_login_attempts())),
            'require_mfa': self.mode == ConfigurationMode.PRODUCTION,
            'jwt_secret_key': os.getenv('JWT_SECRET_KEY', self._generate_secure_jwt_key()),
            'encryption_enabled': True,
            'audit_logging': True
        }
    
    def get_basketball_config(self, league: LeagueType = LeagueType.NBA) -> Dict[str, Any]:
        """Get dynamic basketball configuration for specific league"""
        if league == LeagueType.NBA:
            return self.basketball_config['nba_config']
        elif league == LeagueType.WNBA:
            return self.basketball_config['wnba_config']
        else:
            return {
                'nba': self.basketball_config['nba_config'],
                'wnba': self.basketball_config['wnba_config']
            }
    
    def _get_dynamic_retraining_schedule(self) -> str:
        """Calculate dynamic retraining schedule based on environment"""
        if self.mode == ConfigurationMode.DEVELOPMENT:
            return "0 */6 * * *"  # Every 6 hours for development
        elif self.mode == ConfigurationMode.STAGING:
            return "0 */12 * * *"  # Every 12 hours for staging
        else:
            return "0 0 * * SUN"  # Weekly for production
    
    def _calculate_dynamic_update_interval(self) -> int:
        """Calculate dynamic update interval based on load and environment"""
        base_interval = 60  # Base 60 seconds
        
        if self.mode == ConfigurationMode.DEVELOPMENT:
            return 30  # Faster updates for development
        elif self.mode == ConfigurationMode.PRODUCTION:
            # Adjust based on system load (simplified)
            load_factor = float(os.getenv('SYSTEM_LOAD_FACTOR', '1.0'))
            return max(30, int(base_interval * load_factor))
        
        return base_interval
    
    def _calculate_max_predictions_per_request(self) -> int:
        """Calculate maximum predictions per request based on resources"""
        base_max = 100
        
        if self.mode == ConfigurationMode.DEVELOPMENT:
            return 50  # Lower for development
        elif self.mode == ConfigurationMode.PRODUCTION:
            # Scale based on available resources
            resource_factor = float(os.getenv('RESOURCE_SCALE_FACTOR', '1.0'))
            return min(500, int(base_max * resource_factor))
        
        return base_max
    
    def _get_dynamic_session_timeout(self) -> str:
        """Get dynamic session timeout based on security requirements"""
        if self.mode == ConfigurationMode.DEVELOPMENT:
            return "120"  # 2 hours for development
        elif self.mode == ConfigurationMode.PRODUCTION:
            return "30"   # 30 minutes for production
        else:
            return "60"   # 1 hour for staging/testing
    
    def _get_dynamic_login_attempts(self) -> str:
        """Get dynamic login attempts based on security level"""
        if self.mode == ConfigurationMode.DEVELOPMENT:
            return "10"  # More attempts for development
        elif self.mode == ConfigurationMode.PRODUCTION:
            return "3"   # Strict for production
        else:
            return "5"   # Moderate for staging/testing
    
    def _generate_secure_jwt_key(self) -> str:
        """Generate secure JWT key for production"""
        if self.mode == ConfigurationMode.PRODUCTION:
            # In production, this should come from secure key management
            return os.getenv('JWT_SECRET_KEY', 'CHANGE_THIS_IN_PRODUCTION_IMMEDIATELY')
        else:
            return f"dev_key_{self.mode.value}_{datetime.now().strftime('%Y%m%d')}"
    
    def update_thresholds_from_model_performance(self, model_metrics: Dict[str, float]):
        """Update thresholds based on real model performance"""
        if 'accuracy' in model_metrics:
            # Adjust confidence threshold based on model accuracy
            accuracy = model_metrics['accuracy']
            if accuracy > 0.80:
                self.model_thresholds.confidence_threshold = min(0.85, accuracy - 0.05)
            elif accuracy < 0.60:
                self.model_thresholds.confidence_threshold = max(0.55, accuracy + 0.10)
        
        logger.info(f"🏀 Updated thresholds based on model performance: {model_metrics}")
    
    def get_full_config(self) -> Dict[str, Any]:
        """Get complete dynamic configuration"""
        return {
            'environment': {
                'mode': self.mode.value,
                'config': self.env_config.__dict__
            },
            'model_service': self.get_model_service_config(),
            'prediction_service': self.get_prediction_service_config(),
            'security': self.get_security_config(),
            'basketball': self.get_basketball_config(LeagueType.BOTH),
            'thresholds': self.model_thresholds.__dict__,
            'last_updated': datetime.now().isoformat()
        }

# Global instance for easy access
dynamic_config = DynamicConfigManager()
