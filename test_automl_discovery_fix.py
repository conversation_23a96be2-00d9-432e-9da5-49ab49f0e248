#!/usr/bin/env python3
"""
Test AutoML Discovery Logger Fix
===============================

Test that the logger error in AutoML Discovery has been resolved.
"""

import sys
import os
import logging

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), ".")))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AutoMLDiscoveryTest")

def test_automl_discovery_import():
    """Test that AutoMLFeatureDiscovery can be imported without logger errors"""
    logger.info("🔍 Testing AutoML Discovery import...")
    
    try:
        from src.models.revolutionary_prediction_system import AutoMLFeatureDiscovery
        logger.info("✅ AutoMLFeatureDiscovery imported successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to import AutoMLFeatureDiscovery: {e}")
        return False

def test_automl_discovery_initialization():
    """Test that AutoMLFeatureDiscovery can be initialized without logger errors"""
    logger.info("🔍 Testing AutoML Discovery initialization...")
    
    try:
        from src.models.revolutionary_prediction_system import AutoMLFeatureDiscovery
        
        # Test initialization without integration (simpler)
        automl = AutoMLFeatureDiscovery(enable_integration=False)
        logger.info("✅ AutoMLFeatureDiscovery initialized successfully (no integration)")
        
        # Test initialization with integration
        automl_integrated = AutoMLFeatureDiscovery(enable_integration=True)
        logger.info("✅ AutoMLFeatureDiscovery initialized successfully (with integration)")
        
        return True
    except Exception as e:
        logger.error(f"❌ Failed to initialize AutoMLFeatureDiscovery: {e}")
        return False

def test_feature_alchemist_automl_integration():
    """Test that FeatureAlchemist can initialize AutoML Discovery without errors"""
    logger.info("🔍 Testing FeatureAlchemist AutoML integration...")
    
    try:
        from src.features.feature_alchemist import FeatureAlchemist
        
        # Initialize FeatureAlchemist (this should trigger AutoML Discovery initialization)
        alchemist = FeatureAlchemist()
        logger.info("✅ FeatureAlchemist initialized successfully")
        
        # Check if AutoML Discovery was initialized
        if hasattr(alchemist, 'automl_discovery') and alchemist.automl_discovery:
            logger.info("✅ AutoML Discovery integrated successfully in FeatureAlchemist")
        else:
            logger.warning("⚠️ AutoML Discovery not available in FeatureAlchemist")
        
        return True
    except Exception as e:
        logger.error(f"❌ Failed to test FeatureAlchemist AutoML integration: {e}")
        return False

def main():
    """Run all AutoML Discovery tests"""
    logger.info("🚀 TESTING AUTOML DISCOVERY LOGGER FIX")
    logger.info("=" * 50)
    
    tests = [
        ("Import Test", test_automl_discovery_import),
        ("Initialization Test", test_automl_discovery_initialization),
        ("FeatureAlchemist Integration Test", test_feature_alchemist_automl_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🎯 Running {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST RESULTS SUMMARY")
    logger.info("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 ALL TESTS PASSED - AutoML Discovery logger error is FIXED!")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed - further investigation needed")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
