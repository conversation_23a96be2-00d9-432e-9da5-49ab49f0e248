import os
import sys
import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import json
import glob
from datetime import datetime
import hashlib
import re
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading

#!/usr/bin/env python3
"""
Bulk Data Ingestion System
Efficiently ingest all 1,363 CSV files into the unified database
"""


# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BulkDataIngestionSystem:
    """Efficiently ingest all CSV files into the database"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.data_directories = ["data", "data/ml_training", ".nba_api_cache", "src/data"]
        self.lock = threading.Lock()
        self.ingestion_stats = {
            'total_files': 0,
            'processed_files': 0,
            'successful_files': 0,
            'failed_files': 0,
            'total_records': 0,
            'skipped_files': 0
        }
        
    def find_all_csv_files(self) -> List[str]:
        """Find all CSV files in data directories"""
        logger.info("🔍 FINDING ALL CSV FILES")
        logger.info("=" * 30)
        
        csv_files = []
        
        for data_dir in self.data_directories:
            if os.path.exists(data_dir):
                pattern = os.path.join(data_dir, "**/*.csv")
                files = glob.glob(pattern, recursive=True)
                csv_files.extend(files)
                logger.info(f"📁 {data_dir}: {len(files)} CSV files")
        
        # Remove duplicates and sort
        csv_files = sorted(list(set(csv_files)))
        
        logger.info(f"\n📊 TOTAL CSV FILES FOUND: {len(csv_files)}")
        self.ingestion_stats['total_files'] = len(csv_files)
        
        return csv_files
    
    def prioritize_files(self, csv_files: List[str]) -> Tuple[List[str], List[str], List[str]]:
        """Prioritize files by importance"""
        logger.info("\n🎯 PRIORITIZING FILES")
        logger.info("=" * 25)
        
        high_priority = []
        medium_priority = []
        low_priority = []
        
        high_priority_patterns = [
            'training_data', 'player_game_logs', 'complete_player_game_logs',
            'game_data', 'team_stats', 'shot_chart', 'play_by_play',
            'clean_', 'processed_', 'hybrid_'
        ]
        
        medium_priority_patterns = [
            'players_', 'teams_', 'schedule_', 'clutch_stats',
            'hustle_stats', 'advanced_', 'league_'
        ]
        
        for file_path in csv_files:
            file_name = os.path.basename(file_path).lower()
            
            if any(pattern in file_name for pattern in high_priority_patterns):
                high_priority.append(file_path)
            elif any(pattern in file_name for pattern in medium_priority_patterns):
                medium_priority.append(file_path)
            else:
                low_priority.append(file_path)
        
        logger.info(f"🔥 High priority: {len(high_priority)} files")
        logger.info(f"📈 Medium priority: {len(medium_priority)} files")
        logger.info(f"📋 Low priority: {len(low_priority)} files")
        
        return high_priority, medium_priority, low_priority
    
    def is_file_already_ingested(self, file_path: str) -> bool:
        """Check if file is already in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            file_name = os.path.basename(file_path)
            
            result = conn.execute("""
                SELECT COUNT(*) FROM unified_nba_wnba_data 
                WHERE source_file = ?
            """, (file_name,)).fetchone()[0]
            
            conn.close()
            return result > 0
            
        except Exception:
            return False
    
    def prepare_data_for_ingestion(self, df: pd.DataFrame, file_path: str) -> pd.DataFrame:
        """Prepare DataFrame for ingestion"""
        try:
            # Create a copy
            processed_df = df.copy()
            
            # Add metadata
            processed_df['source_file'] = os.path.basename(file_path)
            processed_df['ingestion_timestamp'] = datetime.now().isoformat()
            
            # Standardize column names
            processed_df.columns = [col.lower().replace(' ', '_').replace('-', '_') for col in processed_df.columns]
            
            # Infer metadata from filename
            file_name = os.path.basename(file_path).lower()
            
            # League inference
            if 'league_name' not in processed_df.columns:
                if '_10_' in file_name or 'wnba' in file_name:
                    processed_df['league_name'] = 'WNBA'
                    processed_df['league_id'] = '10'
                else:
                    processed_df['league_name'] = 'NBA'
                    processed_df['league_id'] = '00'
            
            # Season inference
            if 'season' not in processed_df.columns:
                season_match = re.search(r'(20\d{2}-\d{2}|20\d{2})', file_name)
                if season_match:
                    processed_df['season'] = season_match.group(1)
                else:
                    processed_df['season'] = '2024-25'
            
            # Source table inference
            if 'source_table' not in processed_df.columns:
                if 'player' in file_name:
                    processed_df['source_table'] = 'player_data'
                elif 'team' in file_name:
                    processed_df['source_table'] = 'team_data'
                elif 'game' in file_name:
                    processed_df['source_table'] = 'game_data'
                elif 'shot' in file_name:
                    processed_df['source_table'] = 'shot_chart_data'
                elif 'play_by_play' in file_name:
                    processed_df['source_table'] = 'play_by_play_data'
                else:
                    processed_df['source_table'] = 'general_data'
            
            # Data category inference
            if 'data_category' not in processed_df.columns:
                if 'training' in file_name:
                    processed_df['data_category'] = 'training_data'
                elif 'clutch' in file_name:
                    processed_df['data_category'] = 'clutch_stats'
                elif 'hustle' in file_name:
                    processed_df['data_category'] = 'hustle_stats'
                elif 'advanced' in file_name:
                    processed_df['data_category'] = 'advanced_stats'
                else:
                    processed_df['data_category'] = 'general_stats'
            
            # Data type inference
            if 'data_type' not in processed_df.columns:
                if 'shot' in file_name:
                    processed_df['data_type'] = 'shot_data'
                elif 'game_log' in file_name:
                    processed_df['data_type'] = 'game_logs'
                else:
                    processed_df['data_type'] = 'statistics'
            
            # Handle missing values
            processed_df = processed_df.fillna('')
            
            # Ensure all values are strings or numbers
            for col in processed_df.columns:
                if processed_df[col].dtype == 'object':
                    processed_df[col] = processed_df[col].astype(str)
            
            return processed_df
            
        except Exception as e:
            logger.error(f"❌ Error preparing data from {file_path}: {e}")
            return df
    
    def ingest_single_file(self, file_path: str) -> Dict[str, Any]:
        """Ingest a single CSV file"""
        result = {
            'file_path': file_path,
            'success': False,
            'records_added': 0,
            'error': None
        }
        
        try:
            # Check if already ingested
            if self.is_file_already_ingested(file_path):
                result['success'] = True
                result['records_added'] = 0
                result['skipped'] = True
                with self.lock:
                    self.ingestion_stats['skipped_files'] += 1
                return result
            
            # Read CSV
            df = pd.read_csv(file_path)
            
            if df.empty:
                result['error'] = 'Empty file'
                return result
            
            # Prepare data
            processed_df = self.prepare_data_for_ingestion(df, file_path)
            
            # Insert into database
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                processed_df.to_sql(
                    'unified_nba_wnba_data',
                    conn,
                    if_exists='append',
                    index=False,
                    method='multi'
                )
                conn.close()
                
                # Update stats
                self.ingestion_stats['successful_files'] += 1
                self.ingestion_stats['total_records'] += len(processed_df)
            
            result['success'] = True
            result['records_added'] = len(processed_df)
            
        except Exception as e:
            result['error'] = str(e)
            with self.lock:
                self.ingestion_stats['failed_files'] += 1
        
        with self.lock:
            self.ingestion_stats['processed_files'] += 1
            
        return result
    
    def bulk_ingest_files(self, file_list: List[str], batch_name: str = "files", max_workers: int = 4) -> None:
        """Bulk ingest files with parallel processing"""
        logger.info(f"\n📥 BULK INGESTING {batch_name.upper()}")
        logger.info(f"=" * (20 + len(batch_name)))
        logger.info(f"📊 Files to process: {len(file_list)}")
        
        if not file_list:
            logger.info("✅ No files to process")
            return
        
        # Process files in parallel
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self.ingest_single_file, file_path): file_path 
                for file_path in file_list
            }
            
            # Process completed tasks
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    
                    if result.get('skipped'):
                        logger.info(f"⏭️ Skipped (already ingested): {os.path.basename(file_path)}")
                    elif result['success']:
                        logger.info(f"✅ Ingested {result['records_added']:,} records: {os.path.basename(file_path)}")
                    else:
                        logger.error(f"❌ Failed: {os.path.basename(file_path)} - {result['error']}")
                        
                except Exception as e:
                    logger.error(f"❌ Exception processing {file_path}: {e}")
                
                # Progress update
                if self.ingestion_stats['processed_files'] % 50 == 0:
                    progress = (self.ingestion_stats['processed_files'] / self.ingestion_stats['total_files']) * 100
                    logger.info(f"📊 Progress: {self.ingestion_stats['processed_files']}/{self.ingestion_stats['total_files']} ({progress:.1f}%)")

def main():
    """Execute bulk data ingestion"""
    
    ingestion_system = BulkDataIngestionSystem()
    
    # Step 1: Find all CSV files
    csv_files = ingestion_system.find_all_csv_files()
    
    if not csv_files:
        logger.info("✅ No CSV files found to ingest")
        return
    
    # Step 2: Prioritize files
    high_priority, medium_priority, low_priority = ingestion_system.prioritize_files(csv_files)
    
    # Step 3: Ingest in priority order
    start_time = datetime.now()
    
    # High priority first
    ingestion_system.bulk_ingest_files(high_priority, "high priority files", max_workers=2)
    
    # Medium priority second
    ingestion_system.bulk_ingest_files(medium_priority, "medium priority files", max_workers=4)
    
    # Low priority last
    ingestion_system.bulk_ingest_files(low_priority, "low priority files", max_workers=6)
    
    # Final statistics
    end_time = datetime.now()
    duration = end_time - start_time
    
    stats = ingestion_system.ingestion_stats
    
    
    success_rate = (stats['successful_files'] / stats['total_files']) * 100 if stats['total_files'] > 0 else 0
    
    if stats['successful_files'] > 0:
    else:
    
    return ingestion_system.ingestion_stats

if __name__ == "__main__":
    main()
