import sqlite3
import logging

#!/usr/bin/env python3
"""
Fix Remaining Player ID Seasons
Fix the remaining 12,166 records where player IDs were mistaken for seasons
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_remaining_player_id_seasons():
    """Fix the remaining player ID seasons using simple string matching"""
    logger.info("🔧 FIXING REMAINING PLAYER ID SEASONS")
    logger.info("=" * 40)
    
    try:
        conn = sqlite3.connect("medusa_master.db")
        cursor = conn.cursor()
        
        # Fix specific player ID seasons one by one
        player_id_seasons = [
            '1610-11', '1627-28', '1628-29', '1627', '1611', '1611-12', '1628'
        ]
        
        total_fixed = 0
        
        for bad_season in player_id_seasons:
            # Determine appropriate real season based on player ID era
            if bad_season.startswith('1628'):
                real_season = '2020-21'  # 2020 draft class era
            elif bad_season.startswith('1627'):
                real_season = '2019-20'  # 2019 draft class era
            elif bad_season.startswith('1611'):
                real_season = '2010-11'  # 2010 era players
            elif bad_season.startswith('1610'):
                real_season = '2010-11'  # 2010 era players
            else:
                real_season = '2024-25'  # Default to current season
            
            # Fix the records
            fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET season = ?
                WHERE season = ?
            """, (real_season, bad_season)).rowcount
            
            logger.info(f"✅ Fixed {fixes:,} records: {bad_season} → {real_season}")
            total_fixed += fixes
        
        conn.commit()
        
        # Verify final cleanup
        remaining_invalid = cursor.execute("""
            SELECT COUNT(*) 
            FROM unified_nba_wnba_data 
            WHERE season NOT IN (
                '2024-25', '2023-24', '2022-23', '2021-22', '2020-21',
                '2019-20', '2018-19', '2017-18', '2016-17', '2015-16',
                '2014-15', '2013-14', '2012-13', '2011-12', '2010-11',
                '2025', '2024', '2023', '2022', '2021', '2020', '2019', 
                '2018', '2017', '2016', '2015', '2014', '2013', '2012', '2011',
                'Unknown', 'NULL', 'ALL_SEASONS'
            )
            AND season IS NOT NULL
        """).fetchone()[0]
        
        # Get final season breakdown
        final_seasons = cursor.execute("""
            SELECT season, COUNT(*) as count 
            FROM unified_nba_wnba_data 
            GROUP BY season 
            ORDER BY count DESC
            LIMIT 25
        """).fetchall()
        
        conn.close()
        
        logger.info(f"\n🎯 FINAL RESULTS:")
        logger.info(f"   Total fixed: {total_fixed:,}")
        logger.info(f"   Remaining invalid: {remaining_invalid:,}")
        
        logger.info(f"\n📊 TOP 25 SEASONS AFTER CLEANUP:")
        for season, count in final_seasons:
            logger.info(f"   {season}: {count:,} records")
        
        if remaining_invalid == 0:
            logger.info(f"\n🎉 ALL SEASON DATA SUCCESSFULLY CLEANED!")
            logger.info(f"✅ 100% valid season data achieved!")
        else:
            logger.warning(f"\n⚠️ {remaining_invalid:,} records still need manual review")
        
        return {
            'total_fixed': total_fixed,
            'remaining_invalid': remaining_invalid,
            'final_seasons': final_seasons
        }
        
    except Exception as e:
        logger.error(f"❌ Error fixing remaining player ID seasons: {e}")
        return {'error': str(e)}

def main():
    """Execute the remaining player ID season fixes"""
    
    results = fix_remaining_player_id_seasons()
    
    
    if 'total_fixed' in results:
        
        if results['remaining_invalid'] == 0:
        else:
    
    return results

if __name__ == "__main__":
    main()
