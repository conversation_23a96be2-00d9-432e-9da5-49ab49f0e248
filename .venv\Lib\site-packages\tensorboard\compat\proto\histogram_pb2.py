# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/histogram.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(tensorboard/compat/proto/histogram.proto\x12\x0btensorboard\"\x87\x01\n\x0eHistogramProto\x12\x0b\n\x03min\x18\x01 \x01(\x01\x12\x0b\n\x03max\x18\x02 \x01(\x01\x12\x0b\n\x03num\x18\x03 \x01(\x01\x12\x0b\n\x03sum\x18\x04 \x01(\x01\x12\x13\n\x0bsum_squares\x18\x05 \x01(\x01\x12\x18\n\x0c\x62ucket_limit\x18\x06 \x03(\x01\x42\x02\x10\x01\x12\x12\n\x06\x62ucket\x18\x07 \x03(\x01\x42\x02\x10\x01\x42\\\n\x18org.tensorflow.frameworkP\x01Z;github.com/google/tsl/tsl/go/core/protobuf/summary_go_proto\xf8\x01\x01\x62\x06proto3')



_HISTOGRAMPROTO = DESCRIPTOR.message_types_by_name['HistogramProto']
HistogramProto = _reflection.GeneratedProtocolMessageType('HistogramProto', (_message.Message,), {
  'DESCRIPTOR' : _HISTOGRAMPROTO,
  '__module__' : 'tensorboard.compat.proto.histogram_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.HistogramProto)
  })
_sym_db.RegisterMessage(HistogramProto)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkP\001Z;github.com/google/tsl/tsl/go/core/protobuf/summary_go_proto\370\001\001'
  _HISTOGRAMPROTO.fields_by_name['bucket_limit']._options = None
  _HISTOGRAMPROTO.fields_by_name['bucket_limit']._serialized_options = b'\020\001'
  _HISTOGRAMPROTO.fields_by_name['bucket']._options = None
  _HISTOGRAMPROTO.fields_by_name['bucket']._serialized_options = b'\020\001'
  _HISTOGRAMPROTO._serialized_start=58
  _HISTOGRAMPROTO._serialized_end=193
# @@protoc_insertion_point(module_scope)
