import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any

#!/usr/bin/env python3
"""
Fix Unknown League Records
Investigate and fix the 624 records with missing/null league identifiers
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UnknownLeagueRecordsFixer:
    """Fix records with missing or null league identifiers"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def investigate_unknown_records(self) -> Dict[str, Any]:
        """Investigate what the 624 'Unknown' league records are"""
        logger.info("🔍 INVESTIGATING UNKNOWN LEAGUE RECORDS")
        logger.info("=" * 45)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get sample of unknown records
            unknown_samples = conn.execute("""
                SELECT 
                    id, source_file, source_table, data_category, season, 
                    league_id, player_id, team_id, raw_data
                FROM unified_nba_wnba_data 
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'Unknown'
                LIMIT 20
            """).fetchall()
            
            logger.info(f"📊 SAMPLE OF UNKNOWN RECORDS:")
            for record in unknown_samples[:10]:
                logger.info(f"   ID {record[0]}: {record[1]} | {record[2]} | {record[3]} | Season: {record[4]} | League ID: {record[5]}")
            
            # Analyze patterns in unknown records
            unknown_patterns = conn.execute("""
                SELECT 
                    COALESCE(source_table, 'No Source Table') as source_table,
                    COALESCE(data_category, 'No Category') as data_category,
                    COALESCE(season, 'No Season') as season,
                    COALESCE(league_id, 'No League ID') as league_id,
                    COUNT(*) as count
                FROM unified_nba_wnba_data 
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'Unknown'
                GROUP BY source_table, data_category, season, league_id
                ORDER BY count DESC
            """).fetchall()
            
            logger.info(f"\n📈 PATTERNS IN UNKNOWN RECORDS:")
            for source_table, category, season, league_id, count in unknown_patterns:
                logger.info(f"   {source_table} | {category} | {season} | League ID: {league_id} | Count: {count}")
            
            # Check if we can infer league from league_id
            league_id_analysis = conn.execute("""
                SELECT 
                    COALESCE(league_id, 'NULL') as league_id,
                    COUNT(*) as count
                FROM unified_nba_wnba_data 
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'Unknown'
                GROUP BY league_id
                ORDER BY count DESC
            """).fetchall()
            
            logger.info(f"\n🏀 LEAGUE ID ANALYSIS:")
            for league_id, count in league_id_analysis:
                logger.info(f"   League ID '{league_id}': {count} records")
            
            conn.close()
            
            return {
                'unknown_samples': unknown_samples,
                'unknown_patterns': unknown_patterns,
                'league_id_analysis': league_id_analysis
            }
            
        except Exception as e:
            logger.error(f"❌ Error investigating unknown records: {e}")
            return {'error': str(e)}
    
    def fix_unknown_league_records(self) -> Dict[str, Any]:
        """Fix records with missing league identifiers"""
        logger.info("\n🔧 FIXING UNKNOWN LEAGUE RECORDS")
        logger.info("=" * 35)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            fixed_count = 0
            
            # Fix records where league_id = '00' (NBA)
            nba_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'NBA'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'Unknown')
                AND league_id = '00'
            """).rowcount
            
            logger.info(f"✅ Fixed {nba_fixes} records with league_id '00' → NBA")
            fixed_count += nba_fixes
            
            # Fix records where league_id = '10' (WNBA)
            wnba_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'WNBA'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'Unknown')
                AND league_id = '10'
            """).rowcount
            
            logger.info(f"✅ Fixed {wnba_fixes} records with league_id '10' → WNBA")
            fixed_count += wnba_fixes
            
            # Fix records based on season format (NBA uses YYYY-YY, WNBA uses YYYY)
            nba_season_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'NBA', league_id = '00'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'Unknown')
                AND season LIKE '____-__'
                AND season NOT LIKE '%-%-%'
            """).rowcount
            
            logger.info(f"✅ Fixed {nba_season_fixes} records with NBA season format → NBA")
            fixed_count += nba_season_fixes
            
            # Fix records based on WNBA season format
            wnba_season_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'WNBA', league_id = '10'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'Unknown')
                AND season REGEXP '^[0-9]{4}$'
                AND CAST(season AS INTEGER) >= 1997
            """).rowcount
            
            logger.info(f"✅ Fixed {wnba_season_fixes} records with WNBA season format → WNBA")
            fixed_count += wnba_season_fixes
            
            # Fix records based on source file patterns
            nba_file_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'NBA', league_id = '00'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'Unknown')
                AND (source_file LIKE '%nba%' OR source_file LIKE '%NBA%')
            """).rowcount
            
            logger.info(f"✅ Fixed {nba_file_fixes} records with NBA file patterns → NBA")
            fixed_count += nba_file_fixes
            
            wnba_file_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'WNBA', league_id = '10'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'Unknown')
                AND (source_file LIKE '%wnba%' OR source_file LIKE '%WNBA%')
            """).rowcount
            
            logger.info(f"✅ Fixed {wnba_file_fixes} records with WNBA file patterns → WNBA")
            fixed_count += wnba_file_fixes
            
            # Check remaining unknown records
            remaining_unknown = cursor.execute("""
                SELECT COUNT(*) 
                FROM unified_nba_wnba_data 
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'Unknown'
            """).fetchone()[0]
            
            conn.commit()
            conn.close()
            
            logger.info(f"\n🎯 FIXING RESULTS:")
            logger.info(f"   Total fixed: {fixed_count}")
            logger.info(f"   Remaining unknown: {remaining_unknown}")
            
            if remaining_unknown == 0:
                logger.info(f"✅ ALL UNKNOWN LEAGUE RECORDS FIXED!")
            else:
                logger.warning(f"⚠️ {remaining_unknown} records still need manual review")
            
            return {
                'fixed_count': fixed_count,
                'remaining_unknown': remaining_unknown,
                'nba_fixes': nba_fixes,
                'wnba_fixes': wnba_fixes,
                'nba_season_fixes': nba_season_fixes,
                'wnba_season_fixes': wnba_season_fixes,
                'nba_file_fixes': nba_file_fixes,
                'wnba_file_fixes': wnba_file_fixes
            }
            
        except Exception as e:
            logger.error(f"❌ Error fixing unknown records: {e}")
            return {'error': str(e)}
    
    def verify_league_distribution(self) -> Dict[str, Any]:
        """Verify the final league distribution after fixes"""
        logger.info("\n📊 FINAL LEAGUE DISTRIBUTION")
        logger.info("=" * 30)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get final league breakdown
            final_breakdown = conn.execute("""
                SELECT 
                    COALESCE(league_name, 'Still Unknown') as league,
                    COUNT(*) as count 
                FROM unified_nba_wnba_data 
                GROUP BY league_name 
                ORDER BY count DESC
            """).fetchall()
            
            total_records = sum(count for _, count in final_breakdown)
            
            logger.info(f"📊 FINAL LEAGUE BREAKDOWN:")
            for league, count in final_breakdown:
                percentage = (count / total_records) * 100
                logger.info(f"   {league}: {count:,} records ({percentage:.1f}%)")
            
            logger.info(f"\n📊 TOTAL RECORDS: {total_records:,}")
            
            conn.close()
            
            return {
                'final_breakdown': final_breakdown,
                'total_records': total_records
            }
            
        except Exception as e:
            logger.error(f"❌ Error verifying league distribution: {e}")
            return {'error': str(e)}

def main():
    """Execute unknown league records investigation and fixing"""
    
    fixer = UnknownLeagueRecordsFixer()
    
    # Investigate unknown records
    investigation = fixer.investigate_unknown_records()
    
    # Fix unknown records
    fixes = fixer.fix_unknown_league_records()
    
    # Verify final distribution
    verification = fixer.verify_league_distribution()
    
    
    if 'fixed_count' in fixes:
        
        if fixes['remaining_unknown'] == 0:
        else:
    
    return {
        'investigation': investigation,
        'fixes': fixes,
        'verification': verification
    }

if __name__ == "__main__":
    main()
