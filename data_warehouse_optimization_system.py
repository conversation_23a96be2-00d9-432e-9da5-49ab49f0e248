import os
import sys
import sqlite3
import asyncio
import logging
import json
import time
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

#!/usr/bin/env python3
"""
Data Warehouse Optimization System - HYPER MEDUSA NEURAL VAULT
==============================================================

Comprehensive database optimization system for handling 330,800+ records with:
- Advanced indexing strategies for basketball analytics
- Query optimization and performance monitoring
- Partitioning strategies for large-scale data
- Connection pooling and caching optimization
- Real-time performance monitoring and auto-tuning
"""


# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DATA_WAREHOUSE_OPTIMIZER")

class OptimizationType(Enum):
    """Types of database optimizations"""
    INDEXING = "indexing"
    PARTITIONING = "partitioning"
    QUERY_OPTIMIZATION = "query_optimization"
    CONNECTION_POOLING = "connection_pooling"
    CACHING = "caching"
    MAINTENANCE = "maintenance"

class PerformanceLevel(Enum):
    """Performance level classifications"""
    EXCELLENT = "excellent"  # < 10ms
    GOOD = "good"           # 10-50ms
    FAIR = "fair"           # 50-200ms
    POOR = "poor"           # 200-1000ms
    CRITICAL = "critical"   # > 1000ms

@dataclass
class IndexStrategy:
    """Index optimization strategy"""
    table_name: str
    index_name: str
    columns: List[str]
    index_type: str  # btree, hash, composite
    priority: int
    estimated_improvement: float
    basketball_specific: bool = False

@dataclass
class QueryOptimization:
    """Query optimization result"""
    query_pattern: str
    original_time_ms: float
    optimized_time_ms: float
    improvement_percentage: float
    optimization_strategy: str

@dataclass
class PerformanceMetrics:
    """Database performance metrics"""
    total_records: int
    database_size_mb: float
    average_query_time_ms: float
    slow_queries_count: int
    index_efficiency: float
    cache_hit_ratio: float
    connection_pool_utilization: float
    overall_performance_score: float

class DataWarehouseOptimizationSystem:
    """
    Comprehensive data warehouse optimization system
    """
    
    def __init__(self, database_path: str = "hyper_medusa_consolidated.db"):
        self.database_path = database_path
        self.logger = logging.getLogger(self.__class__.__name__)
        self.optimization_history: List[Dict[str, Any]] = []
        self.performance_baseline: Optional[PerformanceMetrics] = None
        
        # Basketball-specific optimization strategies
        self.basketball_indexes = self._define_basketball_indexes()
        self.query_patterns = self._define_query_patterns()
        
    def _define_basketball_indexes(self) -> List[IndexStrategy]:
        """Define basketball-specific indexing strategies"""
        return [
            # Player performance indexes
            IndexStrategy("player_game_stats", "idx_player_performance", 
                         ["hero_id", "points", "total_rebounds", "assists"], 
                         "composite", 1, 0.75, True),
            IndexStrategy("player_game_stats", "idx_player_season", 
                         ["hero_id", "season", "date"], 
                         "composite", 1, 0.80, True),
            IndexStrategy("player_game_stats", "idx_player_efficiency", 
                         ["hero_id", "field_goal_percentage", "three_point_percentage"], 
                         "composite", 2, 0.65, True),
            
            # Game analysis indexes
            IndexStrategy("games", "idx_game_date_season", 
                         ["date", "season"], 
                         "composite", 1, 0.85, True),
            IndexStrategy("games", "idx_game_teams", 
                         ["home_team_id", "away_team_id"], 
                         "composite", 1, 0.70, True),
            
            # Analytics indexes
            IndexStrategy("clutch_analytics", "idx_clutch_performance", 
                         ["player_id", "clutch_efficiency", "clutch_volume"], 
                         "composite", 2, 0.60, True),
            IndexStrategy("shot_analytics", "idx_shot_zones", 
                         ["player_id", "zone_type", "shot_percentage"], 
                         "composite", 2, 0.55, True),
            IndexStrategy("advanced_analytics", "idx_advanced_metrics", 
                         ["player_id", "efficiency_rating", "usage_rate"], 
                         "composite", 2, 0.65, True),
            
            # Labeling system indexes
            IndexStrategy("player_performance_labels", "idx_performance_tier", 
                         ["hero_id", "performance_tier", "performance_score"], 
                         "composite", 1, 0.70, True),
            IndexStrategy("game_context_labels", "idx_game_context", 
                         ["titan_clash_id", "game_competitiveness", "season_phase"], 
                         "composite", 1, 0.75, True),
            IndexStrategy("clutch_situation_labels", "idx_clutch_situations", 
                         ["player_id", "clutch_tier", "clutch_efficiency"], 
                         "composite", 2, 0.60, True),
        ]
    
    def _define_query_patterns(self) -> List[str]:
        """Define common basketball query patterns for optimization"""
        return [
            # Player performance queries
            "SELECT * FROM player_game_stats WHERE hero_id = ? AND season = ?",
            "SELECT AVG(points), AVG(total_rebounds), AVG(assists) FROM player_game_stats WHERE hero_id = ?",
            "SELECT * FROM player_game_stats WHERE points > ? AND date >= ?",
            
            # Game analysis queries
            "SELECT * FROM games WHERE date BETWEEN ? AND ? ORDER BY date",
            "SELECT * FROM games WHERE home_team_id = ? OR away_team_id = ?",
            
            # Analytics queries
            "SELECT * FROM clutch_analytics WHERE clutch_efficiency > ? ORDER BY clutch_volume DESC",
            "SELECT * FROM advanced_analytics WHERE efficiency_rating > ? AND usage_rate > ?",
            
            # Cross-table analytics
            """SELECT p.full_name, AVG(pgs.points), COUNT(*) 
               FROM players p JOIN player_game_stats pgs ON p.id = pgs.hero_id 
               WHERE pgs.season = ? GROUP BY p.id""",
            
            # Performance labeling queries
            "SELECT * FROM player_performance_labels WHERE performance_tier = ?",
            "SELECT * FROM game_context_labels WHERE game_competitiveness = 'high'",
        ]
    
    async def run_comprehensive_optimization(self) -> Dict[str, Any]:
        """Run comprehensive data warehouse optimization"""
        self.logger.info("🚀 Starting Comprehensive Data Warehouse Optimization...")
        
        start_time = time.time()
        optimization_results = {
            "optimization_timestamp": datetime.now().isoformat(),
            "database_path": self.database_path,
            "optimization_phases": {}
        }
        
        # Phase 1: Baseline Performance Assessment
        self.logger.info("📊 Phase 1: Baseline Performance Assessment")
        baseline_results = await self._assess_baseline_performance()
        optimization_results["optimization_phases"]["baseline_assessment"] = baseline_results
        
        # Phase 2: Advanced Indexing Optimization
        self.logger.info("🔍 Phase 2: Advanced Indexing Optimization")
        indexing_results = await self._optimize_indexing()
        optimization_results["optimization_phases"]["indexing_optimization"] = indexing_results
        
        # Phase 3: Query Pattern Optimization
        self.logger.info("⚡ Phase 3: Query Pattern Optimization")
        query_results = await self._optimize_query_patterns()
        optimization_results["optimization_phases"]["query_optimization"] = query_results
        
        # Phase 4: Database Maintenance and Cleanup
        self.logger.info("🧹 Phase 4: Database Maintenance and Cleanup")
        maintenance_results = await self._perform_database_maintenance()
        optimization_results["optimization_phases"]["maintenance"] = maintenance_results
        
        # Phase 5: Performance Validation
        self.logger.info("✅ Phase 5: Performance Validation")
        validation_results = await self._validate_optimization_results()
        optimization_results["optimization_phases"]["validation"] = validation_results
        
        # Calculate overall improvement
        total_time = time.time() - start_time
        optimization_results["total_optimization_time_seconds"] = round(total_time, 2)
        optimization_results["overall_improvement"] = self._calculate_overall_improvement(
            baseline_results, validation_results
        )
        
        # Save optimization report
        report_file = f"data_warehouse_optimization_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(optimization_results, f, indent=2)
        
        self.logger.info(f"📋 Optimization report saved to: {report_file}")
        
        return optimization_results
    
    async def _assess_baseline_performance(self) -> Dict[str, Any]:
        """Assess baseline database performance"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Get database size
            db_size_mb = os.path.getsize(self.database_path) / (1024*1024)
            
            # Count total records
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            total_records = 0
            table_stats = {}
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                total_records += count
                table_stats[table] = count
            
            # Test query performance
            query_performance = []
            for query in self.query_patterns[:5]:  # Test first 5 patterns
                start_time = time.time()
                try:
                    if "?" in query:
                        # Use sample parameters for testing
                        if "hero_id" in query:
                            cursor.execute(query.replace("?", "1"))
                        elif "date" in query:
                            cursor.execute(query.replace("?", "'2024-01-01'"))
                        else:
                            cursor.execute(query.replace("?", "10"))
                    else:
                        cursor.execute(query)
                    
                    results = cursor.fetchall()
                    query_time = (time.time() - start_time) * 1000  # Convert to ms
                    
                    query_performance.append({
                        "query": query[:100] + "..." if len(query) > 100 else query,
                        "execution_time_ms": round(query_time, 2),
                        "result_count": len(results)
                    })
                except Exception as e:
                    query_performance.append({
                        "query": query[:100] + "..." if len(query) > 100 else query,
                        "execution_time_ms": 0,
                        "error": str(e)
                    })
            
            # Check existing indexes
            existing_indexes = {}
            for table in tables:
                cursor.execute(f"PRAGMA index_list({table})")
                indexes = cursor.fetchall()
                existing_indexes[table] = len(indexes)
            
            conn.close()
            
            # Calculate performance metrics
            avg_query_time = sum(q.get("execution_time_ms", 0) for q in query_performance) / len(query_performance)
            slow_queries = sum(1 for q in query_performance if q.get("execution_time_ms", 0) > 100)
            
            self.performance_baseline = PerformanceMetrics(
                total_records=total_records,
                database_size_mb=db_size_mb,
                average_query_time_ms=avg_query_time,
                slow_queries_count=slow_queries,
                index_efficiency=0.5,  # Will be calculated after indexing
                cache_hit_ratio=0.0,   # Not applicable for SQLite
                connection_pool_utilization=0.0,  # Not applicable for SQLite
                overall_performance_score=self._calculate_performance_score(avg_query_time, slow_queries)
            )
            
            return {
                "success": True,
                "database_size_mb": db_size_mb,
                "total_records": total_records,
                "total_tables": len(tables),
                "table_statistics": table_stats,
                "query_performance": query_performance,
                "average_query_time_ms": round(avg_query_time, 2),
                "slow_queries_count": slow_queries,
                "existing_indexes": existing_indexes,
                "performance_level": self._classify_performance_level(avg_query_time),
                "baseline_metrics": {
                    "total_records": total_records,
                    "database_size_mb": db_size_mb,
                    "average_query_time_ms": avg_query_time,
                    "slow_queries_count": slow_queries,
                    "overall_performance_score": self.performance_baseline.overall_performance_score
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Baseline performance assessment failed"
            }
    
    def _calculate_performance_score(self, avg_query_time: float, slow_queries: int) -> float:
        """Calculate overall performance score (0-100)"""
        # Base score starts at 100
        score = 100.0
        
        # Deduct points for slow average query time
        if avg_query_time > 10:
            score -= min(50, (avg_query_time - 10) * 2)
        
        # Deduct points for slow queries
        score -= min(30, slow_queries * 5)
        
        return max(0, score)
    
    def _classify_performance_level(self, avg_query_time: float) -> str:
        """Classify performance level based on query time"""
        if avg_query_time < 10:
            return PerformanceLevel.EXCELLENT.value
        elif avg_query_time < 50:
            return PerformanceLevel.GOOD.value
        elif avg_query_time < 200:
            return PerformanceLevel.FAIR.value
        elif avg_query_time < 1000:
            return PerformanceLevel.POOR.value
        else:
            return PerformanceLevel.CRITICAL.value

    async def _optimize_indexing(self) -> Dict[str, Any]:
        """Optimize database indexing with basketball-specific strategies"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            indexing_results = {
                "indexes_created": [],
                "indexes_failed": [],
                "performance_improvements": [],
                "total_indexes_created": 0
            }

            for index_strategy in self.basketball_indexes:
                try:
                    # Check if index already exists
                    cursor.execute(f"PRAGMA index_list({index_strategy.table_name})")
                    existing_indexes = [idx[1] for idx in cursor.fetchall()]

                    if index_strategy.index_name not in existing_indexes:
                        # Create the index
                        columns_str = ", ".join(index_strategy.columns)
                        create_index_sql = f"""
                        CREATE INDEX IF NOT EXISTS {index_strategy.index_name}
                        ON {index_strategy.table_name} ({columns_str})
                        """

                        start_time = time.time()
                        cursor.execute(create_index_sql)
                        creation_time = time.time() - start_time

                        indexing_results["indexes_created"].append({
                            "index_name": index_strategy.index_name,
                            "table_name": index_strategy.table_name,
                            "columns": index_strategy.columns,
                            "creation_time_seconds": round(creation_time, 3),
                            "basketball_specific": index_strategy.basketball_specific,
                            "estimated_improvement": index_strategy.estimated_improvement
                        })

                        indexing_results["total_indexes_created"] += 1
                        self.logger.info(f"✅ Created index: {index_strategy.index_name}")
                    else:
                        self.logger.info(f"⏭️ Index already exists: {index_strategy.index_name}")

                except Exception as e:
                    indexing_results["indexes_failed"].append({
                        "index_name": index_strategy.index_name,
                        "table_name": index_strategy.table_name,
                        "error": str(e)
                    })
                    self.logger.warning(f"❌ Failed to create index {index_strategy.index_name}: {e}")

            # Commit changes
            conn.commit()
            conn.close()

            return {
                "success": True,
                "indexing_results": indexing_results,
                "message": f"Successfully created {indexing_results['total_indexes_created']} basketball-specific indexes"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Indexing optimization failed"
            }

    async def _optimize_query_patterns(self) -> Dict[str, Any]:
        """Optimize common query patterns"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            query_optimizations = []

            for query in self.query_patterns:
                try:
                    # Test query performance before optimization
                    start_time = time.time()

                    # Execute with sample parameters
                    if "?" in query:
                        if "hero_id" in query:
                            test_query = query.replace("?", "1")
                        elif "date" in query:
                            test_query = query.replace("?", "'2024-01-01'")
                        elif "season" in query:
                            test_query = query.replace("?", "'2023-24'")
                        else:
                            test_query = query.replace("?", "10")
                    else:
                        test_query = query

                    cursor.execute(test_query)
                    results = cursor.fetchall()
                    query_time = (time.time() - start_time) * 1000

                    # Analyze query plan
                    cursor.execute(f"EXPLAIN QUERY PLAN {test_query}")
                    query_plan = cursor.fetchall()

                    optimization = QueryOptimization(
                        query_pattern=query[:100] + "..." if len(query) > 100 else query,
                        original_time_ms=round(query_time, 2),
                        optimized_time_ms=round(query_time, 2),  # Same for now, will improve with indexes
                        improvement_percentage=0.0,
                        optimization_strategy="Index-based optimization"
                    )

                    query_optimizations.append({
                        "query_pattern": optimization.query_pattern,
                        "execution_time_ms": optimization.original_time_ms,
                        "result_count": len(results),
                        "query_plan": [str(step) for step in query_plan],
                        "performance_level": self._classify_performance_level(optimization.original_time_ms)
                    })

                except Exception as e:
                    query_optimizations.append({
                        "query_pattern": query[:100] + "..." if len(query) > 100 else query,
                        "error": str(e),
                        "execution_time_ms": 0
                    })

            conn.close()

            # Calculate average improvement
            successful_queries = [q for q in query_optimizations if "error" not in q]
            avg_query_time = sum(q["execution_time_ms"] for q in successful_queries) / len(successful_queries) if successful_queries else 0

            return {
                "success": True,
                "query_optimizations": query_optimizations,
                "total_queries_tested": len(self.query_patterns),
                "successful_queries": len(successful_queries),
                "average_query_time_ms": round(avg_query_time, 2),
                "performance_level": self._classify_performance_level(avg_query_time),
                "message": f"Analyzed {len(successful_queries)} query patterns successfully"
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Query pattern optimization failed"
            }

    async def _perform_database_maintenance(self) -> Dict[str, Any]:
        """Perform database maintenance and cleanup"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            maintenance_results = {
                "operations_performed": [],
                "space_reclaimed_mb": 0,
                "integrity_check": "passed"
            }

            # 1. VACUUM to reclaim space and defragment
            self.logger.info("🧹 Running VACUUM operation...")
            db_size_before = os.path.getsize(self.database_path) / (1024*1024)

            start_time = time.time()
            cursor.execute("VACUUM")
            vacuum_time = time.time() - start_time

            db_size_after = os.path.getsize(self.database_path) / (1024*1024)
            space_reclaimed = db_size_before - db_size_after

            maintenance_results["operations_performed"].append({
                "operation": "VACUUM",
                "duration_seconds": round(vacuum_time, 2),
                "space_reclaimed_mb": round(space_reclaimed, 2)
            })
            maintenance_results["space_reclaimed_mb"] = round(space_reclaimed, 2)

            # 2. ANALYZE to update statistics
            self.logger.info("📊 Running ANALYZE operation...")
            start_time = time.time()
            cursor.execute("ANALYZE")
            analyze_time = time.time() - start_time

            maintenance_results["operations_performed"].append({
                "operation": "ANALYZE",
                "duration_seconds": round(analyze_time, 2),
                "description": "Updated table statistics for query optimizer"
            })

            # 3. Integrity check
            self.logger.info("🔍 Running integrity check...")
            start_time = time.time()
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()[0]
            integrity_time = time.time() - start_time

            maintenance_results["integrity_check"] = integrity_result
            maintenance_results["operations_performed"].append({
                "operation": "INTEGRITY_CHECK",
                "duration_seconds": round(integrity_time, 2),
                "result": integrity_result
            })

            # 4. Update database configuration for performance
            cursor.execute("PRAGMA cache_size = 10000")  # Increase cache size
            cursor.execute("PRAGMA temp_store = MEMORY")  # Use memory for temp storage
            cursor.execute("PRAGMA journal_mode = WAL")   # Use WAL mode for better concurrency
            cursor.execute("PRAGMA synchronous = NORMAL") # Balance between safety and performance

            maintenance_results["operations_performed"].append({
                "operation": "CONFIGURATION_UPDATE",
                "description": "Updated SQLite configuration for optimal performance",
                "settings": {
                    "cache_size": 10000,
                    "temp_store": "MEMORY",
                    "journal_mode": "WAL",
                    "synchronous": "NORMAL"
                }
            })

            conn.close()

            return {
                "success": True,
                "maintenance_results": maintenance_results,
                "message": f"Database maintenance completed. Reclaimed {space_reclaimed:.2f} MB of space."
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Database maintenance failed"
            }

    async def _validate_optimization_results(self) -> Dict[str, Any]:
        """Validate optimization results and measure improvements"""
        try:
            # Re-run baseline assessment to measure improvements
            validation_results = await self._assess_baseline_performance()

            if self.performance_baseline and validation_results["success"]:
                # Calculate improvements
                baseline_metrics = self.performance_baseline
                current_metrics = validation_results["baseline_metrics"]

                improvements = {
                    "query_time_improvement": {
                        "before_ms": baseline_metrics.average_query_time_ms,
                        "after_ms": current_metrics["average_query_time_ms"],
                        "improvement_percentage": round(
                            ((baseline_metrics.average_query_time_ms - current_metrics["average_query_time_ms"])
                             / baseline_metrics.average_query_time_ms) * 100, 2
                        ) if baseline_metrics.average_query_time_ms > 0 else 0
                    },
                    "slow_queries_improvement": {
                        "before_count": baseline_metrics.slow_queries_count,
                        "after_count": current_metrics["slow_queries_count"],
                        "improvement_count": baseline_metrics.slow_queries_count - current_metrics["slow_queries_count"]
                    },
                    "performance_score_improvement": {
                        "before_score": baseline_metrics.overall_performance_score,
                        "after_score": current_metrics["overall_performance_score"],
                        "improvement_points": round(
                            current_metrics["overall_performance_score"] - baseline_metrics.overall_performance_score, 2
                        )
                    }
                }

                validation_results["improvements"] = improvements
                validation_results["optimization_successful"] = (
                    improvements["query_time_improvement"]["improvement_percentage"] > 0 or
                    improvements["slow_queries_improvement"]["improvement_count"] > 0
                )

            return validation_results

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Optimization validation failed"
            }

    def _calculate_overall_improvement(self, baseline_results: Dict[str, Any],
                                     validation_results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall optimization improvement"""
        try:
            if not baseline_results.get("success") or not validation_results.get("success"):
                return {"overall_improvement": "Unable to calculate - missing baseline or validation data"}

            baseline_time = baseline_results.get("average_query_time_ms", 0)
            validation_time = validation_results.get("average_query_time_ms", 0)

            if baseline_time > 0:
                time_improvement = ((baseline_time - validation_time) / baseline_time) * 100
            else:
                time_improvement = 0

            baseline_slow = baseline_results.get("slow_queries_count", 0)
            validation_slow = validation_results.get("slow_queries_count", 0)
            slow_query_improvement = baseline_slow - validation_slow

            return {
                "query_time_improvement_percentage": round(time_improvement, 2),
                "slow_queries_reduced": slow_query_improvement,
                "baseline_performance_level": baseline_results.get("performance_level", "unknown"),
                "optimized_performance_level": validation_results.get("performance_level", "unknown"),
                "optimization_successful": time_improvement > 0 or slow_query_improvement > 0,
                "summary": f"Query time improved by {time_improvement:.1f}%, reduced {slow_query_improvement} slow queries"
            }

        except Exception as e:
            return {
                "error": str(e),
                "message": "Failed to calculate overall improvement"
            }

async def main():
    """Main execution function"""

    # Initialize optimization system
    optimizer = DataWarehouseOptimizationSystem()

    # Run comprehensive optimization
    results = await optimizer.run_comprehensive_optimization()

    # Display results summary

    if results.get("overall_improvement", {}).get("optimization_successful"):
        improvement = results["overall_improvement"]
    else:


    return results

if __name__ == "__main__":
    asyncio.run(main())
