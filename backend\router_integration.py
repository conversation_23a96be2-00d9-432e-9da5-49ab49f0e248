import logging
import importlib
import os
from typing import Dict, List, Optional, Any
from pathlib import Path
from fastapi import APIRouter, FastAPI
from fastapi.routing import APIRoute

#!/usr/bin/env python3
"""
🏀 HYPER MEDUSA NEURAL VAULT - Router Integration System
======================================================

Unified router integration system that consolidates existing expert routers
and eliminates duplication while maintaining all functionality.

Features:
- Automatic discovery and registration of expert routers
- Elimination of duplicate router functionality
- Unified authentication and middleware integration
- Production-ready router consolidation
- Backward compatibility with existing endpoints
"""



# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("hyper_medusa_router_integration")

class RouterIntegrationManager:
    """
    Router Integration Manager
    
    Manages the integration and consolidation of all backend routers
    into a unified, production-ready system.
    """
    
    def __init__(self, backend_path: str = "backend"):
        self.backend_path = backend_path
        # Handle case where we're running from backend directory
        if os.path.basename(os.getcwd()) == "backend":
            self.routers_path = "routers"
        else:
            self.routers_path = os.path.join(backend_path, "routers")
        self.discovered_routers = {}
        self.expert_routers = {}
        self.regular_routers = {}
        self.consolidated_routers = {}
        self.integration_stats = {
            "total_discovered": 0,
            "expert_routers": 0,
            "regular_routers": 0,
            "consolidated": 0,
            "duplicates_eliminated": 0
        }
    
    def discover_routers(self) -> Dict[str, Any]:
        """Discover all available routers in the backend"""
        logger.info("🔍 Discovering available routers...")
        
        if not os.path.exists(self.routers_path):
            logger.warning(f"Routers path not found: {self.routers_path}")
            return {}
        
        router_files = []
        for file in os.listdir(self.routers_path):
            if file.endswith('.py') and not file.startswith('__'):
                router_files.append(file[:-3])  # Remove .py extension
        
        logger.info(f"Found {len(router_files)} router files: {router_files}")
        
        # Categorize routers
        for router_name in router_files:
            try:
                # Attempt to import the router
                module_path = f"backend.routers.{router_name}"
                module = importlib.import_module(module_path)
                
                if hasattr(module, 'router'):
                    router = module.router
                    self.discovered_routers[router_name] = {
                        "module": module,
                        "router": router,
                        "is_expert": "expert" in router_name.lower(),
                        "base_name": router_name.replace("_expert", "").replace("expert_", ""),
                        "endpoints": len(router.routes) if hasattr(router, 'routes') else 0
                    }
                    
                    # Categorize
                    if "expert" in router_name.lower():
                        self.expert_routers[router_name] = self.discovered_routers[router_name]
                    else:
                        self.regular_routers[router_name] = self.discovered_routers[router_name]
                    
                    logger.info(f"✅ Discovered router: {router_name} ({'expert' if 'expert' in router_name.lower() else 'regular'})")
                else:
                    logger.warning(f"⚠️ No 'router' attribute found in {router_name}")
                    
            except ImportError as e:
                logger.warning(f"⚠️ Failed to import {router_name}: {e}")
            except Exception as e:
                logger.error(f"❌ Error processing {router_name}: {e}")
        
        self.integration_stats["total_discovered"] = len(self.discovered_routers)
        self.integration_stats["expert_routers"] = len(self.expert_routers)
        self.integration_stats["regular_routers"] = len(self.regular_routers)
        
        logger.info(f"📊 Discovery complete: {self.integration_stats}")
        return self.discovered_routers
    
    def identify_duplicates(self) -> Dict[str, List[str]]:
        """Identify duplicate router functionality"""
        logger.info("🔍 Identifying duplicate routers...")
        
        duplicates = {}
        
        # Check for expert/regular pairs
        for expert_name, expert_info in self.expert_routers.items():
            base_name = expert_info["base_name"]
            
            # Look for corresponding regular router
            regular_candidates = [
                name for name, info in self.regular_routers.items()
                if info["base_name"] == base_name
            ]
            
            if regular_candidates:
                duplicates[base_name] = {
                    "expert": expert_name,
                    "regular": regular_candidates,
                    "consolidation_strategy": "prefer_expert"
                }
                logger.info(f"🔄 Found duplicate pair: {expert_name} <-> {regular_candidates}")
        
        self.integration_stats["duplicates_eliminated"] = len(duplicates)
        logger.info(f"📊 Found {len(duplicates)} duplicate router pairs")
        return duplicates
    
    def consolidate_routers(self) -> Dict[str, APIRouter]:
        """Consolidate routers by eliminating duplicates and preferring expert versions"""
        logger.info("🔧 Consolidating routers...")
        
        duplicates = self.identify_duplicates()
        
        # Start with all expert routers (they have priority)
        for expert_name, expert_info in self.expert_routers.items():
            base_name = expert_info["base_name"]
            self.consolidated_routers[base_name] = {
                "router": expert_info["router"],
                "source": expert_name,
                "type": "expert",
                "endpoints": expert_info["endpoints"]
            }
            logger.info(f"✅ Consolidated {base_name} using expert router: {expert_name}")
        
        # Add regular routers that don't have expert equivalents
        for regular_name, regular_info in self.regular_routers.items():
            base_name = regular_info["base_name"]
            
            # Only add if no expert version exists
            if base_name not in self.consolidated_routers:
                self.consolidated_routers[base_name] = {
                    "router": regular_info["router"],
                    "source": regular_name,
                    "type": "regular",
                    "endpoints": regular_info["endpoints"]
                }
                logger.info(f"✅ Consolidated {base_name} using regular router: {regular_name}")
        
        self.integration_stats["consolidated"] = len(self.consolidated_routers)
        
        logger.info(f"🎯 Consolidation complete: {len(self.consolidated_routers)} unified routers")
        return self.consolidated_routers
    
    def register_consolidated_routers(self, app: FastAPI) -> Dict[str, Any]:
        """Register all consolidated routers with the FastAPI app"""
        logger.info("📝 Registering consolidated routers with FastAPI app...")
        
        registration_results = {
            "successful": [],
            "failed": [],
            "total_endpoints": 0
        }
        
        for base_name, router_info in self.consolidated_routers.items():
            try:
                router = router_info["router"]
                
                # Determine prefix based on router type and existing configuration
                prefix = f"/api/v1/{base_name}"
                if hasattr(router, 'prefix') and router.prefix:
                    prefix = router.prefix
                
                # Register the router
                app.include_router(
                    router,
                    prefix=prefix if not prefix.startswith('/api') else "",
                    tags=[f"🔧 {base_name.title()}"]
                )
                
                registration_results["successful"].append({
                    "name": base_name,
                    "source": router_info["source"],
                    "type": router_info["type"],
                    "prefix": prefix,
                    "endpoints": router_info["endpoints"]
                })
                
                registration_results["total_endpoints"] += router_info["endpoints"]
                
                logger.info(f"✅ Registered {base_name} router from {router_info['source']} ({router_info['endpoints']} endpoints)")
                
            except Exception as e:
                logger.error(f"❌ Failed to register {base_name}: {e}")
                registration_results["failed"].append({
                    "name": base_name,
                    "source": router_info["source"],
                    "error": str(e)
                })
        
        logger.info(f"📊 Registration complete: {len(registration_results['successful'])} successful, {len(registration_results['failed'])} failed")
        logger.info(f"🎯 Total endpoints registered: {registration_results['total_endpoints']}")
        
        return registration_results
    
    def get_integration_summary(self) -> Dict[str, Any]:
        """Get comprehensive integration summary"""
        return {
            "discovery": {
                "total_routers_found": self.integration_stats["total_discovered"],
                "expert_routers": self.integration_stats["expert_routers"],
                "regular_routers": self.integration_stats["regular_routers"]
            },
            "consolidation": {
                "duplicates_eliminated": self.integration_stats["duplicates_eliminated"],
                "consolidated_routers": self.integration_stats["consolidated"],
                "consolidation_ratio": f"{self.integration_stats['duplicates_eliminated']}/{self.integration_stats['total_discovered']}"
            },
            "routers": {
                "expert_routers": list(self.expert_routers.keys()),
                "regular_routers": list(self.regular_routers.keys()),
                "consolidated_routers": list(self.consolidated_routers.keys())
            },
            "performance": {
                "total_endpoints": sum(info["endpoints"] for info in self.consolidated_routers.values()),
                "average_endpoints_per_router": sum(info["endpoints"] for info in self.consolidated_routers.values()) / len(self.consolidated_routers) if self.consolidated_routers else 0
            }
        }
    
    def perform_full_integration(self, app: FastAPI) -> Dict[str, Any]:
        """Perform complete router integration process"""
        logger.info("🚀 Starting full router integration process...")
        
        # Step 1: Discover all routers
        self.discover_routers()
        
        # Step 2: Consolidate routers
        self.consolidate_routers()
        
        # Step 3: Register with FastAPI app
        registration_results = self.register_consolidated_routers(app)
        
        # Step 4: Generate summary
        integration_summary = self.get_integration_summary()
        
        logger.info("✅ Full router integration complete!")
        
        return {
            "integration_summary": integration_summary,
            "registration_results": registration_results,
            "status": "completed",
            "timestamp": "2025-07-01T12:00:00Z"
        }

# Global instance for easy access
router_integration_manager = RouterIntegrationManager()

# Convenience function for FastAPI integration
def integrate_all_routers(app: FastAPI) -> Dict[str, Any]:
    """Convenience function to integrate all routers with a FastAPI app"""
    return router_integration_manager.perform_full_integration(app)
