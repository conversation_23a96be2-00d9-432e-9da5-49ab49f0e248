import os
import sqlite3
import pandas as pd
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import numpy as np
from datetime import datetime
        import re

#!/usr/bin/env python3
"""
Comprehensive NBA Data Import
Import all 1,877 NBA files found in filesystem to complete the dataset
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveNBAImporter:
    """Import all NBA data files into the unified database"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.imported_files = []
        self.failed_files = []
        self.total_records = 0
        
        # NBA data directories to process
        self.nba_directories = [
            "data/nba_10year_historical",
            "data/smart_10year_historical", 
            "data"
        ]
        
        # File patterns to look for
        self.nba_patterns = [
            "nba_",
            "_nba_",
            "NBA_",
            "_NBA_"
        ]
        
    def discover_nba_files(self) -> List[str]:
        """Discover all NBA data files"""
        logger.info("🔍 Discovering NBA data files...")
        
        nba_files = []
        
        for directory in self.nba_directories:
            if os.path.exists(directory):
                logger.info(f"📂 Scanning {directory}/")
                
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        # Check if file matches NBA patterns
                        if any(pattern in file for pattern in self.nba_patterns):
                            # Only process CSV files (skip error files)
                            if file.endswith('.csv') and not file.endswith('_ERROR.txt'):
                                file_path = os.path.join(root, file)
                                file_size = os.path.getsize(file_path)
                                
                                # Skip tiny files (likely empty)
                                if file_size > 100:  # More than 100 bytes
                                    nba_files.append(file_path)
                                    logger.info(f"   ✅ Found: {file} ({file_size:,} bytes)")
                                else:
                                    logger.info(f"   ⚠️ Skipped (too small): {file} ({file_size} bytes)")
        
        logger.info(f"🎯 Discovered {len(nba_files)} NBA data files")
        return nba_files
    
    def categorize_nba_files(self, nba_files: List[str]) -> Dict[str, List[str]]:
        """Categorize NBA files by data type"""
        logger.info("📊 Categorizing NBA files by data type...")
        
        categories = {
            'player_stats': [],
            'team_stats': [],
            'game_data': [],
            'advanced_stats': [],
            'shot_data': [],
            'clutch_stats': [],
            'defense_stats': [],
            'bio_data': [],
            'other': []
        }
        
        for file_path in nba_files:
            file_name = os.path.basename(file_path).lower()
            
            if 'player' in file_name and ('stats' in file_name or 'gamelog' in file_name):
                categories['player_stats'].append(file_path)
            elif 'team' in file_name and ('stats' in file_name or 'gamelog' in file_name):
                categories['team_stats'].append(file_path)
            elif 'game' in file_name or 'boxscore' in file_name:
                categories['game_data'].append(file_path)
            elif 'advanced' in file_name:
                categories['advanced_stats'].append(file_path)
            elif 'shot' in file_name or 'catch' in file_name or 'pullup' in file_name:
                categories['shot_data'].append(file_path)
            elif 'clutch' in file_name:
                categories['clutch_stats'].append(file_path)
            elif 'defense' in file_name:
                categories['defense_stats'].append(file_path)
            elif 'bio' in file_name:
                categories['bio_data'].append(file_path)
            else:
                categories['other'].append(file_path)
        
        # Log categorization results
        for category, files in categories.items():
            if files:
                logger.info(f"   📁 {category}: {len(files)} files")
        
        return categories
    
    def import_nba_file(self, file_path: str, category: str) -> Dict[str, Any]:
        """Import a single NBA file into the database"""
        try:
            # Read the file
            df = pd.read_csv(file_path)
            
            if df.empty:
                return {'success': False, 'error': 'Empty file', 'records': 0}
            
            # Extract metadata from filename
            file_name = os.path.basename(file_path)
            metadata = self._extract_metadata_from_filename(file_name)
            
            # Prepare data for database
            records = []
            for _, row in df.iterrows():
                record = {
                    'source_file': file_name,
                    'source_table': category,
                    'data_category': metadata.get('data_type', category),
                    'season': metadata.get('season'),
                    'league_id': '00',  # NBA league ID
                    'league_name': 'NBA',
                    'season_type': metadata.get('season_type', 'Regular Season'),
                    'player_id': row.get('PLAYER_ID') or row.get('player_id'),
                    'player_name': row.get('PLAYER_NAME') or row.get('player_name') or row.get('PLAYER'),
                    'team_id': row.get('TEAM_ID') or row.get('team_id'),
                    'team_abbreviation': row.get('TEAM_ABBREVIATION') or row.get('team_abbreviation') or row.get('TEAM'),
                    'team_name': row.get('TEAM_NAME') or row.get('team_name'),
                    'game_id': row.get('GAME_ID') or row.get('game_id'),
                    'game_date': row.get('GAME_DATE') or row.get('game_date'),
                    'stat_category': self._determine_stat_category(row, category),
                    'stat_value': self._extract_primary_stat(row, category),
                    'rank_position': row.get('RANK') or row.get('rank'),
                    'collection_id': f"nba_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    'data_type': 'historical_nba',
                    'raw_data': json.dumps(row.to_dict(), default=str),
                    'created_at': datetime.now().isoformat()
                }
                records.append(record)
            
            # Insert into database
            self._insert_records(records)
            
            return {
                'success': True,
                'records': len(records),
                'file_size': os.path.getsize(file_path)
            }
            
        except Exception as e:
            logger.error(f"❌ Error importing {file_path}: {e}")
            return {'success': False, 'error': str(e), 'records': 0}
    
    def _extract_metadata_from_filename(self, filename: str) -> Dict[str, str]:
        """Extract metadata from NBA filename"""
        metadata = {}
        
        # Extract season
        season_match = re.search(r'(\d{4})', filename)
        if season_match:
            year = season_match.group(1)
            # Convert to NBA season format (e.g., 2023 -> 2023-24)
            next_year = str(int(year) + 1)[-2:]
            metadata['season'] = f"{year}-{next_year}"
        
        # Extract data type
        if 'advanced' in filename.lower():
            metadata['data_type'] = 'advanced_stats'
        elif 'base' in filename.lower():
            metadata['data_type'] = 'base_stats'
        elif 'bio' in filename.lower():
            metadata['data_type'] = 'bio_stats'
        elif 'clutch' in filename.lower():
            metadata['data_type'] = 'clutch_stats'
        elif 'defense' in filename.lower():
            metadata['data_type'] = 'defense_stats'
        elif 'shot' in filename.lower():
            metadata['data_type'] = 'shot_stats'
        elif 'game' in filename.lower():
            metadata['data_type'] = 'game_stats'
        else:
            metadata['data_type'] = 'general_stats'
        
        return metadata
    
    def _determine_stat_category(self, row: pd.Series, category: str) -> str:
        """Determine the primary stat category for this row"""
        # Look for common stat columns
        if 'PTS' in row.index or 'points' in str(row.index).lower():
            return 'points'
        elif 'REB' in row.index or 'rebounds' in str(row.index).lower():
            return 'rebounds'
        elif 'AST' in row.index or 'assists' in str(row.index).lower():
            return 'assists'
        elif 'FG_PCT' in row.index or 'fg_pct' in str(row.index).lower():
            return 'field_goal_percentage'
        else:
            return category
    
    def _extract_primary_stat(self, row: pd.Series, category: str) -> Optional[float]:
        """Extract the primary statistical value from the row"""
        # Priority order for extracting stats
        stat_columns = ['PTS', 'REB', 'AST', 'FG_PCT', 'MIN', 'GP', 'GS']
        
        for col in stat_columns:
            if col in row.index and pd.notna(row[col]):
                try:
                    return float(row[col])
                except (ValueError, TypeError):
                    continue
        
        # If no standard stat found, try to find any numeric column
        for col, value in row.items():
            if pd.notna(value):
                try:
                    return float(value)
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def _insert_records(self, records: List[Dict]) -> None:
        """Insert records into the unified database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for record in records:
            try:
                cursor.execute("""
                    INSERT INTO unified_nba_wnba_data (
                        source_file, source_table, data_category, season, league_id, league_name,
                        season_type, player_id, player_name, team_id, team_abbreviation, team_name,
                        game_id, game_date, stat_category, stat_value, rank_position, collection_id,
                        data_type, raw_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['source_file'], record['source_table'], record['data_category'],
                    record['season'], record['league_id'], record['league_name'],
                    record['season_type'], record['player_id'], record['player_name'],
                    record['team_id'], record['team_abbreviation'], record['team_name'],
                    record['game_id'], record['game_date'], record['stat_category'],
                    record['stat_value'], record['rank_position'], record['collection_id'],
                    record['data_type'], record['raw_data'], record['created_at']
                ))
            except Exception as e:
                logger.warning(f"⚠️ Failed to insert record: {e}")
        
        conn.commit()
        conn.close()
    
    def run_comprehensive_import(self, max_files: int = None) -> Dict[str, Any]:
        """Run the complete NBA data import process"""
        logger.info("🚀 STARTING COMPREHENSIVE NBA DATA IMPORT")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        # Step 1: Discover files
        nba_files = self.discover_nba_files()
        
        if not nba_files:
            logger.warning("⚠️ No NBA files found!")
            return {'success': False, 'error': 'No NBA files found'}
        
        # Limit files for initial run
        if max_files and len(nba_files) > max_files:
            nba_files = nba_files[:max_files]
            logger.info(f"📊 Processing first {max_files} files for initial import")
        
        # Step 2: Categorize files
        categories = self.categorize_nba_files(nba_files)
        
        # Step 3: Import files by category
        import_results = {}
        total_imported = 0
        
        for category, files in categories.items():
            if not files:
                continue
                
            logger.info(f"\n📊 Importing {category} files ({len(files)} files)...")
            category_results = []
            
            for file_path in files:
                logger.info(f"   📁 Processing: {os.path.basename(file_path)}")
                result = self.import_nba_file(file_path, category)
                
                if result['success']:
                    self.imported_files.append(file_path)
                    total_imported += result['records']
                    logger.info(f"      ✅ Imported {result['records']:,} records")
                else:
                    self.failed_files.append((file_path, result.get('error', 'Unknown error')))
                    logger.error(f"      ❌ Failed: {result.get('error', 'Unknown error')}")
                
                category_results.append(result)
            
            import_results[category] = category_results
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Final summary
        logger.info(f"\n🎯 NBA IMPORT COMPLETE!")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   ✅ Files imported: {len(self.imported_files)}")
        logger.info(f"   ❌ Files failed: {len(self.failed_files)}")
        logger.info(f"   📊 Total records: {total_imported:,}")
        
        return {
            'success': True,
            'duration': str(duration),
            'files_imported': len(self.imported_files),
            'files_failed': len(self.failed_files),
            'total_records': total_imported,
            'import_results': import_results,
            'failed_files': self.failed_files
        }

def main():
    """Run the comprehensive NBA import"""
    
    importer = ComprehensiveNBAImporter()
    
    # Start with first 100 files to test
    results = importer.run_comprehensive_import(max_files=100)
    
    if results['success']:
        
        if results['files_failed'] > 0:
            for file_path, error in results['failed_files']:
        
    else:
    
    return results

if __name__ == "__main__":
    main()
