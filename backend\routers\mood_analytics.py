from fastapi import APIRouter
from datetime import datetime


"""
DEPRECATED: This mood analytics router has been consolidated into unified_analytics.py

All mood analytics functionality has been moved to the unified analytics service.
Please use /api/v1/analytics/mood/current instead.
"""

router = APIRouter(prefix="/mood-analytics", tags=["mood analytics - DEPRECATED"])

@router.get("/")
async def deprecated_notice():
    return {
        "status": "deprecated",
        "message": "This mood analytics router has been deprecated and consolidated into unified_analytics.py",
        "redirect_to": "/api/v1/analytics/mood/current",
        "migration_guide": {
            "mood_monitoring": "/api/v1/analytics/mood/current",
            "mood_analytics": "/api/v1/analytics/unified (with analytics_type=MOOD_ANALYTICS)",
            "system_mood": "/api/v1/analytics/health",
            "mood_trends": "/api/v1/analytics/unified (with analytics_type=MOOD_ANALYTICS)"
        },
        "timestamp": datetime.now()
    }

# Legacy endpoint redirects
@router.get("/current")
async def deprecated_current():
    return {
        "status": "deprecated",
        "message": "Current mood monitoring has been moved",
        "redirect_to": "/api/v1/analytics/mood/current",
        "timestamp": datetime.now()
    }

@router.get("/analytics")
async def deprecated_analytics():
    return {
        "status": "deprecated",
        "message": "Mood analytics have been moved",
        "redirect_to": "/api/v1/analytics/unified",
        "note": "Use analytics_type=MOOD_ANALYTICS",
        "timestamp": datetime.now()
    }

@router.get("/metrics")
async def deprecated_metrics():
    return {
        "status": "deprecated",
        "message": "Mood metrics have been moved",
        "redirect_to": "/api/v1/analytics/unified",
        "note": "Use analytics_type=MOOD_ANALYTICS",
        "timestamp": datetime.now()
    }

@router.get("/health")
async def deprecated_health():
    return {
        "status": "deprecated",
        "message": "Mood health monitoring has been moved",
        "redirect_to": "/api/v1/analytics/health",
        "timestamp": datetime.now()
    }