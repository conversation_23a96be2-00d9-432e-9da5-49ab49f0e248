#!/usr/bin/env python3
"""
Comprehensive Feature Engineering Pipeline

This script creates real basketball features from comprehensive WNBA player statistics
instead of synthetic data. It loads multiple years of player stats and engineers
features for all 6 player props categories: points, rebounds, assists, steals, blocks, threes.

SOLUTION TO SYNTHETIC DATA PROBLEM:
- Load comprehensive WNBA player stats from CSV files (2016-2025)
- Extract all required stats: PTS, REB, AST, STL, BLK, FG3M
- Create meaningful engineered features from real basketball data
- Generate training data for all 6 player props models
- Ensure feature consistency between training and prediction
"""

import pandas as pd
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import sys
import os

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComprehensiveFeatureEngineer:
    """
    Comprehensive feature engineering pipeline that creates real basketball features
    from actual player statistics instead of synthetic data.
    """
    
    def __init__(self, data_dir: str = "data"):
        self.data_dir = Path(data_dir)
        self.logger = logging.getLogger(__name__)
        
    def load_comprehensive_wnba_data(self) -> pd.DataFrame:
        """Load comprehensive WNBA player statistics from multiple years."""
        all_data = []
        
        # Load WNBA player stats from multiple years (comprehensive historical data)
        wnba_files = [
            "player_stats_10_2016.csv",
            "player_stats_10_2017.csv",
            "player_stats_10_2018.csv", 
            "player_stats_10_2019.csv",
            "player_stats_10_2020.csv",
            "player_stats_10_2021.csv", 
            "player_stats_10_2022.csv",
            "player_stats_10_2023.csv",
            "player_stats_10_2024_working.csv",
            "player_stats_10_2025.csv"
        ]
        
        for file_name in wnba_files:
            file_path = self.data_dir / file_name
            if file_path.exists():
                try:
                    year = file_name.split('_')[2].split('.')[0]
                    if year == "2024_working":
                        year = "2024"
                    
                    df = pd.read_csv(file_path)
                    df['season'] = int(year)
                    all_data.append(df)
                    self.logger.info(f"✅ Loaded {len(df)} records from {file_name}")
                except Exception as e:
                    self.logger.warning(f"❌ Failed to load {file_name}: {e}")
            else:
                self.logger.warning(f"📁 File not found: {file_path}")
        
        if not all_data:
            raise ValueError("No WNBA player stats files found!")
            
        combined_df = pd.concat(all_data, ignore_index=True)
        self.logger.info(f"🎯 Total combined records: {len(combined_df)}")
        return combined_df
    
    def engineer_features_from_real_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Engineer comprehensive features from real WNBA player statistics.
        Creates features for all 6 player props categories: points, rebounds, assists, steals, blocks, threes.
        """
        self.logger.info("🔧 Engineering features from real WNBA player statistics...")
        
        # Ensure required columns exist
        required_cols = ['PLAYER_NAME', 'TEAM_ABBREVIATION', 'season', 'PTS', 'REB', 'AST', 'STL', 'BLK', 'FG3M']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns: {missing_cols}")
        
        # Create base features for each player per season
        features_df = df.groupby(['PLAYER_NAME', 'TEAM_ABBREVIATION', 'season']).agg({
            'PTS': 'mean',      # Points per game
            'REB': 'mean',      # Rebounds per game  
            'AST': 'mean',      # Assists per game
            'STL': 'mean',      # Steals per game
            'BLK': 'mean',      # Blocks per game
            'FG3M': 'mean',     # Three-pointers made per game
            'GP': 'first',      # Games played
            'MIN': 'mean',      # Minutes per game
            'FG_PCT': 'mean',   # Field goal percentage
            'FT_PCT': 'mean',   # Free throw percentage
            'AGE': 'first'      # Player age
        }).reset_index()
        
        # Rename columns to match expected format
        features_df = features_df.rename(columns={
            'PLAYER_NAME': 'player_name',
            'TEAM_ABBREVIATION': 'team_abbreviation',
            'PTS': 'points',
            'REB': 'rebounds', 
            'AST': 'assists',
            'STL': 'steals',
            'BLK': 'blocks',
            'FG3M': 'threes',
            'GP': 'games_played',
            'MIN': 'minutes_per_game',
            'FG_PCT': 'field_goal_percentage',
            'FT_PCT': 'free_throw_percentage',
            'AGE': 'age'
        })
        
        # Create engineered features
        features_df = self._create_engineered_features(features_df)
        
        self.logger.info(f"✅ Engineered features for {len(features_df)} player-season records")
        self.logger.info(f"📊 Feature columns: {list(features_df.columns)}")
        
        return features_df
    
    def _create_engineered_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create additional engineered features from base stats."""
        
        # Performance tier features (categorical -> numeric)
        df['scoring_tier'] = pd.cut(df['points'], bins=3, labels=[0, 1, 2])
        df['rebounding_tier'] = pd.cut(df['rebounds'], bins=3, labels=[0, 1, 2])
        df['playmaking_tier'] = pd.cut(df['assists'], bins=3, labels=[0, 1, 2])
        df['defensive_tier'] = pd.cut(df['steals'] + df['blocks'], bins=3, labels=[0, 1, 2])
        
        # Binary performance indicators (top 75th percentile)
        df['high_scorer'] = (df['points'] > df['points'].quantile(0.75)).astype(int)
        df['high_rebounder'] = (df['rebounds'] > df['rebounds'].quantile(0.75)).astype(int)
        df['high_assists'] = (df['assists'] > df['assists'].quantile(0.75)).astype(int)
        df['high_steals'] = (df['steals'] > df['steals'].quantile(0.75)).astype(int)
        df['high_blocks'] = (df['blocks'] > df['blocks'].quantile(0.75)).astype(int)
        df['high_threes'] = (df['threes'] > df['threes'].quantile(0.75)).astype(int)
        
        # Efficiency metrics (per minute stats)
        df['points_per_minute'] = df['points'] / df['minutes_per_game'].replace(0, 1)
        df['rebounds_per_minute'] = df['rebounds'] / df['minutes_per_game'].replace(0, 1)
        df['assists_per_minute'] = df['assists'] / df['minutes_per_game'].replace(0, 1)
        df['steals_per_minute'] = df['steals'] / df['minutes_per_game'].replace(0, 1)
        df['blocks_per_minute'] = df['blocks'] / df['minutes_per_game'].replace(0, 1)
        df['threes_per_minute'] = df['threes'] / df['minutes_per_game'].replace(0, 1)
        
        # Composite features
        df['total_stats'] = df['points'] + df['rebounds'] + df['assists']
        df['defensive_stats'] = df['steals'] + df['blocks']
        df['offensive_stats'] = df['points'] + df['assists'] + df['threes']
        
        # Convert categorical features to numeric
        df['scoring_tier'] = df['scoring_tier'].astype(float)
        df['rebounding_tier'] = df['rebounding_tier'].astype(float)
        df['playmaking_tier'] = df['playmaking_tier'].astype(float)
        df['defensive_tier'] = df['defensive_tier'].astype(float)
        
        # Fill any NaN values with 0
        df = df.fillna(0)
        
        return df
    
    def create_training_datasets(self, features_df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """
        Create separate training datasets for each player props category.
        Each dataset contains features + target variable for that specific prop.
        """
        datasets = {}
        
        # Define the 6 player props categories and their target columns
        player_props = {
            'points': 'points',
            'rebounds': 'rebounds', 
            'assists': 'assists',
            'steals': 'steals',
            'blocks': 'blocks',
            'threes': 'threes'
        }
        
        # Get feature columns (exclude metadata and target columns)
        feature_cols = [col for col in features_df.columns 
                       if col not in ['player_name', 'team_abbreviation', 'season']]
        
        for prop_name, target_col in player_props.items():
            # Create dataset for this prop
            prop_df = features_df.copy()
            
            # Add target variable (the stat we're predicting)
            prop_df['target'] = prop_df[target_col]
            
            # Select features + target
            dataset_cols = feature_cols + ['target']
            prop_dataset = prop_df[dataset_cols].copy()
            
            datasets[prop_name] = prop_dataset
            self.logger.info(f"📈 Created {prop_name} dataset: {len(prop_dataset)} records, {len(dataset_cols)} features")
        
        return datasets
    
    def save_training_data(self, datasets: Dict[str, pd.DataFrame], features_df: pd.DataFrame, output_dir: str = "data"):
        """Save the engineered training datasets to CSV files."""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        for prop_name, dataset in datasets.items():
            filename = f"real_wnba_{prop_name}_training_data.csv"
            filepath = output_path / filename
            dataset.to_csv(filepath, index=False)
            self.logger.info(f"💾 Saved {prop_name} training data: {filepath} ({len(dataset)} records)")

        # Save the complete engineered features WITHOUT target column
        complete_filepath = output_path / "complete_real_wnba_features.csv"
        complete_features = datasets['points'].drop('target', axis=1)  # Get features without target
        complete_features.to_csv(complete_filepath, index=False)
        self.logger.info(f"💾 Saved complete features: {complete_filepath} ({len(complete_features)} records)")

        # Save the complete features WITH metadata for game prediction
        metadata_filepath = output_path / "complete_real_wnba_features_with_metadata.csv"
        features_df.to_csv(metadata_filepath, index=False)
        self.logger.info(f"💾 Saved features with metadata: {metadata_filepath} ({len(features_df)} records)")

def main():
    """Main execution function."""
    logger.info("🚀 Starting Comprehensive Feature Engineering Pipeline")
    
    try:
        # Initialize feature engineer
        engineer = ComprehensiveFeatureEngineer()
        
        # Load comprehensive WNBA data
        logger.info("📊 Loading comprehensive WNBA player statistics...")
        raw_data = engineer.load_comprehensive_wnba_data()
        
        # Engineer features from real data
        logger.info("🔧 Engineering features from real basketball statistics...")
        features_df = engineer.engineer_features_from_real_data(raw_data)
        
        # Create training datasets for each player prop
        logger.info("📈 Creating training datasets for each player props category...")
        datasets = engineer.create_training_datasets(features_df)
        
        # Save training data
        logger.info("💾 Saving training datasets...")
        engineer.save_training_data(datasets, features_df)
        
        logger.info("✅ Comprehensive Feature Engineering Pipeline completed successfully!")
        logger.info(f"📊 Generated {len(datasets)} player props training datasets")
        logger.info(f"🎯 Total player-season records: {len(features_df)}")
        
        # Display summary
        print("\n" + "="*60)
        print("🎯 COMPREHENSIVE FEATURE ENGINEERING COMPLETE")
        print("="*60)
        print(f"📊 Total Records: {len(features_df)}")
        print(f"📈 Player Props Categories: {len(datasets)}")
        print(f"🔧 Features per Dataset: {len(features_df.columns)}")
        print("\n📁 Generated Files:")
        for prop_name in datasets.keys():
            print(f"   • real_wnba_{prop_name}_training_data.csv")
        print("   • complete_real_wnba_features.csv")
        print("\n✅ Ready for model retraining with REAL basketball data!")
        
    except Exception as e:
        logger.error(f"❌ Pipeline failed: {e}")
        raise

if __name__ == "__main__":
    main()
