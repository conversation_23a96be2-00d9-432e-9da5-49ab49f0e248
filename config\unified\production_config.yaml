api:
  cors_enabled: true
  host: ${API_HOST:0.0.0.0}
  port: ${API_PORT:8000}
  prefix: /api/v1
basketball:
  data_sources:
  - nba_api
  - wnba_api
  nba_enabled: true
  prediction_confidence_threshold: 0.65
  wnba_enabled: true
database:
  host: ${DB_HOST:localhost}
  name: ${DB_NAME:hyper_medusa}
  password: ${DB_PASSWORD:}
  pool_size: 10
  port: ${DB_PORT:5432}
  provider: postgresql
  timeout: 30
  user: ${DB_USER:medusa}
models:
  nba_model_path: ${MODELS_DIR:./models}/nba/ensemble_v1.pkl
  retraining_enabled: true
  wnba_model_path: ${MODELS_DIR:./models}/wnba/ensemble_v1.pkl
monitoring:
  grafana_enabled: true
  health_check_interval: 30
  prometheus_enabled: true
redis:
  enabled: true
  host: ${REDIS_HOST:localhost}
  password: ${REDIS_PASSWORD:}
  port: ${REDIS_PORT:6379}
security:
  jwt_algorithm: HS256
  jwt_expiration: 3600
  secret_key: ${SECRET_KEY:}
system:
  debug: false
  environment: production
  log_level: INFO
