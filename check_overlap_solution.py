#!/usr/bin/env python3
"""
Check how overlapping teams are handled
"""

import pandas as pd

def main():
    # Check how we handle overlapping teams
    nba_df = pd.read_csv('data/ml_training/nba_training_data.csv')
    wnba_df = pd.read_csv('data/ml_training/wnba_training_data.csv')

    print('🔍 OVERLAPPING TEAM ANALYSIS')
    print('=' * 40)

    # Check one overlapping team - ATL
    nba_atl = nba_df[nba_df['TEAM_ABBREVIATION'] == 'ATL']
    wnba_atl = wnba_df[wnba_df['TEAM_ABBREVIATION'] == 'ATL']

    print('📊 Atlanta (ATL) Example:')
    print(f'   NBA ATL records: {len(nba_atl):,}')
    print(f'   WNBA ATL records: {len(wnba_atl):,}')

    # Check league separation
    if 'LEAGUE' in nba_df.columns:
        print(f'   NBA ATL league: {nba_atl["LEAGUE"].unique()}')
    if 'LEAGUE' in wnba_df.columns:
        print(f'   WNBA ATL league: {wnba_atl["LEAGUE"].unique()}')

    print()
    print('✅ SOLUTION: Teams are properly separated by:')
    print('   1. Different datasets (nba_training_data.csv vs wnba_training_data.csv)')
    print('   2. LEAGUE column identifier (NBA vs WNBA)')
    print('   3. No data mixing between leagues')
    
    print()
    print('🏀 All 7 Overlapping Teams:')
    overlapping = ['ATL', 'CHI', 'DAL', 'IND', 'MIN', 'PHX', 'WAS']
    for team in overlapping:
        nba_count = len(nba_df[nba_df['TEAM_ABBREVIATION'] == team])
        wnba_count = len(wnba_df[wnba_df['TEAM_ABBREVIATION'] == team])
        print(f'   {team}: NBA={nba_count:,} records, WNBA={wnba_count:,} records')

if __name__ == "__main__":
    main()
