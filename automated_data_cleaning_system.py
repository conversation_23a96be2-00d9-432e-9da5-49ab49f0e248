import asyncio
import sqlite3
import pandas as pd
import numpy as np
import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

#!/usr/bin/env python3
"""
Automated Data Cleaning and Preprocessing System
===============================================

Advanced data cleaning system that automatically addresses quality issues
identified by the Enhanced Data Quality System.

Features:
- Automated data cleaning based on quality assessment results
- Basketball-specific data preprocessing
- Neural threat detection and mitigation
- Duplicate detection and removal
- Missing data imputation with domain knowledge
- Outlier detection and treatment
"""


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CleaningAction(Enum):
    """Types of cleaning actions"""
    REMOVE_DUPLICATES = "REMOVE_DUPLICATES"
    FILL_MISSING = "FILL_MISSING"
    CORRECT_OUTLIERS = "CORRECT_OUTLIERS"
    STANDARDIZE_FORMAT = "STANDARDIZE_FORMAT"
    VALIDATE_RANGES = "VALIDATE_RANGES"
    NORMALIZE_TEXT = "NORMALIZE_TEXT"

@dataclass
class CleaningResult:
    """Result of a cleaning operation"""
    table_name: str
    action: CleaningAction
    records_affected: int
    description: str
    before_quality: float
    after_quality: float
    timestamp: datetime

class AutomatedDataCleaningSystem:
    """
    Automated Data Cleaning System for HYPER MEDUSA NEURAL VAULT
    
    Intelligently cleans and preprocesses basketball data based on
    quality assessment results and domain-specific knowledge.
    """
    
    def __init__(self, db_path: str = "hyper_medusa_consolidated.db"):
        self.db_path = db_path
        self.cleaning_history = []
        
        # Basketball-specific cleaning rules
        self.cleaning_rules = {
            'teams': {
                'required_fields': ['team_id', 'team_name', 'abbreviation'],
                'text_standardization': {
                    'team_name': 'title_case',
                    'abbreviation': 'upper_case'
                },
                'default_values': {}
            },
            'players': {
                'required_fields': ['player_id', 'player_name', 'team_id'],
                'text_standardization': {
                    'player_name': 'title_case',
                    'position': 'upper_case'
                },
                'default_values': {
                    'height': 78,  # Average NBA height in inches
                    'weight': 220,  # Average NBA weight in pounds
                    'age': 26      # Average NBA age
                },
                'outlier_treatment': {
                    'height': (60, 90),
                    'weight': (150, 350),
                    'age': (18, 50)
                }
            },
            'games': {
                'required_fields': ['game_id', 'home_team_id', 'away_team_id', 'game_date'],
                'outlier_treatment': {
                    'home_score': (50, 200),
                    'away_score': (50, 200)
                },
                'default_values': {}
            },
            'player_game_stats': {
                'required_fields': ['player_id', 'game_id'],
                'outlier_treatment': {
                    'points': (0, 100),
                    'rebounds': (0, 30),
                    'assists': (0, 25),
                    'minutes': (0, 48),
                    'fg_pct': (0, 1),
                    'ft_pct': (0, 1),
                    'fg3_pct': (0, 1)
                },
                'default_values': {
                    'points': 0,
                    'rebounds': 0,
                    'assists': 0,
                    'minutes': 0
                }
            },
            'shot_analytics': {
                'required_fields': ['player_id', 'season', 'league'],
                'outlier_treatment': {
                    'fgm': (0, 1000),
                    'fga': (0, 2000),
                    'fg_pct': (0, 1)
                },
                'text_standardization': {
                    'league': 'upper_case'
                },
                'default_values': {}
            },
            'clutch_analytics': {
                'required_fields': ['player_id', 'season', 'league'],
                'outlier_treatment': {
                    'clutch_points': (0, 50),
                    'clutch_fg_pct': (0, 1)
                },
                'text_standardization': {
                    'league': 'upper_case'
                },
                'default_values': {}
            },
            'advanced_analytics': {
                'required_fields': ['player_id', 'season', 'league'],
                'outlier_treatment': {
                    'off_rating': (50, 150),
                    'def_rating': (50, 150),
                    'usage_pct': (0, 50),
                    'true_shooting_pct': (0, 1)
                },
                'text_standardization': {
                    'league': 'upper_case'
                },
                'default_values': {}
            },
            'defense_analytics': {
                'required_fields': ['player_id', 'season', 'league'],
                'outlier_treatment': {
                    'def_rating': (50, 150),
                    'steals': (0, 5),
                    'blocks': (0, 10)
                },
                'text_standardization': {
                    'league': 'upper_case'
                },
                'default_values': {}
            },
            'shooting_analytics': {
                'required_fields': ['player_id', 'season', 'league'],
                'outlier_treatment': {
                    'fg_pct': (0, 1),
                    'fg3_pct': (0, 1),
                    'efg_pct': (0, 1)
                },
                'text_standardization': {
                    'league': 'upper_case'
                },
                'default_values': {}
            }
        }
        
        logger.info("🧹 Automated Data Cleaning System initialized")
    
    async def run_comprehensive_cleaning(self) -> Dict[str, List[CleaningResult]]:
        """Run comprehensive data cleaning on all tables"""
        logger.info("🚀 Starting comprehensive data cleaning process")
        
        cleaning_results = {}
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get all table names (excluding system tables)
            tables = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name != 'sqlite_sequence'").fetchall()
            
            for table_tuple in tables:
                table_name = table_tuple[0]
                
                if table_name in self.cleaning_rules:
                    logger.info(f"🧹 Cleaning table: {table_name}")
                    
                    # Load table data
                    df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
                    original_quality = self._calculate_simple_quality_score(df)
                    
                    # Perform cleaning operations
                    table_results = await self._clean_table(table_name, df, conn)
                    cleaning_results[table_name] = table_results
                    
                    # Calculate final quality
                    cleaned_df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
                    final_quality = self._calculate_simple_quality_score(cleaned_df)
                    
                    logger.info(f"✅ {table_name}: Quality improved from {original_quality:.1%} to {final_quality:.1%}")
            
            conn.close()
            
            # Generate cleaning summary
            await self._generate_cleaning_summary(cleaning_results)
            
            logger.info("✅ Comprehensive data cleaning completed")
            return cleaning_results
            
        except Exception as e:
            logger.error(f"❌ Data cleaning failed: {e}")
            raise
    
    async def _clean_table(self, table_name: str, df: pd.DataFrame, conn: sqlite3.Connection) -> List[CleaningResult]:
        """Clean a specific table"""
        results = []
        rules = self.cleaning_rules[table_name]
        
        # 1. Remove exact duplicates
        duplicate_result = await self._remove_duplicates(table_name, df, conn)
        if duplicate_result:
            results.append(duplicate_result)
            # Reload data after duplicate removal
            df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
        
        # 2. Standardize text fields
        text_result = await self._standardize_text_fields(table_name, df, conn, rules)
        if text_result:
            results.append(text_result)
            # Reload data after text standardization
            df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
        
        # 3. Handle missing values
        missing_result = await self._handle_missing_values(table_name, df, conn, rules)
        if missing_result:
            results.append(missing_result)
            # Reload data after missing value handling
            df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
        
        # 4. Treat outliers
        outlier_result = await self._treat_outliers(table_name, df, conn, rules)
        if outlier_result:
            results.append(outlier_result)
        
        # 5. Validate data ranges
        validation_result = await self._validate_data_ranges(table_name, df, conn, rules)
        if validation_result:
            results.append(validation_result)
        
        return results
    
    async def _remove_duplicates(self, table_name: str, df: pd.DataFrame, conn: sqlite3.Connection) -> Optional[CleaningResult]:
        """Remove duplicate records"""
        original_count = len(df)
        
        # Remove exact duplicates
        df_cleaned = df.drop_duplicates()
        duplicates_removed = original_count - len(df_cleaned)
        
        if duplicates_removed > 0:
            # Update database
            df_cleaned.to_sql(table_name, conn, if_exists='replace', index=False)
            
            logger.info(f"   🗑️  Removed {duplicates_removed} duplicate records from {table_name}")
            
            return CleaningResult(
                table_name=table_name,
                action=CleaningAction.REMOVE_DUPLICATES,
                records_affected=duplicates_removed,
                description=f"Removed {duplicates_removed} exact duplicate records",
                before_quality=0.0,  # Will be calculated separately
                after_quality=0.0,   # Will be calculated separately
                timestamp=datetime.now()
            )
        
        return None
    
    async def _standardize_text_fields(self, table_name: str, df: pd.DataFrame, 
                                     conn: sqlite3.Connection, rules: Dict) -> Optional[CleaningResult]:
        """Standardize text field formatting"""
        text_rules = rules.get('text_standardization', {})
        if not text_rules:
            return None
        
        changes_made = 0
        df_cleaned = df.copy()
        
        for field, format_type in text_rules.items():
            if field in df_cleaned.columns:
                original_values = df_cleaned[field].copy()
                
                if format_type == 'title_case':
                    df_cleaned[field] = df_cleaned[field].astype(str).str.title()
                elif format_type == 'upper_case':
                    df_cleaned[field] = df_cleaned[field].astype(str).str.upper()
                elif format_type == 'lower_case':
                    df_cleaned[field] = df_cleaned[field].astype(str).str.lower()
                
                # Count changes
                changes_made += (original_values != df_cleaned[field]).sum()
        
        if changes_made > 0:
            # Update database
            df_cleaned.to_sql(table_name, conn, if_exists='replace', index=False)
            
            logger.info(f"   📝 Standardized {changes_made} text values in {table_name}")
            
            return CleaningResult(
                table_name=table_name,
                action=CleaningAction.STANDARDIZE_FORMAT,
                records_affected=changes_made,
                description=f"Standardized formatting for {changes_made} text values",
                before_quality=0.0,
                after_quality=0.0,
                timestamp=datetime.now()
            )
        
        return None

    async def _handle_missing_values(self, table_name: str, df: pd.DataFrame,
                                   conn: sqlite3.Connection, rules: Dict) -> Optional[CleaningResult]:
        """Handle missing values with basketball-specific intelligence"""
        default_values = rules.get('default_values', {})
        if not default_values:
            return None

        changes_made = 0
        df_cleaned = df.copy()

        for field, default_value in default_values.items():
            if field in df_cleaned.columns:
                missing_count = df_cleaned[field].isnull().sum()
                if missing_count > 0:
                    df_cleaned[field] = df_cleaned[field].fillna(default_value)
                    changes_made += missing_count

        if changes_made > 0:
            # Update database
            df_cleaned.to_sql(table_name, conn, if_exists='replace', index=False)

            logger.info(f"   🔧 Filled {changes_made} missing values in {table_name}")

            return CleaningResult(
                table_name=table_name,
                action=CleaningAction.FILL_MISSING,
                records_affected=changes_made,
                description=f"Filled {changes_made} missing values with domain-specific defaults",
                before_quality=0.0,
                after_quality=0.0,
                timestamp=datetime.now()
            )

        return None

    async def _treat_outliers(self, table_name: str, df: pd.DataFrame,
                            conn: sqlite3.Connection, rules: Dict) -> Optional[CleaningResult]:
        """Treat outliers based on basketball domain knowledge"""
        outlier_rules = rules.get('outlier_treatment', {})
        if not outlier_rules:
            return None

        changes_made = 0
        df_cleaned = df.copy()

        for field, (min_val, max_val) in outlier_rules.items():
            if field in df_cleaned.columns:
                # Convert to numeric
                numeric_series = pd.to_numeric(df_cleaned[field], errors='coerce')

                # Count outliers
                outliers = ((numeric_series < min_val) | (numeric_series > max_val)).sum()

                if outliers > 0:
                    # Cap outliers to valid range
                    df_cleaned[field] = numeric_series.clip(lower=min_val, upper=max_val)
                    changes_made += outliers

        if changes_made > 0:
            # Update database
            df_cleaned.to_sql(table_name, conn, if_exists='replace', index=False)

            logger.info(f"   ⚡ Treated {changes_made} outliers in {table_name}")

            return CleaningResult(
                table_name=table_name,
                action=CleaningAction.CORRECT_OUTLIERS,
                records_affected=changes_made,
                description=f"Corrected {changes_made} outlier values to valid basketball ranges",
                before_quality=0.0,
                after_quality=0.0,
                timestamp=datetime.now()
            )

        return None

    async def _validate_data_ranges(self, table_name: str, df: pd.DataFrame,
                                  conn: sqlite3.Connection, rules: Dict) -> Optional[CleaningResult]:
        """Validate and correct data ranges"""
        changes_made = 0
        df_cleaned = df.copy()

        # Example: Ensure shooting percentages are between 0 and 1
        percentage_fields = [col for col in df_cleaned.columns if 'pct' in col.lower() or 'percentage' in col.lower()]

        for field in percentage_fields:
            if field in df_cleaned.columns:
                numeric_series = pd.to_numeric(df_cleaned[field], errors='coerce')

                # Check for percentages > 1 (might be in 0-100 format instead of 0-1)
                over_one = (numeric_series > 1) & (numeric_series <= 100)
                if over_one.sum() > 0:
                    df_cleaned.loc[over_one, field] = numeric_series[over_one] / 100
                    changes_made += over_one.sum()

                # Ensure all percentages are between 0 and 1
                invalid_pct = (numeric_series < 0) | (numeric_series > 1)
                if invalid_pct.sum() > 0:
                    df_cleaned.loc[invalid_pct, field] = np.nan  # Mark as missing for further handling
                    changes_made += invalid_pct.sum()

        if changes_made > 0:
            # Update database
            df_cleaned.to_sql(table_name, conn, if_exists='replace', index=False)

            logger.info(f"   ✅ Validated {changes_made} data range issues in {table_name}")

            return CleaningResult(
                table_name=table_name,
                action=CleaningAction.VALIDATE_RANGES,
                records_affected=changes_made,
                description=f"Validated and corrected {changes_made} data range issues",
                before_quality=0.0,
                after_quality=0.0,
                timestamp=datetime.now()
            )

        return None

    def _calculate_simple_quality_score(self, df: pd.DataFrame) -> float:
        """Calculate a simple quality score for before/after comparison"""
        if df.empty:
            return 0.0

        # Simple quality metrics
        total_cells = df.shape[0] * df.shape[1]
        missing_cells = df.isnull().sum().sum()
        completeness = 1.0 - (missing_cells / total_cells) if total_cells > 0 else 0.0

        # Uniqueness (no duplicates)
        uniqueness = len(df.drop_duplicates()) / len(df) if len(df) > 0 else 0.0

        # Simple average
        quality_score = (completeness + uniqueness) / 2

        return max(0.0, min(1.0, quality_score))

    async def _generate_cleaning_summary(self, cleaning_results: Dict[str, List[CleaningResult]]) -> None:
        """Generate comprehensive cleaning summary"""
        logger.info("📋 AUTOMATED DATA CLEANING SUMMARY")
        logger.info("=" * 50)

        total_actions = sum(len(results) for results in cleaning_results.values())
        total_records_affected = sum(
            sum(result.records_affected for result in results)
            for results in cleaning_results.values()
        )

        logger.info(f"🔧 Total Cleaning Actions: {total_actions}")
        logger.info(f"📊 Total Records Affected: {total_records_affected:,}")
        logger.info("")

        # Table-by-table summary
        for table_name, results in cleaning_results.items():
            if results:
                logger.info(f"📈 {table_name.upper()}:")
                for result in results:
                    logger.info(f"   {result.action.value}: {result.records_affected:,} records - {result.description}")
                logger.info("")

        # Action type summary
        action_summary = {}
        for results in cleaning_results.values():
            for result in results:
                action_type = result.action.value
                if action_type not in action_summary:
                    action_summary[action_type] = 0
                action_summary[action_type] += result.records_affected

        logger.info("📊 CLEANING ACTIONS SUMMARY:")
        for action_type, count in action_summary.items():
            logger.info(f"   {action_type}: {count:,} records")

        logger.info("")
        logger.info("✅ Data cleaning completed successfully!")

async def main():
    """Main execution function"""
    cleaning_system = AutomatedDataCleaningSystem()

    try:
        # Run comprehensive data cleaning
        cleaning_results = await cleaning_system.run_comprehensive_cleaning()

        # Save results to JSON for analysis
        results_summary = {
            'timestamp': datetime.now().isoformat(),
            'total_tables_cleaned': len(cleaning_results),
            'total_actions': sum(len(results) for results in cleaning_results.values()),
            'total_records_affected': sum(
                sum(result.records_affected for result in results)
                for results in cleaning_results.values()
            ),
            'cleaning_details': {
                table_name: [
                    {
                        'action': result.action.value,
                        'records_affected': int(result.records_affected),
                        'description': result.description,
                        'timestamp': result.timestamp.isoformat()
                    }
                    for result in results
                ]
                for table_name, results in cleaning_results.items()
            }
        }

        with open('data_cleaning_results.json', 'w') as f:
            json.dump(results_summary, f, indent=2)

        logger.info("💾 Cleaning results saved to data_cleaning_results.json")

    except Exception as e:
        logger.error(f"❌ Data cleaning failed: {e}")

if __name__ == "__main__":
    asyncio.run(main())
