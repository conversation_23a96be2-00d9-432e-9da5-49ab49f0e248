{"timestamp": 1751385667, "config": {"search_strategy": "hybrid", "population_size": 10, "generations": 20, "accuracy_target": 0.75, "quantum_enhanced": true}, "best_architecture": {"architecture_id": "complex_2_527382", "layers": [{"type": "basketball_specific", "input_dim": 256, "output_dim": 128, "activation": "relu", "dropout": 0.2, "normalization": "none"}, {"type": "lstm", "input_dim": 128, "output_dim": 128, "activation": "relu", "dropout": 0.2, "normalization": "none"}, {"type": "attention", "input_dim": 128, "output_dim": 128, "activation": "relu", "dropout": 0.2, "normalization": "none"}, {"type": "attention", "input_dim": 128, "output_dim": 128, "activation": "relu", "dropout": 0.2, "normalization": "none"}, {"type": "linear", "input_dim": 128, "output_dim": 128, "activation": "relu", "dropout": 0.2, "normalization": "none"}, {"type": "linear", "input_dim": 128, "output_dim": 1, "activation": "relu", "dropout": 0.2, "normalization": "none"}], "performance_metrics": {"accuracy": 0.5178530288008633, "loss": 0.9310498237609863, "efficiency": 0.7146924305206401, "complexity": 0.360577, "training_time": 0.3631455898284912, "basketball_relevance": 0.9, "total_params": 360577}, "complexity_score": 0.360577, "training_time": 0.3631455898284912, "memory_usage": 0.0, "basketball_relevance": 0.9, "quantum_enhancement": false}, "search_history": []}