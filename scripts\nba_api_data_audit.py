import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any, Set
import requests
import json
from datetime import datetime


#!/usr/bin/env python3
"""
NBA API Data Audit - What We Have vs What We Can Get
Comprehensive analysis of available NBA API endpoints and our current data coverage
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NBAAPIDataAuditor:
    """Audit NBA API data availability vs current collection"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }
        
        # Comprehensive NBA API endpoints
        self.nba_api_endpoints = {
            # Player Data
            'player_endpoints': {
                'commonallplayers': 'All players list',
                'playercareerstats': 'Player career statistics',
                'playergamelog': 'Player game logs',
                'playerdashboardbygeneralsplits': 'Player dashboard general splits',
                'playerdashboardbyshootingsplits': 'Player shooting splits',
                'playerdashboardbyclutch': 'Player clutch stats',
                'playerdashboardbyplayertype': 'Player type stats',
                'playerdashboardbyyearoveryear': 'Player year over year',
                'playerprofilev2': 'Player profile',
                'playerawards': 'Player awards',
                'playerfantasyprofile': 'Player fantasy profile',
                'playerestimatedmetrics': 'Player estimated metrics',
                'playernextngames': 'Player next N games',
                'playercompare': 'Player comparison',
                'playerdashptpass': 'Player passing tracking',
                'playerdashptreb': 'Player rebounding tracking',
                'playerdashptshotdefend': 'Player shot defense tracking',
                'playerdashptshots': 'Player shot tracking',
                'playerdashboardbyteamperformance': 'Player team performance',
                'playerdashboardbygamesplits': 'Player game splits',
                'playerdashboardbylastngames': 'Player last N games',
                'playerdashboardbyopponent': 'Player vs opponent',
            },
            
            # Team Data
            'team_endpoints': {
                'commonteamroster': 'Team roster',
                'teamdashboardbygeneralsplits': 'Team general splits',
                'teamdashboardbyshootingsplits': 'Team shooting splits',
                'teamdashboardbyclutch': 'Team clutch stats',
                'teamdashboardbyplayertype': 'Team player type stats',
                'teamdashboardbyyearoveryear': 'Team year over year',
                'teamgamelog': 'Team game logs',
                'teamplayerdashboard': 'Team player dashboard',
                'teamplayeronoffdetails': 'Team player on/off details',
                'teamplayeronoffsummary': 'Team player on/off summary',
                'teamvsplayer': 'Team vs player',
                'teamestimatedmetrics': 'Team estimated metrics',
                'teamdashptpass': 'Team passing tracking',
                'teamdashptreb': 'Team rebounding tracking',
                'teamdashptshotdefend': 'Team shot defense tracking',
                'teamdashptshots': 'Team shot tracking',
                'teamdashlineups': 'Team lineups',
                'teamdashboardbyteamperformance': 'Team performance',
                'teamdashboardbygamesplits': 'Team game splits',
                'teamdashboardbylastngames': 'Team last N games',
                'teamdashboardbyopponent': 'Team vs opponent',
            },
            
            # Game Data
            'game_endpoints': {
                'boxscoretraditionalv2': 'Game box score traditional',
                'boxscoreadvancedv2': 'Game box score advanced',
                'boxscoremiscv2': 'Game box score misc',
                'boxscorescoringv2': 'Game box score scoring',
                'boxscoreusagev2': 'Game box score usage',
                'boxscorefourfactorsv2': 'Game box score four factors',
                'boxscoreplayertrackv2': 'Game box score player tracking',
                'boxscorehustlev2': 'Game box score hustle',
                'boxscoredefensev2': 'Game box score defense',
                'boxscoresummaryv2': 'Game box score summary',
                'playbyplayv2': 'Game play by play',
                'shotchartdetail': 'Shot chart detail',
                'winprobabilitypbp': 'Win probability play by play',
                'hustlestatsboxscore': 'Hustle stats box score',
            },
            
            # League Data
            'league_endpoints': {
                'leaguegamefinder': 'League game finder',
                'leaguedashplayerstats': 'League player stats',
                'leaguedashteamstats': 'League team stats',
                'leaguedashplayerbiostats': 'League player bio stats',
                'leaguedashplayerclutch': 'League player clutch',
                'leaguedashplayershotlocations': 'League player shot locations',
                'leaguedashteamclutch': 'League team clutch',
                'leaguedashteamshotlocations': 'League team shot locations',
                'leaguedashlineups': 'League lineups',
                'leaguedashoppptshot': 'League opponent shot dashboard',
                'leaguedashptdefend': 'League defense tracking',
                'leaguedashptstats': 'League player tracking stats',
                'leaguedashptteamdefend': 'League team defense tracking',
                'leaguehustlestatsplayer': 'League hustle stats player',
                'leaguehustlestatsteam': 'League hustle stats team',
                'leagueseasonmatchups': 'League season matchups',
                'leagueleaders': 'League leaders',
                'leaguestandings': 'League standings',
                'leaguestandingsv3': 'League standings v3',
            },
            
            # Advanced Analytics
            'advanced_endpoints': {
                'synergyplaytypes': 'Synergy play types',
                'trackingshots': 'Shot tracking data',
                'defensehub': 'Defense hub data',
                'speedandistance': 'Speed and distance tracking',
                'catchandshoot': 'Catch and shoot stats',
                'pullupshot': 'Pull up shot stats',
                'reboundtracking': 'Rebound tracking',
                'passtracking': 'Pass tracking',
                'touches': 'Player touches data',
                'possessions': 'Possession data',
                'efficiency': 'Efficiency metrics',
                'hustle': 'Hustle stats',
                'defense': 'Defensive metrics',
                'shooting': 'Shooting analytics',
                'playtype': 'Play type analytics',
            }
        }
        
    def analyze_current_data_coverage(self) -> Dict[str, Any]:
        """Analyze what data we currently have"""
        logger.info("📊 ANALYZING CURRENT DATA COVERAGE")
        logger.info("=" * 40)
        
        conn = sqlite3.connect(self.db_path)
        
        # Current data categories
        categories_query = """
        SELECT 
            league_name,
            data_category,
            data_type,
            source_table,
            COUNT(*) as records,
            COUNT(DISTINCT player_name) as unique_players,
            COUNT(DISTINCT season) as seasons,
            MIN(season) as first_season,
            MAX(season) as last_season
        FROM unified_nba_wnba_data
        WHERE league_name IN ('NBA', 'WNBA')
        GROUP BY league_name, data_category, data_type, source_table
        ORDER BY league_name, records DESC
        """
        
        current_coverage = pd.read_sql_query(categories_query, conn)
        
        # Source files analysis
        sources_query = """
        SELECT 
            league_name,
            source_file,
            COUNT(*) as records,
            COUNT(DISTINCT player_name) as players
        FROM unified_nba_wnba_data
        WHERE league_name IN ('NBA', 'WNBA')
        GROUP BY league_name, source_file
        ORDER BY league_name, records DESC
        LIMIT 50
        """
        
        source_coverage = pd.read_sql_query(sources_query, conn)
        
        # Data type distribution
        types_query = """
        SELECT 
            league_name,
            data_type,
            COUNT(*) as records,
            COUNT(DISTINCT player_name) as players,
            COUNT(DISTINCT season) as seasons
        FROM unified_nba_wnba_data
        WHERE league_name IN ('NBA', 'WNBA')
        AND data_type IS NOT NULL
        GROUP BY league_name, data_type
        ORDER BY league_name, records DESC
        """
        
        type_distribution = pd.read_sql_query(types_query, conn)
        
        conn.close()
        
        # Log current coverage
        logger.info("📊 CURRENT DATA CATEGORIES:")
        for _, row in current_coverage.head(20).iterrows():
            logger.info(f"   {row['league_name']} | {row['data_category']} | {row['data_type']}: {row['records']:,} records, {row['unique_players']} players ({row['first_season']}-{row['last_season']})")
        
        logger.info("\n📊 DATA TYPE DISTRIBUTION:")
        for _, row in type_distribution.iterrows():
            logger.info(f"   {row['league_name']} | {row['data_type']}: {row['records']:,} records, {row['players']} players, {row['seasons']} seasons")
        
        return {
            'current_coverage': current_coverage.to_dict('records'),
            'source_coverage': source_coverage.to_dict('records'),
            'type_distribution': type_distribution.to_dict('records')
        }
    
    def identify_missing_endpoints(self) -> Dict[str, Any]:
        """Identify NBA API endpoints we haven't collected from"""
        logger.info("\n🔍 IDENTIFYING MISSING NBA API ENDPOINTS")
        logger.info("=" * 45)
        
        conn = sqlite3.connect(self.db_path)
        
        # Get unique source files to identify collected endpoints
        collected_query = """
        SELECT DISTINCT source_file
        FROM unified_nba_wnba_data
        WHERE league_name IN ('NBA', 'WNBA')
        AND source_file IS NOT NULL
        """
        
        collected_files = pd.read_sql_query(collected_query, conn)
        collected_endpoints = set()
        
        # Extract endpoint names from source files
        for _, row in collected_files.iterrows():
            source_file = row['source_file'].lower()
            for category, endpoints in self.nba_api_endpoints.items():
                for endpoint in endpoints.keys():
                    if endpoint.lower() in source_file:
                        collected_endpoints.add(endpoint)
        
        conn.close()
        
        # Identify missing endpoints
        missing_endpoints = {}
        total_endpoints = 0
        collected_count = 0
        
        for category, endpoints in self.nba_api_endpoints.items():
            missing_in_category = []
            for endpoint, description in endpoints.items():
                total_endpoints += 1
                if endpoint in collected_endpoints:
                    collected_count += 1
                else:
                    missing_in_category.append({
                        'endpoint': endpoint,
                        'description': description,
                        'priority': self._get_endpoint_priority(endpoint)
                    })
            
            if missing_in_category:
                missing_endpoints[category] = missing_in_category
        
        coverage_percentage = (collected_count / total_endpoints) * 100
        
        logger.info(f"📊 ENDPOINT COVERAGE ANALYSIS:")
        logger.info(f"   Total NBA API endpoints: {total_endpoints}")
        logger.info(f"   Collected endpoints: {collected_count}")
        logger.info(f"   Missing endpoints: {total_endpoints - collected_count}")
        logger.info(f"   Coverage percentage: {coverage_percentage:.1f}%")
        
        logger.info(f"\n🎯 HIGH PRIORITY MISSING ENDPOINTS:")
        high_priority_count = 0
        for category, endpoints in missing_endpoints.items():
            high_priority = [ep for ep in endpoints if ep['priority'] == 'HIGH']
            if high_priority:
                logger.info(f"   {category.replace('_', ' ').title()}:")
                for ep in high_priority[:5]:  # Show top 5
                    logger.info(f"     • {ep['endpoint']}: {ep['description']}")
                    high_priority_count += 1
        
        return {
            'missing_endpoints': missing_endpoints,
            'collected_endpoints': list(collected_endpoints),
            'total_endpoints': total_endpoints,
            'collected_count': collected_count,
            'coverage_percentage': coverage_percentage,
            'high_priority_count': high_priority_count
        }
    
    def _get_endpoint_priority(self, endpoint: str) -> str:
        """Determine priority level for missing endpoints"""
        # High priority endpoints for player props
        high_priority = [
            'playerdashboardbyclutch', 'playerdashboardbyshootingsplits',
            'playerdashptshots', 'playerdashptpass', 'playerdashptreb',
            'shotchartdetail', 'boxscoreadvancedv2', 'boxscoreplayertrackv2',
            'leaguedashplayerclutch', 'leaguedashplayershotlocations',
            'trackingshots', 'catchandshoot', 'pullupshot',
            'hustle', 'defense', 'playtype'
        ]
        
        # Medium priority
        medium_priority = [
            'playerdashboardbyteamperformance', 'playerdashboardbygamesplits',
            'teamdashboardbyclutch', 'teamdashptshots', 'boxscorehustlev2',
            'leaguehustlestatsplayer', 'synergyplaytypes'
        ]
        
        if endpoint in high_priority:
            return 'HIGH'
        elif endpoint in medium_priority:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def create_collection_strategy(self) -> Dict[str, Any]:
        """Create comprehensive data collection strategy"""
        logger.info("\n🚀 CREATING COMPREHENSIVE COLLECTION STRATEGY")
        logger.info("=" * 55)
        
        # Get current analysis
        current_data = self.analyze_current_data_coverage()
        missing_analysis = self.identify_missing_endpoints()
        
        # Create collection phases
        collection_phases = {
            'Phase 1 - Critical Player Props Data': {
                'priority': 'IMMEDIATE',
                'endpoints': [
                    'playerdashboardbyclutch',
                    'playerdashboardbyshootingsplits', 
                    'playerdashptshots',
                    'shotchartdetail',
                    'boxscoreadvancedv2',
                    'leaguedashplayerclutch'
                ],
                'description': 'Essential for player props accuracy improvement',
                'estimated_records': 500000,
                'leagues': ['NBA', 'WNBA']
            },
            
            'Phase 2 - Advanced Tracking Data': {
                'priority': 'HIGH',
                'endpoints': [
                    'playerdashptpass',
                    'playerdashptreb', 
                    'boxscoreplayertrackv2',
                    'trackingshots',
                    'catchandshoot',
                    'pullupshot'
                ],
                'description': 'Advanced analytics for enhanced predictions',
                'estimated_records': 300000,
                'leagues': ['NBA', 'WNBA']
            },
            
            'Phase 3 - Situational Analytics': {
                'priority': 'MEDIUM',
                'endpoints': [
                    'playerdashboardbyteamperformance',
                    'playerdashboardbygamesplits',
                    'playerdashboardbylastngames',
                    'playerdashboardbyopponent',
                    'leaguedashplayershotlocations'
                ],
                'description': 'Situational and contextual data',
                'estimated_records': 200000,
                'leagues': ['NBA', 'WNBA']
            },
            
            'Phase 4 - Team and Game Context': {
                'priority': 'MEDIUM',
                'endpoints': [
                    'teamdashboardbyclutch',
                    'teamdashptshots',
                    'boxscorehustlev2',
                    'winprobabilitypbp',
                    'playbyplayv2'
                ],
                'description': 'Team context and game situation data',
                'estimated_records': 150000,
                'leagues': ['NBA', 'WNBA']
            },
            
            'Phase 5 - Comprehensive Analytics': {
                'priority': 'LOW',
                'endpoints': [
                    'synergyplaytypes',
                    'leaguehustlestatsplayer',
                    'defensehub',
                    'speedandistance',
                    'efficiency'
                ],
                'description': 'Complete analytics coverage',
                'estimated_records': 100000,
                'leagues': ['NBA', 'WNBA']
            }
        }
        
        # Calculate total potential data
        total_estimated_records = sum(phase['estimated_records'] for phase in collection_phases.values())
        
        logger.info("📋 COLLECTION STRATEGY PHASES:")
        for phase_name, phase_info in collection_phases.items():
            logger.info(f"\n   {phase_name} ({phase_info['priority']} Priority):")
            logger.info(f"     Description: {phase_info['description']}")
            logger.info(f"     Endpoints: {len(phase_info['endpoints'])}")
            logger.info(f"     Estimated records: {phase_info['estimated_records']:,}")
            logger.info(f"     Leagues: {', '.join(phase_info['leagues'])}")
        
        logger.info(f"\n🎯 COLLECTION IMPACT PROJECTION:")
        logger.info(f"   Total new endpoints: {sum(len(phase['endpoints']) for phase in collection_phases.values())}")
        logger.info(f"   Total estimated new records: {total_estimated_records:,}")
        logger.info(f"   Current records: ~3.9M")
        logger.info(f"   Projected total: ~{(3900000 + total_estimated_records) / 1000000:.1f}M records")
        
        return {
            'collection_phases': collection_phases,
            'total_estimated_records': total_estimated_records,
            'current_analysis': current_data,
            'missing_analysis': missing_analysis
        }

def main():
    """Run comprehensive NBA API data audit"""
    
    auditor = NBAAPIDataAuditor()
    
    # Run comprehensive audit
    strategy = auditor.create_collection_strategy()
    
    
    missing = strategy['missing_analysis']
    
    
    if missing['coverage_percentage'] < 50:
    elif missing['coverage_percentage'] < 75:
    else:
    
    
    return strategy

if __name__ == "__main__":
    main()
