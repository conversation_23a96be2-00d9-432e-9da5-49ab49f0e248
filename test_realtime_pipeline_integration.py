#!/usr/bin/env python3
"""
Real-Time Pipeline Integration Test Suite - HYPER MEDUSA NEURAL VAULT
=====================================================================

Comprehensive test suite for validating the real-time data pipeline integration
and orchestrator functionality.

Test Categories:
- Component initialization and startup
- Data flow and processing
- Error handling and recovery
- Performance and scalability
- Integration with existing infrastructure
- WebSocket communication
- Database updates
- Caching mechanisms
"""

import os
import sys
import asyncio
import logging
import json
import time
import unittest
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import sqlite3

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import components to test
try:
    from enhanced_realtime_pipeline_integration import (
        EnhancedRealTimePipelineIntegration,
        create_enhanced_pipeline_integration,
        DataStreamType, DataPriority, RealTimeDataEvent
    )
    from realtime_pipeline_orchestrator import (
        RealTimePipelineOrchestrator,
        create_orchestrator,
        OrchestratorMode, PipelineStatus
    )
except ImportError as e:
    logging.warning(f"Import failed: {e}")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("REALTIME_PIPELINE_TEST")

class RealTimePipelineTestSuite:
    """
    Comprehensive test suite for real-time pipeline integration
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.test_database = "test_realtime_pipeline.db"
        self.test_results: Dict[str, Any] = {}
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all test categories"""
        self.logger.info("🧪 Starting Real-Time Pipeline Integration Test Suite...")
        
        start_time = time.time()
        
        test_categories = [
            ("Component Initialization", self._test_component_initialization),
            ("Pipeline Integration", self._test_pipeline_integration),
            ("Data Processing", self._test_data_processing),
            ("Error Handling", self._test_error_handling),
            ("Performance", self._test_performance),
            ("Database Integration", self._test_database_integration),
            ("Orchestrator", self._test_orchestrator)
        ]
        
        for category_name, test_method in test_categories:
            self.logger.info(f"🔬 Running {category_name} tests...")
            
            try:
                result = await test_method()
                self.test_results[category_name] = {
                    "status": "PASSED" if result.get("success", False) else "FAILED",
                    "details": result,
                    "timestamp": datetime.now().isoformat()
                }
                
                if result.get("success", False):
                    self.logger.info(f"✅ {category_name} tests PASSED")
                else:
                    self.logger.error(f"❌ {category_name} tests FAILED: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                self.logger.error(f"❌ {category_name} tests FAILED with exception: {e}")
                self.test_results[category_name] = {
                    "status": "ERROR",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
        
        total_time = time.time() - start_time
        
        # Generate summary
        passed_tests = sum(1 for result in self.test_results.values() if result["status"] == "PASSED")
        total_tests = len(self.test_results)
        
        summary = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": (passed_tests / total_tests) * 100 if total_tests > 0 else 0,
            "total_time_seconds": round(total_time, 2),
            "test_results": self.test_results,
            "overall_status": "PASSED" if passed_tests == total_tests else "FAILED"
        }
        
        self.logger.info(f"🏁 Test Suite Complete: {passed_tests}/{total_tests} tests passed ({summary['success_rate']:.1f}%)")
        
        return summary
    
    async def _test_component_initialization(self) -> Dict[str, Any]:
        """Test component initialization"""
        try:
            self.logger.info("🔧 Testing component initialization...")
            
            # Test Enhanced Pipeline Integration initialization
            pipeline = EnhancedRealTimePipelineIntegration()
            await pipeline.initialize()
            
            # Verify initialization
            assert pipeline.event_queue is not None, "Event queue not initialized"
            assert pipeline.metrics is not None, "Metrics not initialized"
            assert pipeline.thread_pool is not None, "Thread pool not initialized"
            
            # Test graceful shutdown
            await pipeline.stop_pipeline()
            
            return {
                "success": True,
                "message": "Component initialization successful",
                "components_tested": ["EnhancedRealTimePipelineIntegration"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Component initialization failed"
            }
    
    async def _test_pipeline_integration(self) -> Dict[str, Any]:
        """Test pipeline integration functionality"""
        try:
            self.logger.info("🔗 Testing pipeline integration...")
            
            pipeline = await create_enhanced_pipeline_integration()
            
            # Start pipeline
            await pipeline.start_pipeline()
            
            # Verify pipeline is running
            assert pipeline.is_running, "Pipeline not running after start"
            
            # Test metrics collection
            metrics = pipeline.get_metrics()
            assert "events_processed" in metrics, "Metrics missing events_processed"
            assert "is_running" in metrics, "Metrics missing is_running"
            assert metrics["is_running"], "Metrics show pipeline not running"
            
            # Stop pipeline
            await pipeline.stop_pipeline()
            assert not pipeline.is_running, "Pipeline still running after stop"
            
            return {
                "success": True,
                "message": "Pipeline integration successful",
                "metrics": metrics
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Pipeline integration failed"
            }
    
    async def _test_data_processing(self) -> Dict[str, Any]:
        """Test data processing functionality"""
        try:
            self.logger.info("📊 Testing data processing...")
            
            pipeline = await create_enhanced_pipeline_integration()
            await pipeline.start_pipeline()
            
            # Create test event
            test_event = RealTimeDataEvent(
                event_id="test_event_001",
                stream_type=DataStreamType.LIVE_GAMES,
                priority=DataPriority.HIGH,
                timestamp=datetime.now(timezone.utc),
                data={
                    "titan_clash_id": "test_game_001",
                    "home_team": "Test Lakers",
                    "away_team": "Test Warriors",
                    "status": "Live",
                    "home_score": 95,
                    "away_score": 92
                },
                source="TEST_API",
                league="NBA",
                game_id="test_game_001"
            )
            
            # Add event to queue
            await pipeline.event_queue.put(test_event)
            
            # Wait for processing
            await asyncio.sleep(2)
            
            # Check metrics
            metrics = pipeline.get_metrics()
            events_processed = metrics.get("events_processed", 0)
            
            await pipeline.stop_pipeline()
            
            return {
                "success": True,
                "message": "Data processing successful",
                "events_processed": events_processed,
                "test_event_id": test_event.event_id
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Data processing failed"
            }
    
    async def _test_error_handling(self) -> Dict[str, Any]:
        """Test error handling and recovery"""
        try:
            self.logger.info("⚠️ Testing error handling...")
            
            pipeline = await create_enhanced_pipeline_integration()
            await pipeline.start_pipeline()
            
            # Create invalid test event
            invalid_event = RealTimeDataEvent(
                event_id="invalid_event_001",
                stream_type=DataStreamType.LIVE_GAMES,
                priority=DataPriority.HIGH,
                timestamp=datetime.now(timezone.utc),
                data={"invalid": "data_structure"},  # Invalid data
                source="TEST_API",
                league="NBA"
            )
            
            # Add invalid event to queue
            await pipeline.event_queue.put(invalid_event)
            
            # Wait for processing
            await asyncio.sleep(2)
            
            # Check that pipeline is still running despite error
            assert pipeline.is_running, "Pipeline stopped due to error"
            
            # Check error metrics
            metrics = pipeline.get_metrics()
            
            await pipeline.stop_pipeline()
            
            return {
                "success": True,
                "message": "Error handling successful",
                "pipeline_survived": pipeline.is_running,
                "metrics": metrics
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Error handling test failed"
            }
    
    async def _test_performance(self) -> Dict[str, Any]:
        """Test performance characteristics"""
        try:
            self.logger.info("⚡ Testing performance...")
            
            pipeline = await create_enhanced_pipeline_integration()
            await pipeline.start_pipeline()
            
            # Generate multiple test events
            start_time = time.time()
            num_events = 10
            
            for i in range(num_events):
                test_event = RealTimeDataEvent(
                    event_id=f"perf_test_event_{i:03d}",
                    stream_type=DataStreamType.LIVE_GAMES,
                    priority=DataPriority.MEDIUM,
                    timestamp=datetime.now(timezone.utc),
                    data={
                        "titan_clash_id": f"perf_game_{i:03d}",
                        "test_data": f"performance_test_{i}"
                    },
                    source="PERFORMANCE_TEST",
                    league="NBA"
                )
                
                await pipeline.event_queue.put(test_event)
            
            # Wait for processing
            await asyncio.sleep(3)
            
            processing_time = time.time() - start_time
            
            # Get final metrics
            metrics = pipeline.get_metrics()
            
            await pipeline.stop_pipeline()
            
            # Calculate performance metrics
            throughput = num_events / processing_time if processing_time > 0 else 0
            
            return {
                "success": True,
                "message": "Performance test successful",
                "events_generated": num_events,
                "processing_time_seconds": round(processing_time, 3),
                "throughput_events_per_second": round(throughput, 2),
                "average_latency": metrics.get("average_latency", 0),
                "queue_size": metrics.get("queue_size", 0)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Performance test failed"
            }
    
    async def _test_database_integration(self) -> Dict[str, Any]:
        """Test database integration"""
        try:
            self.logger.info("🗄️ Testing database integration...")
            
            # Create test database
            self._create_test_database()
            
            pipeline = EnhancedRealTimePipelineIntegration(database_path=self.test_database)
            await pipeline.initialize()
            await pipeline.start_pipeline()
            
            # Test database update
            test_game_data = {
                "titan_clash_id": "db_test_game_001",
                "status": "Live",
                "home_score": 88,
                "away_score": 85
            }
            
            await pipeline._update_live_game_data(test_game_data)
            
            # Verify database update
            conn = sqlite3.connect(self.test_database)
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM games WHERE id = ?", ("db_test_game_001",))
            result = cursor.fetchone()
            conn.close()
            
            await pipeline.stop_pipeline()
            
            # Clean up test database
            os.remove(self.test_database)
            
            return {
                "success": True,
                "message": "Database integration successful",
                "database_updated": result is not None,
                "test_game_id": "db_test_game_001"
            }
            
        except Exception as e:
            # Clean up on error
            if os.path.exists(self.test_database):
                os.remove(self.test_database)
            
            return {
                "success": False,
                "error": str(e),
                "message": "Database integration failed"
            }
    
    async def _test_orchestrator(self) -> Dict[str, Any]:
        """Test orchestrator functionality"""
        try:
            self.logger.info("🎭 Testing orchestrator...")
            
            orchestrator = await create_orchestrator(OrchestratorMode.TESTING)
            
            # Start orchestrator
            await orchestrator.start()
            
            # Verify orchestrator is running
            status = orchestrator.get_status()
            assert status["orchestrator_status"] == PipelineStatus.RUNNING.value, "Orchestrator not running"
            
            # Check component status
            components = status.get("components", {})
            assert len(components) > 0, "No components registered"
            
            # Stop orchestrator
            await orchestrator.shutdown()
            
            final_status = orchestrator.get_status()
            assert final_status["orchestrator_status"] == PipelineStatus.STOPPED.value, "Orchestrator not stopped"
            
            return {
                "success": True,
                "message": "Orchestrator test successful",
                "components_count": len(components),
                "final_status": final_status["orchestrator_status"]
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Orchestrator test failed"
            }
    
    def _create_test_database(self):
        """Create test database with minimal schema"""
        conn = sqlite3.connect(self.test_database)
        cursor = conn.cursor()
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS games (
                id TEXT PRIMARY KEY,
                status TEXT,
                home_score INTEGER,
                away_score INTEGER,
                last_updated TEXT
            )
        """)
        
        # Insert test game
        cursor.execute("""
            INSERT OR REPLACE INTO games (id, status, home_score, away_score, last_updated)
            VALUES (?, ?, ?, ?, ?)
        """, ("db_test_game_001", "Scheduled", 0, 0, datetime.now().isoformat()))
        
        conn.commit()
        conn.close()

async def main():
    """Run the test suite"""
    logger.info("🚀 Starting Real-Time Pipeline Integration Test Suite...")
    
    test_suite = RealTimePipelineTestSuite()
    results = await test_suite.run_all_tests()
    
    # Save results to file
    results_file = f"realtime_pipeline_test_results_{int(time.time())}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"📊 Test results saved to: {results_file}")
    
    # Print summary
    print("\n" + "="*80)
    print("REAL-TIME PIPELINE INTEGRATION TEST RESULTS")
    print("="*80)
    print(f"Overall Status: {results['overall_status']}")
    print(f"Tests Passed: {results['passed_tests']}/{results['total_tests']} ({results['success_rate']:.1f}%)")
    print(f"Total Time: {results['total_time_seconds']} seconds")
    print("\nTest Category Results:")
    
    for category, result in results['test_results'].items():
        status_emoji = "✅" if result['status'] == "PASSED" else "❌"
        print(f"  {status_emoji} {category}: {result['status']}")
        if result['status'] != "PASSED" and 'error' in result:
            print(f"    Error: {result['error']}")
    
    print("="*80)
    
    return results['overall_status'] == "PASSED"

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
