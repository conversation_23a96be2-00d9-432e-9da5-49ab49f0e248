#!/usr/bin/env python3
"""
Test Transfer Learning & Domain Adaptation System
"""

import sys
sys.path.append('.')
import asyncio
import numpy as np
from transfer_learning_domain_adaptation import (
    TransferLearningConfig, TransferLearningEngine, League, TransferStrategy
)

async def test_transfer_learning_system():
    print('🏀 MEDUSA VAULT: Testing Transfer Learning & Domain Adaptation System')
    
    # Generate comprehensive test data for both leagues
    np.random.seed(42)
    
    # NBA data (source)
    nba_samples = 1500
    nba_features = 30
    nba_X = np.random.randn(nba_samples, nba_features)
    # NBA-specific patterns (higher scoring, different pace)
    nba_X[:, 0] = np.random.uniform(100, 130, nba_samples)  # team_points
    nba_X[:, 1] = np.random.uniform(95, 125, nba_samples)   # opponent_points
    nba_X[:, 2] = np.random.uniform(40, 55, nba_samples)    # team_rebounds
    nba_X[:, 3] = np.random.uniform(35, 50, nba_samples)    # opponent_rebounds
    
    # Create NBA target (win probability)
    nba_point_diff = nba_X[:, 0] - nba_X[:, 1]
    nba_win_prob = 1 / (1 + np.exp(-nba_point_diff / 12))
    nba_y = (np.random.random(nba_samples) < nba_win_prob).astype(int)
    
    # WNBA data (target) - different characteristics
    wnba_samples = 800
    wnba_features = 30
    wnba_X = np.random.randn(wnba_samples, wnba_features)
    # WNBA-specific patterns (lower scoring, different pace)
    wnba_X[:, 0] = np.random.uniform(70, 95, wnba_samples)   # team_points
    wnba_X[:, 1] = np.random.uniform(65, 90, wnba_samples)   # opponent_points
    wnba_X[:, 2] = np.random.uniform(30, 45, wnba_samples)   # team_rebounds
    wnba_X[:, 3] = np.random.uniform(25, 40, wnba_samples)   # opponent_rebounds
    
    # Create WNBA target (win probability)
    wnba_point_diff = wnba_X[:, 0] - wnba_X[:, 1]
    wnba_win_prob = 1 / (1 + np.exp(-wnba_point_diff / 8))
    wnba_y = (np.random.random(wnba_samples) < wnba_win_prob).astype(int)
    
    feature_names = [
        'team_points', 'opponent_points', 'team_rebounds', 'opponent_rebounds',
        'team_assists', 'opponent_assists', 'team_steals', 'opponent_steals'
    ] + [f'advanced_metric_{i}' for i in range(8, nba_features)]
    
    print(f'📊 NBA Dataset: {nba_X.shape[0]} samples, {nba_X.shape[1]} features')
    print(f'📊 WNBA Dataset: {wnba_X.shape[0]} samples, {wnba_X.shape[1]} features')
    print(f'🎯 NBA Win Rate: {np.mean(nba_y):.3f}')
    print(f'🎯 WNBA Win Rate: {np.mean(wnba_y):.3f}')
    
    # Test different transfer learning strategies
    strategies = [
        TransferStrategy.FEATURE_BASED,
        TransferStrategy.MODEL_BASED,
        TransferStrategy.ENSEMBLE_BASED,
        TransferStrategy.QUANTUM_ENHANCED
    ]
    
    results = {}
    
    for strategy in strategies:
        print(f'\n🔄 Testing Transfer Strategy: {strategy.value}')
        
        # Configure transfer learning
        config = TransferLearningConfig(
            source_league=League.NBA,
            target_league=League.WNBA,
            transfer_strategy=strategy,
            adaptation_strength=0.7,
            quantum_enhancement=True,
            cross_validation_folds=3,
            ensemble_integration=True,
            basketball_cortex_integration=True
        )
        
        # Initialize transfer learning engine
        engine = TransferLearningEngine(config)
        
        try:
            # Prepare data
            source_data = {
                'features': nba_X,
                'labels': nba_y,
                'feature_names': feature_names
            }
            
            target_data = {
                'features': wnba_X,
                'labels': wnba_y,
                'feature_names': feature_names
            }
            
            # Perform transfer learning
            result = await engine.perform_transfer_learning(source_data, target_data)
            
            if result.success:
                print(f'✅ {strategy.value} Transfer Success!')
                print(f'   🎯 Target Accuracy: {result.target_performance.accuracy:.3f}')
                print(f'   📊 Source Accuracy: {result.source_performance.accuracy:.3f}')
                print(f'   🔄 Transfer Effectiveness: {result.transfer_effectiveness:.3f}')
                print(f'   ⚡ Adaptation Quality: {result.adaptation_metrics.get("adaptation_quality", 0):.3f}')
                print(f'   🏀 Cross-League Score: {result.cross_league_performance.get("cross_league_score", 0):.3f}')
                
                results[strategy.value] = {
                    'success': True,
                    'target_accuracy': result.target_performance.accuracy,
                    'source_accuracy': result.source_performance.accuracy,
                    'transfer_effectiveness': result.transfer_effectiveness,
                    'adaptation_quality': result.adaptation_metrics.get("adaptation_quality", 0),
                    'cross_league_score': result.cross_league_performance.get("cross_league_score", 0)
                }
                
                # Check if target accuracy is met (using 70% as target)
                target_accuracy = 0.70
                if result.target_performance.accuracy >= target_accuracy:
                    print(f'   ✅ Target Accuracy Met: {result.target_performance.accuracy:.3f} >= {target_accuracy}')
                else:
                    print(f'   ⚠️ Target Accuracy Not Met: {result.target_performance.accuracy:.3f} < {target_accuracy}')
                    
            else:
                print(f'❌ {strategy.value} Transfer Failed: {result.error_message}')
                results[strategy.value] = {'success': False, 'error': result.error_message}
                
        except Exception as e:
            print(f'❌ Exception during {strategy.value} transfer: {e}')
            results[strategy.value] = {'success': False, 'error': str(e)}
    
    # Summary
    print('\n🏆 TRANSFER LEARNING RESULTS SUMMARY:')
    successful_strategies = [k for k, v in results.items() if v.get('success', False)]
    print(f'✅ Successful Strategies: {len(successful_strategies)}/{len(strategies)}')
    
    if successful_strategies:
        print('\n📈 Performance Comparison:')
        for strategy in successful_strategies:
            result = results[strategy]
            print(f'   {strategy}:')
            print(f'     Target Accuracy: {result["target_accuracy"]:.3f}')
            print(f'     Transfer Effectiveness: {result["transfer_effectiveness"]:.3f}')
            print(f'     Cross-League Score: {result["cross_league_score"]:.3f}')
        
        # Find best strategy
        best_strategy = max(successful_strategies, 
                          key=lambda x: results[x]["target_accuracy"])
        best_result = results[best_strategy]
        print(f'\n🏆 Best Strategy: {best_strategy}')
        print(f'   🎯 Best Target Accuracy: {best_result["target_accuracy"]:.3f}')
        print(f'   🔄 Transfer Effectiveness: {best_result["transfer_effectiveness"]:.3f}')
        
        return len(successful_strategies) >= 3  # Success if at least 3 strategies work
    else:
        print('❌ No successful transfer learning strategies')
        return False

if __name__ == "__main__":
    # Run comprehensive test
    success = asyncio.run(test_transfer_learning_system())
    print(f'\n🏆 Transfer Learning Test Result: {"SUCCESS" if success else "FAILED"}')
