#!/usr/bin/env python3
"""
Test TemporalFluxStabilizer Import Fix

This test validates that the vault_oracle.core.temporal_flux module
and TemporalFluxStabilizer class can be imported successfully.
"""

import logging
import sys

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s:%(name)s:%(message)s')
logger = logging.getLogger(__name__)

def test_temporal_flux_import():
    """Test that TemporalFluxStabilizer can be imported successfully."""
    logger.info("🚀 Testing TemporalFluxStabilizer Import")
    
    try:
        # Test the import that was previously failing
        from vault_oracle.core.temporal_flux import TemporalFluxStabilizer
        logger.info("✅ Successfully imported TemporalFluxStabilizer")
        
        # Test creating an instance
        stabilizer = TemporalFluxStabilizer()
        logger.info("✅ Successfully created TemporalFluxStabilizer instance")
        
        # Test basic functionality
        result = stabilizer.stabilize_flux(0.5)
        logger.info(f"✅ stabilize_flux(0.5) returned: {result}")
        
        # Test getting metrics
        metrics = stabilizer.get_stabilization_metrics()
        logger.info(f"✅ get_stabilization_metrics() returned: {metrics}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ TemporalFluxStabilizer import test failed: {e}")
        return False

def test_medusa_core_import():
    """Test that MedusaCore can import TemporalFluxStabilizer without errors."""
    logger.info("🚀 Testing MedusaCore Import with TemporalFluxStabilizer")
    
    try:
        # Test the specific import that was failing in MedusaCore
        from vault_oracle.core.temporal_flux import TemporalFluxStabilizer
        logger.info("✅ MedusaCore can now import TemporalFluxStabilizer successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ MedusaCore TemporalFluxStabilizer import test failed: {e}")
        return False

def main():
    """Run all temporal flux import tests."""
    logger.info("🧪 Starting Temporal Flux Import Tests")
    
    tests = [
        ("TemporalFluxStabilizer Import", test_temporal_flux_import),
        ("MedusaCore Import", test_medusa_core_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASSED" if result else "❌ FAILED"
            logger.info(f"{status}: {test_name}")
        except Exception as e:
            logger.error(f"❌ FAILED: {test_name} - {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{status}: {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All temporal flux import tests PASSED!")
        logger.info("🔧 Infrastructure Issue #8 (Fix Import Path Issues) is RESOLVED!")
        return True
    else:
        logger.error(f"💥 {total - passed} tests FAILED!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
