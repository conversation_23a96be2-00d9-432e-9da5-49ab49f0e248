```mermaid
graph TD
    subgraph Frontend
        A[UI Components<br/>(<PERSON>act/Vue, Brand SDK)]
        B[Dynamic Sidebar &<br/>Tier-Gated Features]
        C[Custom Animations<br/>(GSAP/Framer Motion)]
        D[Brand Assets<br/>(Logo, Icons, Colors)]
        E[User Personalization]
    end
    subgraph API Layer
        F[FastAPI Routers<br/>(Expert, Prophecy Audio, Firebase)]
        G[Authentication &<br/>Tier Enforcement]
    end
    subgraph Core Services
        H[Prediction & Analytics<br/>(Oracle, ML Models)]
        I[Messaging & Alerts<br/>(Firebase, Email, TTS)]
        J[Database Adapter<br/>(Unified Config)]
        K[Brand Compliance<br/>CI/CD Pipeline]
    end
    subgraph Data Layer
        L[PostgreSQL/Redis/S3]
        M[Config Files<br/>(TOML, YAML)]
    end
    A --> B
    A --> C
    A --> D
    A --> E
    B --> F
    C --> F
    E --> F
    F --> G
    G --> H
    G --> I
    H --> J
    I --> J
    J --> L
    J --> M
    K --> D
    K --> A
    K --> F
    K --> H
    K --> I
    K --> J
    K --> L
    K --> M
```

---
**How to use:**
- Paste this Mermaid code into a Markdown file or a tool like mermaid.live or VS Code with Mermaid support.
- It visualizes the architecture: frontend, API, core services, and data layers, with brand and compliance as a cross-cutting concern.
- Each box represents a major module or integration described in your branding and technical documentation.
