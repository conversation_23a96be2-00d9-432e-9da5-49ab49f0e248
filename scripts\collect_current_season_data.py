import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any, Set
import requests
import time
import json
from datetime import datetime
import os

#!/usr/bin/env python3
"""
Collect Current Season Data - 2024-25 NBA & 2025 WNBA
Critical: Collect the most recent season data for current player props predictions
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CurrentSeasonDataCollector:
    """Collect current season data for both NBA and WNBA"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }
        
        # Current seasons
        self.current_seasons = {
            'NBA': '2024-25',
            'WNBA': '2025'
        }
        
        # Track existing data
        self.existing_current_data = set()
        self.load_existing_current_data()
        
    def load_existing_current_data(self) -> None:
        """Load existing current season data to avoid duplicates"""
        logger.info("📊 Checking existing current season data...")
        
        conn = sqlite3.connect(self.db_path)
        
        # Check for existing 2024-25 NBA and 2025 WNBA data
        existing_query = """
        SELECT DISTINCT league_name, player_id, season, game_id
        FROM unified_nba_wnba_data
        WHERE (season = '2024-25' AND league_name = 'NBA')
        OR (season = '2025' AND league_name = 'WNBA')
        AND data_type = 'individual_player_game_log'
        """
        
        existing_df = pd.read_sql_query(existing_query, conn)
        
        for _, row in existing_df.iterrows():
            self.existing_current_data.add((row['league_name'], row['player_id'], row['season'], row['game_id']))
        
        conn.close()
        
        logger.info(f"✅ Found {len(self.existing_current_data)} existing current season game logs")
    
    def get_current_season_players(self, league: str) -> List[Dict[str, Any]]:
        """Get current season active players"""
        logger.info(f"📋 Getting current {league} players for {self.current_seasons[league]}...")
        
        league_id = '00' if league == 'NBA' else '10'
        current_season = self.current_seasons[league]
        
        try:
            url = f"{self.base_url}/commonallplayers"
            params = {
                'LeagueID': league_id,
                'Season': current_season,
                'IsOnlyCurrentSeason': '1'  # Only current season players
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            players = []
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                headers = data['resultSets'][0]['headers']
                rows = data['resultSets'][0]['rowSet']
                
                for row in rows:
                    player_dict = dict(zip(headers, row))
                    
                    # Only get active players
                    if player_dict.get('ROSTERSTATUS') == 1:  # Active
                        players.append({
                            'player_id': str(player_dict.get('PERSON_ID')),
                            'player_name': player_dict.get('DISPLAY_FIRST_LAST'),
                            'team_id': player_dict.get('TEAM_ID'),
                            'team_abbreviation': player_dict.get('TEAM_ABBREVIATION'),
                            'is_active': True,
                            'league': league,
                            'season': current_season
                        })
            
            logger.info(f"✅ Found {len(players)} active {league} players for {current_season}")
            return players
            
        except Exception as e:
            logger.error(f"❌ Error getting {league} current players: {e}")
            return []
    
    def collect_current_season_game_logs(self, player_id: str, player_name: str, league: str) -> List[Dict[str, Any]]:
        """Collect current season game logs"""
        current_season = self.current_seasons[league]
        game_logs = []
        
        try:
            logger.info(f"   📊 Collecting {player_name} current season ({current_season}) game logs...")
            
            url = f"{self.base_url}/playergamelog"
            params = {
                'PlayerID': player_id,
                'Season': current_season,
                'SeasonType': 'Regular Season',
                'LeagueID': '00' if league == 'NBA' else '10'
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                headers = data['resultSets'][0]['headers']
                rows = data['resultSets'][0]['rowSet']
                
                for row in rows:
                    game_dict = dict(zip(headers, row))
                    game_id = game_dict.get('Game_ID')
                    
                    # Skip if we already have this game
                    if (league, player_id, current_season, game_id) in self.existing_current_data:
                        continue
                    
                    # Create comprehensive current season game log
                    game_log = {
                        'player_id': player_id,
                        'player_name': player_name,
                        'season': current_season,
                        'league_name': league,
                        'league_id': '00' if league == 'NBA' else '10',
                        'game_id': game_id,
                        'game_date': game_dict.get('GAME_DATE'),
                        'matchup': game_dict.get('MATCHUP'),
                        'team_abbreviation': game_dict.get('TEAM_ABBREVIATION'),
                        'points': game_dict.get('PTS'),
                        'rebounds': game_dict.get('REB'),
                        'assists': game_dict.get('AST'),
                        'steals': game_dict.get('STL'),
                        'blocks': game_dict.get('BLK'),
                        'turnovers': game_dict.get('TOV'),
                        'field_goals_made': game_dict.get('FGM'),
                        'field_goals_attempted': game_dict.get('FGA'),
                        'three_pointers_made': game_dict.get('FG3M'),
                        'three_pointers_attempted': game_dict.get('FG3A'),
                        'free_throws_made': game_dict.get('FTM'),
                        'free_throws_attempted': game_dict.get('FTA'),
                        'minutes': game_dict.get('MIN'),
                        'plus_minus': game_dict.get('PLUS_MINUS'),
                        'data_type': 'individual_player_game_log',
                        'data_category': 'current_season_player_performance',
                        'source_file': f'current_season_gamelog_{player_id}_{current_season}.json',
                        'raw_data': json.dumps(game_dict),
                        'created_at': datetime.now().isoformat()
                    }
                    
                    game_logs.append(game_log)
                    
                    # Add to existing set
                    self.existing_current_data.add((league, player_id, current_season, game_id))
            
            time.sleep(0.6)  # Rate limiting
            
        except Exception as e:
            logger.warning(f"⚠️ Error collecting {player_name} current season logs: {e}")
        
        logger.info(f"✅ Collected {len(game_logs)} current season game logs for {player_name}")
        return game_logs
    
    def get_current_season_stats(self, player_id: str, player_name: str, league: str) -> List[Dict[str, Any]]:
        """Get current season stats and averages"""
        current_season = self.current_seasons[league]
        season_stats = []
        
        try:
            # Get current season stats
            url = f"{self.base_url}/playerdashboardbygeneralsplits"
            params = {
                'PlayerID': player_id,
                'Season': current_season,
                'SeasonType': 'Regular Season',
                'LeagueID': '00' if league == 'NBA' else '10'
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                # Overall stats (first result set)
                headers = data['resultSets'][0]['headers']
                rows = data['resultSets'][0]['rowSet']
                
                if rows:
                    row = rows[0]  # Overall stats
                    stats_dict = dict(zip(headers, row))
                    
                    season_stat = {
                        'player_id': player_id,
                        'player_name': player_name,
                        'season': current_season,
                        'league_name': league,
                        'league_id': '00' if league == 'NBA' else '10',
                        'team_abbreviation': stats_dict.get('TEAM_ABBREVIATION'),
                        'games_played': stats_dict.get('GP'),
                        'games_started': stats_dict.get('GS'),
                        'minutes_per_game': stats_dict.get('MIN'),
                        'points_per_game': stats_dict.get('PTS'),
                        'rebounds_per_game': stats_dict.get('REB'),
                        'assists_per_game': stats_dict.get('AST'),
                        'steals_per_game': stats_dict.get('STL'),
                        'blocks_per_game': stats_dict.get('BLK'),
                        'field_goal_percentage': stats_dict.get('FG_PCT'),
                        'three_point_percentage': stats_dict.get('FG3_PCT'),
                        'free_throw_percentage': stats_dict.get('FT_PCT'),
                        'data_type': 'current_season_averages',
                        'data_category': 'current_season_stats',
                        'source_file': f'current_season_stats_{player_id}_{current_season}.json',
                        'raw_data': json.dumps(stats_dict),
                        'created_at': datetime.now().isoformat()
                    }
                    
                    season_stats.append(season_stat)
        
        except Exception as e:
            logger.warning(f"⚠️ Error collecting {player_name} current season stats: {e}")
        
        return season_stats
    
    def insert_current_season_data(self, player_data: List[Dict[str, Any]]) -> int:
        """Insert current season data into database"""
        if not player_data:
            return 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        
        for record in player_data:
            try:
                if record.get('data_type') == 'individual_player_game_log':
                    # Create separate records for each stat category
                    base_record = {
                        'source_file': record['source_file'],
                        'source_table': 'current_season_game_logs',
                        'data_category': record['data_category'],
                        'season': record['season'],
                        'league_id': record['league_id'],
                        'league_name': record['league_name'],
                        'season_type': 'Regular Season',
                        'player_id': record['player_id'],
                        'player_name': record['player_name'],
                        'team_abbreviation': record['team_abbreviation'],
                        'game_id': record['game_id'],
                        'game_date': record['game_date'],
                        'data_type': record['data_type'],
                        'raw_data': record['raw_data'],
                        'created_at': record['created_at']
                    }
                    
                    # Individual stat records for current season player props
                    stat_mappings = {
                        'points': record.get('points'),
                        'rebounds': record.get('rebounds'),
                        'assists': record.get('assists'),
                        'steals': record.get('steals'),
                        'blocks': record.get('blocks'),
                        'turnovers': record.get('turnovers'),
                        'three_pointers_made': record.get('three_pointers_made'),
                        'field_goals_made': record.get('field_goals_made'),
                        'free_throws_made': record.get('free_throws_made'),
                        'minutes': record.get('minutes')
                    }
                    
                    for stat_category, stat_value in stat_mappings.items():
                        if stat_value is not None:
                            stat_record = base_record.copy()
                            stat_record['stat_category'] = stat_category
                            stat_record['stat_value'] = stat_value
                            
                            cursor.execute("""
                                INSERT INTO unified_nba_wnba_data (
                                    source_file, source_table, data_category, season, league_id, league_name,
                                    season_type, player_id, player_name, team_abbreviation, game_id, game_date,
                                    stat_category, stat_value, data_type, raw_data, created_at
                                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                            """, (
                                stat_record['source_file'], stat_record['source_table'], stat_record['data_category'],
                                stat_record['season'], stat_record['league_id'], stat_record['league_name'],
                                stat_record['season_type'], stat_record['player_id'], stat_record['player_name'],
                                stat_record['team_abbreviation'], stat_record['game_id'], stat_record['game_date'],
                                stat_record['stat_category'], stat_record['stat_value'], stat_record['data_type'],
                                stat_record['raw_data'], stat_record['created_at']
                            ))
                            inserted_count += 1
                            
            except Exception as e:
                logger.warning(f"⚠️ Error inserting current season record: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        return inserted_count
    
    def collect_current_season_data(self, league: str) -> Dict[str, Any]:
        """Collect all current season data for a league"""
        logger.info(f"🚀 COLLECTING CURRENT {league} SEASON DATA ({self.current_seasons[league]})")
        logger.info("=" * 70)
        
        start_time = datetime.now()
        
        # Get current season players
        current_players = self.get_current_season_players(league)
        
        if not current_players:
            return {'success': False, 'error': f'No current {league} players found'}
        
        logger.info(f"🎯 Processing {len(current_players)} current {league} players...")
        
        total_records = 0
        players_processed = 0
        batch_size = 10
        
        for i in range(0, len(current_players), batch_size):
            batch = current_players[i:i + batch_size]
            batch_data = []
            
            logger.info(f"📦 Processing batch {i//batch_size + 1}/{(len(current_players) + batch_size - 1)//batch_size}")
            
            for player in batch:
                player_id = player['player_id']
                player_name = player['player_name']
                
                if not player_id or not player_name:
                    continue
                
                logger.info(f"   📊 {player_name} ({players_processed + 1}/{len(current_players)})...")
                
                # Collect current season game logs
                game_logs = self.collect_current_season_game_logs(player_id, player_name, league)
                batch_data.extend(game_logs)
                
                # Collect current season stats
                season_stats = self.get_current_season_stats(player_id, player_name, league)
                batch_data.extend(season_stats)
                
                players_processed += 1
            
            # Insert batch
            if batch_data:
                inserted_count = self.insert_current_season_data(batch_data)
                total_records += inserted_count
                logger.info(f"   ✅ Batch inserted: {inserted_count:,} records")
            
            logger.info(f"📈 Progress: {players_processed}/{len(current_players)} players, {total_records:,} total records")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎉 CURRENT {league} SEASON COLLECTION COMPLETE!")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   👥 Players processed: {players_processed}")
        logger.info(f"   📊 Total records: {total_records:,}")
        
        return {
            'success': True,
            'league': league,
            'season': self.current_seasons[league],
            'duration': str(duration),
            'players_processed': players_processed,
            'total_records': total_records
        }

def main():
    """Collect current season data for both leagues"""
    
    collector = CurrentSeasonDataCollector()
    
    # Collect NBA 2024-25 first
    nba_results = collector.collect_current_season_data('NBA')
    
    if nba_results['success']:
        
        # Now collect WNBA 2025
        wnba_results = collector.collect_current_season_data('WNBA')
        
        if wnba_results['success']:
            
            total_records = nba_results['total_records'] + wnba_results['total_records']
            total_players = nba_results['players_processed'] + wnba_results['players_processed']
            
            
    
    return nba_results

if __name__ == "__main__":
    main()
