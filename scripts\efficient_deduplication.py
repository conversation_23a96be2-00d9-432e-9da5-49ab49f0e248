import sqlite3
import pandas as pd
import logging
from typing import Dict, Any
import os

#!/usr/bin/env python3
"""
Efficient Deduplication Script
Fast, memory-efficient deduplication of the WNBA/NBA dataset
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EfficientDeduplicator:
    """Fast, memory-efficient deduplication"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def quick_deduplication(self) -> Dict[str, Any]:
        """Fast deduplication using SQL operations"""
        logger.info("🚀 STARTING EFFICIENT DEDUPLICATION")
        logger.info("=" * 50)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Step 1: Get initial count
        cursor.execute("SELECT COUNT(*) FROM unified_nba_wnba_data WHERE league_name = 'WNBA'")
        initial_count = cursor.fetchone()[0]
        logger.info(f"📊 Initial WNBA records: {initial_count:,}")
        
        # Step 2: Create deduplicated table using SQL
        logger.info("🔄 Creating deduplicated table...")
        
        # Drop existing clean table if it exists
        cursor.execute("DROP TABLE IF EXISTS clean_wnba_data")
        
        # Create clean table with deduplication
        dedup_sql = """
        CREATE TABLE clean_wnba_data AS
        SELECT 
            player_name,
            season,
            stat_category,
            stat_value,
            data_category,
            game_date,
            team_abbreviation,
            source_file,
            data_type,
            league_name,
            league_id,
            MIN(rowid) as original_rowid
        FROM unified_nba_wnba_data 
        WHERE league_name = 'WNBA'
        AND stat_value IS NOT NULL
        AND player_name IS NOT NULL
        AND player_name != ''
        AND player_name != 'None'
        GROUP BY 
            player_name,
            season,
            stat_category,
            stat_value,
            data_category,
            game_date,
            team_abbreviation
        """
        
        cursor.execute(dedup_sql)
        
        # Step 3: Get deduplicated count
        cursor.execute("SELECT COUNT(*) FROM clean_wnba_data")
        clean_count = cursor.fetchone()[0]
        
        # Step 4: Additional quality filters
        logger.info("🔄 Applying quality filters...")
        
        cursor.execute("""
        DELETE FROM clean_wnba_data 
        WHERE CAST(stat_value AS FLOAT) < 0 
        OR CAST(stat_value AS FLOAT) > 1000
        """)
        
        cursor.execute("SELECT COUNT(*) FROM clean_wnba_data")
        final_count = cursor.fetchone()[0]
        
        # Step 5: Create indexes for performance
        logger.info("🔄 Creating indexes...")
        cursor.execute("CREATE INDEX idx_player_name ON clean_wnba_data(player_name)")
        cursor.execute("CREATE INDEX idx_season ON clean_wnba_data(season)")
        cursor.execute("CREATE INDEX idx_stat_category ON clean_wnba_data(stat_category)")
        
        # Step 6: Get quality statistics
        quality_stats = self._get_quality_stats(cursor)
        
        conn.commit()
        conn.close()
        
        # Calculate results
        removed_count = initial_count - final_count
        removal_percentage = (removed_count / initial_count) * 100
        
        logger.info(f"✅ DEDUPLICATION COMPLETE!")
        logger.info(f"   Initial: {initial_count:,} records")
        logger.info(f"   Final: {final_count:,} records")
        logger.info(f"   Removed: {removed_count:,} ({removal_percentage:.1f}%)")
        
        return {
            'initial_count': initial_count,
            'final_count': final_count,
            'removed_count': removed_count,
            'removal_percentage': removal_percentage,
            'quality_stats': quality_stats
        }
    
    def _get_quality_stats(self, cursor: sqlite3.Cursor) -> Dict[str, Any]:
        """Get quality statistics for clean dataset"""
        
        # Unique players
        cursor.execute("SELECT COUNT(DISTINCT player_name) FROM clean_wnba_data")
        unique_players = cursor.fetchone()[0]
        
        # Unique seasons
        cursor.execute("SELECT COUNT(DISTINCT season) FROM clean_wnba_data")
        unique_seasons = cursor.fetchone()[0]
        
        # Stat categories
        cursor.execute("SELECT COUNT(DISTINCT stat_category) FROM clean_wnba_data")
        stat_categories = cursor.fetchone()[0]
        
        # Data categories
        cursor.execute("SELECT COUNT(DISTINCT data_category) FROM clean_wnba_data")
        data_categories = cursor.fetchone()[0]
        
        # Top players by record count
        cursor.execute("""
        SELECT player_name, COUNT(*) as record_count
        FROM clean_wnba_data
        GROUP BY player_name
        ORDER BY record_count DESC
        LIMIT 5
        """)
        top_players = cursor.fetchall()
        
        return {
            'unique_players': unique_players,
            'unique_seasons': unique_seasons,
            'stat_categories': stat_categories,
            'data_categories': data_categories,
            'top_players': top_players
        }
    
    def export_clean_data(self, output_file: str = "data/clean_wnba_training_data.csv") -> str:
        """Export clean data to CSV for ML training"""
        logger.info("💾 Exporting clean data for ML training...")
        
        conn = sqlite3.connect(self.db_path)
        
        # Export clean data
        query = """
        SELECT 
            player_name,
            season,
            stat_category,
            CAST(stat_value AS FLOAT) as stat_value,
            data_category,
            game_date,
            team_abbreviation,
            source_file,
            data_type
        FROM clean_wnba_data
        ORDER BY player_name, season, game_date
        """
        
        clean_df = pd.read_sql_query(query, conn)
        conn.close()
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # Export to CSV
        clean_df.to_csv(output_file, index=False)
        
        logger.info(f"✅ Clean data exported: {output_file}")
        logger.info(f"📊 Records: {len(clean_df):,}")
        logger.info(f"📊 Players: {clean_df['player_name'].nunique():,}")
        logger.info(f"📊 File size: {os.path.getsize(output_file) / 1024 / 1024:.1f} MB")
        
        return output_file
    
    def validate_clean_data(self) -> Dict[str, Any]:
        """Validate the clean dataset"""
        logger.info("🔍 Validating clean dataset...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check for remaining duplicates
        cursor.execute("""
        SELECT COUNT(*) FROM (
            SELECT player_name, season, stat_category, stat_value, COUNT(*) as cnt
            FROM clean_wnba_data
            GROUP BY player_name, season, stat_category, stat_value
            HAVING COUNT(*) > 1
        )
        """)
        remaining_duplicates = cursor.fetchone()[0]
        
        # Check data quality
        cursor.execute("SELECT COUNT(*) FROM clean_wnba_data WHERE stat_value < 0")
        negative_values = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM clean_wnba_data WHERE player_name IS NULL OR player_name = ''")
        missing_players = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(stat_value), MAX(stat_value), AVG(stat_value) FROM clean_wnba_data")
        stat_range = cursor.fetchone()
        
        conn.close()
        
        validation_results = {
            'remaining_duplicates': remaining_duplicates,
            'negative_values': negative_values,
            'missing_players': missing_players,
            'stat_range': {
                'min': stat_range[0],
                'max': stat_range[1],
                'avg': stat_range[2]
            }
        }
        
        # Log validation results
        if remaining_duplicates == 0:
            logger.info("✅ No remaining duplicates")
        else:
            logger.warning(f"⚠️ {remaining_duplicates} duplicate groups remain")
        
        if negative_values == 0:
            logger.info("✅ No negative stat values")
        else:
            logger.warning(f"⚠️ {negative_values} negative stat values found")
        
        if missing_players == 0:
            logger.info("✅ No missing player names")
        else:
            logger.warning(f"⚠️ {missing_players} records with missing player names")
        
        logger.info(f"📊 Stat value range: {stat_range[0]:.1f} - {stat_range[1]:.1f} (avg: {stat_range[2]:.1f})")
        
        return validation_results

def main():
    """Run efficient deduplication"""
    
    deduplicator = EfficientDeduplicator()
    
    # Step 1: Quick deduplication
    results = deduplicator.quick_deduplication()
    
    
    quality = results['quality_stats']
    
    for player, count in quality['top_players']:
    
    # Step 2: Export clean data
    clean_file = deduplicator.export_clean_data()
    
    # Step 3: Validate
    validation = deduplicator.validate_clean_data()
    
    
    # Assessment
    if results['removal_percentage'] > 40:
    
    if validation['remaining_duplicates'] == 0:
    
    
    return results

if __name__ == "__main__":
    main()
