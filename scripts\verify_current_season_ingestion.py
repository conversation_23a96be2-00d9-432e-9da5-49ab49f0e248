import sqlite3
import pandas as pd
import logging
from typing import Dict, Any
from datetime import datetime


#!/usr/bin/env python3
"""
Verify Current Season Data Ingestion
Ensure all 2024-25 NBA and 2025 WNBA data is properly ingested into the database
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CurrentSeasonIngestionVerifier:
    """Verify current season data is properly ingested"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def verify_current_season_data(self) -> Dict[str, Any]:
        """Verify current season data ingestion"""
        logger.info("🔍 VERIFYING CURRENT SEASON DATA INGESTION")
        logger.info("=" * 55)
        
        conn = sqlite3.connect(self.db_path)
        
        # Check for 2024-25 NBA data
        nba_2024_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT player_name) as unique_players,
            COUNT(DISTINCT game_id) as unique_games,
            COUNT(CASE WHEN data_type = 'individual_player_game_log' THEN 1 END) as game_logs,
            MIN(created_at) as first_record,
            MAX(created_at) as last_record
        FROM unified_nba_wnba_data
        WHERE league_name = 'NBA' 
        AND season = '2024-25'
        """
        
        nba_2024_stats = pd.read_sql_query(nba_2024_query, conn).iloc[0].to_dict()
        
        # Check for 2025 WNBA data
        wnba_2025_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT player_name) as unique_players,
            COUNT(DISTINCT game_id) as unique_games,
            COUNT(CASE WHEN data_type = 'individual_player_game_log' THEN 1 END) as game_logs,
            MIN(created_at) as first_record,
            MAX(created_at) as last_record
        FROM unified_nba_wnba_data
        WHERE league_name = 'WNBA' 
        AND season = '2025'
        """
        
        wnba_2025_stats = pd.read_sql_query(wnba_2025_query, conn).iloc[0].to_dict()
        
        # Check overall database stats
        total_query = """
        SELECT 
            league_name,
            season,
            COUNT(*) as records,
            COUNT(DISTINCT player_name) as players
        FROM unified_nba_wnba_data
        WHERE (league_name = 'NBA' AND season = '2024-25')
        OR (league_name = 'WNBA' AND season = '2025')
        GROUP BY league_name, season
        ORDER BY league_name, season
        """
        
        current_season_breakdown = pd.read_sql_query(total_query, conn)
        
        # Check data quality
        quality_query = """
        SELECT 
            league_name,
            season,
            stat_category,
            COUNT(*) as records,
            COUNT(DISTINCT player_name) as players,
            AVG(CAST(stat_value AS FLOAT)) as avg_value
        FROM unified_nba_wnba_data
        WHERE ((league_name = 'NBA' AND season = '2024-25')
        OR (league_name = 'WNBA' AND season = '2025'))
        AND stat_category IN ('points', 'rebounds', 'assists', 'steals', 'blocks')
        GROUP BY league_name, season, stat_category
        ORDER BY league_name, season, records DESC
        """
        
        quality_stats = pd.read_sql_query(quality_query, conn)
        
        conn.close()
        
        # Log results
        logger.info("📊 NBA 2024-25 SEASON DATA:")
        logger.info(f"   Total records: {nba_2024_stats['total_records']:,}")
        logger.info(f"   Unique players: {nba_2024_stats['unique_players']:,}")
        logger.info(f"   Game logs: {nba_2024_stats['game_logs']:,}")
        logger.info(f"   Unique games: {nba_2024_stats['unique_games']:,}")
        logger.info(f"   Data range: {nba_2024_stats['first_record']} to {nba_2024_stats['last_record']}")
        
        logger.info("\n📊 WNBA 2025 SEASON DATA:")
        logger.info(f"   Total records: {wnba_2025_stats['total_records']:,}")
        logger.info(f"   Unique players: {wnba_2025_stats['unique_players']:,}")
        logger.info(f"   Game logs: {wnba_2025_stats['game_logs']:,}")
        logger.info(f"   Unique games: {wnba_2025_stats['unique_games']:,}")
        logger.info(f"   Data range: {wnba_2025_stats['first_record']} to {wnba_2025_stats['last_record']}")
        
        logger.info("\n📊 CURRENT SEASON BREAKDOWN:")
        for _, row in current_season_breakdown.iterrows():
            logger.info(f"   {row['league_name']} {row['season']}: {row['records']:,} records, {row['players']:,} players")
        
        logger.info("\n📊 PLAYER PROPS DATA QUALITY:")
        for _, row in quality_stats.iterrows():
            logger.info(f"   {row['league_name']} {row['season']} | {row['stat_category']}: {row['records']:,} records, {row['players']:,} players (avg: {row['avg_value']:.1f})")
        
        return {
            'nba_2024_stats': nba_2024_stats,
            'wnba_2025_stats': wnba_2025_stats,
            'current_season_breakdown': current_season_breakdown.to_dict('records'),
            'quality_stats': quality_stats.to_dict('records')
        }
    
    def check_data_integration(self) -> Dict[str, Any]:
        """Check how current season data integrates with historical data"""
        logger.info("\n🔗 CHECKING DATA INTEGRATION WITH HISTORICAL DATA")
        logger.info("=" * 55)
        
        conn = sqlite3.connect(self.db_path)
        
        # Check complete dataset coverage
        coverage_query = """
        SELECT 
            league_name,
            season,
            COUNT(*) as records,
            COUNT(DISTINCT player_name) as players,
            COUNT(CASE WHEN data_type = 'individual_player_game_log' THEN 1 END) as game_logs
        FROM unified_nba_wnba_data
        WHERE league_name IN ('NBA', 'WNBA')
        GROUP BY league_name, season
        ORDER BY league_name, season DESC
        """
        
        coverage_df = pd.read_sql_query(coverage_query, conn)
        
        # Check player continuity (players in both historical and current data)
        continuity_query = """
        SELECT 
            league_name,
            player_name,
            COUNT(DISTINCT season) as seasons_played,
            MIN(season) as first_season,
            MAX(season) as last_season,
            COUNT(*) as total_records
        FROM unified_nba_wnba_data
        WHERE league_name IN ('NBA', 'WNBA')
        AND player_name IS NOT NULL
        GROUP BY league_name, player_name
        HAVING COUNT(DISTINCT season) >= 2
        ORDER BY league_name, seasons_played DESC, total_records DESC
        LIMIT 20
        """
        
        continuity_df = pd.read_sql_query(continuity_query, conn)
        
        # Check data completeness
        completeness_query = """
        SELECT 
            'Total Records' as metric,
            COUNT(*) as value
        FROM unified_nba_wnba_data
        UNION ALL
        SELECT 
            'Player Records' as metric,
            COUNT(*) as value
        FROM unified_nba_wnba_data
        WHERE player_name IS NOT NULL 
        AND player_name != '' 
        AND player_name != 'None' 
        AND player_name != 'TEAM_RECORD' 
        AND player_name != 'NEEDS_EXTRACTION'
        UNION ALL
        SELECT 
            'Game Logs' as metric,
            COUNT(*) as value
        FROM unified_nba_wnba_data
        WHERE data_type = 'individual_player_game_log'
        UNION ALL
        SELECT 
            'Current Season Records' as metric,
            COUNT(*) as value
        FROM unified_nba_wnba_data
        WHERE (league_name = 'NBA' AND season = '2024-25')
        OR (league_name = 'WNBA' AND season = '2025')
        """
        
        completeness_df = pd.read_sql_query(completeness_query, conn)
        
        conn.close()
        
        # Log integration results
        logger.info("📊 COMPLETE DATASET COVERAGE:")
        current_seasons = ['2024-25', '2025']
        for _, row in coverage_df.iterrows():
            season_type = "🔥 CURRENT" if row['season'] in current_seasons else "📚 HISTORICAL"
            logger.info(f"   {season_type} | {row['league_name']} {row['season']}: {row['records']:,} records, {row['players']:,} players, {row['game_logs']:,} game logs")
        
        logger.info("\n👥 PLAYER CONTINUITY (Multi-Season Players):")
        for _, row in continuity_df.head(10).iterrows():
            logger.info(f"   {row['league_name']} | {row['player_name']}: {row['seasons_played']} seasons ({row['first_season']}-{row['last_season']}), {row['total_records']:,} records")
        
        logger.info("\n📊 DATABASE COMPLETENESS:")
        for _, row in completeness_df.iterrows():
            logger.info(f"   {row['metric']}: {row['value']:,}")
        
        return {
            'coverage': coverage_df.to_dict('records'),
            'continuity': continuity_df.to_dict('records'),
            'completeness': completeness_df.to_dict('records')
        }
    
    def validate_neural_training_readiness(self) -> Dict[str, Any]:
        """Validate data is ready for neural training pipeline"""
        logger.info("\n🧠 VALIDATING NEURAL TRAINING READINESS")
        logger.info("=" * 45)
        
        conn = sqlite3.connect(self.db_path)
        
        # Check training data requirements
        training_readiness_query = """
        SELECT 
            league_name,
            COUNT(CASE WHEN data_type = 'individual_player_game_log' THEN 1 END) as game_logs,
            COUNT(DISTINCT CASE WHEN data_type = 'individual_player_game_log' THEN player_name END) as players_with_logs,
            COUNT(DISTINCT season) as seasons_covered,
            COUNT(DISTINCT CASE WHEN stat_category = 'points' THEN player_name END) as players_with_points,
            COUNT(DISTINCT CASE WHEN stat_category = 'rebounds' THEN player_name END) as players_with_rebounds,
            COUNT(DISTINCT CASE WHEN stat_category = 'assists' THEN player_name END) as players_with_assists,
            COUNT(CASE WHEN (league_name = 'NBA' AND season = '2024-25') OR (league_name = 'WNBA' AND season = '2025') THEN 1 END) as current_season_records
        FROM unified_nba_wnba_data
        WHERE league_name IN ('NBA', 'WNBA')
        GROUP BY league_name
        """
        
        training_stats = pd.read_sql_query(training_readiness_query, conn)
        
        # Check recent data availability
        recent_data_query = """
        SELECT 
            league_name,
            season,
            COUNT(*) as records,
            COUNT(DISTINCT player_name) as players,
            MAX(created_at) as latest_update
        FROM unified_nba_wnba_data
        WHERE (league_name = 'NBA' AND season IN ('2023-24', '2024-25'))
        OR (league_name = 'WNBA' AND season IN ('2024', '2025'))
        GROUP BY league_name, season
        ORDER BY league_name, season DESC
        """
        
        recent_data = pd.read_sql_query(recent_data_query, conn)
        
        conn.close()
        
        # Calculate readiness scores
        readiness_assessment = {}
        
        for _, row in training_stats.iterrows():
            league = row['league_name']
            
            # Calculate readiness metrics
            game_logs_score = min(100, (row['game_logs'] / 100000) * 100)  # Target: 100k+ game logs
            player_coverage_score = min(100, (row['players_with_logs'] / 1000) * 100)  # Target: 1k+ players
            season_coverage_score = min(100, (row['seasons_covered'] / 5) * 100)  # Target: 5+ seasons
            current_data_score = min(100, (row['current_season_records'] / 50000) * 100)  # Target: 50k+ current records
            
            # Props coverage
            props_coverage = (row['players_with_points'] + row['players_with_rebounds'] + row['players_with_assists']) / 3
            props_score = min(100, (props_coverage / 1000) * 100)  # Target: 1k+ players per prop
            
            overall_score = (game_logs_score + player_coverage_score + season_coverage_score + current_data_score + props_score) / 5
            
            readiness_assessment[league] = {
                'game_logs': row['game_logs'],
                'players_with_logs': row['players_with_logs'],
                'seasons_covered': row['seasons_covered'],
                'current_season_records': row['current_season_records'],
                'players_with_points': row['players_with_points'],
                'players_with_rebounds': row['players_with_rebounds'],
                'players_with_assists': row['players_with_assists'],
                'scores': {
                    'game_logs_score': game_logs_score,
                    'player_coverage_score': player_coverage_score,
                    'season_coverage_score': season_coverage_score,
                    'current_data_score': current_data_score,
                    'props_score': props_score,
                    'overall_score': overall_score
                }
            }
            
            logger.info(f"🎯 {league} NEURAL TRAINING READINESS:")
            logger.info(f"   Game logs: {row['game_logs']:,} (score: {game_logs_score:.0f}/100)")
            logger.info(f"   Players with logs: {row['players_with_logs']:,} (score: {player_coverage_score:.0f}/100)")
            logger.info(f"   Seasons covered: {row['seasons_covered']} (score: {season_coverage_score:.0f}/100)")
            logger.info(f"   Current season records: {row['current_season_records']:,} (score: {current_data_score:.0f}/100)")
            logger.info(f"   Props coverage: {props_coverage:.0f} avg players (score: {props_score:.0f}/100)")
            logger.info(f"   OVERALL READINESS: {overall_score:.0f}/100")
            
            if overall_score >= 90:
                logger.info(f"   🏆 EXCELLENT - Ready for elite neural training!")
            elif overall_score >= 75:
                logger.info(f"   ✅ VERY GOOD - Ready for advanced neural training")
            elif overall_score >= 60:
                logger.info(f"   📈 GOOD - Ready for standard neural training")
            else:
                logger.info(f"   ⚠️ NEEDS IMPROVEMENT - More data recommended")
        
        logger.info("\n📊 RECENT DATA AVAILABILITY:")
        for _, row in recent_data.iterrows():
            season_type = "🔥 CURRENT" if row['season'] in ['2024-25', '2025'] else "📚 RECENT"
            logger.info(f"   {season_type} | {row['league_name']} {row['season']}: {row['records']:,} records, {row['players']:,} players (updated: {row['latest_update']})")
        
        return {
            'training_stats': training_stats.to_dict('records'),
            'readiness_assessment': readiness_assessment,
            'recent_data': recent_data.to_dict('records')
        }

def main():
    """Run comprehensive current season data ingestion verification"""
    
    verifier = CurrentSeasonIngestionVerifier()
    
    # Step 1: Verify current season data
    current_season_data = verifier.verify_current_season_data()
    
    # Step 2: Check data integration
    integration_data = verifier.check_data_integration()
    
    # Step 3: Validate neural training readiness
    training_readiness = verifier.validate_neural_training_readiness()
    
    
    # Assessment
    nba_records = current_season_data['nba_2024_stats']['total_records']
    wnba_records = current_season_data['wnba_2025_stats']['total_records']
    total_current = nba_records + wnba_records
    
    
    # Check if data is properly ingested
    if nba_records > 200000 and wnba_records > 10000:
    elif nba_records > 100000 and wnba_records > 5000:
    else:
    
    
    return {
        'current_season_data': current_season_data,
        'integration_data': integration_data,
        'training_readiness': training_readiness
    }

if __name__ == "__main__":
    main()
