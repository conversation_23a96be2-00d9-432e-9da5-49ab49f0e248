import sqlite3
import sys
import os
from datetime import datetime
from typing import Dict, List
        import shutil

#!/usr/bin/env python3
"""
Data Lake Schema Fix Script
===========================

Comprehensive script to create missing tables and columns in the data lake
to match the expected schema from database models.
"""


# Add backend to path for imports
sys.path.append('backend')

def backup_database(db_path: str) -> str:
    """Create backup of database before modifications"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = f"{db_path}.backup_{timestamp}"
    
    try:
        shutil.copy2(db_path, backup_path)
        return backup_path
    except Exception as e:
        raise

def create_missing_tables(conn: sqlite3.Connection) -> int:
    """Create all missing tables with proper schema"""
    cursor = conn.cursor()
    tables_created = 0
    
    # SQL for creating missing tables
    table_definitions = {
        'users': '''
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                email TEXT UNIQUE NOT NULL,
                username TEXT UNIQUE NOT NULL,
                hashed_password TEXT NOT NULL,
                api_key TEXT UNIQUE,
                role TEXT DEFAULT 'user',
                subscription_tier TEXT DEFAULT 'basic',
                is_active BOOLEAN DEFAULT 1,
                is_verified BOOLEAN DEFAULT 0,
                failed_login_attempts INTEGER DEFAULT 0,
                last_login_attempt DATETIME,
                password_reset_token TEXT,
                password_reset_expires DATETIME,
                subscription_start DATETIME,
                subscription_end DATETIME,
                billing_customer_id TEXT,
                api_calls_today INTEGER DEFAULT 0,
                api_calls_month INTEGER DEFAULT 0,
                last_api_call DATETIME,
                timezone TEXT DEFAULT 'UTC',
                notification_preferences TEXT,
                dashboard_settings TEXT,
                permission_flags TEXT,
                access_history TEXT,
                security_clearance TEXT DEFAULT 'basic',
                two_factor_enabled BOOLEAN DEFAULT 0,
                backup_codes TEXT,
                prediction_streak INTEGER DEFAULT 0,
                expertise_score REAL DEFAULT 0.0,
                vault_mastery_level REAL DEFAULT 0.0,
                neural_synchronization REAL DEFAULT 0.0,
                expert_level TEXT DEFAULT 'novice',
                expert_since DATETIME,
                vault_operations INTEGER DEFAULT 0,
                chronicle_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_prophecy_update DATETIME
            )
        ''',
        
        'user_sessions': '''
            CREATE TABLE IF NOT EXISTS user_sessions (
                id TEXT PRIMARY KEY,
                vault_user_id TEXT NOT NULL,
                session_token TEXT UNIQUE NOT NULL,
                ip_address TEXT,
                user_agent TEXT,
                device_type TEXT,
                location TEXT,
                chronicle_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME,
                last_activity DATETIME,
                is_active BOOLEAN DEFAULT 1,
                logout_reason TEXT,
                FOREIGN KEY (vault_user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ''',
        
        'player_props': '''
            CREATE TABLE IF NOT EXISTS player_props (
                id TEXT PRIMARY KEY,
                hero_id TEXT NOT NULL,
                titan_clash_id TEXT NOT NULL,
                prop_type TEXT NOT NULL,
                line REAL NOT NULL,
                over_odds INTEGER,
                under_odds INTEGER,
                sportsbook TEXT NOT NULL,
                opening_line REAL,
                line_movement REAL,
                volume_over INTEGER DEFAULT 0,
                volume_under INTEGER DEFAULT 0,
                sharp_money_percentage REAL,
                public_betting_percentage REAL,
                chronicle_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_prophecy_update DATETIME,
                data_source TEXT,
                confidence_score REAL,
                market_efficiency REAL,
                FOREIGN KEY (hero_id) REFERENCES players(id) ON DELETE CASCADE,
                FOREIGN KEY (titan_clash_id) REFERENCES games(id) ON DELETE CASCADE
            )
        ''',
        
        'game_predictions': '''
            CREATE TABLE IF NOT EXISTS game_predictions (
                id TEXT PRIMARY KEY,
                titan_clash_id TEXT NOT NULL,
                model_name TEXT NOT NULL,
                model_version TEXT NOT NULL,
                predicted_home_score REAL,
                predicted_away_score REAL,
                predicted_total REAL,
                predicted_spread REAL,
                predicted_margin REAL,
                home_win_probability REAL,
                away_win_probability REAL,
                over_probability REAL,
                under_probability REAL,
                spread_confidence REAL,
                total_confidence REAL,
                moneyline_confidence REAL,
                model_accuracy_recent REAL,
                feature_importance TEXT,
                prediction_factors TEXT,
                risk_assessment TEXT,
                chronicle_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_prophecy_update DATETIME,
                actual_home_score INTEGER,
                actual_away_score INTEGER,
                prediction_accuracy REAL,
                FOREIGN KEY (titan_clash_id) REFERENCES games(id) ON DELETE CASCADE
            )
        ''',
        
        'prop_predictions': '''
            CREATE TABLE IF NOT EXISTS prop_predictions (
                id TEXT PRIMARY KEY,
                prop_id TEXT NOT NULL,
                model_name TEXT NOT NULL,
                model_version TEXT NOT NULL,
                predicted_value REAL NOT NULL,
                over_probability REAL NOT NULL,
                under_probability REAL NOT NULL,
                confidence_score REAL NOT NULL,
                expected_value_over REAL,
                expected_value_under REAL,
                kelly_bet_size_over REAL,
                kelly_bet_size_under REAL,
                recommendation TEXT,
                feature_importance TEXT,
                prediction_factors TEXT,
                model_accuracy_recent REAL,
                chronicle_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_prophecy_update DATETIME,
                actual_value REAL,
                prediction_accuracy REAL,
                FOREIGN KEY (prop_id) REFERENCES player_props(id) ON DELETE CASCADE
            )
        ''',
        
        'achievements': '''
            CREATE TABLE IF NOT EXISTS achievements (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT NOT NULL,
                achievement_type TEXT NOT NULL,
                rarity TEXT DEFAULT 'common',
                criteria_json TEXT NOT NULL,
                target_value REAL,
                category TEXT,
                icon_url TEXT,
                badge_color TEXT,
                points_reward INTEGER DEFAULT 0,
                tier_requirement TEXT,
                is_active BOOLEAN DEFAULT 1,
                is_hidden BOOLEAN DEFAULT 0,
                unlock_order INTEGER,
                prerequisite_achievements TEXT,
                chronicle_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_prophecy_update DATETIME
            )
        ''',
        
        'user_achievements': '''
            CREATE TABLE IF NOT EXISTS user_achievements (
                id TEXT PRIMARY KEY,
                vault_user_id TEXT NOT NULL,
                achievement_id TEXT NOT NULL,
                current_progress REAL DEFAULT 0.0,
                target_progress REAL NOT NULL,
                progress_percentage REAL DEFAULT 0.0,
                is_completed BOOLEAN DEFAULT 0,
                unlocked_at DATETIME,
                completion_context TEXT,
                milestone_data TEXT,
                chronicle_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_prophecy_update DATETIME,
                FOREIGN KEY (vault_user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (achievement_id) REFERENCES achievements(id) ON DELETE CASCADE
            )
        ''',
        
        'user_stats': '''
            CREATE TABLE IF NOT EXISTS user_stats (
                id TEXT PRIMARY KEY,
                vault_user_id TEXT UNIQUE NOT NULL,
                total_achievements INTEGER DEFAULT 0,
                achievements_unlocked INTEGER DEFAULT 0,
                achievement_points INTEGER DEFAULT 0,
                achievement_completion_rate REAL DEFAULT 0.0,
                total_bets_placed INTEGER DEFAULT 0,
                total_bet_amount REAL DEFAULT 0.0,
                total_winnings REAL DEFAULT 0.0,
                win_rate REAL DEFAULT 0.0,
                roi REAL DEFAULT 0.0,
                longest_winning_streak INTEGER DEFAULT 0,
                longest_losing_streak INTEGER DEFAULT 0,
                current_streak INTEGER DEFAULT 0,
                favorite_bet_type TEXT,
                total_predictions_made INTEGER DEFAULT 0,
                correct_predictions INTEGER DEFAULT 0,
                prediction_accuracy REAL DEFAULT 0.0,
                chronicle_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_prophecy_update DATETIME,
                FOREIGN KEY (vault_user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ''',
        
        'vault_operations': '''
            CREATE TABLE IF NOT EXISTS vault_operations (
                id TEXT PRIMARY KEY,
                vault_user_id TEXT NOT NULL,
                operation_type TEXT NOT NULL,
                operation_name TEXT NOT NULL,
                operation_data TEXT,
                vault_session_id TEXT NOT NULL,
                quantum_signature TEXT UNIQUE,
                neural_pattern_id TEXT,
                operation_status TEXT DEFAULT 'pending',
                execution_time_ms INTEGER,
                resource_usage TEXT,
                error_details TEXT,
                chronicle_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                last_prophecy_update DATETIME,
                FOREIGN KEY (vault_user_id) REFERENCES users(id) ON DELETE CASCADE
            )
        ''',
        
        'line_movements': '''
            CREATE TABLE IF NOT EXISTS line_movements (
                id TEXT PRIMARY KEY,
                titan_clash_id TEXT NOT NULL,
                sportsbook TEXT NOT NULL,
                spread_home REAL,
                total_points REAL,
                moneyline_home INTEGER,
                moneyline_away INTEGER,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                movement_direction TEXT,
                movement_magnitude REAL,
                volume_indicator TEXT,
                sharp_money_indicator BOOLEAN DEFAULT 0,
                chronicle_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_prophecy_update DATETIME,
                FOREIGN KEY (titan_clash_id) REFERENCES games(id) ON DELETE CASCADE
            )
        '''
    }
    
    for table_name, sql in table_definitions.items():
        try:
            cursor.execute(sql)
            tables_created += 1
        except Exception as e:
    
    return tables_created

def add_missing_columns(conn: sqlite3.Connection) -> int:
    """Add missing columns to existing tables"""
    cursor = conn.cursor()
    columns_added = 0

    # Define missing columns for each table
    missing_columns = {
        'users': [
            ('cognitive_preferences', 'TEXT'),
            ('last_login', 'DATETIME'),
            ('quantum_access_level', 'TEXT DEFAULT "basic"'),
            ('vault_session_tokens', 'TEXT')
        ],
        'games': [
            ('chronicle_timestamp', 'DATETIME')
        ],
        'teams': [
            ('arena_capacity', 'INTEGER'),
            ('arena_name', 'TEXT'),
            ('avg_margin_of_defeat', 'REAL'),
            ('avg_margin_of_victory', 'REAL'),
            ('away_record', 'TEXT'),
            ('chronicle_timestamp', 'DATETIME'),
            ('current_streak', 'TEXT'),
            ('data_completeness_score', 'REAL'),
            ('defensive_rebound_pct', 'REAL'),
            ('effective_fg_pct', 'REAL'),
            ('founded_year', 'INTEGER'),
            ('full_name', 'TEXT'),
            ('games_within_5pts', 'INTEGER'),
            ('home_record', 'TEXT'),
            ('injury_count', 'INTEGER'),
            ('last_10_record', 'TEXT'),
            ('last_prophecy_update', 'DATETIME'),
            ('last_stats_update', 'DATETIME'),
            ('logo_url', 'TEXT'),
            ('offensive_rebound_pct', 'REAL'),
            ('overtime_record', 'TEXT'),
            ('power_ranking', 'INTEGER'),
            ('primary_color', 'TEXT'),
            ('remaining_strength_of_schedule', 'REAL'),
            ('roster_stability_score', 'REAL'),
            ('secondary_color', 'TEXT'),
            ('strength_of_schedule', 'REAL'),
            ('true_shooting_pct', 'REAL'),
            ('turnover_pct', 'REAL'),
            ('vs_conference_record', 'TEXT'),
            ('vs_division_record', 'TEXT')
        ],
        'players': [
            ('age', 'INTEGER'),
            ('assist_percentage', 'REAL'),
            ('back_to_back_performance', 'REAL'),
            ('betting_value_score', 'REAL'),
            ('birthdate', 'TEXT'),
            ('block_percentage', 'REAL'),
            ('chronicle_timestamp', 'DATETIME'),
            ('contract_years_remaining', 'INTEGER'),
            ('corner_three_fg_percentage', 'REAL'),
            ('corner_three_percentage', 'REAL'),
            ('data_quality_score', 'REAL'),
            ('defensive_rating', 'REAL'),
            ('defensive_rebounds_per_game', 'REAL'),
            ('draft_pick', 'INTEGER'),
            ('draft_round', 'INTEGER'),
            ('draft_year', 'INTEGER'),
            ('effective_field_goal_percentage', 'REAL'),
            ('experience_years', 'INTEGER'),
            ('field_goals_attempted_per_game', 'REAL'),
            ('field_goals_made_per_game', 'REAL'),
            ('fouls_per_game', 'REAL'),
            ('free_throws_attempted_per_game', 'REAL'),
            ('free_throws_made_per_game', 'REAL'),
            ('games_played', 'INTEGER'),
            ('games_started', 'INTEGER'),
            ('height_display', 'TEXT'),
            ('height_inches', 'INTEGER'),
            ('home_vs_away_differential', 'REAL'),
            ('injury_status', 'TEXT'),
            ('is_starter', 'BOOLEAN DEFAULT 0'),
            ('last_10_games_avg_points', 'REAL'),
            ('last_5_games_avg_points', 'REAL'),
            ('last_game_date', 'DATETIME'),
            ('last_prophecy_update', 'DATETIME'),
            ('mid_range_fg_percentage', 'REAL'),
            ('mid_range_percentage', 'REAL'),
            ('minutes_per_game', 'REAL'),
            ('name', 'TEXT'),
            ('net_rating', 'REAL'),
            ('offensive_rating', 'REAL'),
            ('offensive_rebounds_per_game', 'REAL'),
            ('plus_minus_per_game', 'REAL'),
            ('primary_position', 'TEXT'),
            ('prop_hit_rate_assists', 'REAL'),
            ('prop_hit_rate_points', 'REAL'),
            ('prop_hit_rate_rebounds', 'REAL'),
            ('rebound_percentage', 'REAL'),
            ('salary_current', 'REAL'),
            ('secondary_position', 'TEXT'),
            ('shots_at_rim_fg_percentage', 'REAL'),
            ('shots_at_rim_percentage', 'REAL'),
            ('stats_last_updated', 'DATETIME'),
            ('steal_percentage', 'REAL'),
            ('three_pointers_attempted_per_game', 'REAL'),
            ('three_pointers_made_per_game', 'REAL'),
            ('turnover_percentage', 'REAL'),
            ('turnovers_per_game', 'REAL'),
            ('vs_winning_teams_performance', 'REAL'),
            ('weight_lbs', 'INTEGER'),
            ('wingspan_inches', 'INTEGER')
        ],
        'player_game_stats': [
            ('back_to_back', 'BOOLEAN DEFAULT 0'),
            ('bench_points', 'INTEGER'),
            ('chronicle_timestamp', 'DATETIME'),
            ('efficiency_rating', 'REAL'),
            ('flagrant_fouls', 'INTEGER'),
            ('game_score', 'REAL'),
            ('home_game', 'BOOLEAN DEFAULT 0'),
            ('lead_changes_responsible', 'INTEGER'),
            ('rest_days', 'INTEGER'),
            ('seconds_played', 'INTEGER'),
            ('shots_blocked', 'INTEGER'),
            ('shots_contested', 'INTEGER'),
            ('shots_created_for_others', 'INTEGER'),
            ('starter', 'BOOLEAN DEFAULT 0'),
            ('stat_completeness', 'REAL'),
            ('technical_fouls', 'INTEGER'),
            ('usage_rate_game', 'REAL')
        ],
        'player_props': [
            ('back_to_back', 'BOOLEAN DEFAULT 0'),
            ('closing_line', 'REAL'),
            ('data_freshness_score', 'REAL'),
            ('edge_over', 'REAL'),
            ('edge_under', 'REAL'),
            ('fair_value', 'REAL'),
            ('home_away_split', 'REAL'),
            ('juice_over', 'INTEGER'),
            ('juice_under', 'INTEGER'),
            ('last_updated', 'DATETIME'),
            ('minutes_projection', 'REAL'),
            ('player_hit_rate_l10', 'REAL'),
            ('player_hit_rate_season', 'REAL'),
            ('player_rest_days', 'INTEGER'),
            ('public_money_percentage', 'REAL'),
            ('sharp_money_indicator', 'BOOLEAN DEFAULT 0'),
            ('usage_projection', 'REAL'),
            ('vs_opponent_performance', 'REAL')
        ],
        'game_predictions': [
            ('actual_margin', 'REAL'),
            ('actual_total', 'REAL'),
            ('confidence_score', 'REAL'),
            ('data_quality_score', 'REAL'),
            ('ev_away_ml', 'REAL'),
            ('ev_home_ml', 'REAL'),
            ('ev_over', 'REAL'),
            ('ev_spread_away', 'REAL'),
            ('ev_spread_home', 'REAL'),
            ('ev_under', 'REAL'),
            ('expected_value_over', 'REAL'),
            ('feature_count', 'INTEGER'),
            ('head_to_head_factor', 'REAL'),
            ('home_court_advantage', 'REAL'),
            ('injury_impact', 'REAL'),
            ('key_factors', 'TEXT'),
            ('matchup_rating', 'REAL'),
            ('model_agreement', 'REAL'),
            ('predicted_moneyline_away', 'INTEGER'),
            ('predicted_moneyline_home', 'INTEGER'),
            ('prediction_locked_at', 'DATETIME'),
            ('prediction_type', 'TEXT'),
            ('prediction_variance', 'REAL'),
            ('processing_time_ms', 'INTEGER'),
            ('recent_form_factor', 'REAL'),
            ('rest_impact', 'REAL'),
            ('score_accuracy', 'REAL'),
            ('situational_factors', 'TEXT'),
            ('spread_accuracy', 'REAL'),
            ('spread_cover_probability', 'REAL'),
            ('total_accuracy', 'REAL'),
            ('travel_impact', 'REAL'),
            ('win_prediction_correct', 'BOOLEAN DEFAULT 0'),
            ('win_probability_away', 'REAL'),
            ('win_probability_home', 'REAL')
        ],
        'prop_predictions': [
            ('absolute_error', 'REAL'),
            ('actual_result', 'REAL'),
            ('backtesting_accuracy', 'REAL'),
            ('bet_outcome', 'TEXT'),
            ('blowout_risk_factor', 'REAL'),
            ('calibration_error', 'REAL'),
            ('data_completeness', 'REAL'),
            ('feature_count', 'INTEGER'),
            ('feature_importance_score', 'REAL'),
            ('injury_risk_factor', 'REAL'),
            ('kelly_criterion_over', 'REAL'),
            ('kelly_criterion_under', 'REAL'),
            ('key_factors', 'TEXT'),
            ('matchup_impact', 'REAL'),
            ('model_stability', 'REAL'),
            ('optimal_bet_size_over', 'REAL'),
            ('optimal_bet_size_under', 'REAL'),
            ('pace_impact', 'REAL'),
            ('percentile_25', 'REAL'),
            ('percentile_5', 'REAL'),
            ('percentile_75', 'REAL'),
            ('percentile_95', 'REAL'),
            ('player_trend_factor', 'REAL'),
            ('prediction_error', 'REAL'),
            ('prediction_locked_at', 'DATETIME'),
            ('prediction_std_dev', 'REAL'),
            ('prediction_variance', 'REAL'),
            ('probability_score', 'REAL'),
            ('processing_time_ms', 'INTEGER'),
            ('projected_minutes', 'REAL'),
            ('projected_usage_rate', 'REAL'),
            ('recent_form_impact', 'REAL'),
            ('regression_factor', 'REAL'),
            ('rest_impact', 'REAL'),
            ('situational_performance', 'REAL'),
            ('squared_error', 'REAL'),
            ('usage_impact', 'REAL'),
            ('vs_opponent_history', 'REAL')
        ],
        'achievements': [
            ('badge_icon', 'TEXT'),
            ('created_by', 'TEXT'),
            ('is_repeatable', 'BOOLEAN DEFAULT 0'),
            ('points_awarded', 'INTEGER'),
            ('prerequisites', 'TEXT'),
            ('tier', 'TEXT'),
            ('total_unlocked', 'INTEGER DEFAULT 0'),
            ('unlock_rate', 'REAL')
        ],
        'user_achievements': [
            ('completion_time_seconds', 'INTEGER'),
            ('first_progress_at', 'DATETIME'),
            ('last_progress_at', 'DATETIME'),
            ('points_claimed', 'INTEGER'),
            ('rewards_claimed_at', 'DATETIME'),
            ('unlock_context', 'TEXT'),
            ('unlock_method', 'TEXT')
        ],
        'user_stats': [
            ('average_session_duration', 'REAL'),
            ('community_contributions', 'INTEGER DEFAULT 0'),
            ('days_active', 'INTEGER DEFAULT 0'),
            ('forum_posts', 'INTEGER DEFAULT 0'),
            ('last_activity_date', 'DATETIME'),
            ('longest_streak', 'INTEGER DEFAULT 0'),
            ('milestone_progress', 'TEXT'),
            ('milestones_completed', 'INTEGER DEFAULT 0'),
            ('monthly_bets', 'INTEGER DEFAULT 0'),
            ('monthly_winnings', 'REAL DEFAULT 0.0'),
            ('next_milestone', 'TEXT'),
            ('next_tier', 'TEXT'),
            ('percentile_rank', 'REAL'),
            ('ranking_position', 'INTEGER'),
            ('referrals_made', 'INTEGER DEFAULT 0'),
            ('stats_last_calculated', 'DATETIME'),
            ('tier_progress', 'REAL'),
            ('total_predictions', 'INTEGER DEFAULT 0'),
            ('total_sessions', 'INTEGER DEFAULT 0'),
            ('user_tier', 'TEXT'),
            ('weekly_accuracy', 'REAL')
        ],
        'vault_operations': [
            ('accuracy_score', 'REAL'),
            ('complexity_score', 'REAL'),
            ('confidence_level', 'REAL'),
            ('debug_information', 'TEXT'),
            ('expires_at', 'DATETIME'),
            ('game_ids', 'TEXT'),
            ('neural_sync_quality', 'REAL'),
            ('player_ids', 'TEXT'),
            ('prediction_ids', 'TEXT'),
            ('processing_time_ms', 'INTEGER'),
            ('quantum_coherence', 'REAL'),
            ('result_data', 'TEXT'),
            ('retry_count', 'INTEGER DEFAULT 0'),
            ('status', 'TEXT'),
            ('vault_energy_consumed', 'REAL'),
            ('vault_resonance', 'REAL')
        ]
    }

    for table_name, columns in missing_columns.items():
        for column_name, column_type in columns:
            try:
                sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}"
                cursor.execute(sql)
                columns_added += 1
            except Exception as e:
                if "duplicate column name" not in str(e).lower():

    return columns_added

def main():
    """Main execution function"""
    # Check for database file
    db_files = [
        'hyper_medusa_consolidated.db',
        'hyper_medusa_vault.db',
        'data/hyper_medusa_vault.db'
    ]
    
    db_path = None
    for path in db_files:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        return
    
    
    # Backup database
    backup_path = backup_database(db_path)
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        
        # Create missing tables
        tables_created = create_missing_tables(conn)
        
        # Add missing columns
        columns_added = add_missing_columns(conn)
        
        # Commit changes
        conn.commit()
        conn.close()
        
        
    except Exception as e:
        raise

if __name__ == "__main__":
    main()
