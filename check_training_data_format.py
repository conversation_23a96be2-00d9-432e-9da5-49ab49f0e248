#!/usr/bin/env python3
"""
Check the actual format of training data to understand if it's per-game or season totals
"""

import pandas as pd
import numpy as np
from pathlib import Path

def check_data_format():
    """Check if training data is per-game or season totals"""
    print("🔍 CHECKING TRAINING DATA FORMAT")
    print("=" * 50)
    
    # Load 2023 WNBA data
    csv_file = Path("data/wnba_10year_historical/wnba_base_stats_2023.csv")
    df = pd.read_csv(csv_file)
    
    # Look at A'ja <PERSON>'s data
    aja_data = df[df['PLAYER_NAME'].str.contains('A\'ja Wilson', na=False)]
    
    if len(aja_data) > 0:
        aja = aja_data.iloc[0]
        print(f"📊 A'ja Wilson 2023 Season:")
        print(f"   Games Played: {aja['GP']}")
        print(f"   Total Points: {aja['PTS']}")
        print(f"   Total Rebounds: {aja['REB']}")
        print(f"   Total Assists: {aja['AST']}")
        print(f"   Total Steals: {aja['STL']}")
        print(f"   Total Blocks: {aja['BLK']}")
        print(f"   Total 3PM: {aja['FG3M']}")
        
        print(f"\n📈 Per-Game Averages:")
        print(f"   Points per game: {aja['PTS'] / aja['GP']:.1f}")
        print(f"   Rebounds per game: {aja['REB'] / aja['GP']:.1f}")
        print(f"   Assists per game: {aja['AST'] / aja['GP']:.1f}")
        print(f"   Steals per game: {aja['STL'] / aja['GP']:.1f}")
        print(f"   Blocks per game: {aja['BLK'] / aja['GP']:.1f}")
        print(f"   3PM per game: {aja['FG3M'] / aja['GP']:.1f}")
        
        print(f"\n🎯 Expected vs Current Predictions:")
        print(f"   Expected Points: {aja['PTS'] / aja['GP']:.1f} PPG")
        print(f"   Current Prediction: 1.3 PPG ❌ (WAY TOO LOW)")
        
        print(f"\n🔍 Analysis:")
        print(f"   The training data contains SEASON TOTALS")
        print(f"   But our scaling analysis treated them as individual values")
        print(f"   We need to determine what the models actually learned")
        
        # Check what the scaling parameters represent
        print(f"\n📊 Scaling Analysis:")
        print(f"   Training mean for points: 40.051")
        print(f"   A'ja's actual season total: {aja['PTS']}")
        print(f"   A'ja's per-game average: {aja['PTS'] / aja['GP']:.1f}")
        
        # The training mean of 40.051 is much closer to per-game averages than season totals
        # This suggests the models were trained on per-game data, not season totals
        
    else:
        print("❌ A'ja Wilson not found in 2023 data")
    
    # Check a few more players
    print(f"\n📊 Sample of other players (first 5):")
    for i in range(min(5, len(df))):
        player = df.iloc[i]
        ppg = player['PTS'] / player['GP'] if player['GP'] > 0 else 0
        rpg = player['REB'] / player['GP'] if player['GP'] > 0 else 0
        print(f"   {player['PLAYER_NAME']}: {ppg:.1f} PPG, {rpg:.1f} RPG")

if __name__ == "__main__":
    check_data_format()
