import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
import pandas as pd
import numpy as np
            from src.integrations.live_realtime_data_integrator import create_live_realtime_data_integrator
            from src.integrations.odds_prediction_integrator import create_odds_prediction_integrator


#!/usr/bin/env python3
"""
Live Dynamic Predictor - HYPER MEDUSA NEURAL VAULT
=================================================

Real-time prediction updates that adapt as games unfold, providing:
- Updated win probability based on live game state
- First basket predictions
- Quarter-by-quarter predictions
- Halftime adjustments
- Live prop bet opportunities
- Momentum-based prediction shifts
"""


# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class LivePredictionUpdate:
    """Live prediction update with confidence and reasoning"""
    
    # Prediction details
    prediction_type: str  # "win_probability", "first_basket", "quarter_winner", etc.
    current_prediction: float
    pregame_prediction: float
    confidence_change: float
    
    # Game context
    game_time: str
    period: int
    score_differential: int
    
    # Reasoning
    key_factors: List[str]
    momentum_indicator: float
    prediction_shift: float  # How much prediction changed
    
    # Betting intelligence
    recommended_action: str  # "HOLD", "HEDGE", "DOUBLE_DOWN", "NEW_BET"
    edge_opportunity: float
    
    # Timestamps
    timestamp: datetime = field(default_factory=datetime.utcnow)

@dataclass
class LiveBettingOpportunity:
    """Live betting opportunity identified during game"""
    
    opportunity_type: str  # "first_basket", "quarter_winner", "halftime_total", etc.
    prediction: float
    confidence: float
    current_odds: Optional[float]
    edge_percentage: float
    
    # Context
    game_situation: str
    time_sensitive: bool
    expiry_time: Optional[datetime]
    
    # Recommendation
    bet_recommendation: str
    stake_percentage: float  # Kelly criterion
    expected_roi: float

class LiveDynamicPredictor:
    """
    Dynamic prediction system that updates predictions in real-time
    based on live game data and momentum shifts
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.live_predictions: Dict[str, Dict] = {}
        self.pregame_predictions: Dict[str, Dict] = {}
        self.momentum_history: Dict[str, List[float]] = {}
        
        # Initialize components
        self.live_integrator = None
        self.odds_integrator = None
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize live data and prediction components"""
        try:
            
            self.live_integrator = create_live_realtime_data_integrator()
            self.odds_integrator = create_odds_prediction_integrator()
            
            self.logger.info("✅ Live dynamic predictor components initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize components: {e}")
    
    async def store_pregame_predictions(self, titan_clash_id: str, predictions: Dict[str, Any]):
        """Store pregame predictions for comparison with live updates"""
        self.pregame_predictions[titan_clash_id] = {
            'win_probability': predictions.get('win_probability', 0.5),
            'total_points': predictions.get('total_points', 220),
            'spread_prediction': predictions.get('spread_prediction', 0),
            'first_basket_player': predictions.get('first_basket_player', None),
            'quarter_winners': predictions.get('quarter_winners', [0.5, 0.5, 0.5, 0.5]),
            'halftime_leader': predictions.get('halftime_leader', 0.5),
            'timestamp': datetime.utcnow()
        }
        
        self.logger.info(f"📊 Stored pregame predictions for {titan_clash_id}")
    
    async def update_live_predictions(self, titan_clash_id: str) -> List[LivePredictionUpdate]:
        """Update predictions based on current live game state"""
        try:
            self.logger.info(f"🔄 Updating live predictions for {titan_clash_id}")
            
            # Get comprehensive live data
            live_data = await self.live_integrator.get_comprehensive_live_data(titan_clash_id)
            
            if not live_data or not live_data.get('game_state'):
                self.logger.warning(f"⚠️ No live data available for {titan_clash_id}")
                return []
            
            game_state = live_data['game_state']
            play_by_play = live_data.get('play_by_play', [])
            boxscore = live_data.get('boxscore', [])
            
            prediction_updates = []
            
            # 1. Update win probability
            win_update = await self._update_win_probability(titan_clash_id, game_state, play_by_play)
            if win_update:
                prediction_updates.append(win_update)
            
            # 2. Update quarter predictions
            quarter_updates = await self._update_quarter_predictions(titan_clash_id, game_state, play_by_play)
            prediction_updates.extend(quarter_updates)
            
            # 3. Check for first basket opportunities
            first_basket_update = await self._check_first_basket_opportunity(titan_clash_id, game_state, play_by_play)
            if first_basket_update:
                prediction_updates.append(first_basket_update)
            
            # 4. Update halftime predictions
            halftime_update = await self._update_halftime_predictions(titan_clash_id, game_state, boxscore)
            if halftime_update:
                prediction_updates.append(halftime_update)
            
            # Store current predictions
            self.live_predictions[titan_clash_id] = {
                'updates': prediction_updates,
                'last_updated': datetime.utcnow(),
                'game_state': game_state
            }
            
            self.logger.info(f"✅ Generated {len(prediction_updates)} live prediction updates")
            return prediction_updates
            
        except Exception as e:
            self.logger.error(f"❌ Failed to update live predictions: {e}")
            return []
    
    async def _update_win_probability(self, titan_clash_id: str, game_state: Dict, plays: List[Dict]) -> Optional[LivePredictionUpdate]:
        """Update win probability based on current game state"""
        try:
            pregame = self.pregame_predictions.get(titan_clash_id, {})
            pregame_win_prob = pregame.get('win_probability', 0.5)
            
            # Calculate current win probability based on:
            # 1. Score differential
            # 2. Time remaining
            # 3. Recent momentum
            
            home_score = game_state.get('home_score', 0)
            away_score = game_state.get('away_score', 0)
            score_diff = home_score - away_score
            period = game_state.get('period', 1)
            
            # Simple win probability model (can be enhanced with ML)
            time_factor = self._calculate_time_factor(period, game_state.get('time_remaining', '12:00'))
            score_factor = self._calculate_score_factor(score_diff, time_factor)
            momentum_factor = self._calculate_momentum_factor(plays[-10:] if len(plays) >= 10 else plays)
            
            # Combine factors
            current_win_prob = 0.5 + (score_factor * 0.4) + (momentum_factor * 0.2)
            current_win_prob = max(0.05, min(0.95, current_win_prob))  # Clamp between 5% and 95%
            
            prediction_shift = current_win_prob - pregame_win_prob
            confidence_change = abs(prediction_shift) * 2  # Higher shift = higher confidence in change
            
            # Determine recommended action
            if abs(prediction_shift) > 0.15:  # Significant shift
                if prediction_shift > 0:
                    recommended_action = "DOUBLE_DOWN" if pregame_win_prob > 0.5 else "HEDGE"
                else:
                    recommended_action = "HEDGE" if pregame_win_prob > 0.5 else "DOUBLE_DOWN"
            else:
                recommended_action = "HOLD"
            
            return LivePredictionUpdate(
                prediction_type="win_probability",
                current_prediction=current_win_prob,
                pregame_prediction=pregame_win_prob,
                confidence_change=confidence_change,
                game_time=game_state.get('time_remaining', ''),
                period=period,
                score_differential=score_diff,
                key_factors=[
                    f"Score differential: {score_diff}",
                    f"Time factor: {time_factor:.2f}",
                    f"Momentum: {momentum_factor:+.2f}"
                ],
                momentum_indicator=momentum_factor,
                prediction_shift=prediction_shift,
                recommended_action=recommended_action,
                edge_opportunity=abs(prediction_shift) * 100
            )
            
        except Exception as e:
            self.logger.error(f"❌ Failed to update win probability: {e}")
            return None
    
    async def _update_quarter_predictions(self, titan_clash_id: str, game_state: Dict, plays: List[Dict]) -> List[LivePredictionUpdate]:
        """Update quarter-by-quarter predictions"""
        updates = []
        
        try:
            period = game_state.get('period', 1)
            time_remaining = game_state.get('time_remaining', '12:00')
            
            # Only predict current quarter if there's still time
            if period <= 4 and self._parse_time_remaining(time_remaining) > 0:
                # Calculate quarter winner probability
                quarter_momentum = self._calculate_quarter_momentum(plays, period)
                quarter_score_trend = self._calculate_quarter_score_trend(plays, period)
                
                current_quarter_prob = 0.5 + (quarter_momentum * 0.3) + (quarter_score_trend * 0.2)
                current_quarter_prob = max(0.1, min(0.9, current_quarter_prob))
                
                pregame_quarter_prob = self.pregame_predictions.get(titan_clash_id, {}).get('quarter_winners', [0.5, 0.5, 0.5, 0.5])[period-1]
                
                update = LivePredictionUpdate(
                    prediction_type=f"quarter_{period}_winner",
                    current_prediction=current_quarter_prob,
                    pregame_prediction=pregame_quarter_prob,
                    confidence_change=abs(current_quarter_prob - pregame_quarter_prob),
                    game_time=time_remaining,
                    period=period,
                    score_differential=game_state.get('home_score', 0) - game_state.get('away_score', 0),
                    key_factors=[
                        f"Quarter momentum: {quarter_momentum:+.2f}",
                        f"Score trend: {quarter_score_trend:+.2f}"
                    ],
                    momentum_indicator=quarter_momentum,
                    prediction_shift=current_quarter_prob - pregame_quarter_prob,
                    recommended_action="NEW_BET" if abs(current_quarter_prob - pregame_quarter_prob) > 0.2 else "HOLD",
                    edge_opportunity=abs(current_quarter_prob - pregame_quarter_prob) * 100
                )
                
                updates.append(update)
            
        except Exception as e:
            self.logger.error(f"❌ Failed to update quarter predictions: {e}")
        
        return updates
    
    async def _check_first_basket_opportunity(self, titan_clash_id: str, game_state: Dict, plays: List[Dict]) -> Optional[LivePredictionUpdate]:
        """Check for first basket betting opportunities"""
        try:
            # Only relevant at start of game
            period = game_state.get('period', 1)
            time_remaining = game_state.get('time_remaining', '12:00')
            
            if period == 1 and self._parse_time_remaining(time_remaining) > 10*60:  # More than 10 minutes left in Q1
                # Analyze opening plays and player positioning
                opening_plays = plays[:5] if plays else []
                
                # Simple first basket prediction based on opening momentum
                if opening_plays:
                    home_momentum = sum(1 for play in opening_plays if play.get('team') == 'home' and 'Made' in play.get('description', ''))
                    away_momentum = sum(1 for play in opening_plays if play.get('team') == 'away' and 'Made' in play.get('description', ''))
                    
                    if home_momentum > away_momentum:
                        first_basket_prob = 0.65
                        key_factor = "Home team showing early shooting success"
                    elif away_momentum > home_momentum:
                        first_basket_prob = 0.35
                        key_factor = "Away team showing early shooting success"
                    else:
                        first_basket_prob = 0.5
                        key_factor = "Even early game momentum"
                    
                    pregame_first_basket = self.pregame_predictions.get(titan_clash_id, {}).get('first_basket_player', 0.5)
                    
                    return LivePredictionUpdate(
                        prediction_type="first_basket",
                        current_prediction=first_basket_prob,
                        pregame_prediction=pregame_first_basket,
                        confidence_change=0.3,  # First basket is always somewhat uncertain
                        game_time=time_remaining,
                        period=period,
                        score_differential=0,  # Usually 0-0 at start
                        key_factors=[key_factor],
                        momentum_indicator=home_momentum - away_momentum,
                        prediction_shift=first_basket_prob - pregame_first_basket,
                        recommended_action="NEW_BET" if abs(first_basket_prob - 0.5) > 0.1 else "HOLD",
                        edge_opportunity=abs(first_basket_prob - 0.5) * 100
                    )
            
        except Exception as e:
            self.logger.error(f"❌ Failed to check first basket opportunity: {e}")
        
        return None
    
    async def _update_halftime_predictions(self, titan_clash_id: str, game_state: Dict, boxscore: List[Dict]) -> Optional[LivePredictionUpdate]:
        """Update halftime predictions based on first half performance"""
        try:
            period = game_state.get('period', 1)
            
            # Only relevant approaching or at halftime
            if period == 2:
                time_remaining = game_state.get('time_remaining', '12:00')
                time_left = self._parse_time_remaining(time_remaining)
                
                if time_left < 5*60:  # Less than 5 minutes left in Q2
                    home_score = game_state.get('home_score', 0)
                    away_score = game_state.get('away_score', 0)
                    
                    # Predict halftime leader
                    score_diff = home_score - away_score
                    time_factor = time_left / (5*60)  # Normalize to 0-1
                    
                    # Simple halftime prediction
                    halftime_prob = 0.5 + (score_diff / 20) * (1 - time_factor)  # Less time = more certain
                    halftime_prob = max(0.1, min(0.9, halftime_prob))
                    
                    pregame_halftime = self.pregame_predictions.get(titan_clash_id, {}).get('halftime_leader', 0.5)
                    
                    return LivePredictionUpdate(
                        prediction_type="halftime_leader",
                        current_prediction=halftime_prob,
                        pregame_prediction=pregame_halftime,
                        confidence_change=1 - time_factor,  # More confident as time runs out
                        game_time=time_remaining,
                        period=period,
                        score_differential=score_diff,
                        key_factors=[
                            f"Current score differential: {score_diff}",
                            f"Time remaining in half: {time_left//60:.0f}:{time_left%60:02.0f}"
                        ],
                        momentum_indicator=score_diff / 10,
                        prediction_shift=halftime_prob - pregame_halftime,
                        recommended_action="NEW_BET" if abs(halftime_prob - pregame_halftime) > 0.2 else "HOLD",
                        edge_opportunity=abs(halftime_prob - pregame_halftime) * 100
                    )
            
        except Exception as e:
            self.logger.error(f"❌ Failed to update halftime predictions: {e}")
        
        return None
    
    async def identify_live_betting_opportunities(self, titan_clash_id: str) -> List[LiveBettingOpportunity]:
        """Identify specific live betting opportunities"""
        opportunities = []
        
        try:
            # Get latest prediction updates
            prediction_updates = await self.update_live_predictions(titan_clash_id)
            
            for update in prediction_updates:
                # Convert prediction updates to betting opportunities
                if update.edge_opportunity > 5.0:  # 5% edge threshold
                    
                    # Determine opportunity type and timing
                    time_sensitive = update.prediction_type in ["first_basket", "quarter_winner"]
                    expiry_time = None
                    
                    if time_sensitive:
                        # Calculate when opportunity expires
                        if update.prediction_type == "first_basket":
                            expiry_time = datetime.utcnow() + timedelta(minutes=5)  # First few minutes
                        elif "quarter" in update.prediction_type:
                            expiry_time = datetime.utcnow() + timedelta(minutes=12)  # End of quarter
                    
                    # Calculate Kelly criterion stake
                    edge_decimal = update.edge_opportunity / 100
                    kelly_percentage = min(0.25, edge_decimal * 0.5)  # Conservative Kelly
                    
                    opportunity = LiveBettingOpportunity(
                        opportunity_type=update.prediction_type,
                        prediction=update.current_prediction,
                        confidence=update.confidence_change,
                        current_odds=None,  # Would get from odds API
                        edge_percentage=update.edge_opportunity,
                        game_situation=f"Q{update.period} {update.game_time}",
                        time_sensitive=time_sensitive,
                        expiry_time=expiry_time,
                        bet_recommendation=update.recommended_action,
                        stake_percentage=kelly_percentage,
                        expected_roi=update.edge_opportunity * kelly_percentage
                    )
                    
                    opportunities.append(opportunity)
            
            self.logger.info(f"✅ Identified {len(opportunities)} live betting opportunities")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to identify betting opportunities: {e}")
        
        return opportunities
    
    def _calculate_time_factor(self, period: int, time_remaining: str) -> float:
        """Calculate how much time affects win probability"""
        total_seconds = self._parse_time_remaining(time_remaining)
        period_seconds = period * 12 * 60  # Periods completed
        game_seconds = period_seconds + (12*60 - total_seconds)  # Total game time elapsed
        
        # Game is 48 minutes = 2880 seconds
        game_progress = game_seconds / 2880
        return min(1.0, game_progress)
    
    def _calculate_score_factor(self, score_diff: int, time_factor: float) -> float:
        """Calculate how score differential affects win probability"""
        # Score becomes more important as time progresses
        importance = 0.3 + (time_factor * 0.7)
        return (score_diff / 20) * importance  # Normalize by typical score difference
    
    def _calculate_momentum_factor(self, recent_plays: List[Dict]) -> float:
        """Calculate momentum from recent plays"""
        if not recent_plays:
            return 0.0
        
        momentum = 0.0
        for play in recent_plays:
            if play.get('momentum_change'):
                momentum += play['momentum_change']
        
        return momentum / len(recent_plays) if recent_plays else 0.0
    
    def _calculate_quarter_momentum(self, plays: List[Dict], period: int) -> float:
        """Calculate momentum for specific quarter"""
        quarter_plays = [p for p in plays if p.get('period') == period]
        return self._calculate_momentum_factor(quarter_plays)
    
    def _calculate_quarter_score_trend(self, plays: List[Dict], period: int) -> float:
        """Calculate scoring trend for specific quarter"""
        quarter_plays = [p for p in plays if p.get('period') == period]
        
        if not quarter_plays:
            return 0.0
        
        # Simple trend: recent plays vs early plays in quarter
        mid_point = len(quarter_plays) // 2
        early_momentum = self._calculate_momentum_factor(quarter_plays[:mid_point])
        recent_momentum = self._calculate_momentum_factor(quarter_plays[mid_point:])
        
        return recent_momentum - early_momentum
    
    def _parse_time_remaining(self, time_str: str) -> int:
        """Parse time remaining string to seconds"""
        try:
            if ':' in time_str:
                minutes, seconds = time_str.split(':')
                return int(minutes) * 60 + int(seconds)
            return 0
        except:
            return 0

# Factory function for easy import
def create_live_dynamic_predictor() -> LiveDynamicPredictor:
    """Create and return a LiveDynamicPredictor instance"""
    return LiveDynamicPredictor()
