# Production Environment Configuration - HYPER MEDUSA NEURAL VAULT
# =================================================================
# Production-optimized settings with enhanced security and performance

database:
  host: "${DB_HOST}"
  port: 5432
  name: "hyper_medusa_vault_prod"
  user: "${DB_USER}"
  password: "${DB_PASSWORD}"
  pool_size: 50
  max_overflow: 100
  pool_timeout: 60
  pool_recycle: 7200  # 2 hours
  echo: false  # Disable SQL logging in production

redis:
  host: "${REDIS_HOST}"
  port: 6379
  db: 0
  password: "${REDIS_PASSWORD}"
  max_connections: 100
  socket_timeout: 10
  socket_connect_timeout: 10

api:
  host: "0.0.0.0"
  port: 8000
  workers: 8  # Multiple workers for production
  timeout: 30
  max_requests: 10000
  max_requests_jitter: 100
  preload_app: true
  reload: false
  debug: false
  cors_origins:
    - "https://hypermedusa.ai"
    - "https://api.hypermedusa.ai"
    - "https://app.hypermedusa.ai"
  rate_limit: "100/minute"

ml:
  model_path: "/app/models/production"
  batch_size: 64  # Larger batch size for production
  learning_rate: 0.0001  # Conservative learning rate
  epochs: 200
  validation_split: 0.15
  early_stopping_patience: 20
  device: "cuda"  # Use GPU in production
  mixed_precision: true
  gradient_clipping: 1.0
  checkpoint_interval: 25

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "/var/log/hyper_medusa/production.log"
  max_bytes: 52428800  # 50MB
  backup_count: 10
  json_format: true  # Structured logging for production
  structured_logging: true

security:
  secret_key: "${SECRET_KEY}"
  jwt_algorithm: "HS256"
  jwt_expiration: 3600  # 1 hour
  password_min_length: 12  # Strong passwords required
  max_login_attempts: 3
  lockout_duration: 1800  # 30 minutes
  encryption_key: "${ENCRYPTION_KEY}"
  cors_enabled: true
  csrf_protection: true

monitoring:
  prometheus_enabled: true
  prometheus_port: 9090
  grafana_enabled: true
  grafana_port: 3000
  health_check_interval: 15  # Frequent health checks
  metrics_retention_days: 90  # Longer retention for production
  alerting_enabled: true
  slack_webhook: "${SLACK_WEBHOOK_URL}"
  email_alerts: true
