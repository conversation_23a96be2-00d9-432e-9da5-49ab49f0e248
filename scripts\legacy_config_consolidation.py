import os
import sys
import json
import shutil
import logging
from pathlib import Path
from typing import Dict, List, Set, Any, Optional
from datetime import datetime
import re
            import yaml
            import tomli
            import tomli_w


# ⚠️  DEPRECATED CONFIGURATION FILE ⚠️
#
# This configuration file has been deprecated and consolidated into the
# unified configuration system at: config/unified/production_config.toml
#
# Original file archived at: config\legacy_archive\20250703_234346\scripts\legacy_config_consolidation.py
# Consolidation date: 2025-07-03T23:43:46.223360
#
# Please update your code to use:
# from kingdom.config.unified_config_system import UnifiedConfigSystem
# config = UnifiedConfigSystem().get_config()
#
# This file will be removed in a future release.
#

#!/usr/bin/env python3
"""
Legacy Configuration Consolidation System
========================================

Consolidates all redundant and legacy configuration files into a single
production configuration system for HYPER MEDUSA NEURAL VAULT.

This script:
1. Identifies all configuration files throughout the codebase
2. Analyzes their content and purpose
3. Consolidates redundant configurations
4. Creates a unified production configuration system
5. Updates all references to use the new system
6. Archives legacy configuration files
"""


# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class LegacyConfigConsolidator:
    """Consolidates legacy configuration files into unified production system"""
    
    def __init__(self, workspace_root: str = "."):
        self.workspace_root = Path(workspace_root).resolve()
        self.config_files: List[Path] = []
        self.legacy_configs: Dict[str, Dict] = {}
        self.unified_config: Dict[str, Any] = {}
        self.consolidation_report: Dict[str, Any] = {
            'timestamp': datetime.now().isoformat(),
            'files_analyzed': 0,
            'files_consolidated': 0,
            'files_archived': 0,
            'references_updated': 0,
            'legacy_systems': [],
            'unified_config_created': False
        }
        
    def run_consolidation(self) -> Dict[str, Any]:
        """Run complete configuration consolidation process"""
        logger.info("🔧 Starting Legacy Configuration Consolidation...")
        
        try:
            # Step 1: Discover all configuration files
            self._discover_config_files()
            
            # Step 2: Analyze configuration content
            self._analyze_config_files()
            
            # Step 3: Create unified configuration
            self._create_unified_config()
            
            # Step 4: Update code references
            self._update_code_references()
            
            # Step 5: Archive legacy files
            self._archive_legacy_files()
            
            # Step 6: Generate consolidation report
            self._generate_report()
            
            logger.info("✅ Legacy Configuration Consolidation Complete!")
            return self.consolidation_report
            
        except Exception as e:
            logger.error(f"❌ Configuration consolidation failed: {e}")
            self.consolidation_report['error'] = str(e)
            return self.consolidation_report
    
    def _discover_config_files(self):
        """Discover all configuration files in the workspace"""
        logger.info("🔍 Discovering configuration files...")
        
        # Configuration file patterns
        config_patterns = [
            "*.yaml", "*.yml", "*.toml", "*.json", "*.ini", "*.cfg",
            "*config*.py", "*settings*.py", ".env*"
        ]
        
        # Exclude patterns
        exclude_patterns = [
            r"__pycache__", r"\.git", r"node_modules", r"\.venv",
            r"logs/", r"temp/", r"cache/", r"\.pytest_cache"
        ]
        
        for pattern in config_patterns:
            for file_path in self.workspace_root.rglob(pattern):
                # Skip excluded directories
                if any(re.search(excl, str(file_path)) for excl in exclude_patterns):
                    continue
                
                # Skip non-configuration files
                if self._is_config_file(file_path):
                    self.config_files.append(file_path)
        
        self.consolidation_report['files_analyzed'] = len(self.config_files)
        logger.info(f"📁 Found {len(self.config_files)} configuration files")
    
    def _is_config_file(self, file_path: Path) -> bool:
        """Check if file is actually a configuration file"""
        try:
            # Check file name patterns
            config_indicators = [
                'config', 'settings', 'environment', 'production', 'development',
                'testing', '.env', 'vault_config', 'base', 'unified'
            ]
            
            file_name_lower = file_path.name.lower()
            if any(indicator in file_name_lower for indicator in config_indicators):
                return True
            
            # Check file content for configuration patterns
            if file_path.suffix in ['.py']:
                content = file_path.read_text(encoding='utf-8', errors='ignore')
                config_patterns = [
                    r'class.*Config', r'DATABASE_URL', r'API_KEY', r'SECRET_KEY',
                    r'ENVIRONMENT', r'DEBUG', r'HOST.*=', r'PORT.*='
                ]
                if any(re.search(pattern, content, re.IGNORECASE) for pattern in config_patterns):
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _analyze_config_files(self):
        """Analyze configuration files and categorize them"""
        logger.info("📊 Analyzing configuration content...")
        
        for config_file in self.config_files:
            try:
                config_info = self._extract_config_info(config_file)
                if config_info:
                    self.legacy_configs[str(config_file)] = config_info
                    
            except Exception as e:
                logger.warning(f"⚠️ Failed to analyze {config_file}: {e}")
        
        # Identify legacy systems
        self._identify_legacy_systems()
    
    def _extract_config_info(self, config_file: Path) -> Optional[Dict]:
        """Extract configuration information from file"""
        try:
            config_info = {
                'path': str(config_file),
                'type': self._get_config_type(config_file),
                'size': config_file.stat().st_size,
                'modified': datetime.fromtimestamp(config_file.stat().st_mtime).isoformat(),
                'content': {},
                'purpose': self._determine_purpose(config_file),
                'legacy': self._is_legacy_config(config_file)
            }
            
            # Extract content based on file type
            if config_file.suffix in ['.yaml', '.yml']:
                config_info['content'] = self._parse_yaml_content(config_file)
            elif config_file.suffix == '.toml':
                config_info['content'] = self._parse_toml_content(config_file)
            elif config_file.suffix == '.json':
                config_info['content'] = self._parse_json_content(config_file)
            elif config_file.suffix == '.py':
                config_info['content'] = self._parse_python_config(config_file)
            elif config_file.name.startswith('.env'):
                config_info['content'] = self._parse_env_content(config_file)
            
            return config_info
            
        except Exception as e:
            logger.warning(f"Failed to extract info from {config_file}: {e}")
            return None
    
    def _get_config_type(self, config_file: Path) -> str:
        """Determine configuration file type"""
        if 'production' in config_file.name.lower():
            return 'production'
        elif 'development' in config_file.name.lower():
            return 'development'
        elif 'testing' in config_file.name.lower():
            return 'testing'
        elif 'base' in config_file.name.lower():
            return 'base'
        elif '.env' in config_file.name:
            return 'environment'
        else:
            return 'general'
    
    def _determine_purpose(self, config_file: Path) -> str:
        """Determine the purpose of the configuration file"""
        path_str = str(config_file).lower()
        
        if 'backend' in path_str:
            return 'backend_api'
        elif 'vault_oracle' in path_str:
            return 'vault_oracle'
        elif 'kingdom' in path_str:
            return 'kingdom_architecture'
        elif 'database' in path_str or 'db' in path_str:
            return 'database'
        elif 'prometheus' in path_str or 'monitoring' in path_str:
            return 'monitoring'
        elif 'security' in path_str or 'auth' in path_str:
            return 'security'
        elif 'ml' in path_str or 'model' in path_str:
            return 'machine_learning'
        else:
            return 'general'
    
    def _is_legacy_config(self, config_file: Path) -> bool:
        """Check if configuration file is legacy"""
        legacy_indicators = [
            'legacy', 'old', 'backup', 'deprecated', 'archive',
            'vault_config.py', 'production_config.py', 'settings.py'
        ]
        
        file_name_lower = config_file.name.lower()
        return any(indicator in file_name_lower for indicator in legacy_indicators)
    
    def _parse_yaml_content(self, config_file: Path) -> Dict:
        """Parse YAML configuration content"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception:
            return {}
    
    def _parse_toml_content(self, config_file: Path) -> Dict:
        """Parse TOML configuration content"""
        try:
            with open(config_file, 'rb') as f:
                return tomli.load(f)
        except Exception:
            return {}
    
    def _parse_json_content(self, config_file: Path) -> Dict:
        """Parse JSON configuration content"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return {}
    
    def _parse_python_config(self, config_file: Path) -> Dict:
        """Parse Python configuration content"""
        try:
            content = config_file.read_text(encoding='utf-8')
            
            # Extract configuration variables
            config_vars = {}
            
            # Look for class-based configs
            class_match = re.search(r'class\s+(\w*Config\w*)', content, re.IGNORECASE)
            if class_match:
                config_vars['config_class'] = class_match.group(1)
            
            # Look for variable assignments
            var_patterns = [
                r'(\w+)\s*=\s*["\']([^"\']+)["\']',  # String assignments
                r'(\w+)\s*=\s*(\d+)',  # Number assignments
                r'(\w+)\s*=\s*(True|False)',  # Boolean assignments
            ]
            
            for pattern in var_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for var_name, var_value in matches:
                    if any(keyword in var_name.upper() for keyword in 
                          ['HOST', 'PORT', 'URL', 'KEY', 'SECRET', 'DEBUG', 'ENV']):
                        config_vars[var_name] = var_value
            
            return config_vars
            
        except Exception:
            return {}
    
    def _parse_env_content(self, config_file: Path) -> Dict:
        """Parse environment file content"""
        try:
            content = config_file.read_text(encoding='utf-8')
            env_vars = {}
            
            for line in content.split('\n'):
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
            
            return env_vars
            
        except Exception:
            return {}
    
    def _identify_legacy_systems(self):
        """Identify legacy configuration systems"""
        legacy_systems = set()
        
        for config_path, config_info in self.legacy_configs.items():
            if config_info['legacy'] or config_info['purpose'] in ['vault_oracle', 'backend_api']:
                system_name = f"{config_info['purpose']}_{config_info['type']}"
                legacy_systems.add(system_name)
        
        self.consolidation_report['legacy_systems'] = list(legacy_systems)
        logger.info(f"🏗️ Identified {len(legacy_systems)} legacy configuration systems")

    def _create_unified_config(self):
        """Create unified configuration from all legacy configs"""
        logger.info("🔧 Creating unified configuration...")

        # Initialize unified config structure
        self.unified_config = {
            'system': {
                'environment': 'production',
                'debug': False,
                'log_level': 'INFO'
            },
            'database': {
                'provider': 'postgresql',
                'host': '${DB_HOST:localhost}',
                'port': '${DB_PORT:5432}',
                'name': '${DB_NAME:hyper_medusa}',
                'user': '${DB_USER:medusa}',
                'password': '${DB_PASSWORD:}',
                'pool_size': 10,
                'timeout': 30
            },
            'redis': {
                'host': '${REDIS_HOST:localhost}',
                'port': '${REDIS_PORT:6379}',
                'password': '${REDIS_PASSWORD:}',
                'enabled': True
            },
            'api': {
                'host': '${API_HOST:0.0.0.0}',
                'port': '${API_PORT:8000}',
                'prefix': '/api/v1',
                'cors_enabled': True
            },
            'basketball': {
                'nba_enabled': True,
                'wnba_enabled': True,
                'data_sources': ['nba_api', 'wnba_api'],
                'prediction_confidence_threshold': 0.65
            },
            'models': {
                'nba_model_path': '${MODELS_DIR:./models}/nba/ensemble_v1.pkl',
                'wnba_model_path': '${MODELS_DIR:./models}/wnba/ensemble_v1.pkl',
                'retraining_enabled': True
            },
            'security': {
                'secret_key': '${SECRET_KEY:}',
                'jwt_algorithm': 'HS256',
                'jwt_expiration': 3600
            },
            'monitoring': {
                'prometheus_enabled': True,
                'grafana_enabled': True,
                'health_check_interval': 30
            }
        }

        # Merge configurations from legacy files
        for config_path, config_info in self.legacy_configs.items():
            self._merge_config_content(config_info)

        # Save unified configuration
        self._save_unified_config()
        self.consolidation_report['unified_config_created'] = True
        logger.info("✅ Unified configuration created successfully")

    def _merge_config_content(self, config_info: Dict):
        """Merge content from legacy configuration into unified config"""
        try:
            content = config_info.get('content', {})
            purpose = config_info.get('purpose', 'general')

            # Map legacy config to unified structure
            if purpose == 'database':
                if 'host' in content:
                    self.unified_config['database']['host'] = content['host']
                if 'port' in content:
                    self.unified_config['database']['port'] = str(content['port'])
                if 'name' in content or 'database' in content:
                    db_name = content.get('name', content.get('database', ''))
                    if db_name:
                        self.unified_config['database']['name'] = db_name

            elif purpose == 'backend_api':
                if 'HOST' in content:
                    self.unified_config['api']['host'] = content['HOST']
                if 'PORT' in content:
                    self.unified_config['api']['port'] = str(content['PORT'])
                if 'DEBUG' in content:
                    self.unified_config['system']['debug'] = content['DEBUG'] == 'True'

            elif purpose == 'security':
                if 'SECRET_KEY' in content:
                    self.unified_config['security']['secret_key'] = content['SECRET_KEY']
                if 'JWT_ALGORITHM' in content:
                    self.unified_config['security']['jwt_algorithm'] = content['JWT_ALGORITHM']

        except Exception as e:
            logger.warning(f"Failed to merge config content: {e}")

    def _save_unified_config(self):
        """Save unified configuration to file"""
        try:
            # Create unified config directory
            unified_config_dir = self.workspace_root / "config" / "unified"
            unified_config_dir.mkdir(parents=True, exist_ok=True)

            # Save as TOML (primary format)
            unified_config_file = unified_config_dir / "production_config.toml"

            with open(unified_config_file, 'wb') as f:
                tomli_w.dump(self.unified_config, f)

            # Save as YAML (backup format)
            yaml_config_file = unified_config_dir / "production_config.yaml"
            with open(yaml_config_file, 'w') as f:
                yaml.dump(self.unified_config, f, default_flow_style=False)

            logger.info(f"💾 Unified configuration saved to {unified_config_file}")

        except Exception as e:
            logger.error(f"Failed to save unified configuration: {e}")

    def _update_code_references(self):
        """Update code references to use unified configuration"""
        logger.info("🔄 Updating code references...")

        # Find Python files that import legacy configs
        python_files = list(self.workspace_root.rglob("*.py"))
        updated_files = 0

        for py_file in python_files:
            try:
                if self._update_file_references(py_file):
                    updated_files += 1
            except Exception as e:
                logger.warning(f"Failed to update {py_file}: {e}")

        self.consolidation_report['references_updated'] = updated_files
        logger.info(f"📝 Updated {updated_files} files with new configuration references")

    def _update_file_references(self, file_path: Path) -> bool:
        """Update configuration references in a single file"""
        try:
            content = file_path.read_text(encoding='utf-8')
            original_content = content

            # Replace legacy imports
            replacements = [
                (r'from vault_oracle\.core\.config\.config_loader import.*',
                 'from kingdom.config.unified_config_system import UnifiedConfigSystem'),
                (r'from backend\.config\.vault_config import.*',
                 'from kingdom.config.unified_config_system import UnifiedConfigSystem'),
                (r'from src\.autonomous\.autonomous_config_manager import.*',
                 'from kingdom.config.unified_config_system import UnifiedConfigSystem'),
                (r'config_loader\.load_config\(\)',
                 'UnifiedConfigSystem().get_config()'),
                (r'VaultConfig\.load_config\(\)',
                 'UnifiedConfigSystem().get_config()'),
            ]

            for pattern, replacement in replacements:
                content = re.sub(pattern, replacement, content, flags=re.MULTILINE)

            # Save if changed
            if content != original_content:
                file_path.write_text(content, encoding='utf-8')
                return True

            return False

        except Exception:
            return False

    def _archive_legacy_files(self):
        """Archive legacy configuration files"""
        logger.info("📦 Archiving legacy configuration files...")

        # Create archive directory
        archive_dir = self.workspace_root / "config" / "legacy_archive" / datetime.now().strftime('%Y%m%d_%H%M%S')
        archive_dir.mkdir(parents=True, exist_ok=True)

        archived_files = 0

        for config_path, config_info in self.legacy_configs.items():
            if config_info['legacy']:
                try:
                    source_path = Path(config_path)
                    if source_path.exists():
                        # Create archive path maintaining directory structure
                        relative_path = source_path.relative_to(self.workspace_root)
                        archive_path = archive_dir / relative_path
                        archive_path.parent.mkdir(parents=True, exist_ok=True)

                        # Copy to archive
                        shutil.copy2(source_path, archive_path)
                        archived_files += 1

                        # Add deprecation notice to original file
                        self._add_deprecation_notice(source_path, archive_path)

                except Exception as e:
                    logger.warning(f"Failed to archive {config_path}: {e}")

        self.consolidation_report['files_archived'] = archived_files
        logger.info(f"📁 Archived {archived_files} legacy configuration files")

    def _add_deprecation_notice(self, original_path: Path, archive_path: Path):
        """Add deprecation notice to legacy configuration file"""
        try:
            notice = f"""
# ⚠️  DEPRECATED CONFIGURATION FILE ⚠️
#
# This configuration file has been deprecated and consolidated into the
# unified configuration system at: config/unified/production_config.toml
#
# Original file archived at: {archive_path.relative_to(self.workspace_root)}
# Consolidation date: {datetime.now().isoformat()}
#
# Please update your code to use:
# from kingdom.config.unified_config_system import UnifiedConfigSystem
# config = UnifiedConfigSystem().get_config()
#
# This file will be removed in a future release.
#
"""

            if original_path.suffix in ['.py']:
                # Add Python comment notice
                content = original_path.read_text(encoding='utf-8')
                new_content = notice + "\n" + content
                original_path.write_text(new_content, encoding='utf-8')

        except Exception as e:
            logger.warning(f"Failed to add deprecation notice to {original_path}: {e}")

    def _generate_report(self):
        """Generate final consolidation report"""
        logger.info("📊 Generating consolidation report...")

        self.consolidation_report.update({
            'completion_time': datetime.now().isoformat(),
            'total_legacy_configs': len(self.legacy_configs),
            'unified_config_location': 'config/unified/production_config.toml',
            'archive_location': f'config/legacy_archive/{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'next_steps': [
                'Update remaining code references to use UnifiedConfigSystem',
                'Test all services with new configuration system',
                'Remove legacy configuration files after validation',
                'Update deployment scripts to use unified configuration'
            ]
        })

if __name__ == "__main__":
    consolidator = LegacyConfigConsolidator()
    report = consolidator.run_consolidation()

    # Save report
    report_file = f"legacy_config_consolidation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)

