{"total_tests": 5, "passed_tests": 4, "failed_tests": 1, "success_rate": 80.0, "total_time_seconds": 1.67, "test_results": {"Database Connectivity": {"status": "PASSED", "details": {"success": true, "message": "Database connectivity successful", "total_tables": 13, "expected_tables": ["games", "players", "teams", "player_game_stats"], "missing_tables": [], "table_counts": {"games": 135816, "players": 24153, "teams": 4021, "player_game_stats": 26213}}, "timestamp": "2025-07-01T10:49:48.191315"}, "Existing Infrastructure": {"status": "FAILED", "details": {"success": false, "error": "expected an indented block after function definition on line 94 (nba_real_time_pipeline.py, line 95)", "message": "Infrastructure test failed"}, "timestamp": "2025-07-01T10:49:48.194524"}, "Real-Time Data Simulation": {"status": "PASSED", "details": {"success": true, "message": "Real-time data simulation successful", "events_processed": 10, "processing_time_seconds": 1.085, "throughput_events_per_second": 9.21, "sample_event": {"event_id": "sim_event_000", "timestamp": "2025-07-01T14:49:48.194971+00:00", "type": "live_game_update", "data": {"game_id": "sim_game_0", "home_score": 80, "away_score": 75, "quarter": 1, "time_remaining": "12:00"}}}, "timestamp": "2025-07-01T10:49:49.280572"}, "Performance Baseline": {"status": "PASSED", "details": {"success": true, "message": "Performance baseline established", "database_records": 26213, "simple_query_time_ms": 6.99, "complex_query_time_ms": 53.46, "complex_query_results": 0, "memory_processing_time_ms": 0.04, "memory_items_processed": 500}, "timestamp": "2025-07-01T10:49:49.344433"}, "Integration Readiness": {"status": "PASSED", "details": {"success": true, "message": "Integration readiness: 100.0%", "readiness_percentage": 100.0, "passed_checks": 4, "total_checks": 4, "readiness_details": {"Games table schema": true, "Players table schema": true, "Directory structure": true, "Python dependencies": true}}, "timestamp": "2025-07-01T10:49:49.843871"}}, "overall_status": "FAILED"}