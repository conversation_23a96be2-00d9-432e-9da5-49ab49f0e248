{"metrics": {"accuracy": 0.6363636363636364, "high_confidence_accuracy": 0.5714285714285714, "medium_confidence_accuracy": 0.75, "low_confidence_accuracy": 0.0, "baseline_achieved": false}, "optimization": {"optimal_threshold": 0.5, "filtered_accuracy": 0.6363636363636364, "threshold_analysis": [{"threshold": 0.5, "accuracy": 0.6363636363636364, "predictions_kept": 22, "predictions_filtered": 0}, {"threshold": 0.55, "accuracy": 0.6363636363636364, "predictions_kept": 22, "predictions_filtered": 0}, {"threshold": 0.6000000000000001, "accuracy": 0.6363636363636364, "predictions_kept": 22, "predictions_filtered": 0}, {"threshold": 0.6500000000000001, "accuracy": 0.6190476190476191, "predictions_kept": 21, "predictions_filtered": 1}, {"threshold": 0.7000000000000002, "accuracy": 0.5625, "predictions_kept": 16, "predictions_filtered": 6}, {"threshold": 0.7500000000000002, "accuracy": 0.5714285714285714, "predictions_kept": 14, "predictions_filtered": 8}, {"threshold": 0.8000000000000003, "accuracy": 0.5555555555555556, "predictions_kept": 9, "predictions_filtered": 13}, {"threshold": 0.8500000000000003, "accuracy": 0.5, "predictions_kept": 4, "predictions_filtered": 18}]}, "ensemble_analysis": {"base_accuracy": 0.6428571428571429, "ensemble_accuracy": 0.7857142857142857, "improvement": 0.1428571428571428, "ensemble_results": [{"game_id": "test_0_20250627", "home_team": "Warriors", "away_team": "Lakers", "predicted_home_win_prob": 0.4913929491739102, "actual_home_win": false, "predicted_home_win": false, "correct": true, "confidence": 0.7119000408913267, "ensemble_prediction": 0.4369830115638241, "ensemble_correct": true, "ensemble_confidence": 0.9132659347213483, "model_predictions": [0.4612548144711149, 0.33296670648441773, 0.5437709540233278]}, {"game_id": "test_1_20250629", "home_team": "Nuggets", "away_team": "Grizzlies", "predicted_home_win_prob": 0.6993056040712606, "actual_home_win": false, "predicted_home_win": true, "correct": false, "confidence": 0.898005260463389, "ensemble_prediction": 0.7842197647935011, "ensemble_correct": false, "ensemble_confidence": 0.9434898941706279, "model_predictions": [0.7862800508282549, 0.8400755945053221, 0.7027251455413457]}, {"game_id": "test_2_20250626", "home_team": "Pacers", "away_team": "Suns", "predicted_home_win_prob": 0.4593305984903103, "actual_home_win": true, "predicted_home_win": false, "correct": false, "confidence": 0.8249880478078542, "ensemble_prediction": 0.4125735961622603, "ensemble_correct": false, "ensemble_confidence": 0.9921817442888223, "model_predictions": [0.42102293705062743, 0.4020495726666388, 0.41378828363474307]}, {"game_id": "test_3_20250628", "home_team": "Celtics", "away_team": "Grizzlies", "predicted_home_win_prob": 0.6141913852324098, "actual_home_win": true, "predicted_home_win": true, "correct": true, "confidence": 0.6511473340390038, "ensemble_prediction": 0.6141970575682483, "ensemble_correct": true, "ensemble_confidence": 0.9634362966566689, "model_predictions": [0.607930446282205, 0.6556648015368547, 0.5661687940698684]}, {"game_id": "test_4_20250628", "home_team": "<PERSON><PERSON><PERSON>", "away_team": "Nuggets", "predicted_home_win_prob": 0.6169781870394848, "actual_home_win": true, "predicted_home_win": true, "correct": true, "confidence": 0.668933866369439, "ensemble_prediction": 0.5661422988570874, "ensemble_correct": true, "ensemble_confidence": 0.9826498441944577, "model_predictions": [0.5627580965973378, 0.5512369400174494, 0.5924245248481804]}, {"game_id": "test_5_20250701", "home_team": "Mavericks", "away_team": "Clippers", "predicted_home_win_prob": 0.6056406948329034, "actual_home_win": true, "predicted_home_win": true, "correct": true, "confidence": 0.8105051706592736, "ensemble_prediction": 0.6113146709395448, "ensemble_correct": true, "ensemble_confidence": 0.9698636887974598, "model_predictions": [0.6139357502928773, 0.5788214751481972, 0.6526114180820997]}, {"game_id": "test_6_20250626", "home_team": "Hawks", "away_team": "Celtics", "predicted_home_win_prob": 0.5523156015603801, "actual_home_win": true, "predicted_home_win": true, "correct": true, "confidence": 0.65139626281493, "ensemble_prediction": 0.5401320433842038, "ensemble_correct": true, "ensemble_confidence": 0.9126797612447077, "model_predictions": [0.6425445238626786, 0.4286889422156826, 0.5322924162545739]}, {"game_id": "test_7_20250701", "home_team": "Hawks", "away_team": "Nuggets", "predicted_home_win_prob": 0.3674662420338487, "actual_home_win": false, "predicted_home_win": false, "correct": true, "confidence": 0.6094954502139974, "ensemble_prediction": 0.3285967863443074, "ensemble_correct": true, "ensemble_confidence": 0.9710405702281046, "model_predictions": [0.2922789478396728, 0.3613259113839321, 0.34088455289624814]}, {"game_id": "test_8_20250630", "home_team": "Nets", "away_team": "Mavericks", "predicted_home_win_prob": 0.5204505333157731, "actual_home_win": true, "predicted_home_win": true, "correct": true, "confidence": 0.6667012654198353, "ensemble_prediction": 0.5464079392676415, "ensemble_correct": true, "ensemble_confidence": 0.983583491974765, "model_predictions": [0.5447223700702515, 0.5310363803112748, 0.5706250325223791]}, {"game_id": "test_9_20250628", "home_team": "Warriors", "away_team": "Celtics", "predicted_home_win_prob": 0.5034909702482891, "actual_home_win": false, "predicted_home_win": true, "correct": false, "confidence": 0.7401973218529445, "ensemble_prediction": 0.5602428890594893, "ensemble_correct": false, "ensemble_confidence": 0.9738708923195002, "model_predictions": [0.5399588998711842, 0.5964096061799189, 0.5420638677921762]}, {"game_id": "test_10_20250701", "home_team": "Warriors", "away_team": "Magic", "predicted_home_win_prob": 0.4697858856179413, "actual_home_win": true, "predicted_home_win": false, "correct": false, "confidence": 0.7652703228311352, "ensemble_prediction": 0.5144149972786065, "ensemble_correct": true, "ensemble_confidence": 0.9894790966733864, "model_predictions": [0.5251150044379116, 0.4999851437479958, 0.5174967807665738]}, {"game_id": "test_11_20250626", "home_team": "Clippers", "away_team": "Lakers", "predicted_home_win_prob": 0.4912063069795549, "actual_home_win": true, "predicted_home_win": false, "correct": false, "confidence": 0.8710119670008785, "ensemble_prediction": 0.54839674927555, "ensemble_correct": true, "ensemble_confidence": 0.9609339123614842, "model_predictions": [0.5173984716628807, 0.6025543591385133, 0.5221733396476723]}, {"game_id": "test_12_20250627", "home_team": "<PERSON><PERSON><PERSON>", "away_team": "Nets", "predicted_home_win_prob": 0.41607135872678414, "actual_home_win": false, "predicted_home_win": false, "correct": true, "confidence": 0.8560311061367074, "ensemble_prediction": 0.4033655038073617, "ensemble_correct": true, "ensemble_confidence": 0.9862961791469841, "model_predictions": [0.39107027792758314, 0.4025828669311201, 0.4241335568417454]}, {"game_id": "test_13_20250627", "home_team": "Suns", "away_team": "Bucks", "predicted_home_win_prob": 0.5489141786173621, "actual_home_win": true, "predicted_home_win": true, "correct": true, "confidence": 0.8897761562491628, "ensemble_prediction": 0.5724997348695583, "ensemble_correct": true, "ensemble_confidence": 0.966716457417952, "model_predictions": [0.6141559042256134, 0.5346174565175339, 0.5588850535927044]}]}, "improvement_plan": {"current_accuracy": 0.6363636363636364, "target_accuracy": 0.65, "improvement_needed": 0.013636363636363669, "strategies": [{"name": "Ensemble Model Integration", "description": "Combine multiple prediction models", "expected_improvement": "3.0%", "implementation": "Implement weighted ensemble of 3+ models"}, {"name": "Advanced Feature Engineering", "description": "Add temporal and interaction features", "expected_improvement": "2.0%", "implementation": "Implement advanced feature extraction"}], "expected_impact": {"ensemble": 3.0, "feature_engineering": 2.0}, "total_expected_improvement": 5.0, "projected_accuracy": 68.63636363636363}, "recommendations": ["🔧 High-confidence predictions underperforming - review model calibration", "⚡ Medium-confidence predictions outperforming high - adjust confidence thresholds", "🎯 Need 1.4% improvement to reach baseline"]}