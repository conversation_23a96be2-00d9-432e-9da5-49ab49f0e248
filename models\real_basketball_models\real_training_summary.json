{"points": {"prop_name": "points", "input_size": 30, "train_r2": 0.993565736243096, "test_r2": 0.9869549820616316, "train_rmse": 11.309691163588836, "test_rmse": 16.472448371057222, "best_val_loss": 253.39620971679688, "model_path": "models\\real_basketball_models\\real_points_model.pt", "scaler_path": "models\\real_basketball_models\\real_points_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_points_scaler_params.json", "feature_count": 30}, "rebounds": {"prop_name": "rebounds", "input_size": 30, "train_r2": 0.9903209304161532, "test_r2": 0.986429267865709, "train_rmse": 6.047956069574372, "test_rmse": 7.340412322212987, "best_val_loss": 50.08826446533203, "model_path": "models\\real_basketball_models\\real_rebounds_model.pt", "scaler_path": "models\\real_basketball_models\\real_rebounds_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_rebounds_scaler_params.json", "feature_count": 30}, "assists": {"prop_name": "assists", "input_size": 30, "train_r2": 0.9935803290001871, "test_r2": 0.9914482538545714, "train_rmse": 3.1471907893488984, "test_rmse": 3.919988374344452, "best_val_loss": 14.937729835510254, "model_path": "models\\real_basketball_models\\real_assists_model.pt", "scaler_path": "models\\real_basketball_models\\real_assists_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_assists_scaler_params.json", "feature_count": 30}, "steals": {"prop_name": "steals", "input_size": 30, "train_r2": 0.9967775561002737, "test_r2": 0.9938600037491256, "train_rmse": 0.6983017760772582, "test_rmse": 0.9608528167439285, "best_val_loss": 0.9059637188911438, "model_path": "models\\real_basketball_models\\real_steals_model.pt", "scaler_path": "models\\real_basketball_models\\real_steals_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_steals_scaler_params.json", "feature_count": 30}, "blocks": {"prop_name": "blocks", "input_size": 30, "train_r2": 0.9940308368067378, "test_r2": 0.9930207242609627, "train_rmse": 0.7420871815019503, "test_rmse": 0.8959117490014643, "best_val_loss": 0.34392112493515015, "model_path": "models\\real_basketball_models\\real_blocks_model.pt", "scaler_path": "models\\real_basketball_models\\real_blocks_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_blocks_scaler_params.json", "feature_count": 30}, "threes": {"prop_name": "threes", "input_size": 30, "train_r2": 0.9945667460464165, "test_r2": 0.9933624031989838, "train_rmse": 1.1578059750324439, "test_rmse": 1.313356261715987, "best_val_loss": 0.9588085412979126, "model_path": "models\\real_basketball_models\\real_threes_model.pt", "scaler_path": "models\\real_basketball_models\\real_threes_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_threes_scaler_params.json", "feature_count": 30}, "game_prediction": {"model_type": "game_prediction", "input_size": 33, "train_accuracy": 1.0, "test_accuracy": 1.0, "best_val_accuracy": 1.0, "model_path": "models\\real_basketball_models\\real_game_model.pt", "scaler_path": "models\\real_basketball_models\\real_game_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_game_scaler_params.json", "feature_count": 33}}