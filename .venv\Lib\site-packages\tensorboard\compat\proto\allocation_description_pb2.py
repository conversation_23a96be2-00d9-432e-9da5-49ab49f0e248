# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/allocation_description.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n5tensorboard/compat/proto/allocation_description.proto\x12\x0btensorboard\"\xa3\x01\n\x15\x41llocationDescription\x12\x17\n\x0frequested_bytes\x18\x01 \x01(\x03\x12\x17\n\x0f\x61llocated_bytes\x18\x02 \x01(\x03\x12\x16\n\x0e\x61llocator_name\x18\x03 \x01(\t\x12\x15\n\rallocation_id\x18\x04 \x01(\x03\x12\x1c\n\x14has_single_reference\x18\x05 \x01(\x08\x12\x0b\n\x03ptr\x18\x06 \x01(\x04\x42\x9b\x01\n\x18org.tensorflow.frameworkB\x1b\x41llocationDescriptionProtosP\x01Z]github.com/tensorflow/tensorflow/tensorflow/go/core/framework/allocation_description_go_proto\xf8\x01\x01\x62\x06proto3')



_ALLOCATIONDESCRIPTION = DESCRIPTOR.message_types_by_name['AllocationDescription']
AllocationDescription = _reflection.GeneratedProtocolMessageType('AllocationDescription', (_message.Message,), {
  'DESCRIPTOR' : _ALLOCATIONDESCRIPTION,
  '__module__' : 'tensorboard.compat.proto.allocation_description_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.AllocationDescription)
  })
_sym_db.RegisterMessage(AllocationDescription)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkB\033AllocationDescriptionProtosP\001Z]github.com/tensorflow/tensorflow/tensorflow/go/core/framework/allocation_description_go_proto\370\001\001'
  _ALLOCATIONDESCRIPTION._serialized_start=71
  _ALLOCATIONDESCRIPTION._serialized_end=234
# @@protoc_insertion_point(module_scope)
