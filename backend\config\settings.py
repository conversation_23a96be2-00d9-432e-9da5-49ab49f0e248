import os
from typing import Optional, Dict, Any
from functools import lru_cache
import logging
from pydantic_settings import BaseSettings
from pydantic import Field
from pydantic import BaseSettings, Field
    from pydantic_settings import BaseSettings, Field


# ⚠️  DEPRECATED CONFIGURATION FILE ⚠️
#
# This configuration file has been deprecated and consolidated into the
# unified configuration system at: config/unified/production_config.toml
#
# Original file archived at: config\legacy_archive\20250703_234346\backend\config\settings.py
# Consolidation date: 2025-07-03T23:43:46.255376
#
# Please update your code to use:
# from kingdom.config.unified_config_system import UnifiedConfigSystem
# config = UnifiedConfigSystem().get_config()
#
# This file will be removed in a future release.
#


#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Settings Configuration
Centralized settings management for the backend application
"""


# Handle Pydantic v2 BaseSettings import
try:
except ImportError:
    try:
    except ImportError:
        # Fallback for older Pydantic versions
        class BaseSettings:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

        def Field(default=None, **kwargs):
            return default

logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Environment
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    
    # API Configuration
    api_title: str = Field(default="HYPER MEDUSA NEURAL VAULT API", env="API_TITLE")
    api_version: str = Field(default="3.0.0", env="API_VERSION")
    api_description: str = Field(default="Advanced Basketball Analytics & Prediction Engine", env="API_DESCRIPTION")
    
    # Server Configuration
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8001, env="PORT")
    reload: bool = Field(default=True, env="RELOAD")
    
    # Database Configuration
    database_url: Optional[str] = Field(default=None, env="DATABASE_URL")
    database_host: str = Field(default="localhost", env="DATABASE_HOST")
    database_port: int = Field(default=5432, env="DATABASE_PORT")
    database_name: str = Field(default="hyper_medusa_vault", env="DATABASE_NAME")
    database_user: str = Field(default="postgres", env="DATABASE_USER")
    database_password: str = Field(default="", env="DATABASE_PASSWORD")
    
    # Redis Configuration
    redis_url: Optional[str] = Field(default=None, env="REDIS_URL")
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    # Security Configuration
    secret_key: str = Field(default="hyper-medusa-neural-vault-secret-key", env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # External APIs
    nba_api_base_url: str = Field(default="https://stats.nba.com/stats", env="NBA_API_BASE_URL")
    odds_api_key: Optional[str] = Field(default=None, env="ODDS_API_KEY")
    
    # Feature Flags
    enable_analytics: bool = Field(default=True, env="ENABLE_ANALYTICS")
    enable_predictions: bool = Field(default=True, env="ENABLE_PREDICTIONS")
    enable_real_time: bool = Field(default=True, env="ENABLE_REAL_TIME")
    enable_expert_mode: bool = Field(default=True, env="ENABLE_EXPERT_MODE")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT")
    
    # Performance Configuration
    max_workers: int = Field(default=4, env="MAX_WORKERS")
    request_timeout: int = Field(default=30, env="REQUEST_TIMEOUT")
    
    # CORS Configuration
    cors_origins: list = Field(default=["*"], env="CORS_ORIGINS")
    cors_methods: list = Field(default=["*"], env="CORS_METHODS")
    cors_headers: list = Field(default=["*"], env="CORS_HEADERS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        extra = "allow"  # Allow extra fields from .env file
    
    @property
    def database_url_computed(self) -> str:
        """Compute database URL from components if not provided directly"""
        if self.database_url:
            return self.database_url
        
        return f"postgresql://{self.database_user}:{self.database_password}@{self.database_host}:{self.database_port}/{self.database_name}"
    
    @property
    def redis_url_computed(self) -> str:
        """Compute Redis URL from components if not provided directly"""
        if self.redis_url:
            return self.redis_url
        
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    def get_feature_flags(self) -> Dict[str, bool]:
        """Get all feature flags as a dictionary"""
        return {
            "analytics": self.enable_analytics,
            "predictions": self.enable_predictions,
            "real_time": self.enable_real_time,
            "expert_mode": self.enable_expert_mode
        }
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment.lower() == "production"
    
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.environment.lower() == "development"
    
    def is_testing(self) -> bool:
        """Check if running in testing environment"""
        return self.environment.lower() == "testing"

@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance"""
    try:
        settings = Settings()
        logger.info(f"🏀 MEDUSA VAULT: Settings loaded for {settings.environment} environment")
        return settings
    except Exception as e:
        logger.error(f"Failed to load settings: {e}")
        # Return default settings as fallback
        return Settings()

# Global settings instance
settings = get_settings()

# Export commonly used settings
__all__ = [
    "Settings",
    "get_settings", 
    "settings"
]
