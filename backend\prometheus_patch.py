import sys
import logging
from typing import Any, Dict, Optional, List
import prometheus_client

#!/usr/bin/env python3
"""
Prometheus Patch Module
Patches Prometheus imports to use shared registry and prevent conflicts
"""


# Import the shared registry
from backend.shared_prometheus_registry import (
    get_shared_registry,
    get_or_create_counter,
    get_or_create_histogram,
    get_or_create_gauge,
    get_orchestrator_messages_metrics
)

logger = logging.getLogger("prometheus_patch")

class PrometheusMetricsPatch:
    """
    Patches Prometheus metrics to use shared registry
    """
    
    def __init__(self):
        self.patched_modules = []
        self.original_imports = {}
        
    def patch_prometheus_client(self):
        """Patch prometheus_client module to use shared registry"""
        try:
            
            # Store original classes
            self.original_imports['Counter'] = prometheus_client.Counter
            self.original_imports['Histogram'] = prometheus_client.Histogram
            self.original_imports['Gauge'] = prometheus_client.Gauge
            
            # Create patched classes that use shared registry
            class PatchedCounter:
                def __init__(self, name, description, labelnames=None, registry=None):
                    # Always use shared registry
                    self._metric = get_or_create_counter(name, description, labelnames)
                    
                def inc(self, amount=1, **labels):
                    if labels:
                        return self._metric.labels(**labels).inc(amount)
                    return self._metric.inc(amount)
                    
                def labels(self, **labels):
                    return self._metric.labels(**labels)
                    
                def __getattr__(self, name):
                    return getattr(self._metric, name)
            
            class PatchedHistogram:
                def __init__(self, name, description, labelnames=None, registry=None):
                    # Always use shared registry
                    self._metric = get_or_create_histogram(name, description, labelnames)
                    
                def observe(self, amount, **labels):
                    if labels:
                        return self._metric.labels(**labels).observe(amount)
                    return self._metric.observe(amount)
                    
                def labels(self, **labels):
                    return self._metric.labels(**labels)
                    
                def __getattr__(self, name):
                    return getattr(self._metric, name)
            
            class PatchedGauge:
                def __init__(self, name, description, labelnames=None, registry=None):
                    # Always use shared registry
                    self._metric = get_or_create_gauge(name, description, labelnames)
                    
                def set(self, value, **labels):
                    if labels:
                        return self._metric.labels(**labels).set(value)
                    return self._metric.set(value)
                    
                def inc(self, amount=1, **labels):
                    if labels:
                        return self._metric.labels(**labels).inc(amount)
                    return self._metric.inc(amount)
                    
                def dec(self, amount=1, **labels):
                    if labels:
                        return self._metric.labels(**labels).dec(amount)
                    return self._metric.dec(amount)
                    
                def labels(self, **labels):
                    return self._metric.labels(**labels)
                    
                def __getattr__(self, name):
                    return getattr(self._metric, name)
            
            # Replace the classes in prometheus_client
            prometheus_client.Counter = PatchedCounter
            prometheus_client.Histogram = PatchedHistogram
            prometheus_client.Gauge = PatchedGauge
            
            logger.info("Successfully patched prometheus_client to use shared registry")
            return True
            
        except Exception as e:
            logger.error(f"Failed to patch prometheus_client: {e}")
            return False
    
    def patch_vault_oracle_metrics(self):
        """Patch vault_oracle modules that create Prometheus metrics"""
        try:
            # Pre-create the orchestrator metrics that are causing conflicts
            orchestrator_metrics = get_orchestrator_messages_metrics()
            
            logger.info("Pre-created orchestrator metrics to prevent conflicts")
            return True
            
        except Exception as e:
            logger.error(f"Failed to patch vault_oracle metrics: {e}")
            return False
    
    def apply_all_patches(self):
        """Apply all Prometheus patches"""
        results = {
            "prometheus_client": self.patch_prometheus_client(),
            "vault_oracle_metrics": self.patch_vault_oracle_metrics()
        }
        
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)
        
        logger.info(f"Applied {success_count}/{total_count} Prometheus patches")
        
        return {
            "success_rate": (success_count / total_count * 100) if total_count > 0 else 0,
            "results": results,
            "patched": success_count == total_count
        }

# Global patch instance
prometheus_patch = PrometheusMetricsPatch()

def apply_prometheus_patches():
    """Apply all Prometheus patches"""
    return prometheus_patch.apply_all_patches()

# Auto-apply patches on import
patch_result = apply_prometheus_patches()
if patch_result["patched"]:
    logger.info("All Prometheus patches applied successfully")
else:
    logger.warning(f"Prometheus patches partially applied: {patch_result['success_rate']:.1f}%")
