🔒 HYPER MEDUSA NEURAL VAULT - Security Audit Report
============================================================
Total Issues Found: 208

HIGH SEVERITY ISSUES (208)
----------------------------------------
File: vault_oracle\core\entanglement_manager.py:75
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: vault_oracle\core\entanglement_manager.py:75
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: vault_oracle\core\entanglement_manager.py:76
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: vault_oracle\core\entanglement_manager.py:76
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\edge_tts\constants.py:4
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\edge_tts\constants.py:4
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\firebase_admin\_utils.py:344
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\firebase_admin\_utils.py:344
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\flask\config.py:66
Type: jwt_secret
Description: Hardcoded jwt_secret detected
Suggested Fix: Replace with environment variable: ${JWT_SECRET}

File: .venv\Lib\site-packages\fsspec\spec.py:1640
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\fsspec\spec.py:1640
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\git\cmd.py:1410
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\git\remote.py:257
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\git\remote.py:257
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\git\remote.py:259
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\git\remote.py:259
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\git\remote.py:444
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\git\remote.py:444
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\git\remote.py:767
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\git\remote.py:767
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\git\util.py:583
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\git\util.py:583
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\grpc\_server.py:63
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\grpc\_server.py:63
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\grpc\_server.py:64
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\grpc\_server.py:64
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\grpc\_server.py:65
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\grpc\_server.py:65
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\grpc\_server.py:66
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\grpc\_server.py:66
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\grpc\_server.py:70
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\grpc\_server.py:70
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\httpx\_urls.py:336
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\httpx\_urls.py:336
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\psycopg2\errorcodes.py:269
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\psycopg2\errorcodes.py:269
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\psycopg2\__init__.py:103
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\psycopg2\__init__.py:103
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\pydantic\types.py:1819
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\pydantic\types.py:1819
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\pydantic\types.py:1846
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\pydantic\types.py:1846
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\starlette\datastructures.py:166
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\starlette\datastructures.py:166
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\torch\hub.py:87
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\torch\hub.py:87
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\win32\lib\win32cryptcon.py:414
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\win32\lib\win32cryptcon.py:531
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\win32\lib\win32cryptcon.py:531
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\win32\lib\win32rcparser.py:264
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\win32\lib\win32rcparser.py:264
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\werkzeug\debug\tbtools.py:30
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\werkzeug\debug\tbtools.py:30
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\discord.py:6
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\discord.py:6
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\discord.py:105
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\discord.py:105
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\slack.py:6
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\slack.py:6
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\slack.py:68
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\slack.py:68
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\telegram.py:6
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\telegram.py:6
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\telegram.py:102
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\tqdm\contrib\telegram.py:102
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\torch\utils\data\datapipes\gen_pyi.py:57
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\torch\utils\data\datapipes\gen_pyi.py:57
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\torch\utils\data\datapipes\gen_pyi.py:59
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\torch\utils\data\datapipes\gen_pyi.py:59
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\torch\utils\data\datapipes\gen_pyi.py:70
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\torch\utils\data\datapipes\gen_pyi.py:70
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\torch\utils\data\datapipes\gen_pyi.py:71
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\torch\utils\data\datapipes\gen_pyi.py:71
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\torch\distributed\rpc\_utils.py:14
Type: api_key
Description: Hardcoded api_key detected
Suggested Fix: Replace with environment variable: ${API_KEY}

File: .venv\Lib\site-packages\sympy\codegen\fnodes.py:637
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sympy\codegen\fnodes.py:637
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sympy\codegen\fnodes.py:643
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sympy\codegen\fnodes.py:643
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\connectors\pyodbc.py:83
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\connectors\pyodbc.py:83
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\engine\default.py:224
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\engine\default.py:224
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\orm\path_registry.py:85
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\orm\path_registry.py:85
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\dialects\mssql\pyodbc.py:80
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\mssql\pyodbc.py:80
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\mssql\pyodbc.py:215
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\mssql\pyodbc.py:215
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\mssql\pyodbc.py:245
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\mssql\pyodbc.py:245
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\mysql\mysqldb.py:206
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\mysql\mysqldb.py:206
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\cx_oracle.py:198
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\cx_oracle.py:198
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\cx_oracle.py:242
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\cx_oracle.py:242
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\oracledb.py:271
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\oracledb.py:271
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\oracledb.py:313
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\oracledb.py:313
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\oracledb.py:345
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\oracledb.py:345
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\provision.py:193
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\oracle\provision.py:193
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\postgresql\base.py:4380
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\dialects\postgresql\base.py:4380
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\dialects\sqlite\base.py:2067
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\dialects\sqlite\base.py:2067
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\sqlalchemy\dialects\sqlite\provision.py:76
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\sqlalchemy\dialects\sqlite\provision.py:76
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\shap\explainers\_explainer.py:103
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\shap\explainers\_explainer.py:103
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\shap\maskers\_text.py:25
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\shap\maskers\_text.py:25
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\shap\maskers\_text.py:81
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\shap\maskers\_text.py:81
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\pyarrow\tests\conftest.py:171
Type: jwt_secret
Description: Hardcoded jwt_secret detected
Suggested Fix: Replace with environment variable: ${JWT_SECRET}

File: .venv\Lib\site-packages\plotly\io\_orca.py:1528
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\plotly\io\_orca.py:1528
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\pip\_internal\utils\misc.py:478
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\pip\_internal\utils\misc.py:478
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\opentelemetry\semconv\_incubating\attributes\k8s_attributes.py:363
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\opentelemetry\semconv\_incubating\attributes\k8s_attributes.py:363
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\opentelemetry\sdk\environment_variables\__init__.py:239
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\opentelemetry\sdk\environment_variables\__init__.py:239
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\mlflow\johnsnowlabs\__init__.py:113
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\mlflow\johnsnowlabs\__init__.py:113
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\mlflow\johnsnowlabs\__init__.py:114
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\mlflow\johnsnowlabs\__init__.py:114
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\mlflow\utils\openai_utils.py:145
Type: api_key
Description: Hardcoded api_key detected
Suggested Fix: Replace with environment variable: ${API_KEY}

File: .venv\Lib\site-packages\mlflow\utils\openai_utils.py:145
Type: api_key
Description: Hardcoded api_key detected
Suggested Fix: Replace with environment variable: ${API_KEY}

File: .venv\Lib\site-packages\mlflow\server\auth\__init__.py:1129
Type: jwt_secret
Description: Hardcoded jwt_secret detected
Suggested Fix: Replace with environment variable: ${JWT_SECRET}

File: .venv\Lib\site-packages\mlflow\legacy_databricks_cli\configure\provider.py:28
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\mlflow\legacy_databricks_cli\configure\provider.py:28
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\mlflow\legacy_databricks_cli\configure\provider.py:29
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\mlflow\legacy_databricks_cli\configure\provider.py:29
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\mlflow\legacy_databricks_cli\configure\provider.py:30
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\mlflow\legacy_databricks_cli\configure\provider.py:30
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\mlflow\legacy_databricks_cli\configure\provider.py:35
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\mlflow\legacy_databricks_cli\configure\provider.py:35
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\api_core\page_iterator.py:320
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\api_core\page_iterator.py:320
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\api_core\page_iterator.py:322
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\api_core\page_iterator.py:322
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\auth\environment_vars.py:82
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\auth\environment_vars.py:82
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\auth\metrics.py:30
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\auth\metrics.py:30
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\auth\metrics.py:31
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\auth\metrics.py:31
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\protobuf\text_format.py:898
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\protobuf\text_format.py:898
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\protobuf\text_format.py:901
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\protobuf\text_format.py:901
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\protobuf\text_format.py:1059
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\protobuf\text_format.py:1059
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\protobuf\text_format.py:1062
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\protobuf\text_format.py:1062
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\cloud\firestore_v1\base_client.py:198
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\cloud\firestore_v1\base_client.py:198
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\auth\aio\credentials.py:92
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\google\auth\aio\credentials.py:92
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\geopy\extra\rate_limiter.py:199
Type: api_key
Description: Hardcoded api_key detected
Suggested Fix: Replace with environment variable: ${API_KEY}

File: .venv\Lib\site-packages\geopy\extra\rate_limiter.py:199
Type: api_key
Description: Hardcoded api_key detected
Suggested Fix: Replace with environment variable: ${API_KEY}

File: .venv\Lib\site-packages\geopy\extra\rate_limiter.py:323
Type: api_key
Description: Hardcoded api_key detected
Suggested Fix: Replace with environment variable: ${API_KEY}

File: .venv\Lib\site-packages\geopy\extra\rate_limiter.py:323
Type: api_key
Description: Hardcoded api_key detected
Suggested Fix: Replace with environment variable: ${API_KEY}

File: .venv\Lib\site-packages\geopy\geocoders\dot_us.py:162
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\geopy\geocoders\dot_us.py:162
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\fontTools\ttLib\tables\ttProgram.py:201
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\fontTools\ttLib\tables\ttProgram.py:201
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\dateutil\parser\_parser.py:122
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\dateutil\parser\_parser.py:122
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2946
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2946
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2947
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2947
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2949
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2949
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2950
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2950
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2953
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2953
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2957
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\databricks\sdk\service\catalog.py:2957
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\databricks\sdk\service\settings.py:5351
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\settings.py:5351
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\settings.py:5353
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\settings.py:5353
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\settings.py:5357
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\settings.py:5357
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\sharing.py:26
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\databricks\sdk\service\sharing.py:26
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\alembic\script\write_hooks.py:25
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\alembic\script\write_hooks.py:25
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\alembic\templates\multidb\env.py:116
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\alembic\templates\multidb\env.py:116
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\alembic\templates\multidb\env.py:117
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\alembic\templates\multidb\env.py:117
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .venv\Lib\site-packages\adodbapi\test\adodbapitestconfig.py:162
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .venv\Lib\site-packages\adodbapi\test\adodbapitestconfig.py:162
Type: password
Description: Hardcoded password detected
Suggested Fix: Replace with environment variable: ${PASSWORD}

File: .env:42
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .env:42
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .env:46
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .env:46
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .env:49
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .env:49
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .env:53
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}

File: .env:53
Type: secret
Description: Hardcoded secret detected
Suggested Fix: Replace with environment variable: ${SECRET}
