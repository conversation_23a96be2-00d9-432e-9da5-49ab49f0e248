#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Test Unified Neural Predictions
Demonstrates how the unified service combines game and player props predictions
"""

import sys
import os
import asyncio
import json
from datetime import datetime
import logging

# Add project root to path
sys.path.append('.')

from src.services.unified_neural_prediction_service import UnifiedNeuralPredictionService

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_unified_predictions():
    """Test the unified neural prediction service"""
    logger.info("🚀 Testing Unified Neural Prediction Service")
    
    # Initialize service
    service = UnifiedNeuralPredictionService(league="WNBA")
    
    # Initialize all models
    initialized = await service.initialize()
    if not initialized:
        logger.error("❌ Failed to initialize service")
        return
    
    # Test game data
    game_data = {
        'home_team': 'Las Vegas Aces',
        'away_team': 'New York Liberty',
        'league': 'WNBA',
        'game_date': '2025-07-05',
        'season': 2025
    }
    
    # Test player data
    players_data = [
        {
            'player_id': 'aja_wilson',
            'player_name': 'A\'ja <PERSON>',
            'team': 'Las Vegas Aces',
            'position': 'C',
            'rank_position': 15.0,  # Top 15% player
            'high_performer': 1,
            'player_consistency': 0.85,
            'player_tier': 4,  # Elite tier
            'position_encoded': 4,  # Center
            'opponent_strength': 0.75,  # Strong opponent
            'home_advantage': 1,  # Playing at home
            'rest_days': 2,
            'back_to_back': 0,
            'season_progress': 0.6,  # Mid-season
            'recent_form': 0.2,  # Good recent form
            'hot_streak': 1,
            'cold_streak': 0
        },
        {
            'player_id': 'sabrina_ionescu',
            'player_name': 'Sabrina Ionescu',
            'team': 'New York Liberty',
            'position': 'PG',
            'rank_position': 25.0,  # Top 25% player
            'high_performer': 1,
            'player_consistency': 0.78,
            'player_tier': 3,  # All-star tier
            'position_encoded': 1,  # Point guard
            'opponent_strength': 0.80,  # Very strong opponent
            'home_advantage': 0,  # Playing away
            'rest_days': 1,
            'back_to_back': 0,
            'season_progress': 0.6,
            'recent_form': 0.1,  # Decent recent form
            'hot_streak': 0,
            'cold_streak': 0
        },
        {
            'player_id': 'kelsey_plum',
            'player_name': 'Kelsey Plum',
            'team': 'Las Vegas Aces',
            'position': 'SG',
            'rank_position': 30.0,  # Top 30% player
            'high_performer': 0,
            'player_consistency': 0.72,
            'player_tier': 3,
            'position_encoded': 2,  # Shooting guard
            'opponent_strength': 0.75,
            'home_advantage': 1,
            'rest_days': 2,
            'back_to_back': 0,
            'season_progress': 0.6,
            'recent_form': -0.1,  # Slightly below average
            'hot_streak': 0,
            'cold_streak': 0
        }
    ]
    
    logger.info("🎯 Making unified prediction...")
    
    # Make unified prediction
    try:
        result = await service.predict_unified(game_data, players_data)
        
        # Display results
        logger.info("=" * 80)
        logger.info("🏀 UNIFIED NEURAL PREDICTION RESULTS")
        logger.info("=" * 80)
        
        # Game prediction results
        logger.info("🎮 GAME OUTCOME PREDICTION:")
        logger.info(f"   Home Team: {game_data['home_team']}")
        logger.info(f"   Away Team: {game_data['away_team']}")
        logger.info(f"   Home Win Probability: {result.home_win_probability:.1%}")
        logger.info(f"   Away Win Probability: {result.away_win_probability:.1%}")
        logger.info(f"   Predicted Spread: {result.predicted_spread:+.1f}")
        logger.info(f"   Predicted Total: {result.predicted_total:.1f}")
        logger.info(f"   Game Confidence: {result.game_confidence:.1%}")
        
        logger.info("")
        logger.info("👤 PLAYER PROPS PREDICTIONS:")
        
        for player_id, props in result.player_props.items():
            player_name = next((p['player_name'] for p in players_data if p['player_id'] == player_id), player_id)
            player_team = next((p['team'] for p in players_data if p['player_id'] == player_id), 'Unknown')
            
            logger.info(f"   📊 {player_name} ({player_team}):")
            
            for prop_type, prediction in props.items():
                confidence = result.props_confidence[player_id][prop_type]
                logger.info(f"      {prop_type.capitalize()}: {prediction:.1f} (confidence: {confidence:.1%})")
        
        logger.info("")
        logger.info("📈 PREDICTION SUMMARY:")
        logger.info(f"   League: {result.league}")
        logger.info(f"   Timestamp: {result.prediction_timestamp}")
        logger.info(f"   Game Model: {result.model_versions['game_model']}")
        logger.info(f"   Props Models: {result.model_versions['props_models']}")
        
        # Save results to file
        results_dict = result.to_dict()
        with open('unified_prediction_results.json', 'w') as f:
            json.dump(results_dict, f, indent=2)
        
        logger.info("💾 Results saved to unified_prediction_results.json")
        
        # Test individual predictions
        logger.info("")
        logger.info("🔍 TESTING INDIVIDUAL PREDICTIONS:")
        
        # Test game-only prediction
        game_only = await service._predict_game_outcome(game_data)
        logger.info(f"   Game-only prediction: {game_only}")
        
        # Test player-only predictions
        for player_data in players_data[:1]:  # Test first player only
            player_props, player_conf = await service._predict_player_props(player_data)
            logger.info(f"   {player_data['player_name']} props: {player_props}")
        
        logger.info("✅ Unified neural prediction test completed successfully!")
        
    except Exception as e:
        logger.error(f"❌ Unified prediction failed: {e}")
        raise

async def test_model_loading():
    """Test model loading capabilities"""
    logger.info("🔧 Testing model loading...")
    
    service = UnifiedNeuralPredictionService(league="WNBA")
    
    # Test game model loading
    game_loaded = await service._load_game_model()
    logger.info(f"Game model loaded: {game_loaded}")
    
    # Test player props models loading
    props_loaded = await service._load_player_props_models()
    logger.info(f"Player props models loaded: {props_loaded}")
    
    # Show loaded models
    logger.info(f"Available prop types: {list(service.player_props_models.keys())}")
    
    return game_loaded and props_loaded

async def main():
    """Main test function"""
    logger.info("🏀 HYPER MEDUSA NEURAL VAULT - Unified Prediction Test")
    logger.info("=" * 80)
    
    try:
        # Test model loading first
        models_loaded = await test_model_loading()
        
        if models_loaded:
            # Run unified prediction test
            await test_unified_predictions()
        else:
            logger.warning("⚠️ Some models failed to load, skipping unified test")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
