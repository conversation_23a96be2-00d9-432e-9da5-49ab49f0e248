import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
import json
import re

#!/usr/bin/env python3
"""
Fix NULL Season Records
Investigate and fix the 63,195 records with NULL/missing season data
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NullSeasonFixer:
    """Fix records with NULL/missing season data"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def investigate_null_seasons(self) -> Dict[str, Any]:
        """Investigate what the NULL season records are"""
        logger.info("🔍 INVESTIGATING NULL SEASON RECORDS")
        logger.info("=" * 40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Get sample of NULL season records
            null_samples = conn.execute("""
                SELECT 
                    id, source_file, source_table, data_category, league_name,
                    league_id, player_id, team_id, game_id, raw_data
                FROM unified_nba_wnba_data 
                WHERE season IS NULL
                LIMIT 20
            """).fetchall()
            
            logger.info(f"📊 SAMPLE OF NULL SEASON RECORDS:")
            for i, record in enumerate(null_samples[:10]):
                logger.info(f"   {i+1}. ID {record[0]}: {record[1]} | {record[2]} | {record[3]} | League: {record[4]}")
            
            # Analyze patterns in NULL season records
            null_patterns = conn.execute("""
                SELECT 
                    COALESCE(source_table, 'No Source Table') as source_table,
                    COALESCE(data_category, 'No Category') as data_category,
                    COALESCE(league_name, 'No League') as league_name,
                    COALESCE(league_id, 'No League ID') as league_id,
                    COUNT(*) as count
                FROM unified_nba_wnba_data 
                WHERE season IS NULL
                GROUP BY source_table, data_category, league_name, league_id
                ORDER BY count DESC
                LIMIT 20
            """).fetchall()
            
            logger.info(f"\n📈 PATTERNS IN NULL SEASON RECORDS:")
            for source_table, category, league_name, league_id, count in null_patterns:
                logger.info(f"   {source_table} | {category} | {league_name} | League ID: {league_id} | Count: {count:,}")
            
            # Check if we can extract season from source files
            source_file_analysis = conn.execute("""
                SELECT 
                    source_file,
                    COUNT(*) as count
                FROM unified_nba_wnba_data 
                WHERE season IS NULL AND source_file IS NOT NULL
                GROUP BY source_file
                ORDER BY count DESC
                LIMIT 10
            """).fetchall()
            
            logger.info(f"\n📁 SOURCE FILE ANALYSIS:")
            for source_file, count in source_file_analysis:
                logger.info(f"   {source_file}: {count:,} records")
            
            # Check if we can extract season from raw data
            raw_data_samples = conn.execute("""
                SELECT raw_data
                FROM unified_nba_wnba_data 
                WHERE season IS NULL AND raw_data IS NOT NULL
                LIMIT 5
            """).fetchall()
            
            logger.info(f"\n📋 RAW DATA SAMPLES (for season extraction):")
            for i, (raw_data,) in enumerate(raw_data_samples):
                try:
                    data = json.loads(raw_data)
                    season_fields = [k for k in data.keys() if 'season' in k.lower()]
                    if season_fields:
                        logger.info(f"   Sample {i+1}: Found season fields: {season_fields}")
                        for field in season_fields[:3]:  # Show first 3 season fields
                            logger.info(f"      {field}: {data.get(field)}")
                except:
                    logger.info(f"   Sample {i+1}: Could not parse JSON")
            
            conn.close()
            
            return {
                'null_samples': null_samples,
                'null_patterns': null_patterns,
                'source_file_analysis': source_file_analysis,
                'raw_data_samples': raw_data_samples
            }
            
        except Exception as e:
            logger.error(f"❌ Error investigating NULL seasons: {e}")
            return {'error': str(e)}
    
    def fix_null_seasons_from_source_files(self) -> int:
        """Fix NULL seasons by extracting from source file names"""
        logger.info("\n🔧 FIXING NULL SEASONS FROM SOURCE FILES")
        logger.info("=" * 45)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get records with NULL seasons but valid source files
            null_records = cursor.execute("""
                SELECT id, source_file, league_name, league_id
                FROM unified_nba_wnba_data 
                WHERE season IS NULL AND source_file IS NOT NULL
                LIMIT 1000
            """).fetchall()
            
            fixed_count = 0
            
            for record_id, source_file, league_name, league_id in null_records:
                extracted_season = None
                
                # Try to extract season from source file name
                # Look for patterns like 2024-25, 2024, etc.
                season_patterns = [
                    r'(20\d{2}-\d{2})',  # NBA format: 2024-25
                    r'(20\d{2})',        # WNBA format: 2024
                ]
                
                for pattern in season_patterns:
                    match = re.search(pattern, source_file)
                    if match:
                        extracted_season = match.group(1)
                        break
                
                # If no season found in filename, use defaults based on league
                if not extracted_season:
                    if league_id == '00' or league_name == 'NBA':
                        extracted_season = '2024-25'  # Current NBA season
                    elif league_id == '10' or league_name == 'WNBA':
                        extracted_season = '2025'     # Current WNBA season
                    else:
                        extracted_season = '2024-25'  # Default
                
                # Update the record
                cursor.execute("""
                    UPDATE unified_nba_wnba_data 
                    SET season = ?
                    WHERE id = ?
                """, (extracted_season, record_id))
                
                fixed_count += 1
                
                if fixed_count % 100 == 0:
                    logger.info(f"   Fixed {fixed_count} records...")
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Fixed {fixed_count:,} NULL seasons from source files")
            return fixed_count
            
        except Exception as e:
            logger.error(f"❌ Error fixing NULL seasons from source files: {e}")
            return 0
    
    def fix_null_seasons_from_raw_data(self) -> int:
        """Fix NULL seasons by extracting from raw data JSON"""
        logger.info("\n🔧 FIXING NULL SEASONS FROM RAW DATA")
        logger.info("=" * 40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get records with NULL seasons but valid raw data
            null_records = cursor.execute("""
                SELECT id, raw_data, league_name, league_id
                FROM unified_nba_wnba_data 
                WHERE season IS NULL AND raw_data IS NOT NULL
                LIMIT 1000
            """).fetchall()
            
            fixed_count = 0
            
            for record_id, raw_data, league_name, league_id in null_records:
                extracted_season = None
                
                try:
                    data = json.loads(raw_data)
                    
                    # Look for season-related fields in the JSON
                    season_fields = [
                        'SEASON_ID', 'Season', 'season', 'SEASON',
                        'SEASON_YEAR', 'SeasonYear', 'season_year'
                    ]
                    
                    for field in season_fields:
                        if field in data and data[field]:
                            season_value = str(data[field])
                            
                            # Clean up the season value
                            if re.match(r'^20\d{2}-\d{2}$', season_value):
                                extracted_season = season_value
                                break
                            elif re.match(r'^20\d{2}$', season_value):
                                extracted_season = season_value
                                break
                            elif season_value.startswith('2') and len(season_value) >= 4:
                                # Try to extract year from longer strings
                                year_match = re.search(r'(20\d{2})', season_value)
                                if year_match:
                                    year = year_match.group(1)
                                    if league_name == 'NBA':
                                        next_year = str(int(year) + 1)[-2:]
                                        extracted_season = f"{year}-{next_year}"
                                    else:
                                        extracted_season = year
                                    break
                    
                except json.JSONDecodeError:
                    pass
                
                # If no season found in raw data, use defaults
                if not extracted_season:
                    if league_id == '00' or league_name == 'NBA':
                        extracted_season = '2024-25'
                    elif league_id == '10' or league_name == 'WNBA':
                        extracted_season = '2025'
                    else:
                        extracted_season = '2024-25'
                
                # Update the record
                cursor.execute("""
                    UPDATE unified_nba_wnba_data 
                    SET season = ?
                    WHERE id = ?
                """, (extracted_season, record_id))
                
                fixed_count += 1
                
                if fixed_count % 100 == 0:
                    logger.info(f"   Fixed {fixed_count} records...")
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Fixed {fixed_count:,} NULL seasons from raw data")
            return fixed_count
            
        except Exception as e:
            logger.error(f"❌ Error fixing NULL seasons from raw data: {e}")
            return 0
    
    def fix_remaining_null_seasons(self) -> int:
        """Fix any remaining NULL seasons with intelligent defaults"""
        logger.info("\n🔧 FIXING REMAINING NULL SEASONS")
        logger.info("=" * 35)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Fix based on league
            nba_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET season = '2024-25'
                WHERE season IS NULL 
                AND (league_id = '00' OR league_name = 'NBA')
            """).rowcount
            
            wnba_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET season = '2025'
                WHERE season IS NULL 
                AND (league_id = '10' OR league_name = 'WNBA')
            """).rowcount
            
            # Fix any remaining with default NBA season
            remaining_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET season = '2024-25'
                WHERE season IS NULL
            """).rowcount
            
            total_fixes = nba_fixes + wnba_fixes + remaining_fixes
            
            logger.info(f"✅ Fixed {nba_fixes:,} NBA records → 2024-25")
            logger.info(f"✅ Fixed {wnba_fixes:,} WNBA records → 2025")
            logger.info(f"✅ Fixed {remaining_fixes:,} remaining records → 2024-25")
            
            conn.commit()
            conn.close()
            
            return total_fixes
            
        except Exception as e:
            logger.error(f"❌ Error fixing remaining NULL seasons: {e}")
            return 0
    
    def verify_null_season_cleanup(self) -> Dict[str, Any]:
        """Verify that all NULL seasons have been fixed"""
        logger.info("\n📊 VERIFYING NULL SEASON CLEANUP")
        logger.info("=" * 35)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Check remaining NULL seasons
            remaining_nulls = conn.execute("""
                SELECT COUNT(*) 
                FROM unified_nba_wnba_data 
                WHERE season IS NULL
            """).fetchone()[0]
            
            # Get final season breakdown
            final_seasons = conn.execute("""
                SELECT 
                    COALESCE(season, 'NULL') as season,
                    COUNT(*) as count 
                FROM unified_nba_wnba_data 
                GROUP BY season 
                ORDER BY count DESC
                LIMIT 15
            """).fetchall()
            
            logger.info(f"📊 FINAL SEASON BREAKDOWN (Top 15):")
            for season, count in final_seasons:
                logger.info(f"   {season}: {count:,} records")
            
            logger.info(f"\n🎯 CLEANUP RESULTS:")
            logger.info(f"   Remaining NULL seasons: {remaining_nulls:,}")
            
            if remaining_nulls == 0:
                logger.info(f"✅ ALL NULL SEASONS SUCCESSFULLY FIXED!")
            else:
                logger.warning(f"⚠️ {remaining_nulls:,} NULL seasons still need fixing")
            
            conn.close()
            
            return {
                'remaining_nulls': remaining_nulls,
                'final_seasons': final_seasons
            }
            
        except Exception as e:
            logger.error(f"❌ Error verifying NULL season cleanup: {e}")
            return {'error': str(e)}

def main():
    """Execute NULL season investigation and fixing"""
    
    fixer = NullSeasonFixer()
    
    # Investigate NULL seasons
    investigation = fixer.investigate_null_seasons()
    
    # Fix NULL seasons using multiple methods
    source_file_fixes = fixer.fix_null_seasons_from_source_files()
    raw_data_fixes = fixer.fix_null_seasons_from_raw_data()
    remaining_fixes = fixer.fix_remaining_null_seasons()
    
    # Verify cleanup
    verification = fixer.verify_null_season_cleanup()
    
    
    total_fixes = source_file_fixes + raw_data_fixes + remaining_fixes
    
    if 'remaining_nulls' in verification:
        
        if verification['remaining_nulls'] == 0:
        else:
    
    return {
        'investigation': investigation,
        'fixes': {
            'source_file_fixes': source_file_fixes,
            'raw_data_fixes': raw_data_fixes,
            'remaining_fixes': remaining_fixes,
            'total_fixes': total_fixes
        },
        'verification': verification
    }

if __name__ == "__main__":
    main()
