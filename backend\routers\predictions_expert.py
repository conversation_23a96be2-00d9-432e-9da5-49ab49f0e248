import logging
from fastapi import APIRouter, HTTPException
from datetime import datetime

# DEPRECATED: This file has been consolidated into unified_predictions.py
# This file is kept for reference but should not be used in production

"""
DEPRECATED - HYPER MEDUSA NEURAL VAULT - Expert Prediction Router
================================================================

This router has been consolidated into unified_predictions.py for better
maintainability and to eliminate code duplication.

Please use backend.routers.unified_predictions instead.
"""


logger = logging.getLogger("hyper_medusa_neural_vault.deprecated_predictions_expert")
logger.warning("⚠️ DEPRECATED: predictions_expert.py router is deprecated. Use unified_predictions.py instead.")

# Minimal router for backward compatibility
router = APIRouter(
    prefix="/api/v1/predictions/expert/legacy",
    tags=["Deprecated - Legacy Expert Predictions"],
    deprecated=True
)

@router.get("/")
async def deprecated_expert_notice():
    """Deprecation notice for legacy expert prediction router"""
    return {
        "status": "deprecated",
        "message": "This expert prediction router has been deprecated and consolidated into unified_predictions.py",
        "redirect_to": "/api/v1/predictions/unified",
        "expert_mode": "Use prediction_mode='expert' in unified predictions",
        "timestamp": datetime.now()
    }
