# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: tensorboard/compat/proto/step_stats.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from tensorboard.compat.proto import allocation_description_pb2 as tensorboard_dot_compat_dot_proto_dot_allocation__description__pb2
from tensorboard.compat.proto import tensor_description_pb2 as tensorboard_dot_compat_dot_proto_dot_tensor__description__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n)tensorboard/compat/proto/step_stats.proto\x12\x0btensorboard\x1a\x35tensorboard/compat/proto/allocation_description.proto\x1a\x31tensorboard/compat/proto/tensor_description.proto\"=\n\x10\x41llocationRecord\x12\x14\n\x0c\x61lloc_micros\x18\x01 \x01(\x03\x12\x13\n\x0b\x61lloc_bytes\x18\x02 \x01(\x03\"\xc5\x01\n\x13\x41llocatorMemoryUsed\x12\x16\n\x0e\x61llocator_name\x18\x01 \x01(\t\x12\x13\n\x0btotal_bytes\x18\x02 \x01(\x03\x12\x12\n\npeak_bytes\x18\x03 \x01(\x03\x12\x12\n\nlive_bytes\x18\x04 \x01(\x03\x12\x39\n\x12\x61llocation_records\x18\x06 \x03(\x0b\x32\x1d.tensorboard.AllocationRecord\x12\x1e\n\x16\x61llocator_bytes_in_use\x18\x05 \x01(\x03\"V\n\nNodeOutput\x12\x0c\n\x04slot\x18\x01 \x01(\x05\x12:\n\x12tensor_description\x18\x03 \x01(\x0b\x32\x1e.tensorboard.TensorDescription\"\xec\x01\n\x0bMemoryStats\x12\x18\n\x10temp_memory_size\x18\x01 \x01(\x03\x12\x1e\n\x16persistent_memory_size\x18\x03 \x01(\x03\x12#\n\x1bpersistent_tensor_alloc_ids\x18\x05 \x03(\x03\x12#\n\x17\x64\x65vice_temp_memory_size\x18\x02 \x01(\x03\x42\x02\x18\x01\x12)\n\x1d\x64\x65vice_persistent_memory_size\x18\x04 \x01(\x03\x42\x02\x18\x01\x12.\n\"device_persistent_tensor_alloc_ids\x18\x06 \x03(\x03\x42\x02\x18\x01\"\xa2\x04\n\rNodeExecStats\x12\x11\n\tnode_name\x18\x01 \x01(\t\x12\x18\n\x10\x61ll_start_micros\x18\x02 \x01(\x03\x12\x1b\n\x13op_start_rel_micros\x18\x03 \x01(\x03\x12\x19\n\x11op_end_rel_micros\x18\x04 \x01(\x03\x12\x1a\n\x12\x61ll_end_rel_micros\x18\x05 \x01(\x03\x12\x30\n\x06memory\x18\x06 \x03(\x0b\x32 .tensorboard.AllocatorMemoryUsed\x12\'\n\x06output\x18\x07 \x03(\x0b\x32\x17.tensorboard.NodeOutput\x12\x16\n\x0etimeline_label\x18\x08 \x01(\t\x12\x18\n\x10scheduled_micros\x18\t \x01(\x03\x12\x11\n\tthread_id\x18\n \x01(\r\x12=\n\x11referenced_tensor\x18\x0b \x03(\x0b\x32\".tensorboard.AllocationDescription\x12.\n\x0cmemory_stats\x18\x0c \x01(\x0b\x32\x18.tensorboard.MemoryStats\x12\x17\n\x0f\x61ll_start_nanos\x18\r \x01(\x03\x12\x1a\n\x12op_start_rel_nanos\x18\x0e \x01(\x03\x12\x18\n\x10op_end_rel_nanos\x18\x0f \x01(\x03\x12\x19\n\x11\x61ll_end_rel_nanos\x18\x10 \x01(\x03\x12\x17\n\x0fscheduled_nanos\x18\x11 \x01(\x03\"\xca\x01\n\x0f\x44\x65viceStepStats\x12\x0e\n\x06\x64\x65vice\x18\x01 \x01(\t\x12.\n\nnode_stats\x18\x02 \x03(\x0b\x32\x1a.tensorboard.NodeExecStats\x12\x43\n\x0cthread_names\x18\x03 \x03(\x0b\x32-.tensorboard.DeviceStepStats.ThreadNamesEntry\x1a\x32\n\x10ThreadNamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\r\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"<\n\tStepStats\x12/\n\tdev_stats\x18\x01 \x03(\x0b\x32\x1c.tensorboard.DeviceStepStatsB\x83\x01\n\x18org.tensorflow.frameworkB\x0fStepStatsProtosP\x01ZQgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto\xf8\x01\x01\x62\x06proto3')



_ALLOCATIONRECORD = DESCRIPTOR.message_types_by_name['AllocationRecord']
_ALLOCATORMEMORYUSED = DESCRIPTOR.message_types_by_name['AllocatorMemoryUsed']
_NODEOUTPUT = DESCRIPTOR.message_types_by_name['NodeOutput']
_MEMORYSTATS = DESCRIPTOR.message_types_by_name['MemoryStats']
_NODEEXECSTATS = DESCRIPTOR.message_types_by_name['NodeExecStats']
_DEVICESTEPSTATS = DESCRIPTOR.message_types_by_name['DeviceStepStats']
_DEVICESTEPSTATS_THREADNAMESENTRY = _DEVICESTEPSTATS.nested_types_by_name['ThreadNamesEntry']
_STEPSTATS = DESCRIPTOR.message_types_by_name['StepStats']
AllocationRecord = _reflection.GeneratedProtocolMessageType('AllocationRecord', (_message.Message,), {
  'DESCRIPTOR' : _ALLOCATIONRECORD,
  '__module__' : 'tensorboard.compat.proto.step_stats_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.AllocationRecord)
  })
_sym_db.RegisterMessage(AllocationRecord)

AllocatorMemoryUsed = _reflection.GeneratedProtocolMessageType('AllocatorMemoryUsed', (_message.Message,), {
  'DESCRIPTOR' : _ALLOCATORMEMORYUSED,
  '__module__' : 'tensorboard.compat.proto.step_stats_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.AllocatorMemoryUsed)
  })
_sym_db.RegisterMessage(AllocatorMemoryUsed)

NodeOutput = _reflection.GeneratedProtocolMessageType('NodeOutput', (_message.Message,), {
  'DESCRIPTOR' : _NODEOUTPUT,
  '__module__' : 'tensorboard.compat.proto.step_stats_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.NodeOutput)
  })
_sym_db.RegisterMessage(NodeOutput)

MemoryStats = _reflection.GeneratedProtocolMessageType('MemoryStats', (_message.Message,), {
  'DESCRIPTOR' : _MEMORYSTATS,
  '__module__' : 'tensorboard.compat.proto.step_stats_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.MemoryStats)
  })
_sym_db.RegisterMessage(MemoryStats)

NodeExecStats = _reflection.GeneratedProtocolMessageType('NodeExecStats', (_message.Message,), {
  'DESCRIPTOR' : _NODEEXECSTATS,
  '__module__' : 'tensorboard.compat.proto.step_stats_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.NodeExecStats)
  })
_sym_db.RegisterMessage(NodeExecStats)

DeviceStepStats = _reflection.GeneratedProtocolMessageType('DeviceStepStats', (_message.Message,), {

  'ThreadNamesEntry' : _reflection.GeneratedProtocolMessageType('ThreadNamesEntry', (_message.Message,), {
    'DESCRIPTOR' : _DEVICESTEPSTATS_THREADNAMESENTRY,
    '__module__' : 'tensorboard.compat.proto.step_stats_pb2'
    # @@protoc_insertion_point(class_scope:tensorboard.DeviceStepStats.ThreadNamesEntry)
    })
  ,
  'DESCRIPTOR' : _DEVICESTEPSTATS,
  '__module__' : 'tensorboard.compat.proto.step_stats_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.DeviceStepStats)
  })
_sym_db.RegisterMessage(DeviceStepStats)
_sym_db.RegisterMessage(DeviceStepStats.ThreadNamesEntry)

StepStats = _reflection.GeneratedProtocolMessageType('StepStats', (_message.Message,), {
  'DESCRIPTOR' : _STEPSTATS,
  '__module__' : 'tensorboard.compat.proto.step_stats_pb2'
  # @@protoc_insertion_point(class_scope:tensorboard.StepStats)
  })
_sym_db.RegisterMessage(StepStats)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n\030org.tensorflow.frameworkB\017StepStatsProtosP\001ZQgithub.com/tensorflow/tensorflow/tensorflow/go/core/framework/step_stats_go_proto\370\001\001'
  _MEMORYSTATS.fields_by_name['device_temp_memory_size']._options = None
  _MEMORYSTATS.fields_by_name['device_temp_memory_size']._serialized_options = b'\030\001'
  _MEMORYSTATS.fields_by_name['device_persistent_memory_size']._options = None
  _MEMORYSTATS.fields_by_name['device_persistent_memory_size']._serialized_options = b'\030\001'
  _MEMORYSTATS.fields_by_name['device_persistent_tensor_alloc_ids']._options = None
  _MEMORYSTATS.fields_by_name['device_persistent_tensor_alloc_ids']._serialized_options = b'\030\001'
  _DEVICESTEPSTATS_THREADNAMESENTRY._options = None
  _DEVICESTEPSTATS_THREADNAMESENTRY._serialized_options = b'8\001'
  _ALLOCATIONRECORD._serialized_start=164
  _ALLOCATIONRECORD._serialized_end=225
  _ALLOCATORMEMORYUSED._serialized_start=228
  _ALLOCATORMEMORYUSED._serialized_end=425
  _NODEOUTPUT._serialized_start=427
  _NODEOUTPUT._serialized_end=513
  _MEMORYSTATS._serialized_start=516
  _MEMORYSTATS._serialized_end=752
  _NODEEXECSTATS._serialized_start=755
  _NODEEXECSTATS._serialized_end=1301
  _DEVICESTEPSTATS._serialized_start=1304
  _DEVICESTEPSTATS._serialized_end=1506
  _DEVICESTEPSTATS_THREADNAMESENTRY._serialized_start=1456
  _DEVICESTEPSTATS_THREADNAMESENTRY._serialized_end=1506
  _STEPSTATS._serialized_start=1508
  _STEPSTATS._serialized_end=1568
# @@protoc_insertion_point(module_scope)
