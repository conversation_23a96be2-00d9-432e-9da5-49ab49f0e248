<img src="./HMNV LOGO.png" alt="HYPER MEDUSA NEURAL VAULT Logo" width="180"/>

HYPER MEDUSA NEURAL VAULT
Codebase-Integrated Elite Branding Guide
1. Brand Essence & Philosophy
Mission:
Empower advanced thinkers and organizations with mythic intelligence, quantum security, and neural precision—reflected in every model, API, and UI component.

Values:

Visionary Innovation (GameModel, PlayerModel, TeamModel)
Unbreakable Trust (UserModel, security_clearance, two_factor_enabled)
Elite Performance (expertise_score, vault_mastery_level)
Mystique & Authority (Medusa/Greek motifs, shield/circuit iconography)
Precision & Clarity (data validation, access control, audit fields)
2. Logo System
Primary Logo:

Medusa shield with circuit and gold accents (SVG/PNG, used in /media/, /public/, and as favicon)
Use in all flagship assets, mapped to logo_url in TeamModel and app config
Clear Space & Sizing:

Minimum clear space: 1 snake head width (CSS variable: --logo-clear-space)
Minimum size: 64px digital, 20mm print
Backgrounds:

Use Vault Black (#181A1B) or Medusa Green (#0B2F23), as in UI theme config
Incorrect Usage:

Never stretch, recolor, or crop (enforced in UI component props and CSS)
3. Color System (Code-Linked)
Name	HEX	Usage (Code Reference)ON 
Medusa Green	#0B2F23	theme.colors.primary, backgrounds
Quantum Gold	#C9A14A	theme.colors.accent, headings, CTAs
Vault Black	#181A1B	theme.colors.background, text
Circuit Emerald	#1B5C3A	theme.colors.secondary
Oracle Silver	#BFC9C5	theme.colors.muted, UI elements
Aegis Bronze	#8C6B3F	theme.colors.button, borders
Signal Red	#D94F4F	theme.colors.error, alerts
Wisdom White	#F5F7F6	theme.colors.textLight
Implementation:

All colors defined in /src/theme.js or /src/styles/theme.ts
Use CSS variables for easy updates and consistency
4. Typography
Primary:

Playfair Display or EB Garamond (imported in /src/styles/fonts.css)
Used for logo, H1, and major titles (see Typography components)
Secondary:

Inter or Roboto (for body, UI, and technical docs)
Used in all Text, Label, and Button components
Monospace:

JetBrains Mono (for code, logs, and data tables)
Hierarchy:

H1: Playfair Display Bold, Quantum Gold, font-size: 2.5rem
H2: Playfair Display SemiBold, Medusa Green, font-size: 2rem
Body: Inter Regular, Vault Black, font-size: 1rem
Captions: Inter Light, Oracle Silver, font-size: 0.85rem
Implementation:

Typography tokens in /src/styles/typography.ts
Enforced via Typography and Heading React/Vue components
5. Iconography & Motifs
All icons as SVGs in /src/assets/icons/, matching logo style
Motifs: snakes, shields, neural circuits, tablets, quantum gates
Used in navigation, buttons, and status indicators (e.g., “Vault Synergy Score” badge, “Snake Streaks” visualizer)
6. Imagery & Illustration
Custom illustrations in /media/ and /src/assets/illustrations/
All images color-graded to match brand palette
No generic stock; all art is custom or AI-generated in Medusa style
7. UI/UX Guidelines (Code-Connected)
Dark mode default (theme.mode = 'dark')
Gold for CTAs, highlights, and important metrics (Button and Alert components)
Emerald for active states, confirmations, and progress (ProgressBar, Badge)
Red for errors, warnings, and destructive actions (Alert, Modal)
All UI elements mapped to real data models (e.g., UserModel.subscription_tier for feature gating)
Animations:
Snake ripple (GSAP/Framer Motion in /src/components/animations/)
Medusa spinner for loading (/src/components/Loader)
Emerald checkmark for success, Signal Red pulse for errors
Accessibility:

All icons/images have alt text (enforced in components)
Color contrast meets WCAG AA (checked in /src/styles/)
Keyboard navigation and ARIA labels throughout
8. Tone, Messaging & Copy
Confident, clear, mythic gravitas (see /src/content/ and i18n files)
Mythic metaphors only to reinforce intelligence/security
No exclamation marks in professional copy
All copy spell-checked and reviewed
9. Applications & Asset Usage
Big:

Website, dashboards, investor decks, event banners, video intros (assets in /public/, /media/) Medium:
Business cards, letterhead, email signatures, swag (assets in /media/print/) Small:
App icons, favicons, social avatars, watermarks, loading animations (SVG/PNG in /src/assets/) Tiny:
Favicon: /public/favicon.ico
Mobile splash screens: /public/splash/
Email preheader: “Unlock the Vault of Intelligence”
QR codes: /media/qr/
404/empty/error states: /src/components/Error/
Print bleed/safe zone specs: /media/print/
Legal disclaimers: /src/components/Footer/
Accessibility alt text: enforced in all image/icon components
10. Brand Asset Management
All assets versioned in /media/ and /src/assets/
Usage notes in /docs/BRAND_ASSETS.md
Asset update process: PR review, version history, deprecated assets archived
11. Legal & Compliance
Trademark logo, name, and key motifs
Copyright notice in /src/components/Footer/
Brand usage guidelines in /docs/BRAND_GUIDE.md
All third-party fonts/images properly licensed
12. Brand Governance
Brand steward/team for all approvals
Quarterly review of all new assets and campaigns
Annual update of this guide
Brand onboarding for all new hires (ONBOARDING_GUIDE.md)
13. Consistency Checklist (for every asset/feature)
<input disabled="" type="checkbox"> Logo used correctly, with proper clear space and size
<input disabled="" type="checkbox"> Colors match palette and pass accessibility checks
<input disabled="" type="checkbox"> Typography follows hierarchy and microtypography rules
<input disabled="" type="checkbox"> Icons and imagery are on-brand and consistent
<input disabled="" type="checkbox"> All micro-assets (favicons, splash screens, QR codes, etc.) are present and correct
<input disabled="" type="checkbox"> Copy is clear, confident, and spell-checked
<input disabled="" type="checkbox"> Legal and accessibility requirements are met
<input disabled="" type="checkbox"> Asset is stored in the brand repository with versioning
<input disabled="" type="checkbox"> All UI/UX elements mapped to real data models and API endpoints

1. End-User Experience & Flow (with Tier Integration)
Onboarding & First Impressions
Animated Medusa Shield Splash: On first load, display a cinematic Medusa shield animation with gold circuit effects and a personalized welcome (name, tier badge).
Role-Aware Onboarding:
New users: Interactive, step-by-step tour with mythic metaphors, highlighting only features available to their tier.
Returning users: “Since you last visited” dashboard with key changes, new features, and personalized insights.
Tier Reveal:
Prominently display user’s tier (e.g., “Oracle”, “Aegis”, “Initiate”) with animated badge, color-coded to tier, and a tooltip explaining exclusive benefits.
Upgrade prompts are subtle, gold-accented, and mythic—never pushy.
Navigation & Daily Use
Dynamic Sidebar:
Medusa logo always visible, with tier badge overlay.
Navigation items shown/hidden based on tier and security clearance.
Hover/click triggers subtle snake ripple and circuit glow effects.
Tier-Gated Features:
Premium analytics, real-time dashboards, and advanced controls are visually “locked” for lower tiers, with animated gold shimmer and mythic tooltips.
Attempting to access a locked feature triggers a Medusa-themed modal explaining the value and upgrade path.
Live Metrics & Status:
Real-time system health, “Vault Synergy Score,” and user-specific stats always visible, with tier-based data granularity.
Personalization:
Users can customize dashboard layout, color accents, and notification preferences (more options for higher tiers).
“Snake Streaks” and achievement visualizers celebrate user milestones, with more elaborate effects for elite tiers.
Actions & Feedback
Microinteractions:
Every button, toggle, and data update has a custom, mythic animation and haptic-like feedback.
Success: Emerald checkmark with ripple. Error: Signal Red pulse with Medusa motif.
Contextual Help:
Tier-aware tooltips, quick tips, and contextual onboarding for new features.
“Ask the Oracle” AI assistant for elite tiers, providing advanced guidance and insights.
Security & Trust
Quantum Security Visuals:
Animated shield/circuit overlays on sensitive actions.
Real-time trust pulse and session status, with tier-based security controls (e.g., 2FA, device management).
Audit & Transparency:
Users can view their own access logs, feature usage, and security events—more detail for higher tiers.
Support & Community
In-App Support:
Tier-based support: live chat for elite, ticketing for standard, knowledge base for all.
“Vault Council” community for top tiers, with exclusive content and early feature access.
Offboarding & Retention
Graceful Downgrade:
If a user downgrades, show a mythic “Vault Gate” animation, clearly indicating which features are now locked, and offer a one-click upgrade path.
Exit Experience:
On logout or account deletion, display a custom Medusa animation and a summary of achievements, with a personalized message.
2. Tier System Integration (Code & UI)
Tier Model:
UserModel includes tier, security_clearance, and vault_mastery_level.
All UI/UX elements check tier before rendering or enabling features.
Tier Tokens:
Colors, badges, and animations are mapped to tier (e.g., Oracle = Quantum Gold, Aegis = Circuit Emerald, Initiate = Oracle Silver).
Upgrade Flow:
Seamless, in-app upgrade with animated transitions, clear value props, and instant access to new features.
API Integration:
All API endpoints enforce tier-based access and return tier-specific data granularity.
3. Elite, Expensive, and Hard-to-Replicate Touches
Cinematic Animations:
GSAP/Framer Motion for shield, snake, and circuit effects.
Parallax and depth on hero sections and dashboards.
Custom Data Visualizations:
Proprietary “Vault Synergy Score” and “Snake Streaks” charts, only available here.
3D, interactive, and animated data displays.
Micro-Assets & Details:
Custom cursor, branded sound cues, watermarked assets, and print/export layouts.
Every error, empty, and loading state has a unique, mythic illustration and actionable suggestion.
Accessibility & Performance:
All icons/images have alt text, color contrast meets WCAG AA, full keyboard navigation, and ARIA labels.
Blazing fast load times, code splitting, and image optimization.
4. Consistency, Governance, and Brand Protection
Quarterly Brand Audits:
Automated/manual review of every asset, component, and copy string.
Immutable Brand Tokens:
All design tokens are versioned and locked for each release.
Onboarding for All:
Every new contributor must pass a brand and accessibility checklist before merging.
5. Often-Forgotten Details (You Won’t Miss)
Alt text and ARIA labels for every image/icon.
Focus management and skip links for accessibility.
Microcopy for all error, empty, and loading states.
Versioning and documentation for every asset and component.
Keyboard navigation and touch support everywhere.
Color contrast and reduced motion for accessibility.
Legal notices and asset usage tracking.
Automated tests for every component and flow.
Consistent use of design tokens and theme variables.
Onboarding and help for new users and contributors.codebase for seamless implementation.

The HMNV Brand as a Moat: A Complete Implementation Guide (v3.0)
Document Version: 3.0
Last Updated: Tuesday, July 1, 2025, 10:31 PM EDT
Steward Location: Winston-Salem, NC
Mission: To empower advanced thinkers with mythic intelligence and quantum security, reflected in every model, API, and UI component. This guide is the architectural blueprint for that mission.

Core Thesis: The Hyper Medusa Neural Vault brand is not a visual layer; it is the product's architecture. It is a compiled system where our values—Visionary Innovation, Unbreakable Trust, Elite Performance—are enforced by code. This document outlines the four pillars of this strategy, ensuring every asset and feature is a tangible manifestation of our brand promise.

Pillar 1: The Brand as a Compiled System
Our visual identity is not a set of assets but a version-controlled Brand SDK (@hmnv/core-ui), the single source of truth for all frontend development.

The Sigil (Logo System): The official logo (hmnv-sigil.svg) is the system's core identifier.

Implementation: A dedicated <Sigil> React/Vue component encapsulates all brand rules. It enforces a minimum size of 64px and clear space defined by the CSS variable --logo-clear-space. It must be rendered against Vault Black (#181A1B) or Medusa Green (#0B2F23) backgrounds.

Animation: The embedded circuits subtly glow on hover. During system processing, it animates with the "Medusa spinner" effect, driven by GSAP/Framer Motion.

The Palette (Color System): All colors are defined as version-controlled design tokens in /src/theme.ts. Use of raw hex values is a linting error.

Medusa Green #0B2F23: theme.colors.primary (backgrounds)

Quantum Gold #C9A14A: theme.colors.accent (headings, CTAs)

Vault Black #181A1B: theme.colors.background

Circuit Emerald #1B5C3A: theme.colors.secondary (active states, progress)

Oracle Silver #BFC9C5: theme.colors.muted (UI elements)

Aegis Bronze #8C6B3F: theme.colors.button (borders)

Signal Red #D94F4F: theme.colors.error

Wisdom White #F5F7F6: theme.colors.textLight

The Voice (Typography): The brand's tone is enforced through dedicated components.

Primary (Playfair Display): Used for major titles via the <H1> and <H2> components.

Secondary (Inter): Used for all body copy and UI text via the <Text> component.

Monospace (JetBrains Mono): Used for all code and log displays via the <CodeBlock> component.

Pillar 2: The Tiered Experience as a Core API Contract
The user's status is a first-class citizen of our architecture, enforced from the database to the UI.

The Model (UserModel): The user's tier, security_clearance, and vault_mastery_level are the foundation of their experience. All UI elements and API endpoints check these values before rendering or returning data.

Tier-Gated UI: Features are not just hidden; they are not rendered. The code for the "Ask the Oracle" AI assistant is not included in the bundle for non-Oracle tiers. Visually "locked" features use the Quantum Gold shimmer effect to entice upgrades.

Tier-Aware API & Context: API endpoints return different data structures based on tier. All sensitive responses are wrapped with a session_context object for an unbreakable audit trail.


Tier Tokens: The visual language of status is explicitly mapped:

Oracle Tier: Quantum Gold accents, full data granularity, live chat support.

Aegis Tier: Circuit Emerald accents, standard data, ticketing support.

Initiate Tier: Oracle Silver accents, limited data, knowledge base support.

Pillar 3: Perceived Value Through "Computational Elegance"
Our "elite, expensive" feel comes from hard-to-replicate, data-driven visuals.

The Living Interface: The UI is alive with brand motifs.

Sigil: The Medusa logo's eyes subtly track the cursor, and its circuits pulse with CPU load.

Animations: The "Snake ripple" effect is used on navigation interactions. The "Medusa spinner" is the one and only loading animation. All are built with GSAP/Framer Motion as specified in /src/components/animations/.

Data as Art: We do not show numbers; we reveal intelligence.

The "Vault Synergy Score" is a custom data visualization badge, not just a metric.

"Snake Streaks" are a proprietary chart type that visualizes user milestones and consistent engagement.

Ambient Awareness: The system's knowledge of its context (Winston-Salem, 10:31 PM) is used to create an intelligent feel. The login screen may feature a subtle, abstract star map corresponding to the user's location, with a soft Circuit Emerald pulse.

Pillar 4: Governance as Code (GovOps)
Brand consistency is not left to chance; it is enforced by our CI/CD pipeline. The 13-point "Consistency Checklist" is the specification for this automated system.

Automated Brand Compliance Pipeline: Every pull request must pass the brand-compliance.yml workflow, which runs:

Token & Color Check: Lints for any hard-coded colors or fonts not using the theme.ts tokens.

Accessibility Audit: Runs Axe-core to ensure all components and color combinations meet WCAG AA standards. Fails the build if contrast ratios are insufficient or ARIA labels are missing.

Asset Verification: Checks that all images and icons are pulled from /src/assets/ or /media/ and that the primary logo is always the <Sigil> component.

Microcopy Lint: Runs a spell-check and searches for forbidden copy (e.g., exclamation marks).

Visual Regression Testing: A Percy/Chromatic job compares every component against the versioned design system in Storybook. Any unintended visual change (including on all micro-assets like favicons, QR codes, and error states) is a failing check.

Documentation & Onboarding: All brand documentation, including BRAND_GUIDE.md and ONBOARDING_GUIDE.md, is stored in /docs/ and is part of the required reading for any new contributor.

This comprehensive system ensures that the Hyper Medusa Neural Vault brand is not just a guide, but the immutable, expert-level architecture of our product itself.