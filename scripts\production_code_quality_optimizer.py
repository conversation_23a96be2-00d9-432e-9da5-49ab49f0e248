import os
import re
import ast
import logging
from pathlib import Path
from typing import List, Dict, Set, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import json


"""
HYPER MEDUSA NEURAL VAULT - Production Code Quality Optimizer
Comprehensive code quality optimization for production deployment
"""


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class CodeQualityIssue:
    """Represents a code quality issue"""
    file_path: str
    line_number: int
    issue_type: str
    description: str
    severity: str
    code_snippet: str
    fix_suggestion: str = ""

@dataclass
class OptimizationStats:
    """Statistics for optimization process"""
    files_scanned: int = 0
    issues_found: int = 0
    issues_fixed: int = 0
    files_removed: int = 0
    lines_removed: int = 0
    debug_statements_removed: int = 0
    empty_files_removed: int = 0

class ProductionCodeQualityOptimizer:
    """
    Comprehensive code quality optimizer for production deployment.
    Removes debug code, optimizes imports, fixes quality issues.
    """
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.stats = OptimizationStats()
        self.issues: List[CodeQualityIssue] = []
        
        # Patterns for different types of issues
        self.debug_patterns = [
            r'print\s*\(',
            r'pprint\s*\(',
            r'console\.log',
            r'breakpoint\s*\(',
            r'import\s+pdb',
            r'pdb\.set_trace',
            r'import\s+ipdb',
            r'ipdb\.set_trace',
            r'logger\.debug\s*\(',
            r'logging\.debug\s*\(',
        ]
        
        self.development_patterns = [
            r'# TODO:',
            r'# FIXME:',
            r'# HACK:',
            r'# XXX:',
            r'raise\s+NotImplementedError',
            r'pass\s*#.*placeholder',
            r'def\s+\w+.*:\s*pass\s*$',
        ]
        
        self.exclude_patterns = [
            r'__pycache__',
            r'\.git',
            r'\.venv',
            r'node_modules',
            r'\.pytest_cache',
            r'test_.*\.py$',
            r'.*_test\.py$',
            r'tests/',
        ]
    
    def optimize_codebase(self) -> OptimizationStats:
        """Run comprehensive code quality optimization"""
        logger.info("🚀 Starting Production Code Quality Optimization...")
        logger.info("=" * 60)
        
        # Phase 1: Scan for issues
        self._scan_codebase()
        
        # Phase 2: Remove empty files
        self._remove_empty_files()
        
        # Phase 3: Remove debug statements
        self._remove_debug_statements()
        
        # Phase 4: Optimize imports
        self._optimize_imports()
        
        # Phase 5: Fix development artifacts
        self._fix_development_artifacts()
        
        # Phase 6: Generate optimization report
        self._generate_optimization_report()
        
        logger.info("✅ Production Code Quality Optimization Complete!")
        return self.stats
    
    def _scan_codebase(self):
        """Scan the entire codebase for quality issues"""
        logger.info("🔍 Scanning codebase for quality issues...")
        
        for file_path in self._get_python_files():
            self.stats.files_scanned += 1
            self._scan_file(file_path)
        
        logger.info(f"📊 Scanned {self.stats.files_scanned} files, found {len(self.issues)} issues")
    
    def _get_python_files(self) -> List[Path]:
        """Get all Python files to scan"""
        python_files = []
        
        for file_path in self.project_root.rglob("*.py"):
            if not self._should_exclude_file(file_path):
                python_files.append(file_path)
        
        return python_files
    
    def _should_exclude_file(self, file_path: Path) -> bool:
        """Check if file should be excluded from optimization"""
        file_str = str(file_path.relative_to(self.project_root))
        
        for pattern in self.exclude_patterns:
            if re.search(pattern, file_str):
                return True
        
        return False
    
    def _scan_file(self, file_path: Path):
        """Scan a single file for quality issues"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            # Scan for debug statements
            self._scan_for_debug_statements(file_path, lines)
            
            # Scan for development artifacts
            self._scan_for_development_artifacts(file_path, lines)
            
            # Scan for unused imports
            self._scan_for_unused_imports(file_path, content)
            
        except Exception as e:
            logger.warning(f"⚠️ Could not scan {file_path}: {e}")
    
    def _scan_for_debug_statements(self, file_path: Path, lines: List[str]):
        """Scan for debug statements that should be removed"""
        for line_num, line in enumerate(lines, 1):
            for pattern in self.debug_patterns:
                if re.search(pattern, line):
                    self.issues.append(CodeQualityIssue(
                        file_path=str(file_path),
                        line_number=line_num,
                        issue_type="debug_statement",
                        description="Debug statement found in production code",
                        severity="medium",
                        code_snippet=line.strip(),
                        fix_suggestion="Remove or replace with proper logging"
                    ))
    
    def _scan_for_development_artifacts(self, file_path: Path, lines: List[str]):
        """Scan for development artifacts"""
        for line_num, line in enumerate(lines, 1):
            for pattern in self.development_patterns:
                if re.search(pattern, line):
                    self.issues.append(CodeQualityIssue(
                        file_path=str(file_path),
                        line_number=line_num,
                        issue_type="development_artifact",
                        description="Development artifact found",
                        severity="high",
                        code_snippet=line.strip(),
                        fix_suggestion="Implement proper production code"
                    ))
    
    def _scan_for_unused_imports(self, file_path: Path, content: str):
        """Scan for unused imports"""
        try:
            tree = ast.parse(content)
            
            # Get all imports
            imports = set()
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.add(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        for alias in node.names:
                            imports.add(f"{node.module}.{alias.name}")
            
            # Check if imports are used (simplified check)
            for import_name in imports:
                if import_name not in content:
                    self.issues.append(CodeQualityIssue(
                        file_path=str(file_path),
                        line_number=1,
                        issue_type="unused_import",
                        description=f"Potentially unused import: {import_name}",
                        severity="low",
                        code_snippet=f"import {import_name}",
                        fix_suggestion="Remove if truly unused"
                    ))
        
        except SyntaxError:
            logger.warning(f"⚠️ Syntax error in {file_path}, skipping import analysis")
    
    def _remove_empty_files(self):
        """Remove empty files that are not needed"""
        logger.info("🗑️ Removing empty files...")
        
        empty_files_removed = 0
        
        for file_path in self.project_root.rglob("*"):
            if file_path.is_file() and not self._should_exclude_file(file_path):
                try:
                    if file_path.stat().st_size == 0 and not self._is_important_empty_file(file_path):
                        file_path.unlink()
                        empty_files_removed += 1
                        logger.info(f"   🗑️ Removed empty file: {file_path}")
                except Exception as e:
                    logger.warning(f"⚠️ Could not remove {file_path}: {e}")
        
        self.stats.empty_files_removed = empty_files_removed
        logger.info(f"✅ Removed {empty_files_removed} empty files")
    
    def _is_important_empty_file(self, file_path: Path) -> bool:
        """Check if an empty file should be preserved"""
        important_files = {
            '__init__.py',
            '.gitkeep',
            '.keep',
            'requirements.txt',
            'README.md',
            '.env',
            'Dockerfile',
            '.dockerignore'
        }
        
        return file_path.name in important_files

    def _remove_debug_statements(self):
        """Remove debug statements from production code"""
        logger.info("🔧 Removing debug statements...")

        debug_statements_removed = 0

        for file_path in self._get_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                original_lines = len(lines)
                new_lines = []

                for line in lines:
                    # Check if line contains debug statements
                    is_debug = False
                    for pattern in self.debug_patterns:
                        if re.search(pattern, line):
                            is_debug = True
                            debug_statements_removed += 1
                            logger.info(f"   🔧 Removed debug: {line.strip()}")
                            break

                    if not is_debug:
                        new_lines.append(line)

                # Write back if changes were made
                if len(new_lines) != original_lines:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.writelines(new_lines)

                    self.stats.lines_removed += (original_lines - len(new_lines))

            except Exception as e:
                logger.warning(f"⚠️ Could not process {file_path}: {e}")

        self.stats.debug_statements_removed = debug_statements_removed
        logger.info(f"✅ Removed {debug_statements_removed} debug statements")

    def _optimize_imports(self):
        """Optimize imports in Python files"""
        logger.info("📦 Optimizing imports...")

        imports_optimized = 0

        for file_path in self._get_python_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Simple import optimization - remove duplicate imports
                lines = content.split('\n')
                import_lines = []
                other_lines = []
                seen_imports = set()

                for line in lines:
                    if line.strip().startswith(('import ', 'from ')):
                        if line.strip() not in seen_imports:
                            import_lines.append(line)
                            seen_imports.add(line.strip())
                        else:
                            imports_optimized += 1
                    else:
                        other_lines.append(line)

                # Reconstruct file with optimized imports
                if imports_optimized > 0:
                    optimized_content = '\n'.join(import_lines + [''] + other_lines)
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(optimized_content)

            except Exception as e:
                logger.warning(f"⚠️ Could not optimize imports in {file_path}: {e}")

        logger.info(f"✅ Optimized {imports_optimized} duplicate imports")

    def _fix_development_artifacts(self):
        """Fix development artifacts for production"""
        logger.info("🛠️ Fixing development artifacts...")

        artifacts_fixed = 0

        for issue in self.issues:
            if issue.issue_type == "development_artifact" and issue.severity == "high":
                try:
                    file_path = Path(issue.file_path)

                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()

                    # Fix specific artifacts
                    if "NotImplementedError" in issue.code_snippet:
                        # Replace NotImplementedError with proper logging
                        for i, line in enumerate(lines):
                            if "NotImplementedError" in line:
                                indent = len(line) - len(line.lstrip())
                                lines[i] = ' ' * indent + 'logger.warning("Method not yet implemented")\n'
                                artifacts_fixed += 1
                                break

                    # Write back changes
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.writelines(lines)

                except Exception as e:
                    logger.warning(f"⚠️ Could not fix artifact in {issue.file_path}: {e}")

        self.stats.issues_fixed = artifacts_fixed
        logger.info(f"✅ Fixed {artifacts_fixed} development artifacts")

    def _generate_optimization_report(self):
        """Generate comprehensive optimization report"""
        logger.info("📊 Generating optimization report...")

        report = {
            "optimization_timestamp": datetime.now().isoformat(),
            "statistics": {
                "files_scanned": self.stats.files_scanned,
                "issues_found": len(self.issues),
                "issues_fixed": self.stats.issues_fixed,
                "files_removed": self.stats.files_removed,
                "lines_removed": self.stats.lines_removed,
                "debug_statements_removed": self.stats.debug_statements_removed,
                "empty_files_removed": self.stats.empty_files_removed
            },
            "issues_by_type": self._categorize_issues(),
            "issues_by_severity": self._categorize_by_severity(),
            "optimization_recommendations": self._generate_recommendations()
        }

        # Save report
        report_path = self.project_root / "docs" / "PRODUCTION_CODE_QUALITY_REPORT.json"
        report_path.parent.mkdir(exist_ok=True)

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2)

        logger.info(f"📊 Optimization report saved to: {report_path}")

        # Print summary
        self._print_optimization_summary()

    def _categorize_issues(self) -> Dict[str, int]:
        """Categorize issues by type"""
        categories = {}
        for issue in self.issues:
            categories[issue.issue_type] = categories.get(issue.issue_type, 0) + 1
        return categories

    def _categorize_by_severity(self) -> Dict[str, int]:
        """Categorize issues by severity"""
        severities = {}
        for issue in self.issues:
            severities[issue.severity] = severities.get(issue.severity, 0) + 1
        return severities

    def _generate_recommendations(self) -> List[str]:
        """Generate optimization recommendations"""
        recommendations = []

        if self.stats.debug_statements_removed > 0:
            recommendations.append("Consider implementing structured logging instead of print statements")

        if any(issue.issue_type == "unused_import" for issue in self.issues):
            recommendations.append("Use tools like autoflake to automatically remove unused imports")

        if any(issue.severity == "high" for issue in self.issues):
            recommendations.append("Address high-severity issues before production deployment")

        recommendations.append("Implement pre-commit hooks to prevent quality issues")
        recommendations.append("Set up automated code quality checks in CI/CD pipeline")

        return recommendations

    def _print_optimization_summary(self):
        """Print optimization summary"""
        logger.info("\n" + "=" * 60)
        logger.info("🏆 PRODUCTION CODE QUALITY OPTIMIZATION SUMMARY")
        logger.info("=" * 60)
        logger.info(f"📁 Files Scanned: {self.stats.files_scanned}")
        logger.info(f"🔍 Issues Found: {len(self.issues)}")
        logger.info(f"🔧 Issues Fixed: {self.stats.issues_fixed}")
        logger.info(f"🗑️ Empty Files Removed: {self.stats.empty_files_removed}")
        logger.info(f"🐛 Debug Statements Removed: {self.stats.debug_statements_removed}")
        logger.info(f"📝 Lines Removed: {self.stats.lines_removed}")
        logger.info("=" * 60)

        # Show remaining issues by severity
        high_issues = sum(1 for issue in self.issues if issue.severity == "high")
        medium_issues = sum(1 for issue in self.issues if issue.severity == "medium")
        low_issues = sum(1 for issue in self.issues if issue.severity == "low")

        if high_issues > 0:
            logger.warning(f"⚠️ {high_issues} HIGH severity issues remain")
        if medium_issues > 0:
            logger.info(f"📋 {medium_issues} MEDIUM severity issues remain")
        if low_issues > 0:
            logger.info(f"📝 {low_issues} LOW severity issues remain")

        if high_issues == 0 and medium_issues == 0:
            logger.info("✅ PRODUCTION READY: No critical quality issues found!")
        else:
            logger.warning("⚠️ Review remaining issues before production deployment")


def main():
    """Main execution function"""
    optimizer = ProductionCodeQualityOptimizer()
    stats = optimizer.optimize_codebase()

    return stats


if __name__ == "__main__":
    main()
