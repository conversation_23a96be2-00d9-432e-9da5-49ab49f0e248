import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
import json
import re

#!/usr/bin/env python3
"""
Fix None League Records
Investigate and fix records with None/NULL league assignments
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NoneLeagueRecordsFixer:
    """Fix records with None/NULL league assignments"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        
    def investigate_none_league_records(self) -> Dict[str, Any]:
        """Investigate what the None league records are"""
        logger.info("🔍 INVESTIGATING NONE LEAGUE RECORDS")
        logger.info("=" * 40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Count None league records
            none_count = conn.execute("""
                SELECT COUNT(*) 
                FROM unified_nba_wnba_data 
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'None'
            """).fetchone()[0]
            
            logger.info(f"📊 TOTAL NONE LEAGUE RECORDS: {none_count:,}")
            
            # Get sample of None league records
            none_samples = conn.execute("""
                SELECT 
                    id, source_file, source_table, data_category, season,
                    league_id, player_id, team_id, game_id, raw_data
                FROM unified_nba_wnba_data 
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'None'
                LIMIT 20
            """).fetchall()
            
            logger.info(f"\n📊 SAMPLE OF NONE LEAGUE RECORDS:")
            for i, record in enumerate(none_samples[:10]):
                logger.info(f"   {i+1}. ID {record[0]}: {record[1]} | {record[2]} | Season: {record[4]} | League ID: {record[5]}")
            
            # Analyze patterns in None league records
            none_patterns = conn.execute("""
                SELECT 
                    COALESCE(source_table, 'No Source Table') as source_table,
                    COALESCE(data_category, 'No Category') as data_category,
                    COALESCE(season, 'No Season') as season,
                    COALESCE(league_id, 'No League ID') as league_id,
                    COUNT(*) as count
                FROM unified_nba_wnba_data 
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'None'
                GROUP BY source_table, data_category, season, league_id
                ORDER BY count DESC
                LIMIT 15
            """).fetchall()
            
            logger.info(f"\n📈 PATTERNS IN NONE LEAGUE RECORDS:")
            for source_table, category, season, league_id, count in none_patterns:
                logger.info(f"   {source_table} | {category} | {season} | League ID: {league_id} | Count: {count:,}")
            
            # Check if we can infer league from league_id
            league_id_analysis = conn.execute("""
                SELECT 
                    COALESCE(league_id, 'NULL') as league_id,
                    COUNT(*) as count
                FROM unified_nba_wnba_data 
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'None'
                GROUP BY league_id
                ORDER BY count DESC
            """).fetchall()
            
            logger.info(f"\n🏀 LEAGUE ID ANALYSIS:")
            for league_id, count in league_id_analysis:
                logger.info(f"   League ID '{league_id}': {count:,} records")
            
            conn.close()
            
            return {
                'none_count': none_count,
                'none_samples': none_samples,
                'none_patterns': none_patterns,
                'league_id_analysis': league_id_analysis
            }
            
        except Exception as e:
            logger.error(f"❌ Error investigating None league records: {e}")
            return {'error': str(e)}
    
    def fix_none_league_by_league_id(self) -> int:
        """Fix None league records using league_id field"""
        logger.info("\n🔧 FIXING NONE LEAGUE BY LEAGUE_ID")
        logger.info("=" * 35)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Fix records where league_id = '00' (NBA)
            nba_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'NBA'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'None')
                AND league_id = '00'
            """).rowcount
            
            logger.info(f"✅ Fixed {nba_fixes:,} records with league_id '00' → NBA")
            
            # Fix records where league_id = '10' (WNBA)
            wnba_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'WNBA'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'None')
                AND league_id = '10'
            """).rowcount
            
            logger.info(f"✅ Fixed {wnba_fixes:,} records with league_id '10' → WNBA")
            
            conn.commit()
            conn.close()
            
            return nba_fixes + wnba_fixes
            
        except Exception as e:
            logger.error(f"❌ Error fixing None league by league_id: {e}")
            return 0
    
    def fix_none_league_by_season_format(self) -> int:
        """Fix None league records using season format patterns"""
        logger.info("\n🔧 FIXING NONE LEAGUE BY SEASON FORMAT")
        logger.info("=" * 40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Fix records with NBA season format (YYYY-YY)
            nba_season_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'NBA', league_id = '00'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'None')
                AND season LIKE '____-__'
                AND season NOT LIKE '%-%-%'
                AND length(season) = 7
            """).rowcount
            
            logger.info(f"✅ Fixed {nba_season_fixes:,} records with NBA season format → NBA")
            
            # Fix records with WNBA season format (YYYY)
            wnba_season_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'WNBA', league_id = '10'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'None')
                AND season LIKE '20__'
                AND length(season) = 4
                AND CAST(season AS INTEGER) >= 1997
                AND CAST(season AS INTEGER) <= 2030
            """).rowcount
            
            logger.info(f"✅ Fixed {wnba_season_fixes:,} records with WNBA season format → WNBA")
            
            conn.commit()
            conn.close()
            
            return nba_season_fixes + wnba_season_fixes
            
        except Exception as e:
            logger.error(f"❌ Error fixing None league by season format: {e}")
            return 0
    
    def fix_none_league_by_source_file(self) -> int:
        """Fix None league records using source file patterns"""
        logger.info("\n🔧 FIXING NONE LEAGUE BY SOURCE FILE")
        logger.info("=" * 35)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Fix records with NBA in source file
            nba_file_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'NBA', league_id = '00'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'None')
                AND (LOWER(source_file) LIKE '%nba%' 
                     OR LOWER(source_table) LIKE '%nba%'
                     OR LOWER(data_category) LIKE '%nba%')
            """).rowcount
            
            logger.info(f"✅ Fixed {nba_file_fixes:,} records with NBA file patterns → NBA")
            
            # Fix records with WNBA in source file
            wnba_file_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'WNBA', league_id = '10'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'None')
                AND (LOWER(source_file) LIKE '%wnba%' 
                     OR LOWER(source_table) LIKE '%wnba%'
                     OR LOWER(data_category) LIKE '%wnba%')
            """).rowcount
            
            logger.info(f"✅ Fixed {wnba_file_fixes:,} records with WNBA file patterns → WNBA")
            
            conn.commit()
            conn.close()
            
            return nba_file_fixes + wnba_file_fixes
            
        except Exception as e:
            logger.error(f"❌ Error fixing None league by source file: {e}")
            return 0
    
    def fix_remaining_none_league_records(self) -> int:
        """Fix any remaining None league records with intelligent defaults"""
        logger.info("\n🔧 FIXING REMAINING NONE LEAGUE RECORDS")
        logger.info("=" * 40)
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check what's left
            remaining_none = cursor.execute("""
                SELECT 
                    source_file, source_table, data_category, season, COUNT(*) as count
                FROM unified_nba_wnba_data 
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'None'
                GROUP BY source_file, source_table, data_category, season
                ORDER BY count DESC
                LIMIT 10
            """).fetchall()
            
            logger.info(f"📊 REMAINING NONE LEAGUE PATTERNS:")
            for source_file, source_table, category, season, count in remaining_none:
                logger.info(f"   {source_file} | {source_table} | {category} | {season} | Count: {count}")
            
            # For system/cache records, assign based on context
            system_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'NBA', league_id = '00'
                WHERE (league_name IS NULL OR league_name = '' OR league_name = 'None')
                AND (source_table IN ('Settings', 'Cache', 'collection_progress')
                     OR data_category IN ('system_settings', 'api_cache', 'collection_metadata'))
            """).rowcount
            
            logger.info(f"✅ Fixed {system_fixes:,} system/cache records → NBA (default)")
            
            # For any remaining records, use NBA as default
            remaining_fixes = cursor.execute("""
                UPDATE unified_nba_wnba_data 
                SET league_name = 'NBA', league_id = '00'
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'None'
            """).rowcount
            
            logger.info(f"✅ Fixed {remaining_fixes:,} remaining records → NBA (default)")
            
            conn.commit()
            conn.close()
            
            return system_fixes + remaining_fixes
            
        except Exception as e:
            logger.error(f"❌ Error fixing remaining None league records: {e}")
            return 0
    
    def verify_none_league_cleanup(self) -> Dict[str, Any]:
        """Verify that all None league records have been fixed"""
        logger.info("\n📊 VERIFYING NONE LEAGUE CLEANUP")
        logger.info("=" * 35)
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Check remaining None league records
            remaining_none = conn.execute("""
                SELECT COUNT(*) 
                FROM unified_nba_wnba_data 
                WHERE league_name IS NULL OR league_name = '' OR league_name = 'None'
            """).fetchone()[0]
            
            # Get final league breakdown
            final_leagues = conn.execute("""
                SELECT 
                    COALESCE(league_name, 'NULL') as league,
                    COUNT(*) as count 
                FROM unified_nba_wnba_data 
                GROUP BY league_name 
                ORDER BY count DESC
            """).fetchall()
            
            total_records = sum(count for _, count in final_leagues)
            
            logger.info(f"📊 FINAL LEAGUE BREAKDOWN:")
            for league, count in final_leagues:
                percentage = (count / total_records) * 100
                logger.info(f"   {league}: {count:,} records ({percentage:.1f}%)")
            
            logger.info(f"\n🎯 CLEANUP RESULTS:")
            logger.info(f"   Total records: {total_records:,}")
            logger.info(f"   Remaining None leagues: {remaining_none:,}")
            
            if remaining_none == 0:
                logger.info(f"✅ ALL NONE LEAGUE RECORDS SUCCESSFULLY FIXED!")
                logger.info(f"🏀 Database now has clean NBA/WNBA league assignments")
            else:
                logger.warning(f"⚠️ {remaining_none:,} None league records still need fixing")
            
            conn.close()
            
            return {
                'remaining_none': remaining_none,
                'final_leagues': final_leagues,
                'total_records': total_records
            }
            
        except Exception as e:
            logger.error(f"❌ Error verifying None league cleanup: {e}")
            return {'error': str(e)}

def main():
    """Execute None league records investigation and fixing"""
    
    fixer = NoneLeagueRecordsFixer()
    
    # Investigate None league records
    investigation = fixer.investigate_none_league_records()
    
    # Fix None league records using multiple methods
    league_id_fixes = fixer.fix_none_league_by_league_id()
    season_format_fixes = fixer.fix_none_league_by_season_format()
    source_file_fixes = fixer.fix_none_league_by_source_file()
    remaining_fixes = fixer.fix_remaining_none_league_records()
    
    # Verify cleanup
    verification = fixer.verify_none_league_cleanup()
    
    
    total_fixes = league_id_fixes + season_format_fixes + source_file_fixes + remaining_fixes
    
    if 'remaining_none' in verification:
        
        if verification['remaining_none'] == 0:
        else:
    
    return {
        'investigation': investigation,
        'fixes': {
            'league_id_fixes': league_id_fixes,
            'season_format_fixes': season_format_fixes,
            'source_file_fixes': source_file_fixes,
            'remaining_fixes': remaining_fixes,
            'total_fixes': total_fixes
        },
        'verification': verification
    }

if __name__ == "__main__":
    main()
