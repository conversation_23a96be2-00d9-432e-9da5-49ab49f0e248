import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any
import requests
import time
import json
from datetime import datetime
import sys
import os
from smart_duplicate_prevention_system import SmartDuplicatePreventionSystem


#!/usr/bin/env python3
"""
Phase 6 - Critical Contextual Data Collection
Target the most important missing contextual data for elite player props predictions
"""


# Add the scripts directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase6CriticalContextualDataCollector:
    """Collect critical missing contextual data for elite analytics"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }
        
        # Critical contextual endpoints
        self.critical_endpoints = {
            'shotchartdetail': {
                'description': 'Detailed shot charts with location data',
                'priority': 'CRITICAL',
                'params': {
                    'PlayerID': None,
                    'Season': None,
                    'SeasonType': 'Regular Season',
                    'LeagueID': None,
                    'TeamID': 0,
                    'GameID': '',
                    'Outcome': '',
                    'Location': '',
                    'Month': 0,
                    'SeasonSegment': '',
                    'DateFrom': '',
                    'DateTo': '',
                    'OpponentTeamID': 0,
                    'VsConference': '',
                    'VsDivision': '',
                    'Position': '',
                    'RookieYear': '',
                    'GameSegment': '',
                    'Period': 0,
                    'LastNGames': 0,
                    'ContextMeasure': 'FGA'
                }
            },
            'playbyplayv2': {
                'description': 'Play-by-play data for games',
                'priority': 'HIGH',
                'params': {
                    'GameID': None,
                    'StartPeriod': 1,
                    'EndPeriod': 10
                }
            },
            'winprobabilitypbp': {
                'description': 'Win probability by play',
                'priority': 'HIGH',
                'params': {
                    'GameID': None,
                    'RunType': 'each second'
                }
            }
        }
        
        # Initialize duplicate prevention
        logger.info("🔍 Initializing Smart Duplicate Prevention System...")
        self.duplicate_prevention = SmartDuplicatePreventionSystem(db_path)
        logger.info("✅ Duplicate prevention system ready")
        
    def get_sample_games(self, league: str, limit: int = 10) -> List[str]:
        """Get sample game IDs for contextual data collection"""
        logger.info(f"📅 Getting sample games for {league} contextual data...")
        
        try:
            # Use leaguegamelog to get recent games
            url = f"{self.base_url}/leaguegamelog"
            params = {
                'Season': '2024-25' if league == 'NBA' else '2025',
                'SeasonType': 'Regular Season',
                'LeagueID': '00' if league == 'NBA' else '10',
                'PlayerOrTeam': 'T',
                'Direction': 'DESC',
                'Sorter': 'DATE'
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            game_ids = set()
            if 'resultSets' in data and len(data['resultSets']) > 0:
                headers = data['resultSets'][0].get('headers', [])
                rows = data['resultSets'][0].get('rowSet', [])
                
                if 'GAME_ID' in headers:
                    game_id_idx = headers.index('GAME_ID')
                    for row in rows[:limit*3]:  # Get extra in case of duplicates
                        if len(row) > game_id_idx and len(game_ids) < limit:
                            game_ids.add(str(row[game_id_idx]))
            
            game_list = list(game_ids)[:limit]
            logger.info(f"✅ Found {len(game_list)} sample games for {league}")
            
            time.sleep(0.6)
            return game_list
            
        except Exception as e:
            logger.warning(f"⚠️ Error getting sample games for {league}: {e}")
            return []
    
    def get_sample_players(self, league: str, limit: int = 20) -> List[str]:
        """Get ACTIVE CURRENT SEASON player IDs for contextual data collection"""
        logger.info(f"👥 Getting ACTIVE {league} players for contextual data...")

        try:
            # Use commonallplayers to get ONLY ACTIVE CURRENT SEASON players
            url = f"{self.base_url}/commonallplayers"
            params = {
                'Season': '2024-25' if league == 'NBA' else '2025',
                'LeagueID': '00' if league == 'NBA' else '10',
                'IsOnlyCurrentSeason': 1  # CRITICAL: Only current season active players
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            player_ids = []
            if 'resultSets' in data and len(data['resultSets']) > 0:
                headers = data['resultSets'][0].get('headers', [])
                rows = data['resultSets'][0].get('rowSet', [])

                if 'PERSON_ID' in headers:
                    player_id_idx = headers.index('PERSON_ID')
                    # Get additional fields for filtering active players
                    roster_status_idx = headers.index('ROSTERSTATUS') if 'ROSTERSTATUS' in headers else None
                    from_year_idx = headers.index('FROM_YEAR') if 'FROM_YEAR' in headers else None
                    to_year_idx = headers.index('TO_YEAR') if 'TO_YEAR' in headers else None

                    for row in rows:
                        if len(row) > player_id_idx and len(player_ids) < limit:
                            # Filter for ACTIVE players only
                            is_active = True

                            # Check roster status if available
                            if roster_status_idx is not None and len(row) > roster_status_idx:
                                roster_status = row[roster_status_idx]
                                if roster_status == 0:  # 0 = inactive, 1 = active
                                    is_active = False

                            # Check if player is current (to_year should be current or future)
                            if to_year_idx is not None and len(row) > to_year_idx:
                                to_year = row[to_year_idx]
                                current_year = 2024 if league == 'NBA' else 2025
                                if to_year and int(to_year) < current_year:
                                    is_active = False

                            if is_active:
                                player_ids.append(str(row[player_id_idx]))
            
            logger.info(f"✅ Found {len(player_ids)} ACTIVE players for {league} (filtered from {len(rows)} total)")

            time.sleep(0.6)
            return player_ids
            
        except Exception as e:
            logger.warning(f"⚠️ Error getting active players from commonallplayers for {league}: {e}")
            logger.info(f"🔄 Trying alternative method: getting players from recent game logs...")
            return self.get_active_players_from_games(league, limit)

    def get_active_players_from_games(self, league: str, limit: int) -> List[str]:
        """Alternative method: Get active players from recent game logs"""
        try:
            # Use leaguegamelog to get players who actually played recently
            url = f"{self.base_url}/leaguegamelog"
            params = {
                'Season': '2024-25' if league == 'NBA' else '2025',
                'SeasonType': 'Regular Season',
                'LeagueID': '00' if league == 'NBA' else '10',
                'PlayerOrTeam': 'P',  # P = Player logs
                'Direction': 'DESC',
                'Sorter': 'DATE'
            }

            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()

            active_player_ids = set()
            if 'resultSets' in data and len(data['resultSets']) > 0:
                headers = data['resultSets'][0].get('headers', [])
                rows = data['resultSets'][0].get('rowSet', [])

                if 'PLAYER_ID' in headers:
                    player_id_idx = headers.index('PLAYER_ID')
                    for row in rows:
                        if len(row) > player_id_idx and len(active_player_ids) < limit:
                            active_player_ids.add(str(row[player_id_idx]))

            active_players_list = list(active_player_ids)[:limit]
            logger.info(f"✅ Found {len(active_players_list)} ACTIVE players from recent games for {league}")

            time.sleep(0.6)
            return active_players_list

        except Exception as e:
            logger.warning(f"⚠️ Error getting active players from game logs for {league}: {e}")
            return []
    
    def collect_shot_chart_data(self, player_id: str, league: str, season: str) -> List[Dict[str, Any]]:
        """Collect shot chart data for a player"""
        
        # Check if we already have this data
        source_file_signature = f"shotchartdetail_player_{player_id}_{season}.json"
        if source_file_signature in self.duplicate_prevention.existing_source_files:
            return []
        
        collected_data = []
        
        try:
            logger.info(f"   🎯 Collecting shot chart for player {player_id} {league} {season}...")
            
            url = f"{self.base_url}/shotchartdetail"
            params = self.critical_endpoints['shotchartdetail']['params'].copy()
            
            params['PlayerID'] = player_id
            params['Season'] = season
            params['LeagueID'] = '00' if league == 'NBA' else '10'
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                # Shot chart data is typically in the first result set
                result_set = data['resultSets'][0]
                headers = result_set.get('headers', [])
                rows = result_set.get('rowSet', [])
                
                for row in rows:
                    if len(row) == len(headers):
                        row_dict = dict(zip(headers, row))
                        
                        record = {
                            'league_name': league,
                            'league_id': '00' if league == 'NBA' else '10',
                            'season': season,
                            'player_id': player_id,
                            'endpoint': 'shotchartdetail',
                            'data_category': 'phase6_shot_chart',
                            'data_type': 'shot_chart_detail',
                            'source_file': source_file_signature,
                            'source_table': 'shotchartdetail',
                            'raw_data': json.dumps(row_dict),
                            'created_at': datetime.now().isoformat()
                        }
                        
                        # Extract shot location data
                        record['player_name'] = row_dict.get('PLAYER_NAME', f'PLAYER_{player_id}')
                        record['team_id'] = row_dict.get('TEAM_ID')
                        record['team_abbreviation'] = row_dict.get('TEAM_NAME')
                        record['game_id'] = row_dict.get('GAME_ID')
                        record['game_date'] = row_dict.get('GAME_DATE')
                        record['stat_category'] = 'shot_detail'
                        record['stat_value'] = 1 if row_dict.get('SHOT_MADE_FLAG') == 1 else 0
                        record['shot_distance'] = row_dict.get('SHOT_DISTANCE', 0)
                        record['loc_x'] = row_dict.get('LOC_X', 0)
                        record['loc_y'] = row_dict.get('LOC_Y', 0)
                        record['shot_zone'] = row_dict.get('SHOT_ZONE_BASIC', '')
                        record['shot_type'] = row_dict.get('ACTION_TYPE', '')
                        
                        collected_data.append(record)
            
            time.sleep(0.6)  # Rate limiting
            
        except Exception as e:
            logger.warning(f"⚠️ Error collecting shot chart for player {player_id}: {e}")
        
        logger.info(f"✅ Collected {len(collected_data)} shot chart records for player {player_id}")
        return collected_data
    
    def collect_play_by_play_data(self, game_id: str, league: str) -> List[Dict[str, Any]]:
        """Collect play-by-play data for a game"""
        
        # Check if we already have this data
        source_file_signature = f"playbyplayv2_game_{game_id}.json"
        if source_file_signature in self.duplicate_prevention.existing_source_files:
            return []
        
        collected_data = []
        
        try:
            logger.info(f"   📝 Collecting play-by-play for game {game_id}...")
            
            url = f"{self.base_url}/playbyplayv2"
            params = self.critical_endpoints['playbyplayv2']['params'].copy()
            params['GameID'] = game_id
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                result_set = data['resultSets'][0]
                headers = result_set.get('headers', [])
                rows = result_set.get('rowSet', [])
                
                for row in rows:
                    if len(row) == len(headers):
                        row_dict = dict(zip(headers, row))
                        
                        record = {
                            'league_name': league,
                            'league_id': '00' if league == 'NBA' else '10',
                            'season': '2024-25' if league == 'NBA' else '2025',
                            'game_id': game_id,
                            'endpoint': 'playbyplayv2',
                            'data_category': 'phase6_play_by_play',
                            'data_type': 'play_by_play_detail',
                            'source_file': source_file_signature,
                            'source_table': 'playbyplayv2',
                            'raw_data': json.dumps(row_dict),
                            'created_at': datetime.now().isoformat()
                        }
                        
                        # Extract play context
                        record['player_id'] = row_dict.get('PLAYER1_ID') or 'TEAM_PLAY'
                        record['player_name'] = row_dict.get('PLAYER1_NAME') or 'TEAM_ACTION'
                        record['team_id'] = row_dict.get('PLAYER1_TEAM_ID')
                        record['stat_category'] = 'play_action'
                        record['stat_value'] = row_dict.get('SCORE', 0)
                        record['period'] = row_dict.get('PERIOD', 0)
                        record['time_remaining'] = row_dict.get('PCTIMESTRING', '')
                        record['event_type'] = row_dict.get('EVENTMSGTYPE', 0)
                        record['action_type'] = row_dict.get('EVENTMSGACTIONTYPE', 0)
                        record['description'] = row_dict.get('HOMEDESCRIPTION') or row_dict.get('VISITORDESCRIPTION') or ''
                        
                        collected_data.append(record)
            
            time.sleep(0.6)  # Rate limiting
            
        except Exception as e:
            logger.warning(f"⚠️ Error collecting play-by-play for game {game_id}: {e}")
        
        logger.info(f"✅ Collected {len(collected_data)} play-by-play records for game {game_id}")
        return collected_data
    
    def insert_contextual_data(self, data_batch: List[Dict[str, Any]]) -> int:
        """Insert contextual data with duplicate prevention"""
        if not data_batch:
            return 0
        
        # Filter duplicates
        new_records, duplicate_stats = self.duplicate_prevention.filter_new_data_only(data_batch)
        
        if duplicate_stats['duplicates'] > 0:
            logger.info(f"   🔍 Filtered out {duplicate_stats['duplicates']} duplicates, inserting {duplicate_stats['new_records']} new records")
        
        if not new_records:
            return 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        
        for record in new_records:
            try:
                cursor.execute("""
                    INSERT INTO unified_nba_wnba_data (
                        source_file, source_table, data_category, season, league_id, league_name,
                        season_type, player_id, player_name, team_id, team_abbreviation,
                        stat_category, stat_value, game_id, game_date, data_type, raw_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['source_file'], record['source_table'], record['data_category'],
                    record['season'], record['league_id'], record['league_name'],
                    'Regular Season', record['player_id'], record['player_name'],
                    record.get('team_id'), record.get('team_abbreviation'),
                    record.get('stat_category'), record.get('stat_value'),
                    record.get('game_id'), record.get('game_date'),
                    record['data_type'], record['raw_data'], record['created_at']
                ))
                inserted_count += 1
                
            except Exception as e:
                logger.warning(f"⚠️ Error inserting record: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        return inserted_count
    
    def collect_critical_contextual_data(self, league: str) -> Dict[str, Any]:
        """Collect critical contextual data for a league"""
        logger.info(f"🚀 PHASE 6 CRITICAL CONTEXTUAL DATA COLLECTION - {league}")
        logger.info("=" * 75)
        
        start_time = datetime.now()
        total_records = 0
        
        # Get comprehensive data for full collection
        sample_games = self.get_sample_games(league, limit=100)  # FULL SCALE: 100 games per league
        sample_players = self.get_sample_players(league, limit=200)  # FULL SCALE: 200 players per league
        
        # Collect shot chart data
        logger.info(f"\n🎯 Collecting shot chart data for {len(sample_players)} players...")
        season = '2024-25' if league == 'NBA' else '2025'
        
        for player_id in sample_players:
            shot_data = self.collect_shot_chart_data(player_id, league, season)
            if shot_data:
                inserted = self.insert_contextual_data(shot_data)
                total_records += inserted
                if inserted > 0:
                    logger.info(f"   ✅ Player {player_id}: {inserted:,} shot records")
        
        # Collect play-by-play data
        logger.info(f"\n📝 Collecting play-by-play data for {len(sample_games)} games...")
        
        for game_id in sample_games:
            pbp_data = self.collect_play_by_play_data(game_id, league)
            if pbp_data:
                inserted = self.insert_contextual_data(pbp_data)
                total_records += inserted
                if inserted > 0:
                    logger.info(f"   ✅ Game {game_id}: {inserted:,} play records")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎉 PHASE 6 {league} CONTEXTUAL COLLECTION COMPLETE!")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   📊 Total records: {total_records:,}")
        
        return {
            'success': True,
            'league': league,
            'duration': str(duration),
            'total_records': total_records
        }

def main():
    """Execute Phase 6 FULL-SCALE critical contextual data collection"""
    
    collector = Phase6CriticalContextualDataCollector()
    
    # Start with NBA
    nba_results = collector.collect_critical_contextual_data('NBA')
    
    if nba_results['success']:
        
        # Then WNBA
        wnba_results = collector.collect_critical_contextual_data('WNBA')
        
        if wnba_results['success']:
            
            total_records = nba_results['total_records'] + wnba_results['total_records']
            


            grand_total = 2460 + total_records
    
    return nba_results

if __name__ == "__main__":
    main()
