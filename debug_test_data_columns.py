#!/usr/bin/env python3
"""
Debug script to check what columns are available in the test data
"""

import sys
from pathlib import Path
import pandas as pd

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.data.basketball_data_loader import BasketballDataLoader
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """Check the columns in the test data"""
    try:
        # Load the data
        loader = BasketballDataLoader()
        wnba_data = loader.load_training_data('WNBA')
        
        logger.info(f"📊 Data shape: {wnba_data.shape}")
        logger.info(f"📊 Columns ({len(wnba_data.columns)}): {list(wnba_data.columns)}")
        
        # Check for win-related columns
        win_columns = [col for col in wnba_data.columns if 'win' in col.lower() or 'wl' in col.lower()]
        logger.info(f"🎯 Win-related columns: {win_columns}")
        
        # Check for target columns
        target_columns = [col for col in wnba_data.columns if col in ['win_prediction', 'HOME_WL', 'WL']]
        logger.info(f"🎯 Target columns found: {target_columns}")
        
        # Sample a few rows to see the data
        sample_game = wnba_data.iloc[0]
        logger.info(f"📊 Sample game columns with values:")
        for col in ['win_prediction', 'HOME_WL', 'WL'] + win_columns:
            if col in sample_game:
                logger.info(f"   {col}: {sample_game[col]}")
                
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
