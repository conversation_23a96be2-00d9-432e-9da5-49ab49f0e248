# Base Configuration - HYPER MEDUSA NEURAL VAULT
# ===============================================
# Common configuration settings shared across all environments

database:
  host: "localhost"
  port: 5432
  name: "hyper_medusa_vault"
  user: "postgres"
  password: ""
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
  echo: false

redis:
  host: "localhost"
  port: 6379
  db: 0
  password: null
  max_connections: 50
  socket_timeout: 30
  socket_connect_timeout: 30

api:
  host: "0.0.0.0"
  port: 8000
  workers: 4
  timeout: 30
  max_requests: 1000
  max_requests_jitter: 50
  preload_app: true
  reload: false
  debug: false
  cors_origins:
    - "*"
  rate_limit: "100/minute"

ml:
  model_path: "models"
  batch_size: 32
  learning_rate: 0.001
  epochs: 100
  validation_split: 0.2
  early_stopping_patience: 10
  device: "auto"
  mixed_precision: true
  gradient_clipping: 1.0
  checkpoint_interval: 10

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: null
  max_bytes: 10485760  # 10MB
  backup_count: 5
  json_format: false
  structured_logging: true

security:
  secret_key: ""
  jwt_algorithm: "HS256"
  jwt_expiration: 3600  # 1 hour
  password_min_length: 8
  max_login_attempts: 5
  lockout_duration: 900  # 15 minutes
  encryption_key: null
  cors_enabled: true
  csrf_protection: true

monitoring:
  prometheus_enabled: true
  prometheus_port: 9090
  grafana_enabled: true
  grafana_port: 3000
  health_check_interval: 30
  metrics_retention_days: 30
  alerting_enabled: true
  slack_webhook: null
  email_alerts: false
