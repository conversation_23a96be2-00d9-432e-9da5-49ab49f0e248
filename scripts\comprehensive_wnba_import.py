import os
import sqlite3
import pandas as pd
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import numpy as np
from datetime import datetime
        import re

#!/usr/bin/env python3
"""
Comprehensive WNBA Data Import Script
Process all discovered WNBA files and import them into the database
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveWNBAImporter:
    """Import all WNBA data files into the unified database"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.imported_files = []
        self.failed_files = []
        self.total_records = 0
        
        # WNBA data directories to process
        self.wnba_directories = [
            "data/wnba_10year_historical",
            "data/smart_10year_historical", 
            "data"
        ]
        
        # File patterns to look for
        self.wnba_patterns = [
            "wnba_",
            "_wnba_",
            "WNBA_",
            "_WNBA_"
        ]
        
    def discover_wnba_files(self) -> List[str]:
        """Discover all WNBA data files"""
        logger.info("🔍 Discovering WNBA data files...")
        
        wnba_files = []
        
        for directory in self.wnba_directories:
            if os.path.exists(directory):
                logger.info(f"📂 Scanning {directory}/")
                
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        # Check if file matches WNBA patterns
                        if any(pattern in file for pattern in self.wnba_patterns):
                            # Only process CSV files (skip error files)
                            if file.endswith('.csv') and not file.endswith('_ERROR.txt'):
                                file_path = os.path.join(root, file)
                                file_size = os.path.getsize(file_path)
                                
                                # Skip tiny files (likely empty)
                                if file_size > 100:  # More than 100 bytes
                                    wnba_files.append(file_path)
                                    logger.info(f"   ✅ Found: {file} ({file_size:,} bytes)")
                                else:
                                    logger.info(f"   ⚠️ Skipped (too small): {file} ({file_size} bytes)")
        
        logger.info(f"🎯 Discovered {len(wnba_files)} WNBA data files")
        return wnba_files
    
    def analyze_file_structure(self, file_path: str) -> Dict[str, Any]:
        """Analyze the structure of a WNBA data file"""
        try:
            # Read first few rows to understand structure
            df = pd.read_csv(file_path, nrows=5)
            
            return {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'columns': list(df.columns),
                'num_columns': len(df.columns),
                'sample_data': df.head(2).to_dict('records') if not df.empty else [],
                'estimated_rows': self._estimate_file_rows(file_path),
                'file_size': os.path.getsize(file_path)
            }
        except Exception as e:
            logger.warning(f"⚠️ Could not analyze {file_path}: {e}")
            return {
                'file_path': file_path,
                'file_name': os.path.basename(file_path),
                'error': str(e),
                'file_size': os.path.getsize(file_path)
            }
    
    def _estimate_file_rows(self, file_path: str) -> int:
        """Estimate number of rows in file"""
        try:
            with open(file_path, 'r') as f:
                return sum(1 for line in f) - 1  # Subtract header
        except:
            return 0
    
    def categorize_wnba_files(self, wnba_files: List[str]) -> Dict[str, List[str]]:
        """Categorize WNBA files by data type"""
        logger.info("📊 Categorizing WNBA files by data type...")
        
        categories = {
            'player_stats': [],
            'team_stats': [],
            'game_data': [],
            'advanced_stats': [],
            'shot_data': [],
            'clutch_stats': [],
            'defense_stats': [],
            'bio_data': [],
            'other': []
        }
        
        for file_path in wnba_files:
            file_name = os.path.basename(file_path).lower()
            
            if 'player' in file_name and ('stats' in file_name or 'gamelog' in file_name):
                categories['player_stats'].append(file_path)
            elif 'team' in file_name and ('stats' in file_name or 'gamelog' in file_name):
                categories['team_stats'].append(file_path)
            elif 'game' in file_name or 'boxscore' in file_name:
                categories['game_data'].append(file_path)
            elif 'advanced' in file_name:
                categories['advanced_stats'].append(file_path)
            elif 'shot' in file_name or 'catch' in file_name or 'pullup' in file_name:
                categories['shot_data'].append(file_path)
            elif 'clutch' in file_name:
                categories['clutch_stats'].append(file_path)
            elif 'defense' in file_name:
                categories['defense_stats'].append(file_path)
            elif 'bio' in file_name:
                categories['bio_data'].append(file_path)
            else:
                categories['other'].append(file_path)
        
        # Log categorization results
        for category, files in categories.items():
            if files:
                logger.info(f"   📁 {category}: {len(files)} files")
        
        return categories
    
    def import_wnba_file(self, file_path: str, category: str) -> Dict[str, Any]:
        """Import a single WNBA file into the database"""
        try:
            # Read the file
            df = pd.read_csv(file_path)
            
            if df.empty:
                return {'success': False, 'error': 'Empty file', 'records': 0}
            
            # Extract metadata from filename
            file_name = os.path.basename(file_path)
            metadata = self._extract_metadata_from_filename(file_name)
            
            # Prepare data for database
            records = []
            for _, row in df.iterrows():
                record = {
                    'source_file': file_name,
                    'source_table': category,
                    'data_category': metadata.get('data_type', category),
                    'season': metadata.get('season'),
                    'league_id': '10',  # WNBA league ID
                    'league_name': 'WNBA',
                    'season_type': metadata.get('season_type', 'Regular Season'),
                    'player_id': row.get('PLAYER_ID') or row.get('player_id'),
                    'player_name': row.get('PLAYER_NAME') or row.get('player_name') or row.get('PLAYER'),
                    'team_id': row.get('TEAM_ID') or row.get('team_id'),
                    'team_abbreviation': row.get('TEAM_ABBREVIATION') or row.get('team_abbreviation') or row.get('TEAM'),
                    'team_name': row.get('TEAM_NAME') or row.get('team_name'),
                    'game_id': row.get('GAME_ID') or row.get('game_id'),
                    'game_date': row.get('GAME_DATE') or row.get('game_date'),
                    'stat_category': self._determine_stat_category(row, category),
                    'stat_value': self._extract_primary_stat(row, category),
                    'rank_position': row.get('RANK') or row.get('rank'),
                    'collection_id': f"wnba_import_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    'data_type': 'historical_wnba',
                    'raw_data': json.dumps(row.to_dict(), default=str),
                    'created_at': datetime.now().isoformat()
                }
                records.append(record)
            
            # Insert into database
            self._insert_records(records)
            
            return {
                'success': True,
                'records': len(records),
                'file_size': os.path.getsize(file_path)
            }
            
        except Exception as e:
            logger.error(f"❌ Error importing {file_path}: {e}")
            return {'success': False, 'error': str(e), 'records': 0}
    
    def _extract_metadata_from_filename(self, filename: str) -> Dict[str, str]:
        """Extract metadata from WNBA filename"""
        metadata = {}
        
        # Extract season
        season_match = re.search(r'(\d{4})', filename)
        if season_match:
            metadata['season'] = season_match.group(1)
        
        # Extract data type
        if 'advanced' in filename.lower():
            metadata['data_type'] = 'advanced_stats'
        elif 'base' in filename.lower():
            metadata['data_type'] = 'base_stats'
        elif 'bio' in filename.lower():
            metadata['data_type'] = 'bio_stats'
        elif 'clutch' in filename.lower():
            metadata['data_type'] = 'clutch_stats'
        elif 'defense' in filename.lower():
            metadata['data_type'] = 'defense_stats'
        elif 'shot' in filename.lower():
            metadata['data_type'] = 'shot_stats'
        elif 'game' in filename.lower():
            metadata['data_type'] = 'game_stats'
        else:
            metadata['data_type'] = 'general_stats'
        
        return metadata
    
    def _determine_stat_category(self, row: pd.Series, category: str) -> str:
        """Determine the primary stat category for this row"""
        # Look for common stat columns
        if 'PTS' in row.index or 'points' in str(row.index).lower():
            return 'points'
        elif 'REB' in row.index or 'rebounds' in str(row.index).lower():
            return 'rebounds'
        elif 'AST' in row.index or 'assists' in str(row.index).lower():
            return 'assists'
        elif 'FG_PCT' in row.index or 'fg_pct' in str(row.index).lower():
            return 'field_goal_percentage'
        else:
            return category
    
    def _extract_primary_stat(self, row: pd.Series, category: str) -> Optional[float]:
        """Extract the primary statistical value from the row"""
        # Priority order for extracting stats
        stat_columns = ['PTS', 'REB', 'AST', 'FG_PCT', 'MIN', 'GP', 'GS']
        
        for col in stat_columns:
            if col in row.index and pd.notna(row[col]):
                try:
                    return float(row[col])
                except (ValueError, TypeError):
                    continue
        
        # If no standard stat found, try to find any numeric column
        for col, value in row.items():
            if pd.notna(value):
                try:
                    return float(value)
                except (ValueError, TypeError):
                    continue
        
        return None
    
    def _insert_records(self, records: List[Dict]) -> None:
        """Insert records into the unified database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for record in records:
            try:
                cursor.execute("""
                    INSERT INTO unified_nba_wnba_data (
                        source_file, source_table, data_category, season, league_id, league_name,
                        season_type, player_id, player_name, team_id, team_abbreviation, team_name,
                        game_id, game_date, stat_category, stat_value, rank_position, collection_id,
                        data_type, raw_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['source_file'], record['source_table'], record['data_category'],
                    record['season'], record['league_id'], record['league_name'],
                    record['season_type'], record['player_id'], record['player_name'],
                    record['team_id'], record['team_abbreviation'], record['team_name'],
                    record['game_id'], record['game_date'], record['stat_category'],
                    record['stat_value'], record['rank_position'], record['collection_id'],
                    record['data_type'], record['raw_data'], record['created_at']
                ))
            except Exception as e:
                logger.warning(f"⚠️ Failed to insert record: {e}")
        
        conn.commit()
        conn.close()
    
    def run_comprehensive_import(self) -> Dict[str, Any]:
        """Run the complete WNBA data import process"""
        logger.info("🚀 STARTING COMPREHENSIVE WNBA DATA IMPORT")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        # Step 1: Discover files
        wnba_files = self.discover_wnba_files()
        
        if not wnba_files:
            logger.warning("⚠️ No WNBA files found!")
            return {'success': False, 'error': 'No WNBA files found'}
        
        # Step 2: Categorize files
        categories = self.categorize_wnba_files(wnba_files)
        
        # Step 3: Import files by category
        import_results = {}
        total_imported = 0
        
        for category, files in categories.items():
            if not files:
                continue
                
            logger.info(f"\n📊 Importing {category} files ({len(files)} files)...")
            category_results = []
            
            for file_path in files:
                logger.info(f"   📁 Processing: {os.path.basename(file_path)}")
                result = self.import_wnba_file(file_path, category)
                
                if result['success']:
                    self.imported_files.append(file_path)
                    total_imported += result['records']
                    logger.info(f"      ✅ Imported {result['records']:,} records")
                else:
                    self.failed_files.append((file_path, result.get('error', 'Unknown error')))
                    logger.error(f"      ❌ Failed: {result.get('error', 'Unknown error')}")
                
                category_results.append(result)
            
            import_results[category] = category_results
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # Final summary
        logger.info(f"\n🎯 WNBA IMPORT COMPLETE!")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   ✅ Files imported: {len(self.imported_files)}")
        logger.info(f"   ❌ Files failed: {len(self.failed_files)}")
        logger.info(f"   📊 Total records: {total_imported:,}")
        
        return {
            'success': True,
            'duration': str(duration),
            'files_imported': len(self.imported_files),
            'files_failed': len(self.failed_files),
            'total_records': total_imported,
            'import_results': import_results,
            'failed_files': self.failed_files
        }

def main():
    """Run the comprehensive WNBA import"""
    
    importer = ComprehensiveWNBAImporter()
    results = importer.run_comprehensive_import()
    
    if results['success']:
        
        if results['files_failed'] > 0:
            for file_path, error in results['failed_files']:
    else:
    
    return results

if __name__ == "__main__":
    main()
