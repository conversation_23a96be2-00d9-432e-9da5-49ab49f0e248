{"timestamp": "2025-07-03T23:36:56.479474", "files_analyzed": 88, "files_consolidated": 0, "files_archived": 0, "references_updated": 0, "legacy_systems": ["backend_api_testing", "backend_api_base", "vault_oracle_general", "vault_oracle_production", "general_general", "backend_api_development", "backend_api_general", "backend_api_production"], "unified_config_created": false, "error": "'LegacyConfigConsolidator' object has no attribute '_create_unified_config'"}