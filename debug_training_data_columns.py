#!/usr/bin/env python3
"""
Debug what columns are actually available in the training data
"""

import pandas as pd
import numpy as np
from pathlib import Path
import glob

def analyze_wnba_data_columns():
    """Analyze what columns are available in WNBA data"""
    print("🔍 ANALYZING WNBA DATA COLUMNS")
    print("=" * 60)
    
    # Find all WNBA CSV files
    data_dir = Path("data")
    wnba_files = []
    
    # Look for WNBA files
    for pattern in ["*wnba*.csv", "*WNBA*.csv", "*2023*.csv", "*2024*.csv", "*2025*.csv"]:
        wnba_files.extend(glob.glob(str(data_dir / pattern)))
    
    print(f"📁 Found {len(wnba_files)} potential WNBA files:")
    for file in wnba_files:
        print(f"   {file}")
    
    if not wnba_files:
        print("❌ No WNBA files found")
        return
    
    # Analyze the first file
    first_file = wnba_files[0]
    print(f"\n📊 Analyzing: {first_file}")
    
    try:
        df = pd.read_csv(first_file)
        print(f"📈 Shape: {df.shape}")
        print(f"📋 Columns ({len(df.columns)}):")
        
        for i, col in enumerate(df.columns):
            dtype = df[col].dtype
            non_null = df[col].count()
            sample_values = df[col].dropna().head(3).tolist()
            print(f"   {i+1:2d}. {col:<25} | {str(dtype):<10} | {non_null:>5} non-null | Sample: {sample_values}")
        
        # Check for numeric columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        print(f"\n🔢 Numeric columns ({len(numeric_cols)}):")
        for col in numeric_cols:
            print(f"   {col}")
        
        # Show sample data
        print(f"\n📋 Sample data (first 3 rows):")
        print(df.head(3).to_string())
        
        # Check if there are player-specific columns
        player_cols = [col for col in df.columns if any(word in col.lower() for word in ['player', 'name', 'id'])]
        print(f"\n👤 Player-related columns:")
        for col in player_cols:
            unique_count = df[col].nunique()
            print(f"   {col}: {unique_count} unique values")
            if unique_count < 20:
                print(f"      Values: {df[col].unique()[:10].tolist()}")
        
    except Exception as e:
        print(f"❌ Error reading {first_file}: {e}")

def check_feature_creation_during_training():
    """Check how features are created during training"""
    print("\n" + "=" * 60)
    print("🔍 CHECKING FEATURE CREATION LOGIC")
    print("=" * 60)
    
    # Import the training pipeline
    import sys
    sys.path.append('.')
    from src.neural_cortex.player_props_neural_pipeline import PlayerPropsDataset, PlayerPropsConfig
    
    # Create a test config
    config = PlayerPropsConfig(
        prop_type="points",
        include_player_features=True,
        include_matchup_features=True,
        include_situational_features=True,
        include_recent_form=True
    )
    
    print(f"📋 Expected feature names from _get_feature_names():")
    
    # Create a dummy dataset to get feature names
    try:
        # Create dummy data
        dummy_data = pd.DataFrame({
            'prop_target': [10.0, 15.0, 20.0],
            'stat_value': [12.0, 14.0, 18.0],
            'rank_position': [1, 2, 3]
        })
        
        dataset = PlayerPropsDataset(dummy_data, config)
        feature_names = dataset._get_feature_names()
        
        print(f"   Count: {len(feature_names)}")
        for i, name in enumerate(feature_names):
            print(f"   {i+1:2d}. {name}")
        
        # Check actual feature extraction
        print(f"\n🔧 Testing feature extraction:")
        features, targets = dataset._extract_features_and_targets(dummy_data)
        print(f"   Features shape: {features.shape}")
        print(f"   Targets shape: {targets.shape}")
        print(f"   Sample features: {features[0]}")
        
    except Exception as e:
        print(f"❌ Error testing feature creation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_wnba_data_columns()
    check_feature_creation_during_training()
