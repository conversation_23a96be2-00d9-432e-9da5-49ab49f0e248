#!/usr/bin/env python3
"""
Verify Clean Training Data Results
"""

import pandas as pd

def main():
    print('🎯 VERIFICATION: Clean Training Data Results')
    print('=' * 50)

    # Check NBA data
    nba_df = pd.read_csv('data/ml_training/nba_training_data.csv')
    print('🏀 NBA Training Data:')
    print(f'   Records: {len(nba_df):,}')
    print(f'   Teams: {nba_df["TEAM_ABBREVIATION"].nunique()}')
    nba_teams = sorted(nba_df['TEAM_ABBREVIATION'].unique())
    print(f'   Team List: {nba_teams}')

    print()

    # Check WNBA data  
    wnba_df = pd.read_csv('data/ml_training/wnba_training_data.csv')
    print('🏀 WNBA Training Data:')
    print(f'   Records: {len(wnba_df):,}')
    print(f'   Teams: {wnba_df["TEAM_ABBREVIATION"].nunique()}')
    wnba_teams = sorted(wnba_df['TEAM_ABBREVIATION'].unique())
    print(f'   Team List: {wnba_teams}')

    print()
    print('📊 TOTAL SUMMARY:')
    total_records = len(nba_df) + len(wnba_df)
    total_teams = nba_df['TEAM_ABBREVIATION'].nunique() + wnba_df['TEAM_ABBREVIATION'].nunique()
    print(f'   Total Records: {total_records:,}')
    print(f'   Total Teams: {total_teams}')
    print(f'   Expected: 43 teams (30 NBA + 13 WNBA)')

    # Verify no contamination
    all_teams = set(nba_df['TEAM_ABBREVIATION'].unique()) | set(wnba_df['TEAM_ABBREVIATION'].unique())
    print(f'   Unique teams across both leagues: {len(all_teams)}')

    if len(all_teams) == 43:
        print('✅ SUCCESS: Exactly 43 official teams found!')
        print('🧹 Data contamination crisis RESOLVED!')
    else:
        print(f'⚠️ WARNING: Found {len(all_teams)} teams, expected 43')

    # Check for overlapping teams
    nba_set = set(nba_teams)
    wnba_set = set(wnba_teams)
    overlap = nba_set & wnba_set
    print(f'   Overlapping team codes: {len(overlap)} - {sorted(list(overlap))}')

if __name__ == "__main__":
    main()
