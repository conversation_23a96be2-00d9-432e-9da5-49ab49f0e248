#!/usr/bin/env python3
"""
test_multi_objective_optimization.py
====================================

Test script for Multi-Objective Model Optimization System
"""

import asyncio
import numpy as np
import sys
import os
import logging

# Add current directory to path
sys.path.append(os.getcwd())

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

try:
    from multi_objective_model_optimization_system import (
        MultiObjectiveModelOptimizationSystem,
        MultiObjectiveConfig,
        OptimizationObjective,
        create_multi_objective_optimizer
    )
    print("✅ Successfully imported Multi-Objective Model Optimization System")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

async def test_multi_objective_optimization():
    """Test the Multi-Objective Model Optimization System"""
    print("🎯 Starting Multi-Objective Model Optimization Test")
    
    # Create sample basketball-like data
    np.random.seed(42)
    n_samples = 500
    n_features = 8
    
    # Generate features that simulate basketball statistics
    X = np.random.randn(n_samples, n_features)
    # Add some basketball-like patterns
    X[:, 0] = np.random.uniform(0, 50, n_samples)  # Points
    X[:, 1] = np.random.uniform(0, 20, n_samples)  # Rebounds
    X[:, 2] = np.random.uniform(0, 15, n_samples)  # Assists
    X[:, 3] = np.random.uniform(0.3, 0.6, n_samples)  # Field Goal %
    
    # Create target (win/loss)
    y = (X[:, 0] + X[:, 1] * 0.5 + X[:, 2] * 0.3 + X[:, 3] * 50 + np.random.randn(n_samples) * 5 > 35).astype(int)
    
    print(f"📊 Generated dataset: {X.shape[0]} samples, {X.shape[1]} features")
    print(f"📊 Target distribution: {np.bincount(y)}")
    
    # Create optimizer with basketball-specific objectives
    print("🎯 Creating Multi-Objective Optimizer...")
    optimizer = create_multi_objective_optimizer(
        objectives=[
            OptimizationObjective.ACCURACY,
            OptimizationObjective.F1_SCORE,
            OptimizationObjective.BASKETBALL_INTELLIGENCE
        ],
        n_trials=20,  # Reduced for testing
        enable_basketball_intelligence=True
    )
    
    # Override some config for faster testing
    optimizer.config.n_generations = 5
    optimizer.config.population_size = 10
    
    print("🎯 Starting optimization...")
    
    try:
        # Run optimization
        result = await optimizer.optimize_model(
            X=X, 
            y=y, 
            model_type="random_forest"
        )
        
        print("✅ Multi-Objective Optimization completed successfully!")
        print(f"🎯 Pareto frontier size: {len(result.pareto_frontier)}")
        print(f"🎯 Total evaluations: {result.total_evaluations}")
        print(f"🎯 Execution time: {result.execution_time:.2f}s")
        
        if result.best_compromise_solution:
            print(f"🎯 Best compromise solution objectives:")
            for obj, value in result.best_compromise_solution.objectives.items():
                print(f"   - {obj.value}: {value:.4f}")
        
        # Test basketball insights
        if result.basketball_insights:
            print("🏀 Basketball Insights Generated:")
            print(f"   - Pareto frontier analysis: {len(result.basketball_insights.get('pareto_frontier_analysis', {}))}")
            print(f"   - Basketball patterns: {len(result.basketball_insights.get('basketball_optimization_patterns', {}))}")
            print(f"   - Model recommendations: {len(result.basketball_insights.get('model_recommendations', {}))}")
        
        # Test convergence metrics
        if result.convergence_metrics:
            print("📈 Convergence Metrics:")
            for metric, value in result.convergence_metrics.items():
                print(f"   - {metric}: {value:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Optimization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_pareto_solution():
    """Test ParetoSolution class functionality"""
    print("🧪 Testing ParetoSolution class...")
    
    from multi_objective_model_optimization_system import ParetoSolution, OptimizationObjective
    
    # Create test solutions
    solution1 = ParetoSolution(
        parameters={'n_estimators': 100, 'max_depth': 10},
        objectives={
            OptimizationObjective.ACCURACY: 0.85,
            OptimizationObjective.F1_SCORE: 0.82
        }
    )
    
    solution2 = ParetoSolution(
        parameters={'n_estimators': 150, 'max_depth': 8},
        objectives={
            OptimizationObjective.ACCURACY: 0.80,
            OptimizationObjective.F1_SCORE: 0.88
        }
    )
    
    solution3 = ParetoSolution(
        parameters={'n_estimators': 120, 'max_depth': 12},
        objectives={
            OptimizationObjective.ACCURACY: 0.90,
            OptimizationObjective.F1_SCORE: 0.85
        }
    )
    
    # Test domination relationships
    print(f"Solution1 dominates Solution2: {solution1.dominates_solution(solution2)}")
    print(f"Solution2 dominates Solution1: {solution2.dominates_solution(solution1)}")
    print(f"Solution3 dominates Solution1: {solution3.dominates_solution(solution1)}")
    print(f"Solution3 dominates Solution2: {solution3.dominates_solution(solution2)}")
    
    print("✅ ParetoSolution tests completed")

async def main():
    """Main test function"""
    print("🚀 Starting Multi-Objective Model Optimization System Tests")
    print("=" * 60)
    
    # Test ParetoSolution functionality
    await test_pareto_solution()
    print()
    
    # Test full optimization system
    success = await test_multi_objective_optimization()
    
    print("=" * 60)
    if success:
        print("✅ All tests passed! Multi-Objective Model Optimization System is working correctly.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
