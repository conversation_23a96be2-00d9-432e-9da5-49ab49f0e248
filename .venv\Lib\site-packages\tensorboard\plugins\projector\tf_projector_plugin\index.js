// Copyright 2019 The TensorFlow Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//    http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

export function render() {
  const style = document.createElement('style');
  style.innerText = `
html,
body,
iframe {
  border: 0;
  display: block;
  height: 100%;
  margin: 0;
  width: 100%;
}`;
  document.head.appendChild(style);

  const iframe = document.createElement('iframe');
  iframe.src = './projector_binary.html';
  document.body.appendChild(iframe);
}
