{"config": {"base_models": ["xgboost", "random_forest", "gradient_boosting", "neural_network"], "meta_model": "logistic_regression", "ensemble_strategy": "stacking", "cv_folds": 5, "test_size": 0.2, "validation_size": 0.15, "random_state": 42, "target_accuracy": 0.75, "target_auc": 0.8, "target_f1": 0.75, "basketball_features": true, "temporal_modeling": true, "quantum_enhancement": true, "use_gpu": true, "parallel_training": true, "model_versioning": true, "performance_monitoring": true, "auto_hyperparameter_tuning": true, "feature_selection": true, "model_interpretability": true, "real_time_adaptation": true}, "feature_names": ["team_points", "opponent_points", "team_rebounds", "opponent_rebounds", "team_assists", "opponent_assists", "team_steals", "opponent_steals", "feature_8", "feature_9", "feature_10", "feature_11", "feature_12", "feature_13", "feature_14", "feature_15", "feature_16", "feature_17", "feature_18", "feature_19", "feature_20", "feature_21", "feature_22", "feature_23", "feature_24", "feature_25", "feature_26", "feature_27", "feature_28", "feature_29", "feature_30", "feature_31", "feature_32", "feature_33", "feature_34", "feature_35", "feature_36", "feature_37", "feature_38", "feature_39", "feature_40", "feature_41", "feature_42", "feature_43", "feature_44", "feature_45", "feature_46", "feature_47", "feature_48", "feature_49", "feature_50", "feature_51", "feature_52", "feature_53", "feature_54", "feature_55", "feature_56", "feature_57", "feature_58", "feature_59", "feature_60", "feature_61", "feature_62", "feature_63", "feature_64", "feature_65", "feature_66", "feature_67", "feature_68", "feature_69", "feature_70", "feature_71", "feature_72", "feature_73", "feature_74", "feature_75", "feature_76", "feature_77", "feature_78", "feature_79", "feature_80", "feature_81", "feature_82", "feature_83", "feature_84", "feature_85", "feature_86", "feature_87", "feature_88", "feature_89", "feature_90", "feature_91", "feature_92", "feature_93", "feature_94", "feature_95", "feature_96", "feature_97", "feature_98", "feature_99", "feature_100", "feature_101", "feature_102", "feature_103", "feature_104", "feature_105", "feature_106", "feature_107", "feature_108", "feature_109", "feature_110", "feature_111", "feature_112", "feature_113", "feature_114", "feature_115", "feature_116", "feature_117", "feature_118", "feature_119", "feature_120", "feature_121", "feature_122", "feature_123", "feature_124", "feature_125", "feature_126", "feature_127", "feature_128", "feature_129", "feature_130", "feature_131", "feature_132", "feature_133", "feature_134", "feature_135", "feature_136", "feature_137", "feature_138", "feature_139", "feature_140", "feature_141", "feature_142", "feature_143", "feature_144", "feature_145", "feature_146", "feature_147", "feature_148", "feature_149", "feature_150", "feature_151", "feature_152", "feature_153", "feature_154", "feature_155", "feature_156", "feature_157", "feature_158", "feature_159", "feature_160", "feature_161", "feature_162", "feature_163", "feature_164", "feature_165", "feature_166", "feature_167", "feature_168", "feature_169", "feature_170", "feature_171", "feature_172", "feature_173", "feature_174", "feature_175", "feature_176", "feature_177", "feature_178", "feature_179", "feature_180", "feature_181", "feature_182", "feature_183", "feature_184", "feature_185", "feature_186", "feature_187", "feature_188", "feature_189", "feature_190", "feature_191", "feature_192", "feature_193", "feature_194", "feature_195", "feature_196", "feature_197", "feature_198", "feature_199", "feature_200", "feature_201", "feature_202", "feature_203", "feature_204", "feature_205", "feature_206", "feature_207", "feature_208", "feature_209", "feature_210", "feature_211", "feature_212", "feature_213", "feature_214", "feature_215", "feature_216", "feature_217", "feature_218", "feature_219", "feature_220", "feature_221", "feature_222", "feature_223", "feature_224", "feature_225", "feature_226", "feature_227", "feature_228", "feature_229", "feature_230", "feature_231", "feature_232", "feature_233", "feature_234", "feature_235", "feature_236", "feature_237", "feature_238", "feature_239", "feature_240", "feature_241", "feature_242", "feature_243", "feature_244", "feature_245", "feature_246", "feature_247", "feature_248", "feature_249", "feature_250", "feature_251", "feature_252", "feature_253", "feature_254", "feature_255", "feature_256", "feature_257", "feature_258", "feature_259", "feature_260", "feature_261", "feature_262", "feature_263", "feature_264", "feature_265", "feature_266", "feature_267", "feature_268", "feature_269", "feature_270", "feature_271", "feature_272", "feature_273", "feature_274", "feature_275", "feature_276", "feature_277", "feature_278", "feature_279", "feature_280", "feature_281", "feature_282", "feature_283", "feature_284", "feature_285", "feature_286", "feature_287", "feature_288", "feature_289", "feature_290", "feature_291", "feature_292", "feature_293", "feature_294", "feature_295", "feature_296", "feature_297", "feature_298", "feature_299", "feature_300", "feature_301", "feature_302", "feature_303", "feature_304", "feature_305", "feature_306", "feature_307", "feature_308", "feature_309", "feature_310", "feature_311", "feature_312", "feature_313", "feature_314", "feature_315", "feature_316", "feature_317", "feature_318", "feature_319", "feature_320", "feature_321", "feature_322", "feature_323", "feature_324", "feature_325", "feature_326", "feature_327", "feature_328", "feature_329", "feature_330", "feature_331", "feature_332", "feature_333", "feature_334", "feature_335", "feature_336", "feature_337", "feature_338", "feature_339", "feature_340", "feature_341", "feature_342", "feature_343", "feature_344", "feature_345", "feature_346", "feature_347", "feature_348", "feature_349", "feature_350", "feature_351", "feature_352", "feature_353", "feature_354", "feature_355", "feature_356", "feature_357", "feature_358", "feature_359", "feature_360", "feature_361", "feature_362", "feature_363", "feature_364", "feature_365", "feature_366", "feature_367", "feature_368", "feature_369", "feature_370", "feature_371", "feature_372", "feature_373", "feature_374", "feature_375", "feature_376", "feature_377", "feature_378", "feature_379", "feature_380", "feature_381", "feature_382", "feature_383", "feature_384", "feature_385", "feature_386", "feature_387", "feature_388", "feature_389", "feature_390", "feature_391", "feature_392", "feature_393", "feature_394", "feature_395", "feature_396", "feature_397", "feature_398", "feature_399", "feature_400", "feature_401", "feature_402", "feature_403", "feature_404", "feature_405", "feature_406", "feature_407", "feature_408", "feature_409", "feature_410", "feature_411", "feature_412", "feature_413", "feature_414", "feature_415", "feature_416", "feature_417", "feature_418", "feature_419", "feature_420", "feature_421", "feature_422", "feature_423", "feature_424", "feature_425"], "model_paths": {"xgboost": "models\\ensemble\\xgboost_20250701_131645.pkl", "random_forest": "models\\ensemble\\random_forest_20250701_131645.pkl", "gradient_boosting": "models\\ensemble\\gradient_boosting_20250701_131645.pkl", "meta_model": "models\\ensemble\\meta_model_20250701_131645.pkl"}, "training_timestamp": "20250701_131645"}