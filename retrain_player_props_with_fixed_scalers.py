#!/usr/bin/env python3
"""
🔧 RETRAIN PLAYER PROPS MODELS WITH FIXED SCALER PERSISTENCE
============================================================

This script retrains all WNBA player props models with the corrected
scaler saving logic that stores scaler parameters instead of objects.

The issue: PyTorch's torch.save() doesn't reliably serialize sklearn objects.
The fix: Save only scaler parameters (mean_, scale_, etc.) and reconstruct on load.
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path
sys.path.append('.')

from src.neural_cortex.player_props_neural_pipeline import PlayerPropsTrainingPipeline, create_player_props_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def retrain_all_player_props():
    """Retrain all WNBA player props models with fixed scaler persistence"""
    
    print("🔧 RETRAINING PLAYER PROPS MODELS WITH FIXED SCALER PERSISTENCE")
    print("=" * 80)
    print("🎯 Goal: Fix scaler serialization by saving parameters instead of objects")
    print("📊 Models: points, rebounds, assists, steals, blocks, threes")
    print("🏀 League: WNBA")
    print()
    
    # Define all prop types to retrain
    prop_types = ["points", "rebounds", "assists", "steals", "blocks", "threes"]
    
    successful_trainings = 0
    failed_trainings = []
    
    for i, prop_type in enumerate(prop_types, 1):
        print(f"🚀 TRAINING {i}/{len(prop_types)}: {prop_type.upper()}")
        print("-" * 50)
        
        try:
            # Create optimized config for this prop type
            config = create_player_props_config(
                league="WNBA",
                prop_type=prop_type,

                # Basic training parameters
                batch_size=64,
                learning_rate=0.001,
                num_epochs=25,
                early_stopping_patience=8,

                # Model architecture
                hidden_dim=256,
                num_layers=4,
                dropout_rate=0.3,

                # Regularization
                weight_decay=1e-4
            )
            
            # Initialize training pipeline
            pipeline = PlayerPropsTrainingPipeline(config)
            
            # Train the model
            print(f"🔥 Starting {prop_type} model training...")
            training_results = await pipeline.train()
            
            if training_results and training_results.get('success', False):
                print(f"✅ {prop_type} model training completed successfully!")
                print(f"📊 Best validation loss: {training_results.get('best_val_loss', 'N/A'):.4f}")
                print(f"🎯 Final accuracy: {training_results.get('final_accuracy', 'N/A'):.1f}%")
                successful_trainings += 1
            else:
                print(f"❌ {prop_type} model training failed!")
                failed_trainings.append(prop_type)
                
        except Exception as e:
            print(f"💥 Error training {prop_type} model: {e}")
            failed_trainings.append(prop_type)
            logger.error(f"Training error for {prop_type}: {e}")
        
        print()
    
    # Final summary
    print("=" * 80)
    print("📊 RETRAINING SUMMARY")
    print("=" * 80)
    print(f"✅ Successful: {successful_trainings}/{len(prop_types)} models")
    print(f"❌ Failed: {len(failed_trainings)}/{len(prop_types)} models")
    
    if failed_trainings:
        print(f"💥 Failed models: {', '.join(failed_trainings)}")
    
    if successful_trainings == len(prop_types):
        print("🎉 ALL MODELS RETRAINED SUCCESSFULLY!")
        print("🔧 Scaler persistence issue has been resolved")
        print("📈 Models now save scaler parameters for reliable loading")
    else:
        print("⚠️ Some models failed to retrain - check logs for details")
    
    print()
    print("🔍 WHAT WAS FIXED:")
    print("- Scaler objects are no longer saved directly in checkpoints")
    print("- Only scaler parameters (mean_, scale_, var_, etc.) are saved")
    print("- StandardScaler objects are reconstructed on model loading")
    print("- This ensures reliable scaler persistence across PyTorch saves")
    print()
    
    return successful_trainings, failed_trainings

if __name__ == "__main__":
    asyncio.run(retrain_all_player_props())
