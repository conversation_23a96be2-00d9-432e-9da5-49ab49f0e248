{"training_session": {"timestamp": "2025-06-30T01:27:24.708784", "database": "medusa_master.db", "database_size_mb": 599.91}, "training_results": {"hybrid_training": {"status": "success", "training_results": {}, "report": {"training_timestamp": "2025-06-30T01:27:14.044046", "database_path": "medusa_master.db", "sklearn_available": true, "pandas_available": true, "training_results": {}, "data_summary": {"total_features": 10, "total_targets": 0, "feature_names": ["league_name_encoded", "season_type_encoded", "player_name_encoded", "team_abbreviation_encoded", "stat_value", "rank_position", "league_name_encoded", "season_type_encoded", "player_name_encoded", "team_abbreviation_encoded"], "target_names": []}, "models_trained": [], "models_directory": "models/hybrid_trained", "status": "failed"}, "data_summary": {"tables_loaded": ["advanced_stats", "league_leaders", "game_data", "assist_tracker"], "features_engineered": 10, "models_trained": []}}, "standard_training": {"nba": {"status": "failed", "reason": "no_data"}, "wnba": {"status": "failed", "reason": "no_data"}}, "neural_training": {"status": "failed", "error": "Caught TypeError in DataLoader worker process 0.\nOriginal Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\HYPER_MEDUSA_NEURAL_VAULT\\.venv\\Lib\\site-packages\\torch\\utils\\data\\_utils\\worker.py\", line 349, in _worker_loop\n    data = fetcher.fetch(index)  # type: ignore[possibly-undefined]\n           ^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\HYPER_MEDUSA_NEURAL_VAULT\\.venv\\Lib\\site-packages\\torch\\utils\\data\\_utils\\fetch.py\", line 52, in fetch\n    data = [self.dataset[idx] for idx in possibly_batched_index]\n            ~~~~~~~~~~~~^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\HYPER_MEDUSA_NEURAL_VAULT\\src\\neural_cortex\\neural_training_pipeline.py\", line 363, in __getitem__\n    label = torch.LongTensor([self.labels[idx]])\n            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\nTypeError: 'numpy.float32' object cannot be interpreted as an integer\n"}}, "summary": {"hybrid_success": true, "standard_success": true, "neural_success": false, "models_trained": 0}}