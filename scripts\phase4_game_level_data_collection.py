import sqlite3
import pandas as pd
import logging
from typing import Dict, List, Any, <PERSON>ple
import requests
import time
import json
from datetime import datetime, timedelta
import sys
import os
from collections import defaultdict
from smart_duplicate_prevention_system import SmartDuplicatePreventionSystem


#!/usr/bin/env python3
"""
Phase 4 - Game-Level Data Collection
Collect high-volume game-level data including box scores, play-by-play, and shot charts
"""


# Add the scripts directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase4GameLevelDataCollector:
    """Collect high-volume game-level data with focus on recent seasons"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.base_url = "https://stats.nba.com/stats"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.nba.com/',
            'Connection': 'keep-alive',
        }
        
        # Phase 4 Game-Level Endpoints
        self.phase4_endpoints = {
            'boxscoretraditionalv2': {
                'description': 'Traditional box scores for games',
                'success_rate': 'HIGH',
                'params': {
                    'GameID': None,
                    'StartPeriod': 1,
                    'EndPeriod': 10,
                    'StartRange': 0,
                    'EndRange': 28800,
                    'RangeType': 0
                }
            },
            'boxscoreadvancedv2': {
                'description': 'Advanced box scores for games',
                'success_rate': 'MEDIUM',
                'params': {
                    'GameID': None,
                    'StartPeriod': 1,
                    'EndPeriod': 10,
                    'StartRange': 0,
                    'EndRange': 28800,
                    'RangeType': 0
                }
            },
            'boxscoreplayertrackv2': {
                'description': 'Player tracking box scores',
                'success_rate': 'MEDIUM',
                'params': {
                    'GameID': None
                }
            },
            'boxscoremiscv2': {
                'description': 'Miscellaneous box score stats',
                'success_rate': 'HIGH',
                'params': {
                    'GameID': None,
                    'StartPeriod': 1,
                    'EndPeriod': 10,
                    'StartRange': 0,
                    'EndRange': 28800,
                    'RangeType': 0
                }
            },
            'boxscorescoringv2': {
                'description': 'Scoring breakdown box scores',
                'success_rate': 'HIGH',
                'params': {
                    'GameID': None,
                    'StartPeriod': 1,
                    'EndPeriod': 10,
                    'StartRange': 0,
                    'EndRange': 28800,
                    'RangeType': 0
                }
            },
            'boxscoreusagev2': {
                'description': 'Usage statistics box scores',
                'success_rate': 'HIGH',
                'params': {
                    'GameID': None,
                    'StartPeriod': 1,
                    'EndPeriod': 10,
                    'StartRange': 0,
                    'EndRange': 28800,
                    'RangeType': 0
                }
            }
        }
        
        # Priority seasons (recent first for maximum impact)
        self.seasons = {
            'NBA': ['2024-25', '2023-24', '2022-23', '2021-22', '2020-21'],
            'WNBA': ['2025', '2024', '2023', '2022', '2021']
        }
        
        # Initialize duplicate prevention
        logger.info("🔍 Initializing Smart Duplicate Prevention System...")
        self.duplicate_prevention = SmartDuplicatePreventionSystem(db_path)
        logger.info("✅ Duplicate prevention system ready")
        
    def get_season_games(self, league: str, season: str) -> List[str]:
        """Get list of game IDs for a season"""
        logger.info(f"📅 Getting game IDs for {league} {season}...")
        
        try:
            # Use leaguegamelog to get all games for the season
            url = f"{self.base_url}/leaguegamelog"
            params = {
                'Season': season,
                'SeasonType': 'Regular Season',
                'LeagueID': '00' if league == 'NBA' else '10',
                'PlayerOrTeam': 'T',
                'Direction': 'DESC',
                'Sorter': 'DATE'
            }
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            game_ids = set()
            if 'resultSets' in data and len(data['resultSets']) > 0:
                headers = data['resultSets'][0].get('headers', [])
                rows = data['resultSets'][0].get('rowSet', [])
                
                if 'GAME_ID' in headers:
                    game_id_idx = headers.index('GAME_ID')
                    for row in rows:
                        if len(row) > game_id_idx:
                            game_ids.add(str(row[game_id_idx]))
            
            game_list = list(game_ids)
            logger.info(f"✅ Found {len(game_list)} games for {league} {season}")
            
            time.sleep(0.6)  # Rate limiting
            return game_list
            
        except Exception as e:
            logger.warning(f"⚠️ Error getting games for {league} {season}: {e}")
            return []
    
    def collect_game_endpoint_data(self, endpoint: str, game_id: str, league: str, season: str) -> List[Dict[str, Any]]:
        """Collect data from a game endpoint"""
        endpoint_config = self.phase4_endpoints[endpoint]
        
        # Check if we already have this data using source file signature
        source_file_signature = f"{endpoint}_game_{game_id}.json"
        if source_file_signature in self.duplicate_prevention.existing_source_files:
            return []
        
        collected_data = []
        
        try:
            
            url = f"{self.base_url}/{endpoint}"
            params = endpoint_config['params'].copy()
            
            # Set game ID parameter
            params['GameID'] = game_id
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            if 'resultSets' in data and len(data['resultSets']) > 0:
                for result_set_idx, result_set in enumerate(data['resultSets']):
                    headers = result_set.get('headers', [])
                    rows = result_set.get('rowSet', [])
                    result_set_name = result_set.get('name', f'ResultSet_{result_set_idx}')
                    
                    for row_idx, row in enumerate(rows):
                        if len(row) == len(headers):
                            row_dict = dict(zip(headers, row))
                            
                            # Create comprehensive record
                            record = {
                                'league_name': league,
                                'league_id': '00' if league == 'NBA' else '10',
                                'season': season,
                                'game_id': game_id,
                                'endpoint': endpoint,
                                'result_set_name': result_set_name,
                                'data_category': f'phase4_game_{endpoint}',
                                'data_type': f'{endpoint}_game_data',
                                'source_file': f"{endpoint}_game_{game_id}.json",
                                'source_table': endpoint,
                                'raw_data': json.dumps(row_dict),
                                'created_at': datetime.now().isoformat()
                            }
                            
                            # Extract key information
                            self._extract_game_data_fields(record, row_dict, endpoint)
                            
                            collected_data.append(record)
            
            time.sleep(0.6)  # Rate limiting
            
        except Exception as e:
            logger.warning(f"⚠️ Error collecting {endpoint} for game {game_id}: {e}")
        
        return collected_data
    
    def _extract_game_data_fields(self, record: Dict[str, Any], row_dict: Dict[str, Any], endpoint: str) -> None:
        """Extract key fields from game data"""
        
        # Common extractions
        record['player_id'] = row_dict.get('PLAYER_ID') or row_dict.get('TEAM_ID') or 'GAME_DATA'
        record['player_name'] = row_dict.get('PLAYER_NAME') or row_dict.get('TEAM_NAME') or 'GAME_RECORD'
        record['team_id'] = row_dict.get('TEAM_ID')
        record['team_abbreviation'] = row_dict.get('TEAM_ABBREVIATION')
        record['game_date'] = row_dict.get('GAME_DATE')
        
        # Endpoint-specific extractions
        if endpoint == 'boxscoretraditionalv2':
            record['stat_category'] = 'traditional_boxscore'
            record['stat_value'] = row_dict.get('PTS', 0)
            record['minutes'] = row_dict.get('MIN', 0)
            record['field_goals_made'] = row_dict.get('FGM', 0)
            record['field_goals_attempted'] = row_dict.get('FGA', 0)
            record['rebounds'] = row_dict.get('REB', 0)
            record['assists'] = row_dict.get('AST', 0)
            
        elif endpoint == 'boxscoreadvancedv2':
            record['stat_category'] = 'advanced_boxscore'
            record['stat_value'] = row_dict.get('OFF_RATING', 0)
            record['offensive_rating'] = row_dict.get('OFF_RATING', 0)
            record['defensive_rating'] = row_dict.get('DEF_RATING', 0)
            record['net_rating'] = row_dict.get('NET_RATING', 0)
            record['pace'] = row_dict.get('PACE', 0)
            
        elif endpoint == 'boxscoreplayertrackv2':
            record['stat_category'] = 'player_tracking'
            record['stat_value'] = row_dict.get('DIST_MILES', 0)
            record['distance_miles'] = row_dict.get('DIST_MILES', 0)
            record['avg_speed'] = row_dict.get('AVG_SPEED', 0)
            record['touches'] = row_dict.get('TOUCHES', 0)
            
        elif endpoint == 'boxscoremiscv2':
            record['stat_category'] = 'misc_boxscore'
            record['stat_value'] = row_dict.get('PTS_OFF_TOV', 0)
            record['points_off_turnovers'] = row_dict.get('PTS_OFF_TOV', 0)
            record['second_chance_points'] = row_dict.get('PTS_2ND_CHANCE', 0)
            record['fast_break_points'] = row_dict.get('PTS_FB', 0)
            
        elif endpoint == 'boxscorescoringv2':
            record['stat_category'] = 'scoring_boxscore'
            record['stat_value'] = row_dict.get('PCT_PTS_2PT', 0)
            record['pct_points_2pt'] = row_dict.get('PCT_PTS_2PT', 0)
            record['pct_points_3pt'] = row_dict.get('PCT_PTS_3PT', 0)
            record['pct_points_ft'] = row_dict.get('PCT_PTS_FT', 0)
            
        elif endpoint == 'boxscoreusagev2':
            record['stat_category'] = 'usage_boxscore'
            record['stat_value'] = row_dict.get('USG_PCT', 0)
            record['usage_pct'] = row_dict.get('USG_PCT', 0)
            record['pace'] = row_dict.get('PACE', 0)
            record['pie'] = row_dict.get('PIE', 0)
    
    def insert_game_data(self, data_batch: List[Dict[str, Any]]) -> int:
        """Insert game data with duplicate prevention"""
        if not data_batch:
            return 0
        
        # Filter duplicates
        new_records, duplicate_stats = self.duplicate_prevention.filter_new_data_only(data_batch)
        
        if duplicate_stats['duplicates'] > 0:
        
        if not new_records:
            return 0
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        inserted_count = 0
        
        for record in new_records:
            try:
                cursor.execute("""
                    INSERT INTO unified_nba_wnba_data (
                        source_file, source_table, data_category, season, league_id, league_name,
                        season_type, player_id, player_name, team_id, team_abbreviation,
                        stat_category, stat_value, game_id, game_date, data_type, raw_data, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    record['source_file'], record['source_table'], record['data_category'],
                    record['season'], record['league_id'], record['league_name'],
                    'Regular Season', record['player_id'], record['player_name'],
                    record.get('team_id'), record.get('team_abbreviation'),
                    record.get('stat_category'), record.get('stat_value'),
                    record.get('game_id'), record.get('game_date'),
                    record['data_type'], record['raw_data'], record['created_at']
                ))
                inserted_count += 1
                
            except Exception as e:
                logger.warning(f"⚠️ Error inserting record: {e}")
                continue
        
        conn.commit()
        conn.close()
        
        return inserted_count
    
    def collect_season_game_data(self, league: str, season: str, max_games: int = 100) -> Dict[str, Any]:
        """Collect game data for a season with rate limiting"""
        logger.info(f"🎮 COLLECTING GAME DATA - {league} {season}")
        logger.info("=" * 55)
        
        start_time = datetime.now()
        total_records = 0
        
        # Get games for the season
        game_ids = self.get_season_games(league, season)
        
        if not game_ids:
            logger.warning(f"⚠️ No games found for {league} {season}")
            return {'success': False, 'total_records': 0}
        
        # Limit games for testing/rate limiting
        if len(game_ids) > max_games:
            game_ids = game_ids[:max_games]
            logger.info(f"📊 Processing first {max_games} games (rate limiting)")
        
        games_processed = 0
        
        for game_id in game_ids:
            logger.info(f"\n🏀 Processing Game {game_id} ({games_processed + 1}/{len(game_ids)})...")
            
            game_total = 0
            
            # Collect data from each endpoint
            for endpoint, config in self.phase4_endpoints.items():
                endpoint_data = self.collect_game_endpoint_data(endpoint, game_id, league, season)
                
                if endpoint_data:
                    inserted = self.insert_game_data(endpoint_data)
                    game_total += inserted
                    total_records += inserted
                    if inserted > 0:
                        logger.info(f"   ✅ {endpoint}: {inserted:,} records")
            
            games_processed += 1
            logger.info(f"   🎯 Game {game_id} total: {game_total:,} records")
            
            # Rate limiting between games
            time.sleep(1.0)
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎉 {league} {season} GAME COLLECTION COMPLETE!")
        logger.info(f"   🎮 Games processed: {games_processed}")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   📊 Total records: {total_records:,}")
        
        return {
            'success': True,
            'league': league,
            'season': season,
            'games_processed': games_processed,
            'duration': str(duration),
            'total_records': total_records
        }
    
    def collect_phase4_game_data(self, league: str, max_games_per_season: int = 50) -> Dict[str, Any]:
        """Collect all Phase 4 game data for a league"""
        logger.info(f"🚀 PHASE 4 GAME-LEVEL DATA COLLECTION - {league}")
        logger.info("=" * 70)
        
        start_time = datetime.now()
        total_records = 0
        total_games = 0
        
        for season in self.seasons[league]:
            logger.info(f"\n📅 Processing {league} {season}...")
            
            season_results = self.collect_season_game_data(league, season, max_games_per_season)
            
            if season_results['success']:
                total_records += season_results['total_records']
                total_games += season_results['games_processed']
                logger.info(f"✅ {season}: {season_results['total_records']:,} records from {season_results['games_processed']} games")
            else:
                logger.warning(f"⚠️ {season}: Failed to collect data")
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info(f"\n🎉 PHASE 4 {league} GAME COLLECTION COMPLETE!")
        logger.info(f"   🎮 Total games processed: {total_games}")
        logger.info(f"   ⏱️ Duration: {duration}")
        logger.info(f"   📊 Total records: {total_records:,}")
        
        return {
            'success': True,
            'league': league,
            'duration': str(duration),
            'total_records': total_records,
            'total_games': total_games
        }

def main():
    """Execute Phase 4 game-level data collection"""
    
    collector = Phase4GameLevelDataCollector()
    
    # Start with NBA (limited games for rate limiting)
    nba_results = collector.collect_phase4_game_data('NBA', max_games_per_season=25)
    
    if nba_results['success']:
        
        # Then WNBA (limited games for rate limiting)
        wnba_results = collector.collect_phase4_game_data('WNBA', max_games_per_season=15)
        
        if wnba_results['success']:
            
            total_records = nba_results['total_records'] + wnba_results['total_records']
            total_games = nba_results['total_games'] + wnba_results['total_games']
            
            
    
    return nba_results

if __name__ == "__main__":
    main()
